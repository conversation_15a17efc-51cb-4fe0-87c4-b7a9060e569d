﻿using AutoMapper;
using Domain.Search;
using Gina2.Core;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.MySqlRepository.Models;
using Gina2.SPs;
using Gina2.SqlRepository.Models;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Linq.Dynamic.Core;
using System.Xml.Serialization;
using Gina2.SqlRepository.Cache;
using Gina2.Core.Methods;
using Gina2.DbModels.PolicyDrafts;
using Microsoft.IdentityModel.Tokens;

namespace Gina2.MySqlRepository.Repositories
{
    public sealed class PolicyRepository : IPolicyRepository
    {
        private readonly IMapper _mapper;
        private List<Topic> topicList = new();
        private List<TopicParent> topicParentList = new();
        private readonly IDbContextFactory<SqlDbContext> _DbFactory;
        private readonly ILogger<PolicyRepository> _logger;
        private readonly ICacheService _cacheService;

        private string cacheKey = "CombinedSearchResult";
        private string cacheCountryKey = "CountryResult";
        // private readonly string cacheKey = "cachedData";
        public PolicyRepository(IMapper mapper, IDbContextFactory<SqlDbContext> DbFactory, ILogger<PolicyRepository> logger, ICacheService cacheService)
        {
            _DbFactory = DbFactory;
            _mapper = mapper;
            _logger = logger;
            _cacheService = cacheService;
        }


        public static T DeserializeXml<T>(string xml)
        {
            XmlSerializer serializer = new XmlSerializer(typeof(T));
            using (TextReader reader = new StringReader(xml))
            {
                return (T)serializer.Deserialize(reader);
            }
        }

        public async Task<List<PolicyIdAndTitle>> GetAvailablePolicyTitleAndId()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.Policies.Select(e => new PolicyIdAndTitle
            {
                Id = e.Id,
                Title = e.Title
            }).ToListAsync();
        }
        public async Task<List<Policy>> GetPoliciesAsync(string countryCode)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.Policies.AsQueryable();
            query.Select(p => p.PolicyCountryMap).Load();
            query.Select(p => p.PolicyType).Load();

            query = query.Where(p => p.PolicyCountryMap.Any(m => !string.IsNullOrWhiteSpace(m.CountryCode) && m.CountryCode.Equals(countryCode)));

            //query =
            //        from c in query
            //        join pt in _dbContext.PolicyRevisions on c.Id equals pt.VersionId into ps_jointable
            //        from p in ps_jointable.DefaultIfEmpty()
            //        where p.Id > 0
            //        select c;

            query = query
                      .GroupBy(p => p.Id)
                      .Select(g => g.First());

            return await query.ToListAsync();
        }

        public async Task<List<PolicyType>> GetPolicyTypesAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyTypes.OrderBy(o => o.DragAndDropKey).ToListAsync();
        }

        public async Task<Policy> GetPolicyAsync(int id)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.Policies
            .Where(p => p.Id == id);
            query.Select(p => p.PolicyType).Load();
            query.Select(p => p.PolicyCountryMap).Load();
            query.Select(p => p.PolicyCountryMap.Select(c => c.Country)).Load();
            query.Select(p => p.PolicyPartnerCategories).Load();
            query.Select(p => p.PolicyPartnerCategories.Select(a => a.PartnerCategory)).Load();
            query.Select(p => p.PolicyCatogeryPartnerMap).Load();
            query.Select(p => p.PolicyCatogeryPartnerMap.Select(a => a.Partner)).Load();
            query.Select(p => p.PolicyCatogeryPartnerMap.Select(a => a.PartnerCategory)).Load();
            query.Select(p => p.PolicyPrograms).Load();
            query.Select(p => p.PolicyPrograms.Select(q => q.Program)).Load();
            query.Select(p => p.PolicyTopics).Load();
            query.Select(p => p.PolicyTopics.Select(t => t.Topic)).Load();
            query.Select(p => p.PolicyTopics.Select(t => t.Topic).Select(w => w.TopicParents)).Load();
            query.Select(p => p.PolicyAttachments).Load();
            return await query.FirstOrDefaultAsync();
        }

        private bool IsParentAndContainsParentId(List<SelectedTopicsTree> topics, int parentId)
        {
            foreach (var topic in topics)
            {
                if (topic.TopicId == parentId && topic.ChildTopics != null && topic.ChildTopics.Any())
                {
                    return true;
                }

                // Recursively check in the child topics
                if (topic.ChildTopics != null && topic.ChildTopics.Any() && IsParentAndContainsParentId(topic.ChildTopics, parentId))
                {
                    return true;
                }
            }
            return false;
        }

        public async Task<PolicySearchResponse> SearchAsync(SearchRequest searchRequest)
        {
            PolicySearchResponse response = new();

            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.ViewPolicyLists.AsNoTracking().AsQueryable();

            if ((searchRequest.SelectedPolicyTopics != null && searchRequest.SelectedPolicyTopics.Any()) ||
                (searchRequest.SelectedTopcis != null && searchRequest.SelectedTopcis.Any()))
            {
                var themeGrandChildrenTopicIds = _dbContext.TopicParents
                    .Where(i => IsParentAndContainsParentId(searchRequest.SelectedPolicyTopics, i.ParentId) || 
                                searchRequest.SelectedTopcis.Contains(i.ParentId))
                    .Select(i => i.TopicId)
                    .Distinct()
                    .ToList();

                var topicRelatedPolicyIds = _dbContext.PolicyTopics
                    .Where(i => themeGrandChildrenTopicIds.Contains(i.TopicId))
                    .Select(i => i.PolicyId);

                query = query.Where(p => topicRelatedPolicyIds.Any(s => s == p.Id));
            }

            if (searchRequest.SelectedSearchTopics != null && searchRequest.SelectedSearchTopics.Any())
            {
                var searchTopicRelatedPolicyIds = _dbContext.PolicyTopics
                                   .Where(i => searchRequest.SelectedSearchTopics.ContainsKey(i.TopicId))
                                   .Select(i => i.PolicyId);

                query = query.Where(p => searchTopicRelatedPolicyIds.Any(s => s == p.Id));
            }

            string searchText = searchRequest.SearchText;

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                query = query.Where(p => p.Title.Contains(searchText)
                    || p.EnglishTitle.Contains(searchText)
                    || p.AdoptedBy.Contains(searchText)
                    || p.PublishedBy.Contains(searchText)
                    || p.PolicyExtractByOriginalLanguage.Contains(searchText)
                    || p.References.Contains(searchText)
                    || p.Pdfs.Contains(searchText)
                    || p.FurtherNotes.Contains(searchText));
            }

            if (searchRequest.StartYear.HasValue)
            {
                query = query.Where(q => q.PublishedYear.HasValue && q.PublishedYear >= searchRequest.StartYear.Value);
            }

            if (searchRequest.EndYear.HasValue)
            {
                query = query.Where(q => q.PublishedYear.HasValue && q.PublishedYear <= searchRequest.EndYear.Value);
            }

            if (!string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                query = query.Where(r => r.RegionCode.Contains(searchRequest.SelectedRegionCode));
            }
            if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeGroup))
            {
                query = query.Where(r => r.CountryIncomeGroup.Contains(searchRequest.SelectedIncomeGroup));
            }
            if (searchRequest.SelectedCountries.Any())
            {
                query = query.Where(r => searchRequest.SelectedCountries.Contains(r.Country));
            }

            string sortingColumnWithOrder = $"{searchRequest.SortingColumn} {(searchRequest.IsDescendingOrder ? "descending" : "")}".Trim();

            query = query.OrderBy(sortingColumnWithOrder);
            response.NoOfCountriesInPolicy = query.Select(e => e.Country).Distinct().Count();

            response.MapInfo = await query.GroupBy(e => new { e.Country, e.Iso3Code }).Select(e => new MapPopUpModel()
            {
                CountryCode = e.Key.Iso3Code,
                CountryName = e.Key.Country,
                Total = e.Count(),
                Type = "P"
            }).Distinct().ToListAsync();

            query = query.GroupBy(e => new { e.Id })
                .Select(w => new DbModels.Views.ViewPolicyList()
                {
                    //AdoptedBy = w.FirstOrDefault().AdoptedBy,
                    //Country = w.Key.Country,
                    Iso3Code = w.First().Iso3Code,
                    //CountryIncomeGroup = w.FirstOrDefault().CountryIncomeGroup,
                    CountryList = w.First().CountryList,
                    EndYear = w.FirstOrDefault().EndYear,
                    EnglishTitle = w.FirstOrDefault().EnglishTitle,
                    FitlerType = w.First().FitlerType,
                    //FurtherNotes = w.FirstOrDefault().FurtherNotes,
                    Id = w.Key.Id,
                    //PolicyExtractByOriginalLanguage = w.FirstOrDefault().PolicyExtractByOriginalLanguage,
                    //PolicyType = w.First().PolicyType,
                    //PublishedBy = w.FirstOrDefault().PublishedBy,
                    PublishedYear = w.FirstOrDefault().PublishedYear,
                    Title = w.FirstOrDefault().Title,
                    //References = w.FirstOrDefault().References,
                    RegionCode = w.First().RegionCode,
                    StartYear = w.FirstOrDefault().StartYear
                });

            response.FilteredPolicyCount = query.Count();

            response.PaginatedPolicies = await query.Take(searchRequest.PageSize * searchRequest.PageNo).ToListAsync();

            return response;
        }

        public async Task<TopicDataCountryCountModel> GetTopicDataCountryCounts(SearchRequest searchRequest)
        {
            TopicDataCountryCountModel response = new();

            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.ViewPolicyLists.AsQueryable();

            string searchText = searchRequest.SearchText;

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                query = query.Where(p => p.Title.Contains(searchText)
                    || p.EnglishTitle.Contains(searchText)
                    || p.AdoptedBy.Contains(searchText)
                    || p.PublishedBy.Contains(searchText)
                    || p.PolicyExtractByOriginalLanguage.Contains(searchText)
                    || p.References.Contains(searchText)
                    || p.Pdfs.Contains(searchText)
                    || p.FurtherNotes.Contains(searchText));
            }

            if (searchRequest.StartYear.HasValue)
            {
                query = query.Where(q => q.PublishedYear.HasValue && q.PublishedYear >= searchRequest.StartYear.Value);
            }

            if (searchRequest.EndYear.HasValue)
            {
                query = query.Where(q => q.PublishedYear.HasValue && q.PublishedYear <= searchRequest.EndYear.Value);
            }

            if (!string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                query = query.Where(r => r.RegionCode == searchRequest.SelectedRegionCode);
            }
            if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeGroup))
            {
                query = query.Where(r => r.CountryIncomeGroup == searchRequest.SelectedIncomeGroup);
            }
            if (searchRequest.SelectedCountries.Any())
            {
                query = query.Where(r => searchRequest.SelectedCountries.Contains(r.Country));
            }

            // if (searchRequest.SelectedPolicyTopics != null && searchRequest.SelectedPolicyTopics.Any())
            // {
            //     Dictionary<int, List<int>> relatedTopicDictionary = GetRelatedTopicDictionary(_dbContext, searchRequest.SelectedPolicyTopics);

            //     foreach (var key in relatedTopicDictionary.Keys)
            //     {
            //         var topicRelatedPolicyIds = _dbContext.PolicyTopics
            //             .Where(i => relatedTopicDictionary[key].Contains(i.TopicId))
            //             .Select(i => i.PolicyId);

            //         var newQuery = query.Where(p => topicRelatedPolicyIds.Contains(p.Id));

            //         response.TopicDataCountryCountItems.Add(new()
            //         {
            //             TopicId = key,
            //             DataCount = await newQuery.Select(d => d.Id).Distinct().CountAsync(),
            //             CountryIdCount = await newQuery.Select(e => e.Country).Distinct().CountAsync()
            //         });
            //     }
            // }

            return response;
        }

        private Dictionary<int, List<int>> GetRelatedTopicDictionary(SqlDbContext dbContext, IEnumerable<int> selectedPolicyTopics)
        {
            Dictionary<int, List<int>> dictionary = new();

            var topicMapping = dbContext.TopicParents.ToList();

            foreach (var topicId in selectedPolicyTopics)
            {
                List<int> relatedTopicIds = new();
                GetChildrenIds(topicId, relatedTopicIds);
                dictionary.Add(topicId, relatedTopicIds);
            }

            void GetChildrenIds(int topicId, List<int> relatedTopicIds)
            {
                var children = topicMapping.Where(tm => tm.ParentId == topicId).Select(tm => tm.TopicId).ToList();
                relatedTopicIds.Add(topicId);
                relatedTopicIds.AddRange(children);

                foreach (var childId in children)
                {
                    GetChildrenIds(childId, relatedTopicIds);
                }
            }

            return dictionary;
        }

        public async Task<List<PolicyCategoryPartnerMapItem>> GetPartnerCategoriesAsync(int PolicyCode)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyCategoryPartnerMap
                .Include(p => p.Partner)
                .Include(p => p.PartnerCategory)
                .Include(p => p.Policy)
                .Include(p => p.Policy.PolicyPartnerCategories)
                .Where(p => p.PolicyId == PolicyCode).OrderBy(p => p.PartnerCategory.DragAndDropKey).ToListAsync();
        }
        public async Task<List<PolicyTopic>> GetPoliciesTopicsAsync(int id)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyTopics.Where(x => x.PolicyId == id).ToListAsync();
        }

        public async Task<List<Topic>> GetTopicsAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.Topics.AsQueryable();
            query.Select(t => t.TopicParents).Load();
            return await query.OrderBy(t => t.Name).ToListAsync();
        }
        public async Task<List<Topic>> GetTopicsForBasicSearchAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.Topics.Select(e => new Topic
            {
                Id = e.Id,
                Name = e.Name,
                IsAllChildSelected = e.IsAllChildSelected,
                IsSelected = e.IsSelected,
                DragAndDropKey = e.DragAndDropKey
            }).AsNoTracking().AsQueryable();
            return await query.ToListAsync();
        }
        public async Task<List<Topic>> GetTopicsOnlyAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var query = _dbContext.Topics.AsQueryable();
            query.Select(t => t.TopicParents).Load();
            return await query.OrderBy(t => t.Name).ToListAsync();
        }

        public async Task<List<TopicDataTypeInformation>> GetTopicsPolciyInfoAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();

            return await (from p in _dbContext.PolicyTopics
                          join bp in _dbContext.PolicyCountryMapItems
                          on p.PolicyId equals bp.PolicyId
                          select new
                          {
                              p,
                              bp
                          } into t1
                          group t1 by t1.p.TopicId into g
                          select new TopicDataTypeInformation()
                          {
                              TopicId = g.FirstOrDefault().p.TopicId,
                              TotalRecord = g.Select(e => e.bp.PolicyId).Count(),
                              CountriesList = g.Select(e => e.bp.CountryCode).ToList()
                          }).ToListAsync();
        }
        public async Task<List<TopicParent>> GetParentTopics()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.TopicParents.Include(t => t.Topic).AsNoTracking().ToListAsync();
        }
        public async Task<List<TopicParent>> GetParentForBasicSearchTopics()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.TopicParents.AsNoTracking().ToListAsync();
        }

        public async Task SavePolicyPartnerCategoryPartnersAsync(List<PolicyCategoryPartnerMapItem> policyCategoryPartnerMapItems)
        {
            using var _dbContext = _DbFactory.CreateDbContext();

            _dbContext.PolicyCategoryPartnerMap.AddRange(policyCategoryPartnerMapItems);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyPartnerCategoryPartnersAsync(List<PolicyCategoryPartnerMapItem> policyCategoryPartnerMapItems)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var item in policyCategoryPartnerMapItems)
            {
                var policyCatpartnerMap = _dbContext.PolicyCategoryPartnerMap.Where(p => p.PolicyId == item.PolicyId
                && p.PartnerCategoryId == item.PartnerCategoryId && p.PartnerId == item.PartnerId).FirstOrDefault();
                if (policyCatpartnerMap != null)
                {
                    _dbContext.PolicyCategoryPartnerMap.Remove(policyCatpartnerMap);
                }
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task<List<PartnerCategory>> GetPartnerCategoryPartnersAsync()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PartnerCategories.OrderBy(a => a.DragAndDropKey)
              .Include(p => p.Partners.OrderBy(x => x.DragAndDropKey)).ToListAsync();
        }

        private List<GTreeNode> GetTopicChildren(Topic parentTopic)
        {
            var topicChildren = topicParentList.Where(tp => tp.ParentId == parentTopic.Id).ToList();

            if (topicChildren.Count == 0)
            {
                return new();
            }

            List<GTreeNode> children = new();

            foreach (TopicParent item in topicChildren)
            {
                Topic topic = topicList.FirstOrDefault(tl => tl.Id == item.TopicId);

                if (topic == null)
                {
                    break;
                }

                GTreeNode node = new()
                {
                    TopicId = topic.Id,
                    Title = topic.Name
                };

                node.Children = GetTopicChildren(topic);
                children.Add(node);
            }

            return children;
        }


        public async Task SavePolicyTopics(List<PolicyTopic> policyTopics)
        {
            using var _dbContext = _DbFactory.CreateDbContext();

            _dbContext.PolicyTopics.AddRange(policyTopics);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemoveTopicParentsAsync(List<PolicyTopic> policyTopics)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            _dbContext.PolicyTopics.RemoveRange(policyTopics);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<List<PolicyTopic>> GetPolicyTopics(int policyId)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyTopics
                .Include(p => p.Policy)
                .Include(a => a.Topic)
                .Where(w => w.PolicyId == policyId)
                .ToListAsync();
        }

        public async Task<List<PolicyProgram>> GetProgramPolicies(int policyId)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyPrograms
                .Include(p => p.Program)
                .Include(p => p.Program.Actions)
                .Where(w => w.PolicyId == policyId)
                .ToListAsync();
        }

        public async Task SavePolicyPrograms(List<ProgramPolicy> policyProgramList)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var programPolicy in policyProgramList)
            {
                programPolicy.DateCreated = DateTimeOffset.Now;
                programPolicy.DateUpdated = DateTimeOffset.Now;
            }
            await _dbContext.ProgramPolicies.AddRangeAsync(policyProgramList);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyPrograms(List<ProgramPolicy> policyProgramList)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var item in policyProgramList)
            {
                var program = _dbContext.ProgramPolicies.Where(p => p.PolicyId == item.PolicyId && p.ProgramId == item.ProgramId).FirstOrDefault();
                if (program != null)
                {
                    _dbContext.ProgramPolicies.Remove(program);
                }
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyTopics(List<PolicyTopic> policyTopicList)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var item in policyTopicList)
            {
                var policyTopic = _dbContext.PolicyTopics.Where(t => t.PolicyId == item.PolicyId && t.TopicId == item.TopicId).FirstOrDefault();
                if (policyTopic != null)
                {
                    _dbContext.PolicyTopics.Remove(policyTopic);
                }
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task<PolicySaveInfo> SavePolicy(Policy policy, AdditionalInfo additionalInfo)
        {
            return null;
        }

        public async Task<PolicySaveInfo> UpdatePolicy(Policy policy, AdditionalInfo additionalInfo)
        {
            return new PolicySaveInfo
            {
                PolicyRevisionId = 0,//policyRevision.Id,
                PolicyId = policy.Id
            };
        }

        public async Task SavePolicyCountries(List<PolicyCountryMapItem> policyCountryList)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            await _dbContext.PolicyCountryMapItems.AddRangeAsync(policyCountryList);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyCountries(List<PolicyCountryMapItem> policyCountryList)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var item in policyCountryList)
            {
                var entity = await _dbContext.PolicyCountryMapItems
                    .Where(a => a.PolicyId == item.PolicyId && a.CountryCode == item.CountryCode).FirstOrDefaultAsync();
                if (entity != null)
                {
                    _dbContext.PolicyCountryMapItems.Remove(entity);
                }
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task SavePolicyPartnerCategoryDetailsAsync(List<PolicyPartnerCategory> policyCategoryDetails)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            await _dbContext.PolicyPartnerCategories.AddRangeAsync(policyCategoryDetails);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyPartnerCategoryDetailsAsync(List<PolicyPartnerCategory> policyCategoryDetails)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            foreach (var item in policyCategoryDetails)
            {
                var policyCatDetails = _dbContext.PolicyPartnerCategories.Where(p => p.PolicyId == item.PolicyId
                && p.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault();
                if (policyCatDetails != null)
                {
                    _dbContext.PolicyPartnerCategories.Remove(policyCatDetails);
                }
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task SavePolicyAttachment(List<PolicyAttachment> policyAttachments)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            await _dbContext.PolicyAttachments.AddRangeAsync(policyAttachments);
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemovePolicyAttachment(List<PolicyAttachment> policyAttachments)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var ids = policyAttachments.Select(pa => pa.Id).ToList();
            var deletingAttachments = _dbContext.PolicyAttachments.Where(pa => ids.Contains(pa.Id)).ToList();
            _dbContext.PolicyAttachments.RemoveRange(deletingAttachments);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<List<PolicyCountryMapItem>> GetAllPolicyCountries(int policyId)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyCountryMapItems
                    .Include(a => a.Country)
                    .Where(q => q.PolicyId == policyId).ToListAsync();
        }

        public async Task<PolicyAttachment> GetAllPolicyAttachment(int policyId)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyAttachments.Where(p => p.PolicyId == policyId).FirstOrDefaultAsync();
        }

        public async Task<List<PolicyCountryMapItem>> GetAllPolicyCountries()
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyCountryMapItems.Include(a => a.Country).ToListAsync();
        }

        public async Task<bool> UpsertPolicyChildFromDraft(Policy policyCurrentDetail, PolicyRevision policyDraftDetail)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Remove existing policy topics for this policy
                var existingPolicyTopics = await _dbContext.PolicyTopics
                    .Where(pt => pt.PolicyId == policyDraftDetail.Id)
                    .ToListAsync();

                if (existingPolicyTopics.Any())
                {
                    _dbContext.PolicyTopics.RemoveRange(existingPolicyTopics);
                }

                // Map and add new policy topics from draft
                var newPolicyTopics = policyDraftDetail.PolicyTopicDrafts
                    .Select(ptd => new PolicyTopic
                    {
                        PolicyId = policyDraftDetail.Id,
                        TopicId = ptd.TopicId
                    })
                    .ToList();

                if (newPolicyTopics.Any())
                {
                    await _dbContext.PolicyTopics.AddRangeAsync(newPolicyTopics);
                }

                await _dbContext.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Successfully copied {newPolicyTopics.Count} policy topics from draft to published for policy ID: {policyDraftDetail.Id}");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error copying policy topics from draft to published for policy ID: {policyDraftDetail.Id}");
                return false;
            }
        }

        public async Task<IEnumerable<Policy>> GetAllPoliciesNoTracking()
        {
            using (var _dbContext = _DbFactory.CreateDbContext())
            {
                return await _dbContext.Policies.AsNoTracking().ToListAsync();
            }
        }

        public async Task<IEnumerable<Domain.PolicyTopics.PolicyTopicList>> GetAllPolicyTopic()
        {
            var data = Enumerable.Empty<Domain.PolicyTopics.PolicyTopicList>();
            var _dbContext = _DbFactory.CreateDbContext();
            _dbContext.Database.SetCommandTimeout(TimeSpan.FromSeconds(160));
            data = await
                (from policy in _dbContext.Policies.Include(w => w.PolicyTopics)
                 join policyType in _dbContext.PolicyTypes on policy.PolicyTypeId equals policyType.Id
                 join policyCountry in _dbContext.PolicyCountryMapItems on policy.Id equals policyCountry.PolicyId
                 join country in _dbContext.Countries on policyCountry.CountryCode equals country.Iso3Code
                 select new Domain.PolicyTopics.PolicyTopicList
                 {
                     PolicyId = policy.Id,
                     CountryISOCode = policyCountry.CountryCode,
                     CountryName = country.Name,
                     PolicyTitle = policy.Title + (string.IsNullOrEmpty(policy.EnglishTitle) ? "" : $" [{policy.EnglishTitle}]"),
                     PolicyTypeName = policyType.Name,
                     StartMonth = policy.StartMonth,
                     StartYear = policy.StartYear
                 }).AsNoTracking().ToListAsync();

            return data;

        }
        public async Task<IEnumerable<Domain.PolicyTopics.PolicyTopicDTO>> GetAllPolicyTopicOnly()
        {
            var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyTopics.GroupBy(w => w.PolicyId)
                          .Select(s => new Domain.PolicyTopics.PolicyTopicDTO
                          {
                              PolicyId = s.Key,
                              TopicList = s.Select(w => w.TopicId).ToList()
                          })
                          .AsNoTracking().ToListAsync();
        }
        
        public async Task<List<SearchCount>> GetContentCount(string regionCode, string countryStatus)
        {
            var _dbContext = _DbFactory.CreateDbContext();
            string key = $"DataCountByRegion:{regionCode}, CountryStatus:{countryStatus}";

            var cachedData = await _cacheService.GetAsync<List<SearchCount>>(key);
            if (cachedData != null)
            {
                _logger.LogInformation("Retrieved Data count by region from cache");
                return cachedData;
            }

            var query = _dbContext.CombinedSearchResults
                .AsNoTracking()
                .Where(w => w.CountryStatus != null);

            if (!string.IsNullOrEmpty(regionCode))
                query = query.Where(w => w.RegionCode.Contains(regionCode));

            if (!string.IsNullOrEmpty(countryStatus))
                query = query.Where(w => w.CountryStatus.Contains(countryStatus));

            var projectedData = await query
                .Select(e => new { e.Type, e.CountryStatus })
                .ToListAsync();

            var result = projectedData
                .GroupBy(entity => entity.Type)
                .Select(group => new SearchCount()
                {
                    Type = group.Key,
                    Count = group.Count(),
                    DistinctIsoCount = group
                        .Where(e => !string.IsNullOrEmpty(e.CountryStatus))
                        .SelectMany(e => e.CountryStatus
                            .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                            .Select(part => part.Split('|', StringSplitOptions.TrimEntries))
                            .Where(parts => parts.Length == 2 
                                && (string.IsNullOrEmpty(countryStatus) || parts[1] == countryStatus))
                            .Select(parts => parts[0].Trim()) // ISO Code
                        )
                        .Distinct(StringComparer.OrdinalIgnoreCase)
                        .Count()
                }).ToList();

            if (result != null)
            {
                await _cacheService.SetAsync(key, result);
                _logger.LogInformation("Saved Data count by region to cache.");
            }

            return result;
        }


       public async Task<Domain.Search.SearchResponse> GetCombinedSearchResult(SearchRequest searchRequest)
        {
            
            try
            {
                string sortingColumnWithOrder = $"{searchRequest.SortingColumn} {(searchRequest.IsDescendingOrder ? "descending" : "")}".Trim();

                var _dbContext = _DbFactory.CreateDbContext();


                _dbContext.Database.SetCommandTimeout(100000);

                IEnumerable<CombinedSearchResult> filteredData;
                List<CombinedSearchResult> PaginatedList = new();


                var allData = await GetCombinedSearchResultData(_dbContext, cacheKey);

                filteredData = allData.Where(e => searchRequest.SelectedFilterTypes.Contains(e.Type));
                filteredData = ApplyCombineSearchResultFilter(searchRequest, _dbContext, filteredData);
                PaginatedList = filteredData
                                    .OrderByDynamicProperty(searchRequest.SortingColumn.ToString(), searchRequest.IsDescendingOrder)
                                    .ThenByDescending(x => x.StartYear)
                                    .Skip(searchRequest.PageSize * (searchRequest.PageNo - 1))
                                    .Take(searchRequest.PageSize)
                                    .Select(e => new CombinedSearchResult()
                                    {
                                        Id = e.Id,
                                        Title = e.Title,
                                        Type = e.Type,
                                        CountryList = e.CountryList,
                                        CountryISOlist = e.CountryISOlist,
                                        RegionCode = e.RegionCode,
                                        StartYear = e.StartYear
                                    })
                                    .ToList();

                return new Domain.Search.SearchResponse()
                {
                    TotalFilteredCount = filteredData.Count(),
                    SearchResults = _mapper.Map<List<CombinedSearchResult>, List<SearchResult>>(PaginatedList),
                    SearchResultCounts = new()
                };
            }
            catch (Exception ex)
            {
                // Log exception if needed
                return null;
            }
        }

        private async Task<IEnumerable<CombinedSearchResult>> GetCombinedSearchResultData(SqlDbContext _dbContext,  string key)
        {
            IEnumerable<CombinedSearchResult> allData;
            var cachedData = await _cacheService.GetAsync<List<CombinedSearchResult>>(key);
            if (cachedData != null)
            {
                _logger.LogInformation("Retrieved all combined search result data from cache.");
                allData = cachedData;
            }
            else
            {
                _dbContext.Database.SetCommandTimeout(100000);

                allData = _dbContext.CombinedSearchResults
                             .Where(w => w.CountryList != null).AsNoTracking().ToList();

                _logger.LogInformation("Saved all combined search result data into cache");
                _ = _cacheService.SetAsync(key, allData);
            }

            return allData;
        }

        private async Task<IEnumerable<Country>> GetCountryData(SqlDbContext _dbContext, string key)
        {
            IEnumerable<Country> allData;
            var cachedData = await _cacheService.GetAsync<List<Country>>(key);
            if (cachedData != null)
            {
                _logger.LogInformation("Retrieved all country data from cache.");
                allData = cachedData;
            }
            else
            {
                _dbContext.Database.SetCommandTimeout(100000);

                allData = _dbContext.Countries.Include(c => c.CountryRegionMap).Include(c => c.CountryIncomeGroupMap).ToList();


                _logger.LogInformation("Saved all country data into cache");
                _ = _cacheService.SetAsync(key, allData);
            }

            return allData;
        }

        private void CollectTopicIds(SelectedTopicsTree topicNode, HashSet<string> childTopicIdSet)
        {
            if (topicNode.ChildTopics == null || !topicNode.ChildTopics.Any())
            {
                // Only add to childTopicIdSet if it's a leaf node (no children)
                childTopicIdSet.Add(topicNode.TopicId.ToString());
            }
            else
            {
                // Recursively collect for children
                foreach (var child in topicNode.ChildTopics)
                {
                    CollectTopicIds(child, childTopicIdSet);
                }
            }
        }

        private IEnumerable<CombinedSearchResult> ApplyCombineSearchResultFilter(SearchRequest searchRequest, SqlDbContext _dbContext, IEnumerable<CombinedSearchResult> query)
        {
            try
            {

                if (searchRequest.FilterCriteria)
                {


                    if (searchRequest.SelectedPolicyTopics != null && searchRequest.SelectedPolicyTopics.Any())
                    {
                        // Filter out items with an empty TopicIdList
                        query = query.Where(s => !string.IsNullOrEmpty(s.TopicIdList));

                        // Initialize the main predicate
                        var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                        // Iterate over each parent topic and build nested child predicates
                        foreach (var parentTopic in searchRequest.SelectedPolicyTopics)
                        {
                            // Initialize a new HashSet for collecting child topic IDs for this parent
                            HashSet<string> childTopicIdSet = new HashSet<string>();
                            CollectTopicIds(parentTopic, childTopicIdSet);

                            // Initialize a child predicate for the current parent topic
                            var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);

                            // Add each child topic ID to the child predicate
                            foreach (var childTopicId in childTopicIdSet)
                            {
                                childPredicate = childPredicate.Or(s =>
                                    s.TopicIdList.StartsWith(childTopicId + ",") ||
                                    s.TopicIdList.EndsWith("," + childTopicId) ||
                                    s.TopicIdList.Contains("," + childTopicId + ",") ||
                                    s.TopicIdList.Equals(childTopicId));
                            }

                            // Add the child predicate to the main predicate
                            predicate = predicate.And(childPredicate);
                        }

                        predicate = predicate.Or(s => s.Type != Constants.Policy);
                        // Apply the constructed predicate to the query
                        query = query.Where(predicate);
                    }


                    if (searchRequest.SelectedMechanismTopics != null && searchRequest.SelectedMechanismTopics.Any())
                    {
                        // Ensure TopicIdList is not empty
                        query = query.Where(s => !string.IsNullOrEmpty(s.TopicIdList));

                        // Initialize the main predicate for all selected mechanism topics
                        var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                        // Iterate over each parent topic in SelectedMechanismTopics
                        foreach (var parentTopic in searchRequest.SelectedMechanismTopics)
                        {
                            // Initialize a HashSet for collecting child topic IDs for this parent
                            HashSet<string> childTopicIdSet = new HashSet<string>();
                            CollectTopicIds(parentTopic, childTopicIdSet);

                            // Initialize a child predicate for the current parent topic
                            var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);

                            // Add each child topic ID to the child predicate
                            foreach (var childTopicId in childTopicIdSet)
                            {
                                childPredicate = childPredicate.Or(s =>
                                    s.TopicIdList.StartsWith(childTopicId + ",") ||
                                    s.TopicIdList.EndsWith("," + childTopicId) ||
                                    s.TopicIdList.Contains("," + childTopicId + ",") ||
                                    s.TopicIdList.Equals(childTopicId));
                            }

                            // Add the child predicate to the main predicate
                            predicate = predicate.And(childPredicate);
                        }

                        predicate = predicate.Or(s => s.Type != Constants.Mechanism);

                        // Apply the constructed predicate to the query
                        query = query.Where(predicate);
                    }


                    if (searchRequest.SelectedActionTopics != null && searchRequest.SelectedActionTopics.Any())
                    {
                        // Filter to ensure TopicIdList is not empty
                        query = query.Where(s => !string.IsNullOrEmpty(s.TopicIdList));

                        // Initialize the main predicate for all selected action topics
                        var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                        // Iterate over each parent topic in SelectedActionTopics
                        foreach (var parentTopic in searchRequest.SelectedActionTopics)
                        {
                            // Initialize a HashSet for collecting child topic IDs for this parent
                            HashSet<string> childTopicIdSet = new HashSet<string>();
                            CollectTopicIds(parentTopic, childTopicIdSet);

                            // Initialize a child predicate for the current parent topic
                            var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);

                            // Add each child topic ID to the child predicate
                            foreach (var childTopicId in childTopicIdSet)
                            {
                                childPredicate = childPredicate.Or(s =>
                                    s.TopicIdList.StartsWith(childTopicId + ",") ||
                                    s.TopicIdList.EndsWith("," + childTopicId) ||
                                    s.TopicIdList.Contains("," + childTopicId + ",") ||
                                    s.TopicIdList.Equals(childTopicId));
                            }

                            // Add the child predicate to the main predicate
                            predicate = predicate.And(childPredicate);
                        }

                        predicate = predicate.Or(s => s.Type != Constants.Action);

                        // Apply the constructed predicate to the query
                        query = query.Where(predicate);
                    }



                    if (searchRequest.SelectePolicyTypes.Any())
                    {
                        query = query.Where(r => searchRequest.SelectePolicyTypes.Contains(r.PolicyTypeId.Value) || r.PolicyTypeId == 0);
                    }

                    if (searchRequest.SelecteMechanismTypes.Any())
                    {
                        query = query.Where(r => searchRequest.SelecteMechanismTypes.Contains(r.MechanismTypeId) || r.MechanismTypeId == 0);
                    }

                    if (searchRequest.SelectePartners.Any())
                    {
                        query = query.Where(e => !string.IsNullOrEmpty(e.ParntnerIdList) );
                        var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                        foreach (var parentPartner in searchRequest.SelectePartners)
                        {
                            int parentId = parentPartner.Key;
                            var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);
                            if(parentPartner.Value.Count >0)
                            {
                                var partnerIds =  parentPartner.Value.Select(ChildPartner => Convert.ToString(ChildPartner)).ToList();
                                foreach (var ChildPartner in partnerIds)
                                {
                                    childPredicate = childPredicate.Or(s =>
                                                s.ParntnerIdList.StartsWith(ChildPartner + ",") ||
                                                s.ParntnerIdList.EndsWith("," + ChildPartner) ||
                                                s.ParntnerIdList.Contains("," + ChildPartner + ",") ||
                                                s.ParntnerIdList.Equals(ChildPartner));

                                }
                            }
                            else
                            {
                                childPredicate = childPredicate.Or(s =>
                                                s.ParntnerIdList.StartsWith(parentId + ",") ||
                                                s.ParntnerIdList.EndsWith("," + parentId) ||
                                                s.ParntnerIdList.Contains("," + parentId + ",") ||
                                                s.ParntnerIdList.Equals(parentId));
                            }

                            predicate.And(childPredicate);
                        }
                        query = query.Where(predicate);
                    }


                    if (searchRequest.SelecteProgramTypes.Any())
                    {
                        query = query.Where(r => searchRequest.SelecteProgramTypes.Contains(r.ProgramTypeId.Value) || r.ProgramTypeId == 0);
                    }

                    if (searchRequest.SelectedProgramFundingSource.Any())
                    {
                    
                        var predicateFundingSource = PredicateBuilder.New<CombinedSearchResult>(false); // Initialize with false for OR condition
                        foreach (var item in searchRequest.SelectedProgramFundingSource)
                        {
                            // Convert and trim funding source ID
                            string fundingSourceId = Convert.ToString(item).Trim();
                            
                            // Build the predicate to include various conditions
                            predicateFundingSource = predicateFundingSource.Or(s => !string.IsNullOrEmpty(s.ProgramPartnerCategoryIdList) &&
                                                                                    (s.ProgramPartnerCategoryIdList.Equals(fundingSourceId)
                                                                                    || s.ProgramPartnerCategoryIdList.Contains("," + fundingSourceId + ",")
                                                                                    || s.ProgramPartnerCategoryIdList.StartsWith(fundingSourceId + ",")
                                                                                    || s.ProgramPartnerCategoryIdList.EndsWith("," + fundingSourceId)
                                                                                    || s.ProgramPartnerCategoryIdList == "0"));
                        }
                        predicateFundingSource = predicateFundingSource.Or(s => s.Type != Constants.Action);
                        // Apply the predicate to the query
                        query = query.Where(predicateFundingSource);

                    }


                    if (searchRequest.SelectedTargetGroup.Any())
                    {
                        var predicateTargetGroup = PredicateBuilder.New<CombinedSearchResult>(false); // Initialize with false for OR condition
                        foreach (var item in searchRequest.SelectedTargetGroup)
                        {
                            // Convert and trim target group ID
                            string targetGroupId = Convert.ToString(item).Trim();
                            
                            // Build the predicate to include various conditions
                            predicateTargetGroup = predicateTargetGroup.Or(s => !string.IsNullOrEmpty(s.ProgramTargetGroupIdList) &&
                                                                                (s.ProgramTargetGroupIdList.Equals(targetGroupId)
                                                                                || s.ProgramTargetGroupIdList.Contains("," + targetGroupId + ",")
                                                                                || s.ProgramTargetGroupIdList.StartsWith(targetGroupId + ",")
                                                                                || s.ProgramTargetGroupIdList.EndsWith("," + targetGroupId)
                                                                                || s.ProgramTargetGroupIdList == "0"));
                        }
                        // Apply the predicate to the query
                        query = query.Where(predicateTargetGroup);
                    }

                    if (searchRequest.SelectedDeliveryId.Any())
                    {
                        var predicateDelivery = PredicateBuilder.New<CombinedSearchResult>(false); // Initialize with false for OR condition
                        foreach (var item in searchRequest.SelectedDeliveryId)
                        {
                            // Convert and trim action ID
                            var actionId = Convert.ToString(item).Trim();
                            
                            // Build the predicate to include various conditions
                            predicateDelivery = predicateDelivery.Or(s => !string.IsNullOrEmpty(s.ActionDeliveryIdList) &&
                                                                        (s.ActionDeliveryIdList.Equals(actionId)
                                                                        || s.ActionDeliveryIdList.Contains("," + actionId + ",")
                                                                        || s.ActionDeliveryIdList.StartsWith(actionId + ",")
                                                                        || s.ActionDeliveryIdList.EndsWith("," + actionId)
                                                                        || s.ActionDeliveryIdList == "0"));
                        }
                        // Apply the predicate to the query
                        query = query.Where(predicateDelivery);
                    }


                    if (searchRequest.SelectedActionProblems.Any())
                    {
                        var predicateActionProblems = PredicateBuilder.New<CombinedSearchResult>(false); // Initialize with false for OR condition
                        foreach (var item in searchRequest.SelectedActionProblems)
                        {
                            // Convert and trim problem ID
                            var problemId = Convert.ToString(item).Trim();
                            
                            // Build the predicate to include various conditions
                            predicateActionProblems = predicateActionProblems.Or(s => !string.IsNullOrEmpty(s.ActionProblemIdList) &&
                                                                                    (s.ActionProblemIdList.Equals(problemId)
                                                                                    || s.ActionProblemIdList.Contains("," + problemId + ",")
                                                                                    || s.ActionProblemIdList.StartsWith(problemId + ",")
                                                                                    || s.ActionProblemIdList.EndsWith("," + problemId)
                                                                                    || s.ActionProblemIdList == "0"));
                        }
                        // Apply the predicate to the query
                        query = query.Where(predicateActionProblems);
                    }



                    if (searchRequest.SelectedCommitmentIcn2.Any())
                    {
                        query = query.Where(e => !string.IsNullOrEmpty(e.Icn2IdList) );
                        var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                        foreach (var categoryICN2Id in searchRequest.SelectedCommitmentIcn2)
                        {
                            int parentId = categoryICN2Id.Key;
                            var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);
                            if(categoryICN2Id.Value.Count >0)
                            {
                                var icn2CategoryIds =  categoryICN2Id.Value.Select(icn2Action => Convert.ToString(icn2Action)).ToList();
                                foreach (var icn2ActionId in icn2CategoryIds)
                                {
                                    childPredicate = childPredicate.Or(s =>
                                                s.Icn2IdList.StartsWith(icn2ActionId + ",") ||
                                                s.Icn2IdList.EndsWith("," + icn2ActionId) ||
                                                s.Icn2IdList.Contains("," + icn2ActionId + ",") ||
                                                s.Icn2IdList.Equals(icn2ActionId));

                                }
                            }
                            else
                            {
                                childPredicate = childPredicate.Or(s =>
                                                s.Icn2IdList.StartsWith(parentId + ",") ||
                                                s.Icn2IdList.EndsWith("," + parentId) ||
                                                s.Icn2IdList.Contains("," + parentId + ",") ||
                                                s.Icn2IdList.Equals(parentId));
                            }
                            predicate.And(childPredicate);
                        }
                        
                        predicate = predicate.Or(s => s.Type != Constants.Commitment);
                        query = query.Where(predicate);
                    }

                    if (searchRequest.SelectedLanguageIds.Any())
                    {
                        query = query.Where(r => r.LanguageId.HasValue && searchRequest.SelectedLanguageIds.Contains(r.LanguageId.Value));
                    }

                }

                if (searchRequest.SelectedTopcis != null && searchRequest.SelectedTopcis.Any())
                {
                    var themeGrandChildrenTopicIds = _dbContext.TopicParents
                     .Where(i => searchRequest.SelectedTopcis.Contains(i.ParentId))
                     .AsNoTracking().Select(i => i.TopicId).Distinct().ToList();

                    var predicate = PredicateBuilder.New<CombinedSearchResult>(true);

                    if (themeGrandChildrenTopicIds.Any())
                    {
                        foreach (var item in themeGrandChildrenTopicIds)
                        {
                            string topicId = Convert.ToString(item);
                            predicate.Or(s => !string.IsNullOrEmpty(s.TopicIdList) && s.TopicIdList.Contains(topicId));
                        }
                    }
                    else
                    {
                        predicate.And(s => s.Id == -1); // if topic no  theme then show no data 
                    }
                    query = query.Where(predicate);
                }
                if (searchRequest.CountryGroupSelectedCountries.Any())
                {
                    var predicate = PredicateBuilder.New<CombinedSearchResult>(true);

                    List<string> countryGroupCountries = searchRequest.CountryGroupSelectedCountries.SelectMany(x => x.Value).ToList();
                    foreach (var item in countryGroupCountries)
                    {
                        predicate.Or(s => s.CountryISOlist.Contains(Convert.ToString(item)));
                    }
                    query = query.Where(predicate);
                }
                if (searchRequest.CountryRegionSelectedCountries.Any())
                {
                    var predicate = PredicateBuilder.New<CombinedSearchResult>(true);

                    List<string> countryRegionCountries = searchRequest.CountryRegionSelectedCountries.SelectMany(x => x.Value).ToList();
                    foreach (var item in countryRegionCountries)
                    {
                        predicate.Or(s => s.CountryISOlist.Contains(Convert.ToString(item)));
                    }
                    query = query.Where(predicate);
                }

                if (searchRequest.SelectedSearchTopics != null && searchRequest.SelectedSearchTopics.Any())
                {
                
                    query = query.Where(e => !string.IsNullOrEmpty(e.TopicIdList));
                    var predicate = PredicateBuilder.New<CombinedSearchResult>(false);

                    foreach (var parentTopic in searchRequest.SelectedSearchTopics)
                    {
                        int parentId = parentTopic.Key;
                        var childPredicate = PredicateBuilder.New<CombinedSearchResult>(false);
                        var topicIds = parentTopic.Value.Select(ChildTopic => Convert.ToString(ChildTopic)).ToList();
                        foreach (var ChildTopic in topicIds)
                        {
                            childPredicate = childPredicate.Or(s =>
                                          s.TopicIdList.StartsWith(ChildTopic + ",") ||
                                          s.TopicIdList.EndsWith("," + ChildTopic) ||
                                          s.TopicIdList.Contains("," + ChildTopic + ",") ||
                                          s.TopicIdList.Equals(ChildTopic));

                        }

                        predicate.And(childPredicate);
                    }
                    query = query.Where(predicate);
                }
                if (searchRequest.StartYear.HasValue)
                {
                    query = query.Where(q => q.StartYear.HasValue && q.StartYear >= searchRequest.StartYear.Value);
                }
                if (searchRequest.EndYear.HasValue)
                {
                    query = query.Where(q => q.StartYear.HasValue && q.StartYear <= searchRequest.EndYear.Value);
                }
                if (!string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
                {
                    query = query.Where(r => !string.IsNullOrEmpty(r.RegionCode) && r.RegionCode.Contains(searchRequest.SelectedRegionCode));
                }
                if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeGroup))
                {
                    query = query.Where(r => !string.IsNullOrEmpty(r.CountryIncomeGroup) && r.CountryIncomeGroup.Contains(searchRequest.SelectedIncomeGroup));
                }
                if (searchRequest.SelectedCountries.Any())
                {
                    var predicateCountry = PredicateBuilder.New<CombinedSearchResult>(true);
                    foreach (var item in searchRequest.SelectedCountries)
                    {
                        predicateCountry.Or(s => s.CountryList.Contains(item));
                    }
                    query = query.Where(predicateCountry);
                }
                string searchText = searchRequest.SearchText;

                if (!string.IsNullOrWhiteSpace(searchText) && query.Any())
                {
                    searchText = searchText.Trim();

                    query = query.Where(p => (p.Title?.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
                                            || (p.EnglishTitle?.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0)
                                            || (p.OtherData?.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0));

                }
                return query;
            }
            catch (Exception ex)
            {

                throw;
            }

        }
       
        public async Task<SearchResponse> GetCombinedSearchResultCounts(SearchRequest searchRequest)
        {
            try
            {
                string sortingColumnWithOrder = $"{searchRequest.SortingColumn} {(searchRequest.IsDescendingOrder ? "descending" : "")}".Trim();

                var _dbContext = _DbFactory.CreateDbContext();
                IEnumerable<CombinedSearchResult> filteredData;
                IEnumerable<Country> countryData;

                var allData = await GetCombinedSearchResultData(_dbContext, cacheKey);
                filteredData = ApplyCombineSearchResultFilter(searchRequest, _dbContext, allData);

                countryData = await GetCountryData(_dbContext, cacheCountryKey);
                IEnumerable<CombinedSearchResult> allCountryList = ApplyCountryWiseFilter(searchRequest, filteredData, countryData);

                return new SearchResponse()
                {
                    TotalFilteredCount = filteredData.Where(e => searchRequest.SelectedFilterTypes.Contains(e.Type)).Count(),
                    SearchResults = new List<SearchResult>(),
                    SearchResultCounts = GetSearchResultCountWithIEnumerable(filteredData, searchRequest, allCountryList)
                };

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private static IEnumerable<CombinedSearchResult> ApplyCountryWiseFilter(SearchRequest searchRequest, IEnumerable<CombinedSearchResult> filteredData, IEnumerable<Country> countryData)
        {
            if (!string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                countryData = countryData.Where(c => c.CountryRegionMap.Any(r => r.RegionCode.Equals(searchRequest.SelectedRegionCode)));
            }

            if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeGroup))
            {
                countryData = countryData.Where(c => c.CountryIncomeGroupMap.Any(r => r.IncomeGroupCode.Equals(searchRequest.SelectedIncomeGroup)));
            }
            var allCountryList = filteredData.SelectMany(e => e.CountryISOlist.Split(',').Select(w => new CombinedSearchResult() { Type = e.Type, CountryISOlist = w.Trim().ToLower(), CountryList = e.CountryList }));
            if (countryData.Any())
            {
                allCountryList = allCountryList.Where(a => countryData.Any(c => c.Iso3Code.Trim().ToLower().Equals(a.CountryISOlist))).ToList();
            }

            return allCountryList;
        }

        private SearchResultCounts GetSearchResultCountWithIEnumerable(IEnumerable<CombinedSearchResult> allList, SearchRequest searchRequest, IEnumerable<CombinedSearchResult> allCountryList)
        {

            var searchResultCounts = new SearchResultCounts();
            searchResultCounts.TotalRecordCount = allList.Where(e => searchRequest.SelectedFilterTypes.Contains(e.Type)).Count();

            if (!searchRequest.FilterCriteria && searchRequest.DefaultPage == "map")
            {
                var mapData = allList.Where(w => !string.IsNullOrEmpty(w.CountryISOlist) && searchRequest.SelectedFilterTypes.Contains(w.Type))
                           .Select(e => new { e.CountryCodeAndNames, e.Type }).ToList()
                          .SelectMany(s => s.CountryCodeAndNames.Split('$')
                          .Select(pair => pair.Split('|'))
                          .Select(pair => new { CountryISO = pair[0].Trim(), CountryName = pair[1].Trim(), Type = s.Type }))
                    .GroupBy(s => new { s.CountryISO, s.CountryName, s.Type })
                          .Select(e => new MapPopUpModel()
                          {
                              CountryCode = e.Key.CountryISO,
                              CountryName = e.Key.CountryName,
                              Total = e.Count(),
                              Type = e.First().Type
                          }).ToList();

                searchResultCounts.MapData = MapSerachItemCountry.SetColorForMap(mapData);
                var filteredCountries = allList
                                        .Where(e => searchRequest.SelectedFilterTypes.Contains(e.Type))
                                        .Select(e => e.CountryISOlist) // Select the comma-separated string
                                        .ToList()
                                        .SelectMany(s => s.Split(',').Select(code => code.Trim()).Distinct()) // Split and get distinct ISO codes
                                        .Distinct().ToList();
                searchResultCounts.TotalCountryCount = filteredCountries.Count();
            }

        // Normalize selected countries for comparison
        var normalizedSelectedCountries = searchRequest.SelectedCountries
                                                        .Select(c => c.Trim().ToLower())
                                                        .ToHashSet();

            searchResultCounts.DataTypeCount = allList.Select(e => new { e.Type, e.Id }).ToList().GroupBy(s => s.Type)
                                                .ToDictionary(g => g.Key, g => g.Count());


            
            // Adjust counts based on searchRequest.SelectedCountries
            if (normalizedSelectedCountries.Any())
            {
                searchResultCounts.DataTypeWiseCountryCount = allCountryList
                    .Where(s => !string.IsNullOrEmpty(s.CountryList))
                    .Select(e => new { e.CountryList, e.Type }).ToList()
                    .SelectMany(e => e.CountryList.Split(',')
                        .Select(w => new { e.Type, Country = w.Trim().ToLower() })
                        .Where(c => normalizedSelectedCountries.Contains(c.Country)))
                    .GroupBy(s => s)
                    .GroupBy(s => s.FirstOrDefault().Type)
                    .ToDictionary(g => g.Key, g => g.Count());

                // CountryCount for intersections
                searchResultCounts.CountryCount = allList
                    .Where(s => !string.IsNullOrEmpty(s.CountryList))
                    .Select(e => new { e.CountryList, e.Type }).ToList()
                    .SelectMany(e => e.CountryList.Split(',')
                        .Select(w => w.Trim().ToLower())
                        .Where(c => normalizedSelectedCountries.Contains(c)))
                    .GroupBy(s => s)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            else
            {
                searchResultCounts.DataTypeWiseCountryCount = allCountryList.Where(s => !string.IsNullOrEmpty(s.CountryISOlist))
                                               .Select(e => new { e.CountryISOlist, e.Type }).ToList()
                                               .SelectMany(s => s.CountryISOlist.Split(',')
                                               .Select(w => new { Type = s.Type, Country = w.Trim() }))
                                               .GroupBy(s => s)
                                               .GroupBy(s => s.FirstOrDefault().Type)
                                               .ToDictionary(g => g.Key, g => g.Count());

                searchResultCounts.CountryCount = allList.Where(s => !string.IsNullOrEmpty(s.CountryISOlist))
                                               .Select(e => e.CountryISOlist).ToList()
                                               .SelectMany(s => s.Split(',')).Select(s => s.Trim())
                                               .GroupBy(s => s)
                                               .ToDictionary(g => g.Key, g => g.Count());
            }

            if (!searchRequest.FilterCriteria && searchRequest.DefaultPage == "searchpage")
            {
                searchResultCounts.BasicTopicCountList = allList.Where(s => !string.IsNullOrEmpty(s.TopicIdList) && searchRequest.SelectedFilterTypes.Contains(s.Type))
                                               .Select(e => new { e.TopicIdList, e.Id }).ToList()
                                              .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, ActionId = s.Id, }))
                                              .GroupBy(s => int.Parse(s.TopicId))
                                              .ToDictionary(g => g.Key, g => g.Select(s => s.ActionId).Distinct().ToList());



                if (normalizedSelectedCountries.Any())
                {

                    searchResultCounts.BasicTopicCountryList = allList
                                                .Where(s => !string.IsNullOrEmpty(s.TopicIdList) && 
                                                            !string.IsNullOrEmpty(s.CountryList) && 
                                                            searchRequest.SelectedFilterTypes.Contains(s.Type))
                                                .Select(e => new { e.TopicIdList, e.CountryList })
                                                .ToList() // Materialize the query
                                                .SelectMany(s => s.CountryList.Split(',')
                                                    .Select(c => c.Trim().ToLower())
                                                    .Where(c => normalizedSelectedCountries.Contains(c))
                                                    .SelectMany(c => s.TopicIdList.Split(',')
                                                        .Select(t => new { CountryName = c, TopicId = int.Parse(t.Trim()) })))
                                                .GroupBy(s => s.TopicId)
                                                .ToDictionary(g => g.Key, g => g.Select(s => s.CountryName).Distinct().ToList());

                }
                else
                {
                    searchResultCounts.BasicTopicCountryList = allList.Where(s => !string.IsNullOrEmpty(s.TopicIdList) && 
                                                                                  !string.IsNullOrEmpty(s.CountryISOlist) && 
                                                                                  searchRequest.SelectedFilterTypes.Contains(s.Type))
                                                .Select(e => new { e.TopicIdList, e.CountryISOlist }).ToList()
                                                .SelectMany(s => s.CountryISOlist.Split(',')
                                                .SelectMany(c => s.TopicIdList.Split(',')
                                                .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
                                                .GroupBy(s => int.Parse(s.TopicId))
                                                .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
                }
            }

            // advance filter code 
            if (searchRequest.FilterCriteria)
            {

                searchResultCounts.PartnerCount = allList.Where(s => searchRequest.SelectedFilterTypes.Contains(s.Type) 
                    && !string.IsNullOrEmpty(s.ParntnerIdList))
                    .Select(e => new { e.ParntnerIdList, e.Id }).ToList()
                    .SelectMany(s => s.ParntnerIdList.Split(',')
                    .Select(w => new { PartnerId = w, Id = s.Id, }))
                    .GroupBy(s => int.Parse(s.PartnerId))
                    .ToDictionary(g => g.Key, g => g.Select(s => s.Id).Distinct().ToList());

                if (normalizedSelectedCountries.Any())
                {

                    searchResultCounts.BasicPartnerCountryList = allList
                        .Where(s => !string.IsNullOrEmpty(s.ParntnerIdList) 
                             && !string.IsNullOrEmpty(s.CountryList) 
                             && searchRequest.SelectedFilterTypes.Contains(s.Type))
                        .Select(e => new { e.ParntnerIdList, e.CountryList }).ToList() // Materialize the query
                        .SelectMany(s => s.CountryList.Split(',')
                        .Select(c => c.Trim().ToLower())
                        .Where(c => normalizedSelectedCountries.Contains(c))
                        .SelectMany(c => s.ParntnerIdList.Split(',')
                        .Select(t => new { CountryName = c, PartnerId = int.Parse(t.Trim()) })))
                        .GroupBy(s => s.PartnerId)
                        .ToDictionary(g => g.Key, g => g.Select(s => s.CountryName).Distinct().ToList());

                }
                else
                {
                    searchResultCounts.BasicPartnerCountryList = allList.Where(s => !string.IsNullOrEmpty(s.ParntnerIdList) && 
                                                                                  !string.IsNullOrEmpty(s.CountryISOlist) && 
                                                                                  searchRequest.SelectedFilterTypes.Contains(s.Type))
                                                .Select(e => new { e.ParntnerIdList, e.CountryISOlist }).ToList()
                                                .SelectMany(s => s.CountryISOlist.Split(',')
                                                .SelectMany(c => s.ParntnerIdList.Split(',')
                                                .Select(t => new { CountryISO = c.Trim(), ParntnerId = t })))
                                                .GroupBy(s => int.Parse(s.ParntnerId))
                                                .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
                }

                searchResultCounts.LanguageCount = allList.Where(s =>  s.LanguageId != null&& 
                                                            searchRequest.SelectedFilterTypes.Contains(s.Type))
                                                    .Select(w => w.LanguageId)
                                                    .GroupBy(s => s)
                                                    .ToDictionary(s => s.Key.Value,s => s.Count());

                if (normalizedSelectedCountries.Any())
                {
                    // Filtered by selected countries
                    searchResultCounts.LanguageCountryCount = allList
                        .Where(s => s.LanguageId != null && !string.IsNullOrEmpty(s.CountryList) &&
                                    searchRequest.SelectedFilterTypes.Contains(s.Type))
                        .SelectMany(s => s.CountryList.Split(',')
                            .Select(countryCode => new { Language = s.LanguageId.Value, Country = countryCode.Trim().ToLower() })
                            .Where(c => normalizedSelectedCountries.Contains(c.Country)))
                        .GroupBy(item => item.Language)
                        .ToDictionary(
                            g => g.Key,   // LanguageId as the key
                            g => g.Select(item => item.Country).Distinct().Count() // Count distinct Country entries per Language
                        );
                }
                else
                {
                    // Not filtered by selected countries
                    searchResultCounts.LanguageCountryCount = allList
                        .Where(s => s.LanguageId != null && !string.IsNullOrEmpty(s.CountryISOlist) &&
                                    searchRequest.SelectedFilterTypes.Contains(s.Type))
                        .SelectMany(s => s.CountryISOlist.Split(',')
                            .Select(countryCode => new { Language = s.LanguageId.Value, Country = countryCode.Trim().ToLower() }))
                        .GroupBy(item => item.Language)
                        .ToDictionary(
                            g => g.Key,   // LanguageId as the key
                            g => g.Select(item => item.Country).Distinct().Count() // Count distinct Country entries per Language
                        );
                }


                if (searchRequest.SelectedFilterTypes.Contains("C"))
                {
                    searchResultCounts.ICN2Count = allList.Where(s => !string.IsNullOrEmpty(s.Icn2IdList))
                        .Select(e => new { e.Icn2IdList, e.Id }).ToList()
                        .SelectMany(s => s.Icn2IdList.Split(',')
                        .Select(w => new { icn2Id = w, Id = s.Id, }))
                        .GroupBy(s => int.Parse(s.icn2Id))
                        .ToDictionary(g => g.Key, g => g.Select(s => s.Id).Distinct().ToList());

                    if (normalizedSelectedCountries.Any())
                    {

                        searchResultCounts.BasicICN2CountryList = allList
                            .Where(s => !string.IsNullOrEmpty(s.Icn2IdList) 
                                && !string.IsNullOrEmpty(s.CountryList) )
                            .Select(e => new { e.Icn2IdList, e.CountryList }).ToList() // Materialize the query
                            .SelectMany(s => s.CountryList.Split(',')
                            .Select(c => c.Trim().ToLower())
                            .Where(c => normalizedSelectedCountries.Contains(c))
                            .SelectMany(c => s.Icn2IdList.Split(',')
                            .Select(t => new { CountryName = c, icn2Id = int.Parse(t.Trim()) })))
                            .GroupBy(s => s.icn2Id)
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryName).Distinct().ToList());

                    }
                    else
                    {
                        searchResultCounts.BasicICN2CountryList = allList.Where(s => !string.IsNullOrEmpty(s.Icn2IdList) && 
                                                                                    !string.IsNullOrEmpty(s.CountryISOlist) )
                                                    .Select(e => new { e.Icn2IdList, e.CountryISOlist }).ToList()
                                                    .SelectMany(s => s.CountryISOlist.Split(',')
                                                    .SelectMany(c => s.Icn2IdList.Split(',')
                                                    .Select(t => new { CountryISO = c.Trim(), icn2Id = t })))
                                                    .GroupBy(s => int.Parse(s.icn2Id))
                                                    .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
                    }
                }

                if (searchRequest.SelectedFilterTypes.Contains("P"))
                {
                    searchResultCounts.PolicyTypeCount = allList.Where(s => s.Type == "P" && s.PolicyTypeId != 0 && s.PolicyTypeId != null).Select(w => w.PolicyTypeId)
                                                 .GroupBy(s => s)
                                                 .ToDictionary(s => s.Key.Value,s => s.Count());

                    searchResultCounts.PolicyTypeCountryCount = allList
                        .Where(s => s.Type == "P" && s.PolicyTypeId != 0 && s.PolicyTypeId != null && !string.IsNullOrEmpty(s.CountryISOlist))
                        .Select(e => new { e.CountryISOlist, e.PolicyTypeId }).ToList()
                        .SelectMany(s => s.CountryISOlist.Split(',')
                                        .Select(countryCode => new { PolicyType = s.PolicyTypeId.Value, Country = countryCode.Trim() }))
                        .GroupBy(item => item.PolicyType)
                        .ToDictionary(
                            g => g.Key,                   // LanguageId as the key
                            g => g.Select(item => item.Country).Distinct().Count()  // Count distinct Country entries per Language
                        );

                    

                    searchResultCounts.TopicWisePolicyIdList = allList.Where(s => s.Type == "P" && !string.IsNullOrEmpty(s.TopicIdList))
                                                 .Select(e => new { e.TopicIdList, e.Id }).ToList()
                                                   .SelectMany(s => s.TopicIdList.Split(',')
                                                   .Select(w => new { TopicId = w, PolicyId = s.Id, }))
                                                   .GroupBy(s => int.Parse(s.TopicId))
                                                   .ToDictionary(g => g.Key, g => g.Select(s => s.PolicyId).Distinct().ToList());

                    searchResultCounts.TopicWisePolicyCountryList = allList.Where(s => s.Type == "P" && !string.IsNullOrEmpty(s.TopicIdList))
                            .Select(e => new { e.TopicIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.TopicIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
                            .GroupBy(s => int.Parse(s.TopicId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());

                }

                if (searchRequest.SelectedFilterTypes.Contains("M"))
                {
                    searchResultCounts.MechanismTypeCount = allList.Where(s => s.Type == "M" && s.MechanismTypeId != 0)
                                                   .Select(w => w.MechanismTypeId).GroupBy(s => s)
                                                   .ToDictionary(s => s.Key,s => s.Count());

                    
                    searchResultCounts.MechanismTypeCountryCount = allList.Where(s => !string.IsNullOrEmpty(s.CountryISOlist))
                                               .Select(e => new { e.CountryISOlist, e.MechanismTypeId }).ToList()
                                               .SelectMany(s => s.CountryISOlist.Split(',')
                                               .Select(w => new { Type = s.MechanismTypeId, Country = w.Trim() }))
                                               .GroupBy(s => s)
                                               .GroupBy(s => s.FirstOrDefault().Type)
                                               .ToDictionary(g => g.Key, g => g.Count());

                    searchResultCounts.TopicWiseMechanismList = allList.Where(s => s.Type == "M" && !string.IsNullOrEmpty(s.TopicIdList))
                                                .Select(e => new { e.TopicIdList, e.Id }).ToList()
                                                .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, MechanismId = s.Id, }))
                                               .GroupBy(s => int.Parse(s.TopicId))
                                               .ToDictionary(g => g.Key, g => g.Select(s => s.MechanismId).Distinct().ToList());

                    searchResultCounts.TopicWiseMechanismCountryList = allList.Where(s => s.Type == "M" && !string.IsNullOrEmpty(s.TopicIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
                            .Select(e => new { e.TopicIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.TopicIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
                            .GroupBy(s => int.Parse(s.TopicId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
                }

                if (searchRequest.SelectedFilterTypes.Contains("A"))
                {
                    searchResultCounts.FundingSourceCount = allList.Where(s => s.Type == "A" &&!string.IsNullOrEmpty(s.ProgramPartnerCategoryIdList))
                                                     .Select(e => e.ProgramPartnerCategoryIdList).ToList()
                                                    .SelectMany(w => w.Split(',').Distinct())
                                                    .GroupBy(s => s)
                                                    .ToDictionary(s => Int32.Parse(s.Key),s => s.Count());
                    
                    searchResultCounts.FundingSourceCountryCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ProgramPartnerCategoryIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
                            .Select(e => new { e.ProgramPartnerCategoryIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.ProgramPartnerCategoryIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), FundingSourceId = t })))
                            .GroupBy(s => int.Parse(s.FundingSourceId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().Count());

                    searchResultCounts.ProgramTypeCount = allList.Where(s => s.Type == "A" && s.ProgramTypeId != 0 && s.ProgramTypeId != null)
                                                    .Select(w => w.ProgramTypeId).GroupBy(s => s)
                                                    .ToDictionary(s => s.Key.Value,s => s.Count());

                    searchResultCounts.ProgramTypeCountryCount = allList.Where(s => s.Type == "A" &&!string.IsNullOrEmpty(s.CountryISOlist))
                                               .Select(e => new { e.CountryISOlist, e.ProgramTypeId }).ToList()
                                               .SelectMany(s => s.CountryISOlist.Split(',')
                                               .Select(w => new { Type = s.ProgramTypeId, Country = w.Trim() }))
                                               .GroupBy(s => s)
                                               .GroupBy(s => s.FirstOrDefault().Type)
                                               .ToDictionary(g => g.Key.Value, g => g.Count());

                    searchResultCounts.TargetGroupCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ProgramTargetGroupIdList))
                                                    .Select(e => e.ProgramTargetGroupIdList).ToList()
                                                    .SelectMany(w => w.Split(','))
                                                    .GroupBy(s => s)
                                                    .ToDictionary(s => Int32.Parse(s.Key),s => s.Count());

                    searchResultCounts.TargetGroupCountryCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ProgramTargetGroupIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
                            .Select(e => new { e.ProgramTargetGroupIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.ProgramTargetGroupIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
                            .GroupBy(s => int.Parse(s.TopicId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().Count());

                    searchResultCounts.DeliveryChnlCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ActionDeliveryIdList))
                                                        .Select(e => e.ActionDeliveryIdList).ToList()
                                                        .SelectMany(w => w.Split(','))
                                                        .GroupBy(s => s)
                                                        .ToDictionary(s => Int32.Parse(s.Key), s=> s.Count());

                    searchResultCounts.DeliveryChnlCountryCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ActionDeliveryIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
                            .Select(e => new { e.ActionDeliveryIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.ActionDeliveryIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), ActionDeliveryId = t })))
                            .GroupBy(s => int.Parse(s.ActionDeliveryId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().Count());

                    searchResultCounts.ProblemTypeCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ActionProblemIdList))
                                                         .Select(e => e.ActionProblemIdList).ToList()
                                                         .SelectMany(w => w.Split(','))
                                                         .GroupBy(s => s)
                                                         .ToDictionary(s => Int32.Parse(s.Key), s=> s.Count());

                    searchResultCounts.ProblemTypeCountryCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.ActionProblemIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
                            .Select(e => new { e.ActionProblemIdList, e.CountryISOlist }).ToList()
                             .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.ActionProblemIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), ActionProblemId = t })))
                            .GroupBy(s => int.Parse(s.ActionProblemId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().Count());

                    
                    searchResultCounts.TopicWiseActionList = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.TopicIdList))
                                                         .Select(e => new { e.Id, e.TopicIdList }).ToList()
                                                      .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, ActionId = s.Id, }))
                                                      .GroupBy(s => int.Parse(s.TopicId))
                                                      .ToDictionary(g => g.Key, g => g.Select(s => s.ActionId).Distinct().ToList());

                    searchResultCounts.TopicWiseActionCountryList = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.TopicIdList))
                            .Select(e => new { e.CountryISOlist, e.TopicIdList }).ToList()
                            .SelectMany(s => s.CountryISOlist.Split(',')
                            .SelectMany(c => s.TopicIdList.Split(',')
                            .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
                            .GroupBy(s => int.Parse(s.TopicId))
                            .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
                }
            }
            GC.Collect();
            return searchResultCounts;
        }

        // private SearchResultCounts GetSearchResultCount(List<CombinedSearchResult> allList, SearchRequest searchRequest)
        // {
        //     var searchResultCounts = new SearchResultCounts();

        //     if (!searchRequest.FilterCriteria && searchRequest.DefaultPage == "map")
        //     {
        //         var mapData = allList.Where(w => !string.IsNullOrEmpty(w.CountryISOlist) && searchRequest.SelectedFilterTypes.Contains(w.Type))
        //                   .SelectMany(s => s.CountryCodeAndNames.Split('$')
        //                   .Select(pair => pair.Split('|'))
        //                   .Select(pair => new { CountryISO = pair[0].Trim(), CountryName = pair[1].Trim(), Type = s.Type }))
        //             .GroupBy(s => new { s.CountryISO, s.CountryName, s.Type })
        //                   .Select(e => new MapPopUpModel()
        //                   {
        //                       CountryCode = e.Key.CountryISO,
        //                       CountryName = e.Key.CountryName,
        //                       Total = e.Count(),
        //                       Type = e.First().Type
        //                   }).ToList();

        //         searchResultCounts.MapData = MapSerachItemCountry.SetColorForMap(mapData);
        //     }


        //     searchResultCounts.DataTypeCount = allList.GroupBy(s => s.Type)
        //                                         .ToDictionary(g => g.Key, g => g.Count());

        //     searchResultCounts.DataTypeWiseCountryCount = allList.Where(s => !string.IsNullOrEmpty(s.CountryISOlist))
        //                                        .SelectMany(s => s.CountryISOlist.Split(',').Select(w => new { Type = s.Type, Country = w.Trim() }))
        //                                        .GroupBy(s => s)
        //                                        .GroupBy(s => s.FirstOrDefault().Type)
        //                                        .ToDictionary(g => g.Key, g => g.Count());


        //     searchResultCounts.CountryCount = allList.Where(s => !string.IsNullOrEmpty(s.CountryISOlist))
        //                                        .SelectMany(s => s.CountryISOlist.Split(',')).Select(s => s.Trim())
        //                                        .GroupBy(s => s)
        //                                        .ToDictionary(g => g.Key, g => g.Count());
        //     // advance filter code 
        //     if (searchRequest.FilterCriteria)
        //     {
        //         searchResultCounts.PartnerCount = allList.Where(s => !string.IsNullOrEmpty(s.ParntnerIdList))
        //                                        .SelectMany(s => s.ParntnerIdList.Split(','))
        //                                        .GroupBy(s => s)
        //                                        .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));


        //         searchResultCounts.ProgramTypeCount = allList.Where(s => s.ProgramTypeId != 0 && s.ProgramTypeId != null)
        //                                             .Select(w => w.ProgramTypeId).GroupBy(s => s)
        //                                             .Select(s => new KeyValuePair<int, int>(s.Key.Value, s.Count()));

        //         searchResultCounts.LanguageCount = allList.Where(s => s.LanguageId != null)
        //                                             .Select(w => w.LanguageId).GroupBy(s => s)
        //                                             .ToDictionary(s => Int32.Parse(s.Key),s => s.Count());

        //         searchResultCounts.FundingSourceCount = allList.Where(s => !string.IsNullOrEmpty(s.ProgramPartnerCategoryIdList))
        //                                             .SelectMany(w => w.ProgramPartnerCategoryIdList.Split(',').Distinct())
        //                                             .GroupBy(s => s)
        //                                             .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));


        //         searchResultCounts.MechanismTypeCount = allList.Where(s => s.MechanismTypeId != 0)
        //                                             .Select(w => w.MechanismTypeId).GroupBy(s => s)
        //                                             .Select(s => new KeyValuePair<int, int>(s.Key, s.Count()));

        //         searchResultCounts.ICN2Count = allList.Where(s => !string.IsNullOrEmpty(s.Icn2IdList))
        //                                      .SelectMany(w => w.Icn2IdList.Split(','))
        //                                      .GroupBy(s => s)
        //                                      .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));
        //         if (searchRequest.SelectedFilterTypes.Contains("P"))
        //         {
        //             searchResultCounts.PolicyTypeCount = allList.Where(s => s.PolicyTypeId != 0 && s.PolicyTypeId != null).Select(w => w.PolicyTypeId)
        //                                          .GroupBy(s => s)
        //                                          .Select(s => new KeyValuePair<int, int>(s.Key.Value, s.Count()));

        //             searchResultCounts.PolicyTopicCount = allList.Where(s => s.Type == "P" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                         .SelectMany(s => s.TopicIdList.Split(','))
        //                                         .GroupBy(s => s)
        //                                         .ToDictionary(g => Int32.Parse(g.Key), g => g.Count());

        //             searchResultCounts.TopicWisePolicyIdList = allList.Where(s => s.Type == "P" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                            .SelectMany(s => s.TopicIdList.Split(',')
        //                                            .Select(w => new { TopicId = w, PolicyId = s.Id, }))
        //                                            .GroupBy(s => int.Parse(s.TopicId))
        //                                            .ToDictionary(g => g.Key, g => g.Select(s => s.PolicyId).Distinct().ToList());

        //             searchResultCounts.TopicWisePolicyCountryList = allList.Where(s => s.Type == "P" && !string.IsNullOrEmpty(s.TopicIdList))
        //                     .SelectMany(s => s.CountryISOlist.Split(',')
        //                     .SelectMany(c => s.TopicIdList.Split(',')
        //                     .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
        //                     .GroupBy(s => int.Parse(s.TopicId))
        //                     .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());

        //         }

        //         if (searchRequest.SelectedFilterTypes.Contains("M"))
        //         {
        //             searchResultCounts.MechanismTopicCount = allList.Where(s => s.Type == "M" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                          .SelectMany(s => s.TopicIdList.Split(','))
        //                                          .GroupBy(s => s)
        //                                          .ToDictionary(g => Int32.Parse(g.Key), g => g.Count());

        //             searchResultCounts.TopicWiseMechanismList = allList.Where(s => s.Type == "M" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                        .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, MechanismId = s.Id, }))
        //                                        .GroupBy(s => int.Parse(s.TopicId))
        //                                        .ToDictionary(g => g.Key, g => g.Select(s => s.MechanismId).Distinct().ToList());

        //             searchResultCounts.TopicWiseMechanismCountryList = allList.Where(s => s.Type == "M" && !string.IsNullOrEmpty(s.TopicIdList) && !string.IsNullOrEmpty(s.CountryISOlist))
        //                     .SelectMany(s => s.CountryISOlist.Split(',')
        //                     .SelectMany(c => s.TopicIdList.Split(',')
        //                     .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
        //                     .GroupBy(s => int.Parse(s.TopicId))
        //                     .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
        //         }

        //         if (searchRequest.SelectedFilterTypes.Contains("A"))
        //         {
        //             searchResultCounts.TargetGroupCount = allList.Where(s => !string.IsNullOrEmpty(s.ProgramTargetGroupIdList))
        //                                             .SelectMany(w => w.ProgramTargetGroupIdList.Split(','))
        //                                             .GroupBy(s => s)
        //                                             .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));

        //             searchResultCounts.DeliveryChnlCount = allList.Where(s => !string.IsNullOrEmpty(s.ActionDeliveryIdList))
        //                                                 .SelectMany(w => w.ActionDeliveryIdList.Split(','))
        //                                                 .GroupBy(s => s)
        //                                                 .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));

        //             searchResultCounts.ProblemTypeCount = allList.Where(s => !string.IsNullOrEmpty(s.ActionProblemIdList))
        //                                                  .SelectMany(w => w.ActionProblemIdList.Split(','))
        //                                                  .GroupBy(s => s)
        //                                                  .Select(s => new KeyValuePair<int, int>(Int32.Parse(s.Key), s.Count()));

        //             searchResultCounts.ActionTopicCount = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                           .SelectMany(s => s.TopicIdList.Split(','))
        //                                           .GroupBy(s => s)
        //                                           .ToDictionary(g => Int32.Parse(g.Key), g => g.Count());


        //             searchResultCounts.TopicWiseActionList = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.TopicIdList))
        //                                               .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, ActionId = s.Id, }))
        //                                               .GroupBy(s => int.Parse(s.TopicId))
        //                                               .ToDictionary(g => g.Key, g => g.Select(s => s.ActionId).Distinct().ToList());

        //             searchResultCounts.TopicWiseActionCountryList = allList.Where(s => s.Type == "A" && !string.IsNullOrEmpty(s.TopicIdList))
        //                     .SelectMany(s => s.CountryISOlist.Split(',')
        //                     .SelectMany(c => s.TopicIdList.Split(',')
        //                     .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
        //                     .GroupBy(s => int.Parse(s.TopicId))
        //                     .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
        //         }
        //     }
        //     if (!searchRequest.FilterCriteria && searchRequest.DefaultPage == "searchpage")
        //     {
        //         searchResultCounts.BasicTopicCountList = allList.Where(s => !string.IsNullOrEmpty(s.TopicIdList) && searchRequest.SelectedFilterTypes.Contains(s.Type))
        //                                       .SelectMany(s => s.TopicIdList.Split(',').Select(w => new { TopicId = w, ActionId = s.Id, }))
        //                                       .GroupBy(s => int.Parse(s.TopicId))
        //                                       .ToDictionary(g => g.Key, g => g.Select(s => s.ActionId).Distinct().ToList());

        //         searchResultCounts.BasicTopicCountryList = allList.Where(s => !string.IsNullOrEmpty(s.TopicIdList) && !string.IsNullOrEmpty(s.CountryISOlist) && searchRequest.SelectedFilterTypes.Contains(s.Type))
        //                 .SelectMany(s => s.CountryISOlist.Split(',')
        //                 .SelectMany(c => s.TopicIdList.Split(',')
        //                 .Select(t => new { CountryISO = c.Trim(), TopicId = t })))
        //                 .GroupBy(s => int.Parse(s.TopicId))
        //                 .ToDictionary(g => g.Key, g => g.Select(s => s.CountryISO).Distinct().ToList());
        //     }


        //     GC.Collect();
        //     return searchResultCounts;
        // }

        public async Task<List<CombinedSearchResult>> GetSubscriberContentResult(List<string> countryList, List<string> topicids, DateTime?[] range, bool includeRevisedContent)
        {
            List<CombinedSearchResult> allList = new();
            try
            {
                var _dbContext = _DbFactory.CreateDbContext();
                _dbContext.Database.SetCommandTimeout(100000);

                var query = _dbContext.CombinedSearchResults.AsQueryable();
                if (range.Any())
                {
                    query = query.Where(r => r.DateCreated >= range.ElementAt(0) && r.DateCreated <= range.ElementAt(1));
                }
                if (includeRevisedContent)
                {
                    query = query.Where(r => r.DateUpdated >= range.ElementAt(0) && r.DateUpdated <= range.ElementAt(1));
                }

                if (countryList.Any())
                {
                    query = query.Where(r => countryList.Contains(r.CountryList));
                }

                if (topicids.Any())
                {
                    query = query.Where(r => topicids.Contains(r.TopicIdList));
                }

                allList = await query.AsQueryable()
                                     .OrderBy(s => s.Id)
                                     .Select(s => new CombinedSearchResult()
                                     {
                                         Title = s.Title,
                                         CountryIncomeGroup = s.CountryIncomeGroup,
                                         CountryISOlist = s.CountryISOlist,
                                         CountryList = s.CountryList,
                                         EndYear = s.EndYear,
                                         EnglishTitle = s.EnglishTitle,
                                         Id = s.Id,
                                         TopicIdList = s.TopicIdList,
                                         RegionCode = s.RegionCode,
                                         Type = s.Type,
                                         StartYear = s.StartYear
                                     }).ToListAsync();

                return allList.ToList();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<Tuple<int?, int?>> GetAvailableStartYearAndEndYearForDatatype()
        {
            var _dbContext = _DbFactory.CreateDbContext();
            _dbContext.Database.SetCommandTimeout(100000);

            return await _dbContext.CombinedSearchResults
                    .Select(x => new Tuple<int?, int?>(
                        _dbContext.CombinedSearchResults.Min(y => y.StartYear),
                        _dbContext.CombinedSearchResults.Max(y => y.StartYear)
                    )).FirstOrDefaultAsync();
        }

        public async Task<bool> RemoveByIdAsync(int id)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            var match = await _dbContext.Policies.FirstOrDefaultAsync(c => c.Id == id);
            if (match == null)
            {
                return false;
            }
            _dbContext.Policies.Remove(match);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task< HashSet<string>> GetPolicyCountriesByPolicyIdAndRevisionId(int policyId, int revisionId)
        {
            using var _dbContext = _DbFactory.CreateDbContext();
            return await _dbContext.PolicyCountryDrafts
                .Where(e=>e.PolicyId == policyId && e.PolicyVId == revisionId)
                .Select(e=>e.CountryCode)
                .ToHashSetAsync();
        }
    }
}
