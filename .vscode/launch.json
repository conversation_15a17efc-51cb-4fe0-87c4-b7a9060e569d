{"version": "0.2.0", "configurations": [{"name": "C#: <PERSON><PERSON>.<PERSON><PERSON>", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/Gina2.Blazor/bin/Debug/net8.0/Gina2.Blazor.dll", "args": [], "cwd": "${workspaceFolder}/Gina2.Blazor", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}]}