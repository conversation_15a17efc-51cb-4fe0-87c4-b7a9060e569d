﻿namespace Domain.Search
{
    public class SearchResult
    {
        public int Id { get; set; }
        //public int Key { get; set; }
        public string UniqueKey => $"{Type}_{Id}";
        public bool IsSelected { get; set; }
        //public int ForeignKey1 { get; set; }
        public string Country { get; set; }
        //public string CountryCode { get; set; }
        //public string CountryISOlist { get; set; }
        public string CountryList { get; set; }

        public List<string> Countries
        {
            get
            {
                if (string.IsNullOrEmpty(Country))
                {
                    return new List<string>();
                }

                return Country.Split(',').ToList();
            }
        }

        private string regionCode { get; set; }
        public string RegionCode
        {
            get
            {
                if (!string.IsNullOrEmpty(this.regionCode))
                {
                    var spllitedValues = this.regionCode.Split(",");

                    if (spllitedValues.Count() > 1)
                    {
                        var regions = spllitedValues.Select(s => s.Trim()).Distinct();
                        return String.Join(", ", regions.OrderBy(x => x));
                    }
                }
                return this.regionCode;

            }
            set { this.regionCode = value; }
        }
        public string Region { get; set; }
        //public string LowIncomeGroup { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public int? StartYear { get; set; }
        public string StartYearString => StartYear?.ToString() ?? "-";
        public int? EndYear { get; set; }
        public string DetailsUrl { get; set; }
    }
}
