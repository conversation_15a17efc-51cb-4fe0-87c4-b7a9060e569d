﻿
namespace Domain.Search
{
    public class SearchResponse
    {
        public int TotalFilteredCount { get; set; }
        public int NoOfSmartComitments { get; set; }
        public int NoOfMechanism { get; set; }
        public int NoOfActions { get; set; }
        public int NoOfPolices { get; set; }
        public int NoOfSmartComitmentsCountry { get; set; }
        public int NoOfMechanismCountry { get; set; }
        public int NoOfActionsCountry { get; set; }
        public int NoOfPolicesCountry { get; set; }
        public int FilteredPolicyCount { get; set; }
        public int TotalPolicyCount { get; set; }
        // public int DistinctCountryCount { get; set; }
        public List<SearchResult> SearchResults { get; set; }
        //  public List<MapCountrywithColor> MapData { get; set; } = new List<MapCountrywithColor>();
        public SearchResultCounts SearchResultCounts { get; set; } = new();
    }
    public class MapCountrywithColor
    {
        public string Type { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string Color { get; set; }
        public int TotalPolicyByCountry { get; set; }
        public int TotalSmartComitmentsByCountry { get; set; }
        public int TotalMechanismsByCountry { get; set; }
        public int TotalActionByCountry { get; set; }
        public string IndicatorName { get; set; }
        public string IndicatorColor { get; set; }
    }
    public class MapIndicatorDetail
    {
        public string CountryCode { get; set; }
        public string CountryName { get; set; }
        public string IndicatorName { get; set; }
        public float IndicatorValue { get; set; }
        public string ColorCode { get; set; }
        public int Year { get; set; }
    }

    public class SearchResultCounts
    {

        public int TotalRecordCount { get; set; }
        public int TotalCountryCount { get; set; }

        public List<MapCountrywithColor> MapData { get; set; } = new List<MapCountrywithColor>();

        public Dictionary<string, int> DataTypeCount { get; set; } = new();
        public Dictionary<string, int> DataTypeWiseCountryCount { get; set; } = new();
        public Dictionary<string, int> CountryCount { get; set; } = new();
        public Dictionary<int, List<int>> PartnerCount { get; set; } = new();
        public Dictionary<int, int> PolicyTypeCount { get; set; } = new();
        public Dictionary<int, int> PolicyTypeCountryCount { get; set; } = new();
        public Dictionary<int, int> ProgramTypeCount { get; set; } = new();
        public Dictionary<int, int> ProgramTypeCountryCount { get; set; } = new();
        public Dictionary<int, int> FundingSourceCount { get; set; } = new();
        public Dictionary<int, int> FundingSourceCountryCount { get; set; } = new();
        public Dictionary<int, int> TargetGroupCount { get; set; } = new();
        public Dictionary<int, int> TargetGroupCountryCount { get; set; } = new();
        public Dictionary<int, int> DeliveryChnlCount { get; set; } = new();
        public Dictionary<int, int> DeliveryChnlCountryCount { get; set; } = new();
        public Dictionary<int, int> ProblemTypeCount { get; set; } = new();
        public Dictionary<int, int> ProblemTypeCountryCount { get; set; } = new();
        public Dictionary<int, int> MechanismTypeCount { get; set; } = new();
        public Dictionary<int, List<int>> ICN2Count { get; set; } = new();
        public Dictionary<int, List<string>> BasicICN2CountryList { get; set; } = new();
        public Dictionary<int, int> LanguageCount { get; set; } = new();
        public Dictionary<int, int> LanguageCountryCount { get; set; } = new();
        public Dictionary<int, int> PolicyTopicCount { get; set; } = new();
        public Dictionary<int, int> BasicTopicCount { get; set; } = new();
        public Dictionary<int, int> MechanismTopicCount { get; set; } = new();
        public Dictionary<int, int> MechanismTypeCountryCount { get; set; } = new();
        public Dictionary<int, int> ActionTopicCount { get; set; } = new();
        public Dictionary<int, List<int>> TopicWisePolicyIdList { get; set; } = new();
        public Dictionary<int, List<int>> TopicWiseMechanismList { get; set; } = new();
        public Dictionary<int, List<int>> TopicWiseActionList { get; set; } = new();
        public Dictionary<int, List<int>> BasicTopicCountList { get; set; } = new();
        public Dictionary<int, List<string>> BasicTopicCountryList { get; set; } = new();
        public Dictionary<int, List<string>> BasicPartnerCountryList { get; set; } = new();
        public Dictionary<int, List<string>> TopicWisePolicyCountryList { get; set; } = new();
        public Dictionary<int, List<string>> TopicWiseMechanismCountryList { get; set; } = new();
        public Dictionary<int, List<string>> TopicWiseActionCountryList { get; set; } = new();

    }
}