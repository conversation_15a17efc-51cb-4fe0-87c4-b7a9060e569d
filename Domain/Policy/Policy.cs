﻿using Domain.Attributes;
using Domain.Countries;
using System.ComponentModel.DataAnnotations;

namespace Domain.Policy
{
    public class Policy : AuditableEntity
    {
        // TODO: Later this should be removed and the type of the Id (int AuditableEntity) shoudl be int.
        public int Key { get; set; }

        [DataType(DataType.Text)]
        [MaxLength(4000)]
        [Display(Name = "Policy title")]
        public string Title { get; set; }

        [DataType("Select")]
        [Display(Name = "Type of policy")]
        [SelectListValues(selectlist: new string[] { "Type 1", "Type 2" })]
        public string TypeId { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Language")]
        public string LanguageId { get; set; } //TODO: DataType will be 'Select' when Parent component is done. Until then, It is typed as string.

        [DataType(DataType.Text)]
        [Display(Name = "Province/Region")]
        public string SubnationalGeographicArea { get; set; }

        [DataType("MonthSelect")]
        [Display(Name = "Start month")]
        public int StartMonth { get; set; }

        [DataType("YearSelect")]
        [Display(Name = "Start year")]
        public int? StartYear { get; set; }

        [DataType("MonthSelect")]
        [Display(Name = "End month")]
        public int EndMonth { get; set; }

        [DataType("YearSelect")]
        [Display(Name = "End year")]
        public int? EndYear { get; set; }

        [DataType("MonthSelect")]
        [Display(Name = "Published month")]
        public int PublishedMonth { get; set; }

        [DataType("YearSelect")]
        [Display(Name = "Published year")]
        public int PublishedYear { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Published by")]
        public string PublishedBy { get; set; }

        [DataType("Radio")]
        [Display(Name = "Adopted")]
        [SelectListValues(selectlist: new string[] { "Yes", "No / No information", "N/A" })]
        public string Adopted { get; set; }

        [DataType("MonthSelect")]
        [Display(Name = "Adopted month")]
        public int AdoptedMonth { get; set; }

        [DataType("YearSelect")]
        [Display(Name = "Adopted year")]
        public int AdoptedYear { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Adopted by")]
        public string AdoptedBy { get; set; }

        public PolicyPartnerGroup PolicyPartnerGroup { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Goals, objectives or targets related to nutrition")]
        public string GoalsObjectiveOrTargetsRelatedToNutrition { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Strategies and activities related to nutrition")]
        public string StrategiesAndActivitiesRelatedToNutrition { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "M&E Indicators related to nutrition")]
        public string MEIndicatorsRelatedToNutrition { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Legislation details")]
        public string LegislationDetails { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "URL link")]
        public string Url { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "Further notes")]
        public string FurtherNotes { get; set; }

        [DataType(DataType.Text)]
        [Display(Name = "References")]
        public string References { get; set; }

        public List<Country> Countries { get; set; } = new();

        public string Country { get; set; }
        public string CountryCode { get; set; }

        // TODO: how to apply checkbox instead radio here
        [DataType("Radio")]
        [Display(Name = "M & E Indicator Types")]
        [SelectListValues(selectlist: new string[] { "Outcome Indicators", "Process Indicators" })]
        public string MEIndicatorTypes { get; set; }

        // TODO: treeview
        public PolicyTopics.PolicyTopic PolicyTopic { get; set; }

        //TODO: Multiple select
        [DataType("Select")]
        [Display(Name = "Link to existing action records")]
        [SelectListValues(selectlist: new string[] { "Type 1", "Type 2" })] // Get all records from programs
        public List<int> RelatedPrograms { get; set; }
    }
}