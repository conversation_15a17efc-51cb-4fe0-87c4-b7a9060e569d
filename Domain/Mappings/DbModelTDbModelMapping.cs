﻿using AutoMapper;
using Domain.XMartModels.Actions;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace Domain.Mappings
{
    public class DbModelTDbModelMapping : IMapTo<PolicyRevision>
    {
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyRevision, Gina2.DbModels.Policy>()
                 .ForMember(dest => dest.PublishedMonth, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.PublishedMonth)? 0 : Convert.ToInt32(src.PublishedMonth)))
                 .ForMember(dest => dest.PolicyAttachments, opt => opt.MapFrom(src => src.PolicyAttachmentDrafts))
                 .ForMember(dest => dest.PolicyCatogeryPartnerMap, opt => opt.MapFrom(src => src.PolicyPartnerCategoryPartnerDrafts))
                 .ForMember(dest => dest.PolicyCountryMap, opt => opt.MapFrom(src => src.PolicyCountryDrafts))
                 .ForMember(dest => dest.PolicyPartnerCategories, opt => opt.MapFrom(src => src.PolicyCategoryPartnerDrafts))
                 .ForMember(dest => dest.PolicyTopics, opt => opt.MapFrom(src => src.PolicyTopicDrafts))
                 .ForMember(dest => dest.PolicyPrograms, opt => opt.MapFrom(src => src.PolicyProgramRevision));
            profile.CreateMap<PolicyCountryDraft, Gina2.DbModels.PolicyCountryMapItem>();
            profile.CreateMap<PolicyAttachmentDraft, Gina2.DbModels.PolicyAttachment>();
            profile.CreateMap<PolicyTopicDraft, Gina2.DbModels.PolicyTopic>();
            profile.CreateMap<PolicyCategoryPartnerDraft, Gina2.DbModels.PolicyPartnerCategory>();
            profile.CreateMap<PolicyPartnerCategoryPartnerDraft, Gina2.DbModels.PolicyCategoryPartnerMapItem>();
            profile.CreateMap<PolicyProgramRevision, Gina2.DbModels.PolicyProgram>();

            // for mechanism 

            profile.CreateMap<MechanismRevision, Gina2.DbModels.Mechanism>()
                   .ForMember(dest => dest.MechanismCountryMap, opt => opt.MapFrom(src => src.MechanismCountryMapRevision))
                   .ForMember(dest => dest.MechanismCoordination, opt => opt.MapFrom(src => src.MechanismCoordinationRevision))
                   .ForMember(dest => dest.MechanismMonitorings, opt => opt.MapFrom(src => src.MechanismMonitoringRevision))
                   .ForMember(dest => dest.MechanismPartnerCategoryMap, opt => opt.MapFrom(src => src.MechanismPartnerRevision))
                   .ForMember(dest => dest.MechanismPartnerCategories, opt => opt.MapFrom(src => src.MechanismPartnerCategoryDetailsRevision))
                   .ForMember(dest => dest.MechanismPolicies, opt => opt.MapFrom(src => src.MechanismPolicyRevision))
                   .ForMember(dest => dest.MechanismTopics, opt => opt.MapFrom(src => src.MechanismTopicRevision));

            profile.CreateMap<MechanismCountryMapRevision, Gina2.DbModels.MechanismCountryMapItem>();
            profile.CreateMap<MechanismCoordinationRevision, Gina2.DbModels.MechanismCoordination>();
            profile.CreateMap<MechanismMonitoringRevision, Gina2.DbModels.MechanismMonitoring>();
            profile.CreateMap<MechanismTopicRevision, Gina2.DbModels.MechanismTopic>();
            profile.CreateMap<MechanismPartnerRevision, Gina2.DbModels.MechanismPartnerCategoryMapItem>();
            profile.CreateMap<MechanismPartnerCategoryDetailsRevision, Gina2.DbModels.MechanismPartnerCategory>();
            profile.CreateMap<MechanismPolicyRevision, Gina2.DbModels.MechanismPolicy>();
            // for commitment and smart commitment

            profile.CreateMap<CommitmentRevision, Gina2.DbModels.Commitment>()
                .ForMember(dest => dest.CommitmentAttachments, opt => opt.MapFrom(src => src.CommitmentAttachmentRevision))
                .ForMember(dest => dest.CommitmentPolicies, opt => opt.MapFrom(src => src.CommitmentPolicyRevision))
                .ForMember(dest => dest.CommitmentCountryMap, opt => opt.MapFrom(src => src.CommitmentCountryMapRevision))
                .ForMember(dest => dest.CommitmentPartners, opt => opt.MapFrom(src => src.CommitmentPartnerRevision));
            profile.CreateMap<CommitmentCountryMapRevision, Gina2.DbModels.CommitmentCountryMapItem>();
            profile.CreateMap<CommitmentPartnerRevision, Gina2.DbModels.CommitmentPartner>();
            profile.CreateMap<CommitmentAttachmentRevision, Gina2.DbModels.CommitmentAttachment>();

            profile.CreateMap<CommitmentPolicyRevision, Gina2.DbModels.CommitmentPolicy>();
            profile.CreateMap<SmartCommitmentRevision, Gina2.DbModels.SmartCommitment>()
                .ForMember(dest => dest.SmartCommitmentSdgMappingItems, opt => opt.MapFrom(src => src.SmartCommitSDGRevision))
                .ForMember(dest => dest.CommitmentICN2s, opt => opt.MapFrom(src => src.SmartCommitmentICN2Revision));

            profile.CreateMap<SmartCommitmentICN2Revision, Gina2.DbModels.SmartCommitmentIcn2MappingItem>();
            profile.CreateMap<SmartCommitSDGRevision, Gina2.DbModels.SmartCommitmentSdgMappingItem>();

            // for program and action

            profile.CreateMap<ProgrammePartnerRevision, Gina2.DbModels.ProgramCategoryPartnerMapItem>();
            profile.CreateMap<ProgramPartnerCategoryDetailsRevision, Gina2.DbModels.ProgramPartnerCategory>();
            profile.CreateMap<ProgramRevision, Gina2.DbModels.Program>()
             .ForMember(dest => dest.TypeId, opt => opt.MapFrom(src => src.ProgramTypeId))
             .ForMember(dest => dest.ProgramCategoryPartnerMap, opt => opt.MapFrom(src => src.ProgrammePartnerRevision))
             .ForMember(dest => dest.ProgramPartnerCategories, opt => opt.MapFrom(src => src.ProgramPartnerCategoryDetailsRevision))
             .ForMember(dest => dest.ProgramPolicies, opt => opt.MapFrom(src => src.ProgramPolicyRevision))
             .ForMember(dest => dest.ProgrammeCountryMap, opt => opt.MapFrom(src => src.ProgrammeCountryMapRevision));

            profile.CreateMap<ProgrammeCountryMapRevision, Gina2.DbModels.ProgrammeCountryMapItem>();
            profile.CreateMap<ProgramPolicyRevision, Gina2.DbModels.ProgramPolicy>();
            profile.CreateMap<ActionRevision, Gina2.DbModels.Action>()
                   .ForMember(dest => dest.ActionAreas, opt => opt.MapFrom(src => src.ActionAreaRevision))
                   .ForMember(dest => dest.ActionDeliveries, opt => opt.MapFrom(src => src.ActionDeliveryRevision))
                   .ForMember(dest => dest.ActionElenaLinks, opt => opt.MapFrom(src => src.ActionElenaRevision))
                   .ForMember(dest => dest.ActionMicronutrients, opt => opt.MapFrom(src => src.ActionMicronutrientRevision))
                   .ForMember(dest => dest.ActionTargetGroups, opt => opt.MapFrom(src => src.ActionTargetGroupRevision))
                   .ForMember(dest => dest.ActionProblems, opt => opt.MapFrom(src => src.ActionProblemRevision))
                   .ForMember(dest => dest.ActionSocialDeterminants, opt => opt.MapFrom(src => src.ActionSocialDeterminantRevisions)).ReverseMap();

            profile.CreateMap<ActionAreaRevision, Gina2.DbModels.ActionArea>();
            profile.CreateMap<ActionDeliveryRevision, Gina2.DbModels.ActionDelivery>();
            profile.CreateMap<ActionElenaRevision, Gina2.DbModels.ActionElenaLink>();
            profile.CreateMap<ActionMicronutrientRevision, Gina2.DbModels.ActionMicronutrients>();
            profile.CreateMap<ActionProblemRevision, Gina2.DbModels.ActionProblem>();
            profile.CreateMap<ActionSocialDeterminantRevision, Gina2.DbModels.ActionSocialDeterminant>();
            profile.CreateMap<ActionTargetGroupRevision, Gina2.DbModels.ActionTargetGroup>();
            
            //C
            profile.CreateMap<Domain.Mechanism.MechanismPolicy, Gina2.DbModels.PolicyCountryMapItem>().ReverseMap()
                   .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
                   .ForMember(dest => dest.PolicyTitle, opt => opt.MapFrom(src => $"{src.Country.Name}-{src.Policy.Title}"))
                   .ForMember(dest => dest.CountryIsoCode , opt => opt.MapFrom(src => src.CountryCode));
            profile.CreateMap<ProgramFundingResourceDetailRevision, Gina2.DbModels.ProgramFundingResourceDetail>();
            profile.CreateMap<ProgramFundingResourceRevision, Gina2.DbModels.ProgramFundingResource>();
        }
    }
}
