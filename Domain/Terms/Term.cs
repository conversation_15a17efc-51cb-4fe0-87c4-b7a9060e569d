﻿
namespace Domain.Terms
{
    public sealed class Term
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public int? PolicyId { get; set; }
        public int? MechanismId { get; set; }
        public List<int> AllParentId { get; set; } = new List<int>();
        public string ParentName { get; set; }
        public bool IsTopicParent { get; set; } = true;
        public bool IsPlusIcon { get; set; }
        public bool IsCheck { get; set; }
        public bool IsIndeterminate { get; set; }
        public bool IsSearchCheck { get; set; }
        public string ParentIdString { get; set; }
        public string ExistingChildIdString { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string URL { get; set; }
        public string Iso3Code { get; set; }
        public string Code { get; set; }
        public string Key { get; set; }
        public string Status { get; set; }
        public bool IsEditCountry { get; set; }
        public List<Term> Icn2s { get; set; } = new List<Term>();
        public List<Term> Partner { get; set; } = new List<Term>();
        public List<Term> Child { get; set; } = new List<Term>();
        public List<Term> ParentTopic { get; set; } = new List<Term>();
        //public List<Term> CountryGroup { get; set; } = new List<Term>();
        public List<CountryRegionCode> Region { get; set; } = new List<CountryRegionCode>();
        public int DragAndDropKey { get; set; }
        public bool IsAllChildCheck { get; set; }
        public bool IsAlreadyCheck { get; set; }
        public bool IsParentAllChildCheck { get; set; }
        public bool IsParentSearchCheck { get; set; }
        public bool IsDoubleCheck { get; set; }
        public bool IsSelectedCheck { get; set; }
        public bool IsGrandParent { get; set; }
        public bool SelectParentOnly { get; set; }
        public bool ValidateError { get; set; } = true;
        public bool IsActive { get; set; }
        public bool DeleteDisable { get; set; } = true;
    }

    public class CountryRegionCode
    {
        public string Code { get; set; }

    }

    public class TermTreeNode
    {
        public List<TermTreeNode> Children { get; set; }
        public int Id { get; set; }
        public string Name { get; set; }
        public string RegionCode { get; set; }
        public string Iso3Code { get; set; }
    }
}