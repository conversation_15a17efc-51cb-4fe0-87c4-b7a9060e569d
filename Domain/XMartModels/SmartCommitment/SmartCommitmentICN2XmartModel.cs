﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Commitment.SmartCommitment
{
    public class SmartCommitmentICN2XmartModel : IMapFrom<SmartCommitmentICN2Revision>
    {
        public static string SelectedColumns = $"?$select={nameof(ICN2_ID)},{nameof(SMART_COMMITMENT_ID)},{nameof(SMART_COMMITMENT_VID)}";
        public int ICN2_ID { get; set; }
        public int SMART_COMMITMENT_ID { get; set; }
        public int SMART_COMMITMENT_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitmentICN2XmartModel, SmartCommitmentICN2Revision>()
                .ForMember(dest => dest.SmartCommitmentId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_ID))
                .ForMember(dest => dest.SmartCommitmentVId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.Icn2Id, opt => opt.MapFrom(src => src.ICN2_ID));
        }
    }

    public class SmartCommitmentICN2XmartReadModel : IMapFrom<SmartCommitmentICN2Revision>
    {
        public int ICn2Id { get; set; }
        public int Commitmentid { get; set; }
        public int VersionId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitmentICN2XmartModel, SmartCommitmentICN2XmartReadModel>()
                .ForMember(dest => dest.Commitmentid, opt => opt.MapFrom(src => src.SMART_COMMITMENT_ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.ICn2Id, opt => opt.MapFrom(src => src.ICN2_ID));
        }
    }
}
