﻿using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Actions;
using Domain.XMartModels.Commitment.SmartCommitment;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.SmartCommitment
{
    public class SmartCommitmentXMartModel : IMapFrom<SmartCommitmentRevision>, IMapTo<SmartCommitmentCommitmentRevisionMap>
    {
        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(SMART_COMMITMENT_VID)},{nameof(COMMITMENT_VID)},{nameof(COMMITMENT_ID)},{nameof(SMART_COMMITMENT_NUMBER)},{nameof(SMART_COMMITMENT_TITLE)},{nameof(SMART_COMMITMENT_TITLE_ENGLISH_TRANSLATED)},{nameof(SMART_COMMITMENT_EXTRACT)},{nameof(BY_MONTH)},{nameof(BY_YEAR)}";
        public int ID { get; set; }
        public int SMART_COMMITMENT_VID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public int COMMITMENT_ID { get; set; }
        public string SMART_COMMITMENT_NUMBER { get; set; }
        public string SMART_COMMITMENT_TITLE { get; set; }
        public string SMART_COMMITMENT_TITLE_ENGLISH_TRANSLATED { get; set; }
        public string SMART_COMMITMENT_EXTRACT { get; set; }
        public int? BY_MONTH { get; set; }
        public int? BY_YEAR { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitmentXMartModel, SmartCommitmentCommitmentRevisionMap>()
                .ForMember(dest => dest.SmartCommitmentId, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.SmartCommitmentVId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => 0));

            profile.CreateMap<SmartCommitmentXMartModel, SmartCommitmentRevision>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.CommitmentNumber, opt => opt.MapFrom(src => src.SMART_COMMITMENT_NUMBER))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.SMART_COMMITMENT_TITLE))
                .ForMember(dest => dest.EnglishTranslate, opt => opt.MapFrom(src => src.SMART_COMMITMENT_TITLE_ENGLISH_TRANSLATED))
                .ForMember(dest => dest.Month, opt => opt.MapFrom(src => src.BY_MONTH))
                .ForMember(dest => dest.Extract, opt => opt.MapFrom(src => src.SMART_COMMITMENT_EXTRACT))
                .ForMember(dest => dest.Year, opt => opt.MapFrom(src => src.BY_YEAR));
        }
    }

    public class SmartCommitmentXMartReadModel : IMapFrom<SmartCommitmentXMartModel>
    {
        public int SmartCommitmentId { get; set; }
        public int SmartCommitmentVId { get; set; }
        public int CommitmentVId { get; set; }
        public int CommitmentId { get; set; }
        public string CommitmentNumber { get; set; }
        public string SmartCommitmentTitle { get; set; }
        public string SmartCommitmentEnglishTitle { get; set; }
        public string SmartCommitText { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitmentXMartModel, SmartCommitmentXMartReadModel>()
                .ForMember(dest => dest.SmartCommitmentId, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.SmartCommitmentVId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.CommitmentNumber, opt => opt.MapFrom(src => src.SMART_COMMITMENT_NUMBER))
                .ForMember(dest => dest.SmartCommitmentTitle, opt => opt.MapFrom(src => src.SMART_COMMITMENT_TITLE))
                .ForMember(dest => dest.SmartCommitmentEnglishTitle, opt => opt.MapFrom(src => src.SMART_COMMITMENT_TITLE_ENGLISH_TRANSLATED))
                .ForMember(dest => dest.StartMonth, opt => opt.MapFrom(src => src.BY_MONTH))
                .ForMember(dest => dest.SmartCommitText, opt => opt.MapFrom(src => src.SMART_COMMITMENT_EXTRACT))
                .ForMember(dest => dest.StartYear, opt => opt.MapFrom(src => src.BY_YEAR));
        }
    }
}
