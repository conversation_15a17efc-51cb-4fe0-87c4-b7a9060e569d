﻿using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Commitment.SmartCommitment;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.SmartCommitment
{
    public class SmartCommitmentSDGXmartModel : IMapFrom<SmartCommitSDGRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(SDG_ID)},{nameof(SMART_COMMITMENT_ID)},{nameof(SMART_COMMITMENT_VID)}";
        public int SDG_ID { get; set; }
        public int SMART_COMMITMENT_ID { get; set; }
        public int SMART_COMMITMENT_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitmentSDGXmartModel, SmartCommitSDGRevision>()
                .ForMember(dest => dest.SmartCommitmentId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_ID))
                .ForMember(dest => dest.SmartCommitmentVId, opt => opt.MapFrom(src => src.SMART_COMMITMENT_VID))
                .ForMember(dest => dest.SdgId, opt => opt.MapFrom(src => src.SDG_ID));
        }
    }
    public class SmartCommitmentSDGXmartReadModel : IMapFrom<SmartCommitmentSDGXmartModel>
    {
        public int SDGID { get; set; }
        public int SmartCommitmentid { get; set; }
        public int VersionId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SmartCommitSDGRevision, SmartCommitmentSDGXmartReadModel>()
                .ForMember(dest => dest.SmartCommitmentid, opt => opt.MapFrom(src => src.SmartCommitmentId))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.SmartCommitmentVId))
                .ForMember(dest => dest.SDGID, opt => opt.MapFrom(src => src.SdgId));
        }
    }
}
