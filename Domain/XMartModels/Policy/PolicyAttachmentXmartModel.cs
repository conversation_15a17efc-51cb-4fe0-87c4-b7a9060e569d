﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.Policy
{
    public class PolicyAttachmentXmartModel : IMapTo<PolicyAttachmentDraft>
    {
        public static string SelectedColumns = $"?$select={nameof(URL)},{nameof(POLICY_ID)},{nameof(POLICY_VID)},{nameof(FILE)},{nameof(DISPLAY_NAME)},{nameof(FILE_TYPE)}";
        public string URL { get; set; }
        public int POLICY_ID { get; set; }
        public int POLICY_VID { get; set; }
        public string FILE { get; set; }
        public string DISPLAY_NAME { get; set; }
        public string FILE_TYPE { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyAttachmentXmartModel, PolicyAttachmentDraft>()
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.URL))
                .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.FileDisplayName, opt => opt.MapFrom(src => src.DISPLAY_NAME))
                .ForMember(dest => dest.FileType, opt => opt.MapFrom(src => src.FILE_TYPE));
        }
    }

    public class PostPolicyAttachmentXmartModel : IMapTo<PolicyAttachmentDraft>
    {
        public string URL { get; set; }
        public int PolicyId { get; set; }
        public int verisionId { get; set; }
        public string DisplayName { get; set; }
        public string FileType { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyAttachmentDraft,PostPolicyAttachmentXmartModel> ()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(src => src.Url))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.verisionId, opt => opt.MapFrom(src => src.PolicyVId))
                .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.FileDisplayName))
                .ForMember(dest => dest.FileType, opt => opt.MapFrom(src => src.FileType)).ReverseMap();
        }
    }
}