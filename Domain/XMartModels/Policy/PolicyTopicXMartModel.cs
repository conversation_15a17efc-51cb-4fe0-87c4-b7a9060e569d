﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Policy
{
    public class PolicyTopicXMartModel : IMapTo<PolicyTopicDraft>
    {
        public static string SelectedColumns = $"?$select={nameof(TOPIC_ID)},{nameof(POLICY_VID)},{nameof(POLICY_ID)}";
        public int TOPIC_ID { get; set; }
        public int POLICY_VID { get; set; }
        public int POLICY_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyTopicXMartModel, PolicyTopicDraft>()
               .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
               .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
               .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TOPIC_ID));
        }

    }
    public class PostPolicyTopicXMartModel : IMapTo<PolicyTopicDraft>
    {
        public int PolicyId { get; set; }
        public int RevisionId { get; set; }
        public int TopicId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyTopicDraft, PostPolicyTopicXMartModel>()
               .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
               .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.PolicyVId))
               .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TopicId));
        }

    }
}
