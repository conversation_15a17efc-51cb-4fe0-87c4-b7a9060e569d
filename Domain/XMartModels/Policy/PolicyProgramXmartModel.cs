﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Policy
{
    public class PolicyProgramXmartModel : IMapTo<PolicyProgramRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(POLICY_VID)},{nameof(PROGRAMME_ID)}";
        public int POLICY_ID { get; set; }
        public int POLICY_VID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyProgramXmartModel, PolicyProgramRevision>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID));
        }
    }

    public class PostPolicyProgramXmartModel : IMapTo<PolicyProgramRevision>
    {
        public int PolicyId { get; set; }
        public int RevisionId { get; set; }
        public int ProgramId { get; set; }
        
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyProgramRevision, PostPolicyProgramXmartModel>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.PolicyVId))
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.ProgramId));
        }
    }
}
