﻿using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Mechanism;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Policy
{
    public class PolicyRevisionLogXmartModel : IMapTo<PolicyLog>
    {

        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(POLICY_VID)},{nameof(FROM_STATE)},{nameof(TO_STATE)},{nameof(USERNAME)},{nameof(DELEGATED_DATE)},{nameof(REVISED_DATE)},{nameof(DELEGATED_USER)},{nameof(LOG)},{nameof(Is_Published)}";
        public int POLICY_ID { get; set; }
        public int POLICY_VID { get; set; }
        public string FROM_STATE { get; set; }
        public string TO_STATE { get; set; }
        public string USERNAME { get; set; }
        public DateTimeOffset? DELEGATED_DATE { get; set; }
        public DateTimeOffset? REVISED_DATE { get; set; }
        public string DELEGATED_USER { get; set; }
        public string LOG { get; set; }
        public bool Is_Published { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyRevisionLogXmartModel, PolicyLog>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
                .ForMember(dest => dest.DelegatedDate, opt => opt.MapFrom(src => src.DELEGATED_DATE))
                .ForMember(dest => dest.RevisedDate, opt => opt.MapFrom(src => src.REVISED_DATE))
                .ForMember(dest => dest.DelegatedUserName, opt => opt.MapFrom(src => src.DELEGATED_USER))
                .ForMember(dest => dest.Log, opt => opt.MapFrom(src => src.LOG))
                .ForMember(dest => dest.FromState, opt => opt.MapFrom(src => src.FROM_STATE))
                .ForMember(dest => dest.ToState, opt => opt.MapFrom(src => src.TO_STATE))
                .ForMember(dest => dest.IsPublished, opt => opt.MapFrom(src => src.Is_Published))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.USERNAME));
        }
    }
}
