﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System.Data;

namespace Domain.XMartModels.Policy
{
    public class PolicyXmartModel : IMapTo<PolicyRevision>
    {
        public const string TableName = "IMPORT_POLICIES";

        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(POLICY_VID)},{nameof(REFERENCE)},{nameof(SUBNATIONAL_GEOGRAPHIC_AREA)},{nameof(POLICY_TITLE)},{nameof(POLICY_TITLE_ENGLISH_TRANSLATED)},{nameof(POLICY_TYPE_ID)},{nameof(POLICY_TYPE_OTHER)},{nameof(LANGUAGE_ID)},{nameof(START_MONTH)},{nameof(START_YEAR)},{nameof(END_MONTH)},{nameof(END_YEAR)},{nameof(PUBLISHED_BY)},{nameof(PUBLISHED_MONTH)},{nameof(PUBLISHED_YEAR)},{nameof(ADOPTED)},{nameof(ADOPTED_MONTH)},{nameof(ADOPTED_YEAR)},{nameof(ADOPTED_BY)},{nameof(EXTRACTS)},{nameof(URL)},{nameof(FURTHER_NOTES)}";
        public int ID { get; set; }
        public int POLICY_VID { get; set; }
        public string REFERENCE { get; set; }
        public string SUBNATIONAL_GEOGRAPHIC_AREA { get; set; }
        public string POLICY_TITLE { get; set; }
        public string POLICY_TITLE_ENGLISH_TRANSLATED { get; set; }
        public int? POLICY_TYPE_ID { get; set; }
        public string POLICY_TYPE_OTHER { get; set; }
        public int? LANGUAGE_ID { get; set; }
        public int? START_MONTH { get; set; }
        public int? START_YEAR { get; set; }
        public int? END_MONTH { get; set; }
        public int? END_YEAR { get; set; }
        public string PUBLISHED_BY { get; set; }
        public int? PUBLISHED_MONTH { get; set; }
        public int? PUBLISHED_YEAR { get; set; }
        public bool? ADOPTED { get; set; }
        public int? ADOPTED_MONTH { get; set; }
        public int? ADOPTED_YEAR { get; set; }
        public string ADOPTED_BY { get; set; }
        public string URL { get; set; }
        public string FURTHER_NOTES { get; set; }
        public string EXTRACTS { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyRevision, PolicyXmartModel>()
                .ForMember(dest => dest.ID, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.POLICY_VID, opt => opt.MapFrom(src => src.VersionId))
                .ForMember(dest => dest.SUBNATIONAL_GEOGRAPHIC_AREA, opt => opt.MapFrom(src => src.SubnationalGeographicArea))
                .ForMember(dest => dest.POLICY_TITLE_ENGLISH_TRANSLATED, opt => opt.MapFrom(src => src.EnglishTitle))
                .ForMember(dest => dest.POLICY_TITLE, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.POLICY_TYPE_ID, opt => opt.MapFrom(src => src.PolicyTypeId))
                .ForMember(dest => dest.POLICY_TYPE_OTHER, opt => opt.MapFrom(src => src.OtherPolicyType))
                .ForMember(dest => dest.LANGUAGE_ID, opt => opt.MapFrom(src => src.LanguageId))
                .ForMember(dest => dest.START_MONTH, opt => opt.MapFrom(src => src.StartMonth))
                .ForMember(dest => dest.START_YEAR, opt => opt.MapFrom(src => src.StartYear))
                .ForMember(dest => dest.END_MONTH, opt => opt.MapFrom(src => src.EndMonth))
                .ForMember(dest => dest.END_YEAR, opt => opt.MapFrom(src => src.EndYear))
                .ForMember(dest => dest.PUBLISHED_BY, opt => opt.MapFrom(src => src.PublishedBy))
                .ForMember(dest => dest.PUBLISHED_MONTH, opt => opt.MapFrom(src => src.PublishedMonth))
                .ForMember(dest => dest.PUBLISHED_YEAR, opt => opt.MapFrom(src => src.PublishedYear))
                .ForMember(dest => dest.ADOPTED, opt => opt.MapFrom(src => src.Adopted))
                .ForMember(dest => dest.ADOPTED_BY, opt => opt.MapFrom(src => src.AdoptedBy))
                .ForMember(dest => dest.ADOPTED_MONTH, opt => opt.MapFrom(src => src.AdoptedMonth))
                .ForMember(dest => dest.ADOPTED_YEAR, opt => opt.MapFrom(src => src.AdoptedYear))
                .ForMember(dest => dest.EXTRACTS, opt => opt.MapFrom(src => src.Extracts))
                .ForMember(dest => dest.URL, opt => opt.MapFrom(src => src.Url))
                .ForMember(dest => dest.FURTHER_NOTES, opt => opt.MapFrom(src => src.FurtherNotes))
                .ForMember(dest => dest.REFERENCE, opt => opt.MapFrom(src => src.References)).ReverseMap();
        }
    }

    public class PolicyReadXmartModel
    {
        
      
        public int policyId { get; set; }
        public int verisionid { get; set; }
        public string POlicyReference { get; set; }
        public string SubnationalGeographicalArea { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public int? policyTypeId { get; set; }
        public string PolicyOtherType { get; set; }
        public int? LanguageId { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public int? EndMonth { get; set; }
        public int? Endyear { get; set; }
        public string PublishBy { get; set; }

        public int? PublishMonth { get; set; }
        public int? PublishYear { get; set; }
        public bool? isAdopted { get; set; }
        public int? AdoptedMonth { get; set; }
        public int? AdoptedYear { get; set; }
        public string AdoptedBY { get; set; }
        public string linkDetail { get; set; }
        public string FurtherNotes { get; set; }
        //public string Legislations { get; set; }
        //public string Goals { get; set; }
        //public string Indicators { get; set; }
        //public string IndicatorType { get; set; }
        //public string Strategies { get; set; }
        public string Extracts { get; set; }
        public string FromState { get; set; }
        public string ToState { get; set; }
        public string UserName { get; set; }
        public DateTimeOffset? DelegatedDate { get; set; }
        public DateTimeOffset? RevisedDate { get; set; }
        public string DelegatedUser { get; set; }
        public string log { get; set; }
        public bool IsPublished { get; set; }

	   

    }


}
