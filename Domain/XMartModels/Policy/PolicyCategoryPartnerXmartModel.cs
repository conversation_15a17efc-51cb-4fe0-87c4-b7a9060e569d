﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Policy
{
    public class PolicyCategoryPartnerXmartModel : IMapTo<PolicyCategoryPartnerDraft>
    {
        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(POLICY_VID)},{nameof(PARTNER_CATEGORY_ID)},{nameof(DETAILS)}";
        public int POLICY_ID { get; set; }
        public int POLICY_VID { get; set; }
        public int PARTNER_CATEGORY_ID { get; set; }
        public string DETAILS { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyCategoryPartnerXmartModel, PolicyCategoryPartnerDraft>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.DETAILS));
        }

    }

    public class PostPolicyCategoryPartnerXmartModel : IMapTo<PolicyCategoryPartnerDraft>
    {
        public int PolicyId { get; set; }
        public int RevisionId { get; set; }
        public int CategoryID { get; set; }
        public string Details { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyCategoryPartnerDraft, PostPolicyCategoryPartnerXmartModel>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.PolicyVId))
                .ForMember(dest => dest.CategoryID, opt => opt.MapFrom(src => src.PartnerCategoryId))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details));
        }

    }
}
