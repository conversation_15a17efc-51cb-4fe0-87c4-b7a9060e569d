﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Policy
{
    public class PolicyCountryXmartModel : IMapFrom<PolicyCountryDraft>
    {
        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(POLICY_VID)},{nameof(COUNTRY_CODE)}";
        public int POLICY_ID { get; set; }
        public int POLICY_VID { get; set; }
        public string COUNTRY_CODE { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyCountryXmartModel, PolicyCountryDraft>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.PolicyVId, opt => opt.MapFrom(src => src.POLICY_VID))
                .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.COUNTRY_CODE));

        }
    }

    public class PostPolicyCountryXmartModel : IMapFrom<PolicyCountryDraft>
    {
        public int policyId { get; set; }
        public int PolicyVerisionId { get; set; }
        public string CountryCode { get; set; }
      
        public void Mapping(Profile profile)
        {
            profile.CreateMap<PolicyCountryDraft, PostPolicyCountryXmartModel>()
                .ForMember(dest => dest.policyId, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.PolicyVerisionId, opt => opt.MapFrom(src => src.PolicyVId))
                .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.CountryCode));
        }
    }
}
