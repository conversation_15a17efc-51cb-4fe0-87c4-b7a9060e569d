﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.RefModel
{
    public class ElenaLinkXmartModel : IMapTo<ElenaURLMapping>
    {
        public const string ElenaLinkTableName = "REF_ELENA_LINKS";

        public const string SelectedColumns = $"?$select={nameof(ELENA_ID)},{nameof(Title)},{nameof(URL)}";
        public int ELENA_ID { get; set; }
        public string Title { get; set; }
        public string URL { get; set; }
        public bool _Delete { get; set; } = false;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ElenaLinkXmartModel, ElenaURLMapping>()
             .ForMember(dest => dest.ElenaId, opt => opt.MapFrom(src => src.ELENA_ID))
             .ForMember(dest => dest.URLTitle, opt => opt.MapFrom(src => src.Title)).ReverseMap();
        }
    }
}
