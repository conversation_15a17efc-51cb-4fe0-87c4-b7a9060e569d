﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.RefModel
{
    public class SDGXmartModel : IMapTo<Sdg>
    {
        public const string SDGTableName = "REF_SDGS";

        public const string SelectedColumns = $"?$select={nameof(LEGACY_ID)},{nameof(NAME)},{nameof(PARENT_ID)}";
        public int LEGACY_ID { get; set; }
        public string NAME { get; set; }
        public int? PARENT_ID { get; set; }
        public bool _Delete { get; set; } = false;
        public void Mapping(Profile profile)
        {
            profile.CreateMap<SDGXmartModel, Sdg>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
                    .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.PARENT_ID))
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();
        }
    }
}
