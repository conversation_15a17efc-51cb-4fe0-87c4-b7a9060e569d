﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.RefModel
{
    public class TopicParentXmartModel : IMapTo<TopicParent>
    {
        public const string TopicParentTableName = "REF_TOPIC_PARENTS";

        public const string SelectedColumns = $"?$select={nameof(PARENT_ID)},{nameof(TOPIC_ID)}";
        public int PARENT_ID  { get; set; }
        public int TOPIC_ID { get; set; }
        public bool _Delete { get; set; } = false;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<TopicParentXmartModel, TopicParent>()
                .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TOPIC_ID))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.PARENT_ID)).ReverseMap();
        }
    }
}
