﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.RefModel
{
    public class ElenaXmartModel : IMapTo<ElenaLink>
    {
        public const string ElenaTableName = "REF_ELENA";

        public const string SelectedColumns = $"?$select={nameof(Id)},{nameof(TITLE)}";
        public string Id { get; set; }
        public string TITLE { get; set; }
        public bool _Delete { get; set; } = false;  

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ElenaXmartModel, ElenaLink>()
            .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.TITLE)).ReverseMap();
        }
    }
}
