﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.RefModel
{
    public class ReferenceModelXmartModel : IMapTo<PolicyType> 
    {
        public const string CoverageTypeTableName = "REF_COVERAGE_TYPES";
        public const string ActionStatusTableName = "REF_ACTION_STATUSES";
        public const string RefAreaTableName = "REF_AREAS";
        public const string SocialDeterminantableName = "REF_SOCIAL_DETERMINANT";
        
        public const string CoordinationTableName = "REF_COORDINATIONS";
        public const string DeliveryTableName = "REF_DELIVERIES";
        public const string Icn2CategoryName = "REF_ICN2_CATEGORIES";
        public const string LanguageTableName = "REF_LANGUAGES";
        public const string MechanismTypeTableName = "REF_MECHANISM_TYPES";
        public const string MicronutrientTableName = "REF_MICRONUTRIENTS";
        public const string MonitoringTableName = "REF_MONITORINGS";
        public const string PartnerCategoryTableName = "REF_PARTNER_CATEGORIES";
        public const string PolicyTypeTableName = "REF_POLICY_TYPES";
        public const string ProblemTypeTableName = "REF_PROBLEM_TYPES";
        public const string ProgramTypeTableName = "REF_PROGRAMME_TYPES";
        public const string TargetGroupTableName = "REF_TARGET_GROUPS";



        public static string SelectedColumns = $"?$select={nameof(LEGACY_ID)},{nameof(NAME)}";
        public static string SelectedColumnsNameOnly = $"?$select={nameof(NAME)}";
        public int LEGACY_ID { get; set; }
        public string NAME { get; set; }
        public bool _Delete { get; set; } = false ;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ReferenceModelXmartModel, SocialDeterminant>()
       .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Area>()
         .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, ActionStatus>()
          .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, TopicTag>()
           .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, CoverageType>()
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, ProgramType>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Micronutrient>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, MechanismType>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Language>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Icn2Category>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
             .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Delivery>()
              .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
              .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Coordination>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, Monitoring>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, PartnerCategory>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, PolicyType>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, FundingResourcePartnerCategory>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();
            

            profile.CreateMap<ReferenceModelXmartModel, TargetGroup>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();

            profile.CreateMap<ReferenceModelXmartModel, ProblemType>()
              .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
              .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();
        }
    }
}
