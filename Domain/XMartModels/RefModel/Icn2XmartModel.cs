﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.RefModel
{
    public class Icn2XmartModel : IMapTo<Icn2>
    {
        public const string ICN2TableName = "REF_ICN2S";

        public const string SelectedColumns = $"?$select={nameof(LEGACY_ID)},{nameof(NAME)},{nameof(ICN2_CATEGORY_ID)}";
        public int LEGACY_ID { get; set; }
        public string NAME { get; set; }
        public int ICN2_CATEGORY_ID { get; set; }
        public bool _Delete { get; set; } = false;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<Icn2XmartModel, Icn2>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
             .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.ICN2_CATEGORY_ID))
             .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap();
        }
    }
}
