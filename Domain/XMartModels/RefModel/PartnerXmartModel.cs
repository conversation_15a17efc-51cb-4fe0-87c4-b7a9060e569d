﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.RefModel
{
    public class PartnerXmartModel : IMapTo<Partner>
    {
        public const string PartnerTableName = "REF_PARTNERS";
        public const string SelectedColumns = $"?$select={nameof(LEGACY_ID)},{nameof(NAME)},{nameof(PARTNER_CATEGORY_ID)}";
        public int LEGACY_ID { get; set; }
        public string NAME { get; set; }
        public int? PARTNER_CATEGORY_ID { get; set; }
        public bool _Delete { get; set; } = false;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<PartnerXmartModel, Partner>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME))
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID)).ReverseMap();

            profile.CreateMap<PartnerXmartModel, FundingResourcePartner>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME))
               .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID)).ReverseMap();
        }
    }
}
