﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Domain.XMartModels.RefModel
{
    public class TopicXmartModel : IMapTo<Topic>
    {
        public const string TopicTableName = "REF_TOPICS";

        public const string SelectedColumns = $"?$select={nameof(LEGACY_ID)},{nameof(PARENT_ID)},{nameof(NAME)},{nameof(ALLOWED_IN_MECHANISM)},{nameof(ALLOWED_IN_ACTION)},{nameof(ALLOWED_IN_POLICY)}";
        public int LEGACY_ID { get; set; }
        public int? PARENT_ID { get; set; }
        public string NAME { get; set; }
        public bool? ALLOWED_IN_MECHANISM { get; set; }
        public bool? ALLOWED_IN_ACTION { get; set; }
        public bool? ALLOWED_IN_POLICY { get; set; }
        public bool _Delete { get; set; } = false;

        public void Mapping(Profile profile)
        {
            profile.CreateMap<TopicXmartModel, Topic>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.LEGACY_ID))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.PARENT_ID))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NAME)).ReverseMap().ReverseMap();
        }
    }
}
