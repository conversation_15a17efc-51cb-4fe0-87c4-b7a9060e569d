﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace Domain.XMartModels.Programm
{
    public class ProgramCountryXmartModel : IMapTo<ProgrammeCountryMapRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(COUNTRY_CODE)},{nameof(PROGRAMME_VID)},{nameof(PROGRAMME_ID)}";
        public string COUNTRY_CODE { get; set; }
        public int PROGRAMME_VID { get; set; }
        public int PROGRAMME_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramCountryXmartModel, ProgrammeCountryMapRevision>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.COUNTRY_CODE));
        }
    }
    public class PostProgramCountryXmartModel : IMapTo<ProgrammeCountryMapRevision>
    {
        public string CountryISOCode { get; set; }
        public int revisionId { get; set; }
        public int programId { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgrammeCountryMapRevision, PostProgramCountryXmartModel>()
                .ForMember(dest => dest.CountryISOCode, opt => opt.MapFrom(src => src.CountryCode))
                .ForMember(dest => dest.revisionId, opt => opt.MapFrom(src => src.ProgramVId))
                .ForMember(dest => dest.programId, opt => opt.MapFrom(src => src.ProgramId));
        }
    }
}
