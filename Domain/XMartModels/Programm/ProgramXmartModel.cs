﻿using AngleSharp.Dom.Events;
using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Policy;
using Gina2.DbModels;
using Gina2.DbModels.PolicyDrafts;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;

namespace Domain.XMartModels.Programm
{
    public class ProgramXmartModel : IMapTo<ProgramRevision>
    {
        public const string TableName = "IMPORT_ACTIONS";
        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(PROGRAMME_VID)},{nameof(PROGRAMME_TITLE)},{nameof(PROGRAMME_TITLE_ENGLISH_TRANSLATED)},{nameof(PROGRAMME_TYPE_ID)},{nameof(PROGRAMME_TYPE_OTHER)},{nameof(LOCATION)},{nameof(LANGUAGE_ID)},{nameof(BRIEF_DESCRIPTION)},{nameof(NEW_POLICY)},{nameof(COST)},{nameof(LINKS)},{nameof(REFERENCE)}";
        public int ID { get; set; }
        public int PROGRAMME_VID { get; set; }
        public string PROGRAMME_TITLE { get; set; }
        public string PROGRAMME_TITLE_ENGLISH_TRANSLATED { get; set; }
        public int? PROGRAMME_TYPE_ID { get; set; }
        public string PROGRAMME_TYPE_OTHER { get; set; }
        public string LOCATION { get; set; }
        public int? LANGUAGE_ID { get; set; }
        public string BRIEF_DESCRIPTION { get; set; }
        public string NEW_POLICY { get; set; }
        public string COST { get; set; }
        public string LINKS { get; set; }
        public string REFERENCE { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramXmartModel, ProgramRevision>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.PROGRAMME_TITLE))
                .ForMember(dest => dest.EnglishTranslatedTitle, opt => opt.MapFrom(src => src.PROGRAMME_TITLE_ENGLISH_TRANSLATED))
                .ForMember(dest => dest.ProgramTypeId, opt => opt.MapFrom(src => src.PROGRAMME_TYPE_ID))
                .ForMember(dest => dest.TypeOther, opt => opt.MapFrom(src => src.PROGRAMME_TYPE_OTHER))
                .ForMember(dest => dest.Location, opt => opt.MapFrom(src => src.LOCATION))
                .ForMember(dest => dest.LanguageId, opt => opt.MapFrom(src => src.LANGUAGE_ID))
                .ForMember(dest => dest.BriefDescription, opt => opt.MapFrom(src => src.BRIEF_DESCRIPTION))
                .ForMember(dest => dest.NewPolicy, opt => opt.MapFrom(src => src.NEW_POLICY))
                .ForMember(dest => dest.Cost, opt => opt.MapFrom(src => src.COST))
                .ForMember(dest => dest.Links, opt => opt.MapFrom(src => src.LINKS))
                .ForMember(dest => dest.References, opt => opt.MapFrom(src => src.REFERENCE));
        }
    }

    public class PostProgramXmartModel
    {
        public int ProgramId { get; set; }
        public int revisionId { get; set; }
        public string title { get; set; }
        public string EnglishTitle { get; set; }
        public int? ProgramTypeId { get; set; }
        public string ProgramOtherType { get; set; }
        public string Locations { get; set; }
        public int? LanguageId { get; set; }
        public string BriefDescription { get; set; }
        public string newPolicy { get; set; }
        public string cost { get; set; }
        public string LINKS { get; set; }
        public string Reference { get; set; }
        public string FromState { get; set; }
        public string ToState { get; set; }
        public string UserName { get; set; }
        public DateTimeOffset? OperationTime { get; set; }
        public DateTimeOffset? createdDateTime { get; set; }
        public DateTimeOffset? DelegatedDate { get; set; }
        public string DelegatedUser { get; set; }
        public string log { get; set; }
        public bool IsPublished { get; set; }
       
    }
}
