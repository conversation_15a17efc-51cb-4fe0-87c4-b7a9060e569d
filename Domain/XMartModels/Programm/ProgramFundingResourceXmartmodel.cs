﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Programm
{
    public class ProgramFundingResourceXmartmodel : IMapTo<ProgramFundingResourceRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(PARTNER_ID)},{nameof(PROGRAMME_VID)},{nameof(PROGRAMME_ID)},{nameof(CATEGORY_ID)}";
        public int PARTNER_ID { get; set; }
        public int PROGRAMME_VID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public int CATEGORY_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramFundingResourceXmartmodel, ProgramFundingResourceRevision>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PARTNER_ID))
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.CATEGORY_ID));
        }
    }
}
