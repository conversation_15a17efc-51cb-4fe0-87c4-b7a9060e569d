﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Programm
{
    public class ProgramPolicyXmartModel : IMapTo<ProgramPolicyRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(PROGRAMME_ID)},{nameof(PROGRAMME_VID)}";
        public int POLICY_ID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public int PROGRAMME_VID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramPolicyXmartModel, ProgramPolicyRevision>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID));
        }
    }

    public class PostProgramPolicyXmartModel : IMapTo<ProgramPolicyRevision>
    {
        public int PolicyId { get; set; }
        public int ProgramId { get; set; }
        public int RevisionId { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramPolicyRevision, PostProgramPolicyXmartModel>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.ProgramId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ProgramVId))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId));
        }
    }
}
