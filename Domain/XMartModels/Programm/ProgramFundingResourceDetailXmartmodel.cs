﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Programm
{
    public class ProgramFundingResourceDetailXmartmodel : IMapTo<ProgramFundingResourceDetailRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(DETAIL)},{nameof(PROGRAMME_VID)},{nameof(PARTNER_CATEGORY_ID)},{nameof(PROGRAMME_ID)}";
        public string DETAIL { get; set; }
        public int PROGRAMME_VID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public int PARTNER_CATEGORY_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramFundingResourceDetailXmartmodel, ProgramFundingResourceDetailRevision>()
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.DETAIL))
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID));
        }
    }
}
