﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Programm
{
    public class ProgrammeCategoriesofPartnersXmartModel : IMapTo<ProgramPartnerCategoryDetailsRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(PARTNER_CATEGORY_ID)},{nameof(PROGRAMME_ID)},{nameof(PROGRAMME_VID)},{nameof(DETAILS)}";
        public int PARTNER_CATEGORY_ID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public int PROGRAMME_VID { get; set; }
        public string DETAILS { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgrammeCategoriesofPartnersXmartModel, ProgramPartnerCategoryDetailsRevision>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.DETAILS));
        }
    }
    public class PostProgrammeCategoriesofPartnersXmartModel : IMapTo<ProgramPartnerCategoryDetailsRevision>
    {
        public int CategoryID { get; set; }
        public int ProgramId { get; set; }
        public int RevisionId { get; set; }
        public string Details { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ProgramPartnerCategoryDetailsRevision, PostProgrammeCategoriesofPartnersXmartModel>()
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.ProgramId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ProgramVId))
                .ForMember(dest => dest.CategoryID, opt => opt.MapFrom(src => src.PartnerCategoryId))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details));
        }
    }
   
}
