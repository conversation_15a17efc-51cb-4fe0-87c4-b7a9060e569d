﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismPartnerfromCategoriesXmartModel : IMapFrom<MechanismPartnerRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)},{nameof(PARTNER_ID)},{nameof(PARTNER_CATEGORY_ID)}";
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public int PARTNER_ID { get; set; }
        public int PARTNER_CATEGORY_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismPartnerfromCategoriesXmartModel, MechanismPartnerRevision>()
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID))
                .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PARTNER_ID));
        }
    }
    public class PostMechanismPartnerfromCategoriesXmartModel : IMapFrom<MechanismPartnerRevision>
    {
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public int PartnerId { get; set; }
        public int CategoryID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismPartnerRevision, PostMechanismPartnerfromCategoriesXmartModel>()
                .ForMember(dest => dest.CategoryID, opt => opt.MapFrom(src => src.PartnerCategoryId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId))
                .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PartnerId));
        }
    }
}

