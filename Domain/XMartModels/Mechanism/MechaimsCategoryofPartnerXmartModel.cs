﻿using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Policy;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechaimsCategoryofPartnerXmartModel : IMapTo<MechanismPartnerCategoryDetailsRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(PARTNER_CATEGORY_ID)},{nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)},{nameof(DETAILS)}";
        public int PARTNER_CATEGORY_ID { get; set; }
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public string DETAILS { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechaimsCategoryofPartnerXmartModel, MechanismPartnerCategoryDetailsRevision>()
                .ForMember(dest => dest.PartnerCategoryId, opt => opt.MapFrom(src => src.PARTNER_CATEGORY_ID))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.DETAILS));
        }
    }
    public class PostMechaimsCategoryofPartnerXmartModel : IMapTo<MechanismPartnerCategoryDetailsRevision>
    {
        public int CategoryID { get; set; }
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public string Details { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap< MechanismPartnerCategoryDetailsRevision, PostMechaimsCategoryofPartnerXmartModel>()
                .ForMember(dest => dest.CategoryID, opt => opt.MapFrom(src => src.PartnerCategoryId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details));
        }
    }
}
