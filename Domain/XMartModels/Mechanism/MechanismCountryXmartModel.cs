﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismCountryXmartModel : IMapTo<MechanismCountryMapRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(COUNTRY_CODE)},{nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)}";
        public string COUNTRY_CODE { get; set; }
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismCountryXmartModel, MechanismCountryMapRevision>()
                .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.COUNTRY_CODE))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID));
        }
    }
    public class PostMechanismCountryXmartModel : IMapTo<MechanismCountryMapRevision>
    {
        public string CountryISOCode { get; set; }
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismCountryMapRevision, PostMechanismCountryXmartModel>()
                .ForMember(dest => dest.CountryISOCode, opt => opt.MapFrom(src => src.CountryCode))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId));
        }
    }
}
