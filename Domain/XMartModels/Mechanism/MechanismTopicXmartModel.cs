﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismTopicXmartModel : IMapTo<MechanismTopicRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(TOPIC_ID)},{nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)}";
        public int TOPIC_ID { get; set; }
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismTopicXmartModel, MechanismTopicRevision>()
                .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TOPIC_ID))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID));
        }
    }
    public class PostMechanismTopicXmartModel : IMapTo<MechanismTopicRevision>
    {
        public int TopicId { get; set; }
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismTopicRevision, PostMechanismTopicXmartModel>()
                .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TopicId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId));
        }
    }
    
}
