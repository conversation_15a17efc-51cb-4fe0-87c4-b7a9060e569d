﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismPolicyXmartModel : IMapTo<MechanismPolicyRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)},{nameof(POLICY_ID)}";
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public int POLICY_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismPolicyXmartModel, MechanismPolicyRevision>()
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID));
        }
    }
    public class PostMechanismPolicyXmartModel : IMapTo<MechanismPolicyRevision>
    {
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public int PolicyID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismPolicyRevision, PostMechanismPolicyXmartModel>()
                .ForMember(dest => dest.PolicyID, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId));
        }
    }
}
