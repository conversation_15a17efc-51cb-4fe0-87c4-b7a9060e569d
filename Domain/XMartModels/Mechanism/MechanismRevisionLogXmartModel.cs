﻿using AngleSharp.Css.Values;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismRevisionLogXmartModel : IMapTo<MechanismLog>
    {
        public static string SelectedColumns = $"?$select={nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)},{nameof(FROM_STATE)},{nameof(TO_STATE)},{nameof(USERNAME)},{nameof(PUBLISH_DATE)},{nameof(OPERATION_TIME)},{nameof(PUBLISHED_BY)},{nameof(LOG)},{nameof(DELEGATED_DATE)},{nameof(DELEGATED_USER)},{nameof(Is_Published)}";
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public string FROM_STATE { get; set; }
        public string TO_STATE { get; set; }
        public string USERNAME { get; set; }
        public DateTimeOffset? PUBLISH_DATE { get; set; }
        public DateTimeOffset? OPERATION_TIME { get; set; }
        public string PUBLISHED_BY { get; set; }
        public string LOG { get; set; }
        public DateTimeOffset? DELEGATED_DATE { get; set; }
        public string DELEGATED_USER { get; set; }
        public bool Is_Published { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismRevisionLogXmartModel, MechanismLog>()
               .ForMember(dest => dest.RevisedDate, opt => opt.MapFrom(src => src.OPERATION_TIME))
               .ForMember(dest => dest.PublishDate, opt => opt.MapFrom(src => src.PUBLISH_DATE))
               .ForMember(dest => dest.ToState, opt => opt.MapFrom(src => src.TO_STATE))
               .ForMember(dest => dest.FromState, opt => opt.MapFrom(src => src.FROM_STATE))
               .ForMember(dest => dest.Log, opt => opt.MapFrom(src => src.LOG))
               .ForMember(dest => dest.IsPublished, opt => opt.MapFrom(src => src.Is_Published))
               .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.USERNAME))
               .ForMember(dest => dest.DelegatedDate, opt => opt.MapFrom(src => src.DELEGATED_DATE))
               .ForMember(dest => dest.DelegatedUserName, opt => opt.MapFrom(src => src.DELEGATED_USER))
               .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
               .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID));
        }
    }
}
