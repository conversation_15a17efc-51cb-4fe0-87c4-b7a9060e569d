﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismMonitoringXmartModel : IMapTo<MechanismMonitoringRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(MONITORING_ID)},{nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)}";
        public int MONITORING_ID { get; set; }
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismMonitoringXmartModel, MechanismMonitoringRevision>()
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID))
                .ForMember(dest => dest.MonitoringId, opt => opt.MapFrom(src => src.MONITORING_ID));
        }
    }

    public class PostMechanismMonitoringXmartModel : IMapTo<MechanismMonitoringRevision>
    {
        public int MonitoringId { get; set; }
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismMonitoringRevision, PostMechanismMonitoringXmartModel>()
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId))
                .ForMember(dest => dest.MonitoringId, opt => opt.MapFrom(src => src.MonitoringId));
        }
    }
}
