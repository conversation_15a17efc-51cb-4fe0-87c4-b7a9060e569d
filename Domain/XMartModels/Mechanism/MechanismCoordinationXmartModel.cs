﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismCoordinationXmartModel : IMapTo<MechanismCoordinationRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(MECHANISM_ID)},{nameof(MECHANISM_VID)},{nameof(COORDINATION_ID)}";
        public int MECHANISM_ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public int COORDINATION_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismCoordinationXmartModel, MechanismCoordinationRevision>()
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MECHANISM_ID))
                .ForMember(dest => dest.MechanismVId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.CoordinationId, opt => opt.MapFrom(src => src.COORDINATION_ID));
        }
    }
    public class PostMechanismCoordinationXmartModel : IMapTo<MechanismCoordinationRevision>
    {
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public int CoordinationId { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismCoordinationRevision, PostMechanismCoordinationXmartModel>()
                .ForMember(dest => dest.MechanismId, opt => opt.MapFrom(src => src.MechanismId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.MechanismVId))
                .ForMember(dest => dest.CoordinationId, opt => opt.MapFrom(src => src.CoordinationId));
        }
    }
}
