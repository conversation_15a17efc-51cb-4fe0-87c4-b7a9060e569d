﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.MechanismRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Mechanism
{
    public class MechanismXmartModel : IMapTo<MechanismRevision>
    {
        public const string TableName = "IMPORT_MECHANISMS";
        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(MECHANISM_VID)},{nameof(MECHANISM_TITLE)},{nameof(MECHANISM_TITLE_ENGLISH_TRANSLATED)},{nameof(MANDATE)},{nameof(MECHANISM_TYPE_OTHER)},{nameof(LEAD_GOVERNMENT_AGENCY)},{nameof(START_MONTH)},{nameof(START_YEAR)},{nameof(OTHER_TOPICS)},{nameof(LESSONS_LEARNT)},{nameof(URL)},{nameof(NOTES)},{nameof(MECHANISM_TYPE_ID)},{nameof(REFERENCE)}";
        public int ID { get; set; }
        public int MECHANISM_VID { get; set; }
        public string MECHANISM_TITLE { get; set; }
        public string MECHANISM_TITLE_ENGLISH_TRANSLATED { get; set; }
        public string MANDATE { get; set; }
        public string MECHANISM_TYPE_OTHER { get; set; }
        public string LEAD_GOVERNMENT_AGENCY { get; set; }
        public int? START_MONTH { get; set; }
        public int? START_YEAR { get; set; }
        public string OTHER_TOPICS { get; set; }
        public string LESSONS_LEARNT { get; set; }
        public string URL { get; set; }
        public string NOTES { get; set; }
        public int MECHANISM_TYPE_ID { get; set; }
        public string REFERENCE { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<MechanismXmartModel, MechanismRevision>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.MECHANISM_VID))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.MECHANISM_TITLE))
                .ForMember(dest => dest.Mandate, opt => opt.MapFrom(src => src.MANDATE))
                .ForMember(dest => dest.MechanismOtherType, opt => opt.MapFrom(src => src.MECHANISM_TYPE_OTHER))
                .ForMember(dest => dest.LeadGovernmentAgency, opt => opt.MapFrom(src => src.LEAD_GOVERNMENT_AGENCY))
                .ForMember(dest => dest.StartMonth, opt => opt.MapFrom(src => src.START_MONTH))
                .ForMember(dest => dest.StartYear, opt => opt.MapFrom(src => src.START_YEAR))
                .ForMember(dest => dest.OtherTopics, opt => opt.MapFrom(src => src.OTHER_TOPICS))
                .ForMember(dest => dest.LessonsLearnt, opt => opt.MapFrom(src => src.LESSONS_LEARNT))
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.URL))
                .ForMember(dest => dest.MechanismTypeId, opt => opt.MapFrom(src => src.MECHANISM_TYPE_ID))
                .ForMember(dest => dest.References, opt => opt.MapFrom(src => src.REFERENCE))
                .ForMember(dest => dest.EnglishTitle, opt => opt.MapFrom(src => src.MECHANISM_TITLE_ENGLISH_TRANSLATED));
        }
    }
    public class MechanismWriteXmartModel
    {
        public int MechanismId { get; set; }
        public int RevisionId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string Mandate { get; set; }
        public string MechanismOtherType { get; set; }
        public string LeadGovAgency { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public string OtherTopics { get; set; }
        public string LessonLearnt { get; set; }
        public string URL { get; set; }
        public string notes { get; set; }
        public int TypeId { get; set; }
        public string Reference { get; set; }
        public string FromState { get; set; }
        public string ToState { get; set; }
        public string name { get; set; }
        public DateTimeOffset? PublishDate { get; set; }
        public DateTimeOffset? OperationTime { get; set; }
        public string PublishedBy { get; set; }
        public string log { get; set; }
        public DateTimeOffset? DelegatedDate { get; set; }
        public string DelegatedUser { get; set; }
        public bool IsPublished { get; set; }
    }
}
