﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionDeliveryXmartModel : IMapTo<ActionDeliveryRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(DELIVERY_ID)}";
        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public int DELIVERY_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionDeliveryXmartModel, ActionDeliveryRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.DeliveryId, opt => opt.MapFrom(src => src.DELIVERY_ID));
        }
    }

    public class PostActionDeliveryXmartModel : IMapTo<ActionDeliveryRevision>
    {
        public int ActionId { get; set; }
        public int RevisionId { get; set; }
        public int DeliveryId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap< ActionDeliveryRevision, PostActionDeliveryXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.DeliveryId, opt => opt.MapFrom(src => src.DeliveryId));
        }
    }
}
