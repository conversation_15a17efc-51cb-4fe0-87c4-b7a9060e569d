﻿using AutoMapper;
using AutoMapper.Execution;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionProblemXmartModel : IMapTo<ActionProblemRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(PROBLEM_NUMBER)},{nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(PROBLEM_TYPE_ID)}";
        public int PROBLEM_NUMBER { get; set; }
        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public int PROBLEM_TYPE_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionProblemXmartModel, ActionProblemRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ProblemNumber, opt => opt.MapFrom(src => src.PROBLEM_NUMBER))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.ProblemTypeId, opt => opt.MapFrom(src => src.PROBLEM_TYPE_ID));
        }
    }

    public class PostActionProblemXmartModel : IMapTo<ActionProblemRevision>
    {
        public int PorblemNo { get; set; }
        public int ActionId { get; set; }
        public int RevisionId { get; set; }
        public int ProblemTypeId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionProblemRevision, PostActionProblemXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.PorblemNo, opt => opt.MapFrom(src => src.ProblemNumber))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.ProblemTypeId, opt => opt.MapFrom(src => src.ProblemTypeId));
        }
      
    }
}
