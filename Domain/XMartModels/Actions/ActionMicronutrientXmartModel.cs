﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionMicronutrientXmartModel : IMapTo<ActionMicronutrientRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(MICRONUTRIENT_ID)}";
        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public int MICRONUTRIENT_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionMicronutrientXmartModel, ActionMicronutrientRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.MicronutrientId, opt => opt.MapFrom(src => src.MICRONUTRIENT_ID));
        }
    }
    public class PostActionMicronutrientXmartModel : IMapTo<ActionMicronutrientRevision>
    {
        public int ActionId { get; set; }
        public int RevisionId { get; set; }
        public int Mirconutrientid { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionMicronutrientRevision, PostActionMicronutrientXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.Mirconutrientid, opt => opt.MapFrom(src => src.MicronutrientId));
        }
    }
}
