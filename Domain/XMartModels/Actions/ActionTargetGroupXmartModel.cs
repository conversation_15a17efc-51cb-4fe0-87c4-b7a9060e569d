﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionTargetGroupXmartModel : IMapTo<ActionTargetGroupRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(TARGET_GROUP_ID)},{nameof(ORDER_NO)}";
        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public int TARGET_GROUP_ID { get; set; }
        public int ORDER_NO { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionTargetGroupXmartModel, ActionTargetGroupRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.OrderNo, opt => opt.MapFrom(src => src.ORDER_NO))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.TargetGroupId, opt => opt.MapFrom(src => src.TARGET_GROUP_ID));
        }
    }
    public class PostActionTargetGroupXmartModel : IMapTo<ActionTargetGroupRevision>
    {
        public int ActionId { get; set; }
        public int Revisionid { get; set; }
        public int TargetGroupId { get; set; }
        public int OrderNo { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionTargetGroupRevision, PostActionTargetGroupXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.OrderNo, opt => opt.MapFrom(src => src.OrderNo))
                .ForMember(dest => dest.Revisionid, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.TargetGroupId, opt => opt.MapFrom(src => src.TargetGroupId));
        }
         
    }
}
