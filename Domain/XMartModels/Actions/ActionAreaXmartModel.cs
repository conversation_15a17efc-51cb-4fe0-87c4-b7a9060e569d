﻿using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Mechanism;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionAreaXmartModel : IMapTo<ActionAreaRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(AREA_ID)}";

        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public string AREA_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionAreaXmartModel, ActionAreaRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AREA_ID));
        }
    }
    public class PostActionAreaXmartModel : IMapTo<ActionAreaRevision>
    {

        public int ActionId { get; set; }
        public int RevisionId { get; set; }
        public string AreaId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionAreaRevision, PostActionAreaXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.AreaId, opt => opt.MapFrom(src => src.AreaId));
        }
    }
}
