﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionElenaXmartModel : IMapTo<ActionElenaRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(ELENA_ID)}";

        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public string ELENA_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionElenaXmartModel, ActionElenaRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.ElenaLinkId, opt => opt.MapFrom(src => src.ELENA_ID));
        }
    }
    public class PostActionElenaXmartModel : IMapTo<ActionElenaRevision>
    {
        public int ActionId { get; set; }
        public int VersionId { get; set; }
        public string ElenaLinkId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionElenaRevision, PostActionElenaXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.ElenaLinkId, opt => opt.MapFrom(src => src.ElenaLinkId));
        }
    }
}
