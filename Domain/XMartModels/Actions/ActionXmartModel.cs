﻿using AngleSharp.Dom.Events;
using AngleSharp.Dom;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Mappings;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using AutoMapper;
using System.Data;

namespace Domain.XMartModels.Actions
{
    public class ActionXmartModel : IMapTo<ActionRevision>,IMapTo<ActionProgramRevisionMap>
    {
        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(ACTION_VID)},{nameof(PROGRAMME_ID)},{nameof(PROGRAMME_VID)},{nameof(NEW_TOPIC)},{nameof(START_MONTH)},{nameof(START_YEAR)},{nameof(END_MONTH)},{nameof(END_YEAR)},{nameof(MICRONUTRIENT_COMPOUND)},{nameof(AGE_GROUP)},{nameof(PLACE)},{nameof(OTHER_DELIVERY)},{nameof(IMPLEMENTATION_DETAILS)},{nameof(IMPACT_INDICATORS)},{nameof(ME_SYSTEM)},{nameof(TARGET_POPULATION)},{nameof(COVERAGE_PERCENT)},{nameof(POST_INTERVENTION)},{nameof(BASELINE)},{nameof(SOCIAL_OTHER)},{nameof(ELENA_LINK)},{nameof(SOLUTION_0)},{nameof(SOLUTION_1)},{nameof(SOLUTION_2)},{nameof(SOLUTION_3)},{nameof(SOLUTION_4)},{nameof(SOLUTION_5)},{nameof(SOLUTION_6)},{nameof(SOLUTION_7)},{nameof(SOLUTION_8)},{nameof(SOLUTION_9)},{nameof(OTHER_PROBLEMS)},{nameof(OTHER_LESSONS)},{nameof(PERSONAL_STORY)},{nameof(TOPIC_ID)},{nameof(STATUS_ID)},{nameof(COVERAGE_TYPE_ID)}";

        public int ID { get; set; }
        public int ACTION_VID { get; set; }
        public int PROGRAMME_ID { get; set; }
        public int PROGRAMME_VID { get; set; }
        public string NEW_TOPIC { get; set; }
        public int? START_MONTH { get; set; }
        public int? START_YEAR { get; set; }
        public int? END_MONTH { get; set; }
        public int? END_YEAR { get; set; }
        public string MICRONUTRIENT_COMPOUND { get; set; }
        public string AGE_GROUP { get; set; }
        public string PLACE { get; set; }
        public string OTHER_DELIVERY { get; set; }
        public string IMPLEMENTATION_DETAILS { get; set; }
        public string IMPACT_INDICATORS { get; set; }
        public string ME_SYSTEM { get; set; }
        public string TARGET_POPULATION { get; set; }
        public string COVERAGE_PERCENT { get; set; }
        public string POST_INTERVENTION { get; set; }
        public string BASELINE { get; set; }
        public string SOCIAL_OTHER { get; set; }
        public string ELENA_LINK { get; set; }
        public string SOLUTION_0 { get; set; }
        public string SOLUTION_1 { get; set; }
        public string SOLUTION_2 { get; set; }
        public string SOLUTION_3 { get; set; }
        public string SOLUTION_4 { get; set; }
        public string SOLUTION_5 { get; set; }
        public string SOLUTION_6 { get; set; }
        public string SOLUTION_7 { get; set; }
        public string SOLUTION_8 { get; set; }
        public string SOLUTION_9 { get; set; }
        public string OTHER_PROBLEMS { get; set; }
        public string OTHER_LESSONS { get; set; }
        public string PERSONAL_STORY { get; set; }
        public int? TOPIC_ID { get; set; }
        public string STATUS_ID { get; set; }
        public string COVERAGE_TYPE_ID { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionXmartModel, ActionProgramRevisionMap>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.ProgramId, opt => opt.MapFrom(src => src.PROGRAMME_ID))
                .ForMember(dest => dest.ProgramVId, opt => opt.MapFrom(src => src.PROGRAMME_VID))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => 0));

            profile.CreateMap<ActionXmartModel, ActionRevision>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.NewTopic, opt => opt.MapFrom(src => src.NEW_TOPIC))
                .ForMember(dest => dest.StartMonth, opt => opt.MapFrom(src => src.START_MONTH))
                .ForMember(dest => dest.StartYear, opt => opt.MapFrom(src => src.START_YEAR))
                .ForMember(dest => dest.EndMonth, opt => opt.MapFrom(src => src.END_MONTH))
                .ForMember(dest => dest.EndYear, opt => opt.MapFrom(src => src.END_YEAR))
                .ForMember(dest => dest.MicronutrientCompound, opt => opt.MapFrom(src => src.MICRONUTRIENT_COMPOUND))
                .ForMember(dest => dest.AgeGroup, opt => opt.MapFrom(src => src.AGE_GROUP))
                .ForMember(dest => dest.NewTopic, opt => opt.MapFrom(src => src.NEW_TOPIC))
                .ForMember(dest => dest.OtherDelivery, opt => opt.MapFrom(src => src.OTHER_DELIVERY))
                .ForMember(dest => dest.ImplementationDetails, opt => opt.MapFrom(src => src.IMPLEMENTATION_DETAILS))
                .ForMember(dest => dest.ImpactIndicators, opt => opt.MapFrom(src => src.IMPACT_INDICATORS))
                .ForMember(dest => dest.MeSystem, opt => opt.MapFrom(src => src.ME_SYSTEM))
                .ForMember(dest => dest.TargetPopulation, opt => opt.MapFrom(src => src.TARGET_POPULATION))
                .ForMember(dest => dest.CoveragePercent, opt => opt.MapFrom(src => src.COVERAGE_PERCENT))
                .ForMember(dest => dest.PostIntervention, opt => opt.MapFrom(src => src.POST_INTERVENTION))
                .ForMember(dest => dest.ElenaLink, opt => opt.MapFrom(src => src.ELENA_LINK))
                .ForMember(dest => dest.Solution0, opt => opt.MapFrom(src => src.SOLUTION_0))
                .ForMember(dest => dest.Solution1, opt => opt.MapFrom(src => src.SOLUTION_1))
                .ForMember(dest => dest.Solution2, opt => opt.MapFrom(src => src.SOLUTION_2))
                .ForMember(dest => dest.Solution3, opt => opt.MapFrom(src => src.SOLUTION_3))
                .ForMember(dest => dest.Solution4, opt => opt.MapFrom(src => src.SOLUTION_4))
                .ForMember(dest => dest.Solution5, opt => opt.MapFrom(src => src.SOLUTION_5))
                .ForMember(dest => dest.Solution6, opt => opt.MapFrom(src => src.SOLUTION_6))
                .ForMember(dest => dest.Solution7, opt => opt.MapFrom(src => src.SOLUTION_7))
                .ForMember(dest => dest.Solution8, opt => opt.MapFrom(src => src.SOLUTION_8))
                .ForMember(dest => dest.Solution9, opt => opt.MapFrom(src => src.SOLUTION_9))
                .ForMember(dest => dest.OtherProblems, opt => opt.MapFrom(src => src.OTHER_PROBLEMS))
                .ForMember(dest => dest.OtherLessons, opt => opt.MapFrom(src => src.OTHER_LESSONS))
                .ForMember(dest => dest.PersonalStory, opt => opt.MapFrom(src => src.PERSONAL_STORY))
                .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TOPIC_ID))
                .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => src.STATUS_ID))
                .ForMember(dest => dest.CoverageTypeId, opt => opt.MapFrom(src => src.COVERAGE_TYPE_ID));
        }
    }

    public class PostActionXmartModel : IMapTo<ActionRevision>
    {
        public int ActionId { get; set; }
        public int verisionid { get; set; }
        public int ProgramId { get; set; }
        public int ProgramRevisionId { get; set; }
        public string newTopic { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public int? EndMonth { get; set; }
        public int? EndYear { get; set; }
        public string MicronutrientCompound { get; set; }
        public string AgeGroup { get; set; }
        public string place { get; set; }
        public string OtherDelevery { get; set; }
        public string ImplementationDetail { get; set; }
        public string ImpactIndicator { get; set; }
        public string MeSystem { get; set; }
        public string TargetPapulation { get; set; }
        public string ConveragePercent { get; set; }
        public string PostIntervention { get; set; }
        public string baseline { get; set; }
        public string SocialOther { get; set; }
        public string solution0 { get; set; }
        public string solution1 { get; set; }
        public string solution2 { get; set; }
        public string solution3 { get; set; }
        public string solution4 { get; set; }
        public string solution5 { get; set; }
        public string solution6 { get; set; }
        public string solution7 { get; set; }
        public string solution8 { get; set; }
        public string solution9 { get; set; }
        public string otherProblem { get; set; }
        public string otherLesson { get; set; }
        public string personalStory { get; set; }
        public int? TopicId { get; set; }
        public string StatusId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionRevision, PostActionXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.verisionid, opt => opt.MapFrom(src => src.VersionId))
                .ForMember(dest => dest.newTopic, opt => opt.MapFrom(src => src.NewTopic))
                .ForMember(dest => dest.StartMonth, opt => opt.MapFrom(src => src.StartMonth))
                .ForMember(dest => dest.StartYear, opt => opt.MapFrom(src => src.StartYear))
                .ForMember(dest => dest.EndMonth, opt => opt.MapFrom(src => src.EndMonth))
                .ForMember(dest => dest.EndYear, opt => opt.MapFrom(src => src.EndYear))
                .ForMember(dest => dest.MicronutrientCompound, opt => opt.MapFrom(src => src.MicronutrientCompound))
                .ForMember(dest => dest.AgeGroup, opt => opt.MapFrom(src => src.AgeGroup))
                .ForMember(dest => dest.OtherDelevery, opt => opt.MapFrom(src => src.OtherDelivery))
                .ForMember(dest => dest.ImplementationDetail, opt => opt.MapFrom(src => src.ImplementationDetails))
                .ForMember(dest => dest.ImpactIndicator, opt => opt.MapFrom(src => src.ImpactIndicators))
                .ForMember(dest => dest.MeSystem, opt => opt.MapFrom(src => src.MeSystem))
                .ForMember(dest => dest.TargetPapulation, opt => opt.MapFrom(src => src.TargetPopulation))
                .ForMember(dest => dest.ConveragePercent, opt => opt.MapFrom(src => src.CoveragePercent))
                .ForMember(dest => dest.PostIntervention, opt => opt.MapFrom(src => src.PostIntervention))
                .ForMember(dest => dest.solution0, opt => opt.MapFrom(src => src.Solution0))
                .ForMember(dest => dest.solution1, opt => opt.MapFrom(src => src.Solution1))
                .ForMember(dest => dest.solution2, opt => opt.MapFrom(src => src.Solution2))
                .ForMember(dest => dest.solution3, opt => opt.MapFrom(src => src.Solution3))
                .ForMember(dest => dest.solution4, opt => opt.MapFrom(src => src.Solution4))
                .ForMember(dest => dest.solution5, opt => opt.MapFrom(src => src.Solution5))
                .ForMember(dest => dest.solution6, opt => opt.MapFrom(src => src.Solution6))
                .ForMember(dest => dest.solution7, opt => opt.MapFrom(src => src.Solution7))
                .ForMember(dest => dest.solution8, opt => opt.MapFrom(src => src.Solution8))
                .ForMember(dest => dest.solution9, opt => opt.MapFrom(src => src.Solution9))
                .ForMember(dest => dest.otherProblem, opt => opt.MapFrom(src => src.OtherProblems))
                .ForMember(dest => dest.otherLesson, opt => opt.MapFrom(src => src.OtherLessons))
                .ForMember(dest => dest.personalStory, opt => opt.MapFrom(src => src.PersonalStory))
                .ForMember(dest => dest.TopicId, opt => opt.MapFrom(src => src.TopicId))
                .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => src.StatusId));
        }
    }
}
