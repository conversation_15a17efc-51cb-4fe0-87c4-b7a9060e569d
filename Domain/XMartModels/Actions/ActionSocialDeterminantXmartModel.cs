﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Actions
{
    public class ActionSocialDeterminantXmartModel : IMapTo<ActionSocialDeterminantRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(ACTION_ID)},{nameof(ACTION_VID)},{nameof(NAME)},{nameof(OTHER_VALUE)}";
        public int ACTION_ID { get; set; }
        public int ACTION_VID { get; set; }
        public string NAME { get; set; }
        public string OTHER_VALUE { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionSocialDeterminantXmartModel, ActionSocialDeterminantRevision>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ACTION_ID))
                .ForMember(dest => dest.ActionVId, opt => opt.MapFrom(src => src.ACTION_VID))
                .ForMember(dest => dest.SocialDeterminant, opt => opt.MapFrom(src => src.NAME))
                .ForMember(dest => dest.OtherValue, opt => opt.MapFrom(src => src.OTHER_VALUE));
        }
    }
    public class PostActionSocialDeterminantXmartModel : IMapTo<ActionSocialDeterminantRevision>
    {
        public int ActionId { get; set; }
        public int VersionId { get; set; }
        public string Name { get; set; }
        public string OtherValue { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<ActionSocialDeterminantRevision, PostActionSocialDeterminantXmartModel>()
                .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ActionId))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.ActionVId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.SocialDeterminant))
                .ForMember(dest => dest.OtherValue, opt => opt.MapFrom(src => src.OtherValue));
        }
    }
}
