﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.CommitmentRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Commitment
{
    public class CommitmentPolicyXmartModel : IMapFrom<CommitmentPolicyRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(POLICY_ID)},{nameof(COMMITMENT_ID)},{nameof(COMMITMENT_VID)}";
        public int POLICY_ID { get; set; }
        public int COMMITMENT_ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentPolicyXmartModel, CommitmentPolicyRevision>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.POLICY_ID)).ReverseMap();
        }
    }

    public class CommitmentPolicyXmartReadModel : IMapFrom<CommitmentPolicyRevision>
    {
        public int CommitmentId { get; set; }
        public int VersionId { get; set; }
        public int PolicyId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentPolicyRevision, CommitmentPolicyXmartReadModel>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.CommitmentId))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.CommitmentVId))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId)).ReverseMap();
        }
    }
}
