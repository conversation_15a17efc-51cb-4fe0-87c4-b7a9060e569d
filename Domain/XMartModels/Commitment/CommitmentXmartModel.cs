﻿using AngleSharp.Dom.Events;
using AngleSharp.Dom;
using Gina2.DbModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using Domain.Mappings;
using Gina2.DbModels.CommitmentRevisions;
using AutoMapper;
using System.Security.Cryptography.Xml;

namespace Domain.XMartModels.Commitment
{
    public  class CommitmentXmartModel : IMapFrom<CommitmentRevision>
    {
        public const string TableName = "IMPORT_COMMITMENTS";

        public static string SelectedColumns = $"?$select={nameof(ID)},{nameof(COMMITMENT_VID)},{nameof(DESCRIPTION)},{nameof(COMMITMENT_TITLE)},{nameof(COMMITMENT_TITLE_ENGLISH_TRANSLATED)},{nameof(START_MONTH)},{nameof(START_YEAR)},{nameof(MINISTRY_DEPARTMENT)},{nameof(ENDORSED_BY)},{nameof(EVENT)},{nameof(PLANNED_PROGRESS_MONITORING)},{nameof(LINKS)},{nameof(NOTES)},{nameof(RESOURCE_ALLOCATION)}";
        public int ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public string COMMITMENT_TITLE { get; set; }
        public string COMMITMENT_TITLE_ENGLISH_TRANSLATED { get; set; }
        public string DESCRIPTION { get; set; }
        public int? START_MONTH { get; set; }
        public int? START_YEAR { get; set; }
        public string MINISTRY_DEPARTMENT { get; set; }
        public string ENDORSED_BY { get; set; }
        public string EVENT { get; set; }
        public string PLANNED_PROGRESS_MONITORING { get; set; }
        public string LINKS { get; set; }
        public string NOTES { get; set; }
        public string RESOURCE_ALLOCATION { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentXmartModel, CommitmentRevision>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.DESCRIPTION))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.COMMITMENT_TITLE))
                .ForMember(dest => dest.EnglishTitle, opt => opt.MapFrom(src => src.COMMITMENT_TITLE_ENGLISH_TRANSLATED))
                .ForMember(dest => dest.StartMonth, opt => opt.MapFrom(src => src.START_MONTH))
                .ForMember(dest => dest.StartYear, opt => opt.MapFrom(src => src.START_YEAR))
                .ForMember(dest => dest.MinistryDepartment, opt => opt.MapFrom(src => src.MINISTRY_DEPARTMENT))
                .ForMember(dest => dest.EndosedBy, opt => opt.MapFrom(src => src.ENDORSED_BY))
                .ForMember(dest => dest.Event, opt => opt.MapFrom(src => src.EVENT))
                .ForMember(dest => dest.PlannedProgressMonitoring, opt => opt.MapFrom(src => src.PLANNED_PROGRESS_MONITORING))
                .ForMember(dest => dest.Links, opt => opt.MapFrom(src => src.LINKS))
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.NOTES))
                .ForMember(dest => dest.ResourceAllocation, opt => opt.MapFrom(src => src.RESOURCE_ALLOCATION));
        }
    }

    public class CommitmentReadXmartModel 
    {
        public int CommitmentId { get; set; }
        public int RevisionId { get; set; }
        public string title { get; set; }
        public string EnglishTitle { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public string MinistryDepartment { get; set; }
        public string Description { get; set; }
        public string EndorsedBy { get; set; }
        public string Event { get; set; }
        public string PlanProgressMonitoring { get; set; }
        public string Links { get; set; }
        public string notes { get; set; }
        public string ResourceAllocation { get; set; }
        public string FromState { get; set; }
        public string ToState { get; set; }
        public string name { get; set; }
        public DateTimeOffset? OperationTime { get; set; }
        public DateTimeOffset? DelegatedDate { get; set; }
        public string DelegatedUser { get; set; }
        public string log { get; set; }
        public bool IsPublished { get; set; }
        public bool _Delete { get; set; } = true;

    }

}
