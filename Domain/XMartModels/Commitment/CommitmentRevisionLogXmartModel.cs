﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.CommitmentRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Commitment
{
    public class CommitmentRevisionLogXmartModel : IMapFrom<CommitmentLog>
    {
        public static string SelectedColumns = $"?$select={nameof(COMMITMENT_ID)},{nameof(COMMITMENT_VID)},{nameof(FROM_STATE)},{nameof(TO_STATE)},{nameof(USERNAME)},{nameof(OPERATION_TIME)},{nameof(DELEGATED_DATE)},{nameof(DELEGATED_USER)},{nameof(LOG)},{nameof(Is_Published)}";
        public int COMMITMENT_ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public string FROM_STATE { get; set; }
        public string TO_STATE { get; set; }
        public string USERNAME { get; set; }
        public DateTimeOffset? OPERATION_TIME { get; set; }
        public DateTimeOffset? DELEGATED_DATE { get; set; }
        public string DELEGATED_USER { get; set; }
        public string LOG { get; set; }
        public bool Is_Published { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentRevisionLogXmartModel, CommitmentLog>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.ToState, opt => opt.MapFrom(src => src.TO_STATE))
                .ForMember(dest => dest.FromState, opt => opt.MapFrom(src => src.FROM_STATE))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.USERNAME))
                .ForMember(dest => dest.RevisedDate, opt => opt.MapFrom(src => src.OPERATION_TIME))
                .ForMember(dest => dest.DelegatedDate, opt => opt.MapFrom(src => src.DELEGATED_DATE))
                .ForMember(dest => dest.DelegatedUserName, opt => opt.MapFrom(src => src.DELEGATED_USER))
                .ForMember(dest => dest.IsPublished, opt => opt.MapFrom(src => src.Is_Published))
                .ForMember(dest => dest.Log, opt => opt.MapFrom(src => src.LOG));
        }

    }
}
