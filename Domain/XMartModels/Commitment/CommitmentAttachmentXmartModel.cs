﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.CommitmentRevisions;
using System.Data;

namespace Domain.XMartModels.Commitment
{
    public class CommitmentAttachmentXmartModel : IMapTo<CommitmentAttachmentRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(URL)},{nameof(COMMITMENT_VID)},{nameof(COMMITMENT_ID)},{nameof(DISPLAY_NAME)},{nameof(FILE_TYPE)}";
        public string URL { get; set; }
        public int COMMITMENT_ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public string DISPLAY_NAME { get; set; }
        public string FILE_TYPE { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentAttachmentXmartModel, CommitmentAttachmentRevision>()
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.URL))
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.FileDisplayName, opt => opt.MapFrom(src => src.DISPLAY_NAME))
                .ForMember(dest => dest.FileType, opt => opt.MapFrom(src => src.FILE_TYPE)).ReverseMap();
        }
    }

    public class CommitmentAttachmentXmartReadModel : IMapTo<CommitmentAttachmentRevision>
    {
        public string URL { get; set; }
        public int CommitmentId { get; set; }
        public int VersionId { get; set; }
        public string DisplayName { get; set; }
        public string FileType { get; set; }

        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentAttachmentRevision, CommitmentAttachmentXmartReadModel>()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(src => src.Url))
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.CommitmentId))
                .ForMember(dest => dest.VersionId, opt => opt.MapFrom(src => src.CommitmentVId))
                .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.FileDisplayName))
                .ForMember(dest => dest.FileType, opt => opt.MapFrom(src => src.FileType)).ReverseMap();
        }
    }
}
