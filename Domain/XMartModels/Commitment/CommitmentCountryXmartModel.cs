﻿using AngleSharp.Dom;
using AutoMapper;
using Domain.Mappings;
using Domain.XMartModels.Policy;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.PolicyDrafts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Commitment
{
    public class CommitmentCountryXmartModel : IMapFrom<CommitmentCountryMapRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(COUNTRY_CODE)},{nameof(COMMITMENT_ID)},{nameof(COMMITMENT_VID)}";
        public string COUNTRY_CODE { get; set; }
        public int COMMITMENT_ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentCountryXmartModel, CommitmentCountryMapRevision>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.COUNTRY_CODE)).ReverseMap();
        }
    }

    public class CommitmentCountryXmartReadModel : IMapFrom<CommitmentCountryMapRevision>
    {
        public string CountryId { get; set; }
        public int CommitmentId { get; set; }
        public int RevisionId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentCountryMapRevision, CommitmentCountryXmartReadModel>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.CommitmentId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.CommitmentVId))
                .ForMember(dest => dest.CountryId, opt => opt.MapFrom(src => src.CountryCode)).ReverseMap();
        }
    }
}
