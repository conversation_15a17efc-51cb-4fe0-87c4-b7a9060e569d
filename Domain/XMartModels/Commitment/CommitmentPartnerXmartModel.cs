﻿using AutoMapper;
using Domain.Mappings;
using Gina2.DbModels.CommitmentRevisions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.XMartModels.Commitment
{
    public class CommitmentPartnerXmartModel : IMapFrom<CommitmentPartnerRevision>
    {
        public static string SelectedColumns = $"?$select={nameof(COMMITMENT_ID)},{nameof(COMMITMENT_VID)},{nameof(PARTNER_ID)}";
        public int COMMITMENT_ID { get; set; }
        public int COMMITMENT_VID { get; set; }
        public int PARTNER_ID { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentPartnerXmartModel, CommitmentPartnerRevision>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.COMMITMENT_ID))
                .ForMember(dest => dest.CommitmentVId, opt => opt.MapFrom(src => src.COMMITMENT_VID))
                .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PARTNER_ID));
        }
    }

    public class CommitmentPartnerXmartReadModel : IMapFrom<CommitmentPartnerRevision>
    {
        public int CommitmentId { get; set; }
        public int RevisionId { get; set; }
        public int SectorId { get; set; }
        public void Mapping(Profile profile)
        {
            profile.CreateMap<CommitmentPartnerRevision, CommitmentPartnerXmartReadModel>()
                .ForMember(dest => dest.CommitmentId, opt => opt.MapFrom(src => src.CommitmentId))
                .ForMember(dest => dest.RevisionId, opt => opt.MapFrom(src => src.CommitmentVId))
                .ForMember(dest => dest.SectorId, opt => opt.MapFrom(src => src.PartnerId)).ReverseMap();
        }
    }
}
