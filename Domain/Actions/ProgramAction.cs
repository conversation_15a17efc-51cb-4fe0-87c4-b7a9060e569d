﻿using Domain.Countries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Actions
{
    public sealed class ProgramAction
    {
        public int Key { get; set; }
        public int ProgramId { get; set; }
        public string Title { get; set; }
        public string EnglishTranslatedTitle { get; set; }
        public int TypeId { get; set; }
        public string OtherType { get; set; }
        public int LanguageId { get; set; }
        public string Location { get; set; }
        public string BriefDescription { get; set; }
        public string References { get; set; }
        public string NewPolicy { get; set; }
        public string Cost { get; set; }
        public string Links { get; set; }
        public int? StartYear { get; set; }
        public int? EndYear { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public List<Country> Countries { get; set; }

        public string Region { get; set; }
    }
}
