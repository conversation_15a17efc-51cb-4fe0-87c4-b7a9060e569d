﻿

namespace Domain.PolicyTopics
{
    public class PolicyTopicList
    {
        public int PolicyId { get; set; }
        public string CountryISOCode { get; set; }
        public string CountryName { get; set; }
        public string PolicyTitle { get; set; }
        public string PolicyTypeName { get; set; }
        public int? StartYear { get; set; }
        public int? StartMonth { get; set; }
        public IEnumerable<int> TopicList { get; set; }= Enumerable.Empty<int>();

    }

    public class ActionTopicList
    {

        public int ActionId { get; set; }
        public string CountryISOCode { get; set; }
        public string CountryName { get; set; }
        public string ProgramTitle { get; set; }
        public string ProgramTypeName { get; set; }
        public int? StartYear { get; set; }
        public int? StartMonth { get; set; }
        public int? TopicId { get; set; }
    }

    public class MechanismTopicList
    {
        public int MechanismId { get; set; }
        public string CountryISOCode { get; set; }
        public string CountryName { get; set; }
        public string MechanismTitle { get; set; }
        public string MechanismTypeName { get; set; }
        public int? StartYear { get; set; }
        public int? StartMonth { get; set; }
        public IEnumerable<int> TopicList { get; set; } = Enumerable.Empty<int>();
    }

    public class CommitmentTopicList
    {
        public int CommitmentId { get; set; }
        public string CountryISOCode { get; set; }
        public string CountryName { get; set; }
        public string CommitmentTitle { get; set; }
        public string CommitmentTypeName { get; set; }
        public int? StartYear { get; set; }
        public int? StartMonth { get; set; }
    }

    public class PolicyTopicDTO
    {
        public int  PolicyId { get; set; }
        public List<int> TopicList { get; set; }

        public PolicyTopicDTO()
        {
            TopicList = new();
        }
    }
}
