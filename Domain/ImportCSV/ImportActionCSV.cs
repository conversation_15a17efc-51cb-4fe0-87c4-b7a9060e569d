﻿using CsvHelper.Configuration.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ImportCSV
{
    public class ImportActionCSV
    {
        public int? Programme_Id { get; set; }
        public string Programme_Title { get; set; }
        public string Programme_Language { get; set; }
        public string Programme_Type { get; set; }
        public string Other_Program { get; set; }
        public string Region_Code { get; set; }
        public string Iso3Code { get; set; }
        public string Country_Name { get; set; }
        public string Program_Location { get; set; }
        public string Area { get; set; }
        public string Status { get; set; }
        public string Start_Month { get; set; }
        public string Start_Year { get; set; }
        public string End_Month { get; set; }
        public string End_Year { get; set; }
        public string Brief_Description { get; set; }
        public string References { get; set; }
        public string Related_Policy { get; set; }
        public string New_Policy { get; set; }
        public string Partner_Gov { get; set; }
        public string Partner_Government_Details { get; set; }
        public string Partner_UN { get; set; }
        public string Partner_UN_Details { get; set; }
        public string Partner_NGO { get; set; }
        public string Partner_NGO_Details { get; set; }
        public string Partner_Donors { get; set; }
        public string Partner_Donors_Details { get; set; }
        public string Partner_InterGov { get; set; }
        public string Partner_IntGov_Details { get; set; }
        public string Partner_National_NGO { get; set; }
        public string Partner_Nat_NGO_Details { get; set; }
        public string Partner_Research { get; set; }
        public string Partner_Research_Details { get; set; }
        public string Partner_Private { get; set; }
        public string Partner_Private_Details { get; set; }
        public string Partner_Other { get; set; }
        public string Partner_Other_Details { get; set; }
        public string Cost { get; set; }
        public int? Action_Id { get; set; }
        public string Theme { get; set; }
        public string Topic { get; set; }
        public string New_Topic { get; set; }
        public string Micronutrient { get; set; }
        public string Micronutrient_Compound { get; set; }
        public string Target_Group { get; set; }
        public string Age_Group { get; set; }
        public string Place { get; set; }
        public string Delivery { get; set; }
        public string Other_Delivery { get; set; }
        public string Dose_Frequency { get; set; }
        public string Impact_Indicators { get; set; }
        public string ME_System { get; set; }
        public string Target_Pop { get; set; }
        public string Coverage_Percent { get; set; }
        public string Coverage_Type { get; set; }
        public string Baseline { get; set; }
        public string Post_Intervention { get; set; }
        public string Social_Det { get; set; }
        public string Social_Other { get; set; }
        public string Elena_Link { get; set; }
        public string Problem_0 { get; set; }
        public string Solution_0 { get; set; }
        public string Problem_1 { get; set; }
        public string Solution_1 { get; set; }
        public string Problem_2 { get; set; }
        public string Solution_2 { get; set; }
        public string Problem_3 { get; set; }
        public string Solution_3 { get; set; }
        public string Problem_4 { get; set; }
        public string Solution_4 { get; set; }
        public string Problem_5 { get; set; }
        public string Solution_5 { get; set; }
        public string Problem_6 { get; set; }
        public string Solution_6 { get; set; }
        public string Problem_7 { get; set; }
        public string Solution_7 { get; set; }
        public string Problem_8 { get; set; }
        public string Solution_8 { get; set; }
        public string Problem_9 { get; set; }
        public string Solution_9 { get; set; }
        public string Other_Problems { get; set; }
        public string Other_Lessons { get; set; }
        public string Personal_Story { get; set; }
        public string Language { get; set; }
    }
}
