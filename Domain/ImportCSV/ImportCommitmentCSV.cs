﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ImportCSV
{
    public class ImportCommitmentCSV
    {
        public int? commitment_Id { get; set; }
        public string Commitment_Title { get; set; }
        public int? Commitments_Start_Year { get; set; }
        public int? Commitment_Start_Month { get; set; }
        public string Ministry_Department { get; set; }
        public string Endorsed_By { get; set; }
        public string Event { get; set; }
        public string Description { get; set; }
        public string Planned_Progress_Monitoring { get; set; }
        public string Resource_Allocation { get; set; }
        public string Links { get; set; }
        public string Notes { get; set; }
        public string Smart_Commitment_Number { get; set; }
        public string Smart_Commitment_Title { get; set; }
        public string Smart_Commitment_Text { get; set; }
        public int? Smart_Commitment_Id { get; set; }
        public int? Smart_Commitment_Start_Month { get; set; }
        public int? Smart_Commitment_Start_Year { get; set; }
        public string Iso3Code { get; set; }
        public string Country_Name { get; set; }
        public string Servy { get; set; }
        public string Policy_Program { get; set; }
        public string Smart_Commitment_Links_To_Sdgs { get; set; }
        public string Smart_Commitment_Links_To_Icn2 { get; set; }
        public string Region_Code { get; set; }
        public string Sector { get; set; }
    }
}
