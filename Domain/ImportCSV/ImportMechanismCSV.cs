﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ImportCSV
{
    public class ImportMechanismCSV
    {
        public int? Id { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string RegionCode { get; set; }
        public string ISO3Code { get; set; }
        public string CountryName { get; set; }
        public string Mandate { get; set; }
        public string Type { get; set; }
        public string MechanismOtherType { get; set; }
        public string Coordination { get; set; }
        public string Monitoring { get; set; }
        public string LeadGovernmentAgency { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public string OtherTopics { get; set; }
        public string References { get; set; }
        public string Partner_Gov { get; set; }
        public string Partner_Gov_Details { get; set; }
        public string Partner_Un { get; set; }
        public string Partner_Un_Details { get; set; }
        public string Partner_Ngo { get; set; }
        public string Partner_Ngo_Details { get; set; }
        public string Partner_Donors { get; set; }
        public string Partner_Donors_Details { get; set; }
        public string Partner_Intergov { get; set; }
        public string Partner_Intgov_Details { get; set; }
        public string Partner_National_Ngo { get; set; }
        public string Partner_Nat_Ngo_Details { get; set; }
        public string Partner_Research { get; set; }
        public string Partner_Research_Details { get; set; }
        public string Partner_Private { get; set; }
        public string Partner_Private_Details { get; set; }
        public string Partner_Other { get; set; }
        public string Partner_Other_Details { get; set; }
        public string Topics { get; set; }
        public string Other_Topics { get; set; }
        public string Policies { get; set; }
        public string Actions { get; set; }
        public string LessonsLearnt { get; set; }
        public string Url { get; set; }
        public string Notes { get; set; }
    }
}
