﻿using AngleSharp.Dom;
using CsvHelper.Configuration.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ImportCSV
{
    public class ImportPolicyCSV
    {
        public int? Policy_Id { get; set; }
        public string Region_Code { get; set; }
        public string Iso3Code { get; set; }
        public string Country_Name { get; set; }
        public string Province { get; set; } = String.Empty;
        public string Policy_Title { get; set; }
        public string Policy_Type { get; set; }
        public string Policy_Type_Other { get; set; }
        public string Language { get; set; }
        public int? Start_Month { get; set; }
        public int? Start_Year { get; set; }
        public int? End_Month { get; set; }
        public int? End_Year { get; set; }
        public string Published_By { get; set; }
        public string Published_Month { get; set; }
        public int? Published_Year { get; set; }
        public string Adopted { get; set; }
        public int? Adopted_Month { get; set; }
        public int? Adopted_Year { get; set; }
        public string Adopted_By { get; set; }
        public string Partner_Gov { get; set; }
        public string Partner_Government_Details { get; set; }
        public string Partner_UN { get; set; }
        public string Partner_UN_Details { get; set; }
        public string Partner_NGO { get; set; }
        public string Partner_NGO_Details { get; set; }
        public string Partner_Donors { get; set; }
        public string Partner_Donors_Details { get; set; }
        public string Partner_InterGov { get; set; }
        public string Partner_IntGov_Details { get; set; }
        public string Partner_National_NGO { get; set; }
        public string Partner_Nat_NGO_Details { get; set; }
        public string Partner_Research { get; set; }
        public string Partner_Research_Details { get; set; }
        public string Partner_Private { get; set; }
        public string Partner_Private_Details { get; set; }
        public string Partner_Other { get; set; }
        public string Partner_Other_Details { get; set; }
        public string Topics { get; set; }
        public string URL { get; set; }
        public string Further_Notes { get; set; }
        public string References { get; set; }
        public string Attached_File { get; set; }
        public string Extracts { get; set; }
        public string Link_Action { get; set; }

    }
}
