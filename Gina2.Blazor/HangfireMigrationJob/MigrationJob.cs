﻿using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.HangfireMigrationJob
{
    public class MigrationJob
    {
        private readonly ILogger<MigrationJob> _logger;
        private readonly SqlDbContext _SqlDbContext;
        private readonly GenaAppIdentityContext _appIdentityContext;
        private readonly PageConfigruationData _pageConfigruationData;
        private readonly IConfiguration _configuration;
        public MigrationJob(ILogger<MigrationJob> logger,
            SqlDbContext sqlDbContext,
            GenaAppIdentityContext appIdentityContext,
            PageConfigruationData pageConfigruationData,
            IConfiguration configuration)
        {
            _logger = logger;
            _SqlDbContext = sqlDbContext;
            _appIdentityContext = appIdentityContext;
            _pageConfigruationData = pageConfigruationData;
            _configuration = configuration;
        }
        public async Task RunJob()
        {
            _logger.LogInformation("Started Database Migration");
            try
            {
              //  await SQLContextUpdateDatabase();
              //  await AppContextUpdateDatabase();
                await RedisSeedData();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Database Migration failed has error :: {ex}");
                throw;
            }
            _logger.LogInformation("Started Database Migration completed");
        }
        private async Task RedisSeedData()
        {
            _logger.LogInformation($"RedisSeed data: {nameof(RedisSeedData)}");
            await _pageConfigruationData.SeedData();
            _logger.LogInformation($"RedisSeed Data Method Completed");
        }
        private async Task AppContextUpdateDatabase()
        {
            _logger.LogInformation($"App Context Update Database : {nameof(AppContextUpdateDatabase)}");
            await _appIdentityContext.Database.MigrateAsync();
            _logger.LogInformation($"App Context Update Database Method Completed");

        }
        private async Task SQLContextUpdateDatabase()
        {
            _logger.LogInformation($"SQL Context Update Database: {SQLContextUpdateDatabase}");
            await _SqlDbContext.Database.MigrateAsync();
            _logger.LogInformation($"SQL Context Update Database: Completed");

        }
    }
}
