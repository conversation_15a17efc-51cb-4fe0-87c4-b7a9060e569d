﻿using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Core.Interface;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph.Drives.Item.Items.Item.Workbook.Functions.Var_P;
using System.Security.Claims;

namespace Gina2.Blazor.Areas.Identity.IdentityServices
{
    public class CurrentUserService : ICurrentUserServiceExtended
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDbContextFactory<GenaAppIdentityContext> _identityDbContext;
        public CurrentUserService(IHttpContextAccessor httpContextAccessor, IDbContextFactory<GenaAppIdentityContext> identityDbContext)
        {
            _httpContextAccessor = httpContextAccessor;
            _identityDbContext = identityDbContext;

        }
        public string UserId => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        public string UserRole => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value;
        public string UserName => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;

        public string Email => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;
        public string Status => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type.Contains("status"))?.Value;

        public string Country => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type.Contains("country"))?.Value;
        public string FirstName => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type.Contains("firstName"))?.Value;
        public string LastName => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type.Contains("lastName"))?.Value;
        public string FullName => $"{FirstName} {LastName}";
        public string DisplayUserName => !string.IsNullOrEmpty(FirstName) && FirstName.Length >= 1 && !string.IsNullOrEmpty(LastName) && LastName.Length >= 1 ? $"{FirstName?.Substring(0, 1)?.ToUpper()}{LastName?.Substring(0, 1)?.ToUpper()}" : UserName?.Substring(0, 1)?.ToUpper();
        public List<string> ApproverCountryCode => _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type.Contains("approverCountryCode"))?.Value?.Split(',').ToList();
        public Task<List<ApplicationUser>> GetApproverUsersByCountry(HashSet<string> countryCode)
        {
            var dbContext = _identityDbContext.CreateDbContext();
            return dbContext.UserApproverCountries.Where(c => countryCode.Contains(c.CountryCode)).Select(u => u.User).ToListAsync();
        }
        public async Task<string> GetUserNameOrEmailAsync(string email)
        {
            var dbContext = _identityDbContext.CreateDbContext();
            var user = await dbContext.Users.Where(u => u.Email == email || u.UserName == email).FirstOrDefaultAsync();
            if (user is null)
                return email;

            return user.FullNameOrEmail;
        }
    }
}
