﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Gina2.Blazor.Areas.Identity.Data;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using Gina2.Blazor.Models;
using Gina2.Core.Enums;
using static Gina2.Core.Constants;
using Gina2.Core.Constant;
using Microsoft.Graph.Models;
using Gina2.Blazor.Helpers.RefreshToken;

namespace Gena2.Blazor.Identity.Areas.Identity.Pages.Account
{
    [AllowAnonymous]
    public class ExternalLoginModel : PageModel
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IUserStore<ApplicationUser> _userStore;
        private readonly IUserEmailStore<ApplicationUser> _emailStore;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IEmailSender _emailSender;
        private readonly ILogger<ExternalLoginModel> _logger;
        private readonly NavigationManager _navigationManager;
        private readonly IRefreshToken _refreshToken;
        public ExternalLoginModel(
            SignInManager<ApplicationUser> signInManager,
            UserManager<ApplicationUser> userManager,
            IUserStore<ApplicationUser> userStore,
            ILogger<ExternalLoginModel> logger,
            NavigationManager navigationManager,
            IEmailSender emailSender,
            IHttpContextAccessor httpContextAccessor,
            IRefreshToken refreshToken)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _userStore = userStore;
            _emailStore = GetEmailStore();
            _logger = logger;
            _emailSender = emailSender;
            _navigationManager = navigationManager;
            _httpContextAccessor = httpContextAccessor;
            _refreshToken = refreshToken;
        }

        public string ReturnUrl { get; set; }

        public Error ErrorDetail { get; set; } = new Error();

        public IActionResult OnGet() => RedirectToPage("./Login");

        public IActionResult OnPost(string provider, string returnUrl = null)
        {
            // Request a redirect to the external login provider.
            try
            {
                var redirectUrl = Url.Page("/Account/ExternalLogin",
                               pageHandler: "Callback",
                               values: new { area = "Identity", returnUrl = returnUrl });
                var properties = _signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
                properties.Items["scheme"] = provider;
                return new ChallengeResult(provider, properties);
            }
            catch (Exception ex)
            {
                _logger.LogError($"External_LogIn error: {ex}");
            }
            return Page();
        }

        public async Task<IActionResult> OnGetCallbackAsync(string returnUrl = null, string remoteError = null)
        {
            returnUrl ??= "~/";
            var loginInfo = await _signInManager.GetExternalLoginInfoAsync();

            if (loginInfo == null && !string.IsNullOrEmpty(remoteError))
            {
                _logger.LogWarning($"azure ad login failed {remoteError}");
                GetData("", StatusCodeEnum.InternalServer, "Authentication failed, please contact your administrator.");
                return Page();
            }
            if (loginInfo == null)
            {
                _logger.LogWarning("External login info is not available");
                GetData("External login info is not available");
                return Page();
            }

            if (remoteError != null)
            {
                _logger.LogWarning($"External login callback error: {remoteError}");
                GetData($"External login callback error");
                return Page();
            }
            if (string.IsNullOrEmpty(loginInfo.Principal.FindFirstValue(ClaimTypes.Email)))
            {
                _logger.LogWarning($"Azure Ad configuration is wrong. Email Claims is empty");
                GetData("Azure Ad configuration is wrong", StatusCodeEnum.NotFound);
                return Page();
            }

            var user = await _userManager.FindByEmailAsync(loginInfo.Principal.FindFirstValue(ClaimTypes.Email));

            if (user == null)
            {
                user = AddUserWithRole(loginInfo.Principal);
                try
                {
                    await _userManager.CreateAsync(user);
                    await _userManager.AddToRoleAsync(user, RolesConstant.ContributorRoleName);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Expection :: {ex}");
                    await _userManager.DeleteAsync(user);
                    GetData("Unable to add User this time", StatusCodeEnum.InternalServer);
                    return Page();
                }
            }
            else
            {
                if (!user.EmailConfirmed)
                {
                    await LogoutUser();
                    GetData("Your registration is in progress, Please contact administrator", StatusCodeEnum.NotFound);
                    return Page();
                }

                if (string.Equals(user.Status, "Blocked"))
                {
                    await LogoutUser();
                    GetData("Please contact Admin, your account is blocked");
                    return Page();
                }
            }

            if (await _userManager.FindByLoginAsync(loginInfo.LoginProvider, loginInfo.ProviderKey) == null)
            {
                await _userManager.AddLoginAsync(user, loginInfo);
            }

            var result = await _signInManager.ExternalLoginSignInAsync(
                loginInfo.LoginProvider,
                loginInfo.ProviderKey,
                isPersistent: true,
                bypassTwoFactor: true
            );

            if (!result.Succeeded)
            {
                GetData("Unable to sign you in right now", StatusCodeEnum.NotFound);
                Page();
            }
            else if (result.IsLockedOut)
            {
                _logger.LogWarning($"");
                GetData("External login callback error: user is locked out!", StatusCodeEnum.NotFound);
                Page();
            }
            else if (result.IsNotAllowed)
            {
                _logger.LogWarning($"");
                GetData("External login callback error: user is not allowed!", StatusCodeEnum.NotFound);
                Page();
            }
            else if (result.Succeeded)
            {
                var userToken = loginInfo.AuthenticationTokens.FirstOrDefault(e => e.Name == "id_token").Value;
                var userRefreshToken = loginInfo.AuthenticationTokens.FirstOrDefault(e => e.Name == "refresh_token").Value;
                if (string.IsNullOrEmpty(userToken) || string.IsNullOrEmpty(userRefreshToken))
                {
                    _logger.LogWarning("External login callback error: user token or refresh token is null or empty!");
                    GetData("External login callback error: user token or refresh token is null or empty!", StatusCodeEnum.NotFound);
                    return Page();
                }

                if (await UpdateUserStatusandToken(user, userToken, userRefreshToken))
                    return LocalRedirect(returnUrl);
            }
            // if there come something went wrong
            await LogoutUser();
            return Page();
        }

        private async Task<bool> UpdateUserStatusandToken(ApplicationUser user, string token, string RefreshToken)
        {
            user.IsNewRegister = false;
            user.UserRefreshToken = RefreshToken;
            user.UserToken = token;
            user.Status = "Active";

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded) return true;

            return false;
        }

        private static ApplicationUser AddUserWithRole(ClaimsPrincipal principal)
        {
            return new ApplicationUser
            {
                Email = principal.FindFirst(ClaimTypes.Email).Value,
                EmailConfirmed = true,
                FirstName = principal?.FindFirst(ClaimTypes.GivenName)?.Value,
                LastName = principal?.FindFirst(ClaimTypes.Surname)?.Value,
                Country = principal?.FindFirst(ClaimTypes.Country)?.Value,
                UserName = principal.FindFirst(ClaimTypes.Email).Value,
                Status = "Active",
                IsNewRegister = false,
                CreatedDate = DateTime.UtcNow,
            };
        }

        private async Task LogoutUser()
        {
            await _signInManager.SignOutAsync();
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }

        private void GetData(string description, StatusCodeEnum type = StatusCodeEnum.InternalServer, string title = "Login failure")
        {
            ErrorDetail = new Error()
            {
                Title = title,
                Description = description,
                Status = type,
                ButtonName = "Login",
                RedirectURL = "/login"
            };
        }

        private IUserEmailStore<ApplicationUser> GetEmailStore()
        {
            if (!_userManager.SupportsUserEmail)
            {
                throw new NotSupportedException("The default UI requires a user store with email support.");
            }
            return (IUserEmailStore<ApplicationUser>)_userStore;
        }
    }
}
