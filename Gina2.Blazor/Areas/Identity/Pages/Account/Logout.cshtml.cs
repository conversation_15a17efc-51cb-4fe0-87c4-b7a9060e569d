﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using System;
using System.Threading.Tasks;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Models;
using Gina2.Core.Enums;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.JSInterop;

namespace Gena2.Blazor.Identity.Areas.Identity.Pages.Account
{
    public class LogoutModel : PageModel
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ILogger<LogoutModel> _logger;
        public Error ErrorDetail { get; set; } = new Error();


        public LogoutModel(SignInManager<ApplicationUser> signInManager, ILogger<LogoutModel> logger)

        {
            _signInManager = signInManager;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync(string returnUrl = null)
        {

            if (User.Identity.IsAuthenticated)
            {
                await _signInManager.SignOutAsync();
                await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
                _logger.LogInformation("User logged out.");
                GetData("successfully logged out", StatusCodeEnum.OK);
                if(returnUrl == "true")
                {
                    return Redirect("/login?ReturnUrl=/admin/plug-in/home");

                }
                return Redirect("~/");
            }
            var redirectUrl = Url.Page("/Account/Login", values: new { area = "Identity" });
            return RedirectToPage(redirectUrl);
        }

        private void GetData(string description, StatusCodeEnum type = StatusCodeEnum.OK, string title = "successfully logged out")
        {
            ErrorDetail = new Error()
            {
                Title = title,
                Description = description,
                Status = type,
                ButtonName = "Login",
                RedirectURL = "/login"
            };
        }
    }
}
