﻿using System.Text;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Models;
using Gina2.Core.Enums;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.WebUtilities;

namespace Gena2.Blazor.Identity.Areas.Identity.Pages.Account
{
    public class ConfirmEmailModel : PageModel
    {
        [TempData]
        public string StatusMessage { get; set; }

        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IEmailServices _emailService;
        public Error ErrorDetail { get; set; } = new Error();
        public ConfirmEmailModel(UserManager<ApplicationUser> userManager, IEmailServices emailService)
        {
            _userManager = userManager;
            _emailService = emailService;
        }

        public async Task<IActionResult> OnGetAsync(string userId, string code)
        {
            if (userId == null || code == null)
            {
                GetData($"Unable to confirm your email right now, please contact your Admin", StatusCodeEnum.NotFound);
                return Page();
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                GetData($"Unable to find user right now, please contact your Admin", StatusCodeEnum.NotFound);
                return Page();
            }

            code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
            var result = await _userManager.ConfirmEmailAsync(user, code);
            if (result.Succeeded)
            {
                await SendEmail(user);
                GetData("Thank you for confirming your email", StatusCodeEnum.OK);
                return Page();
            }
            GetData($"Unable to confirm your email either code is expired or user not found, please contact your Admin");
            return Page();
        }

        private async Task SendEmail(ApplicationUser user)
        {
            string userFullName = string.Format("{0} {1}", user.FirstName, user.LastName);

            NotificationEmailModel notificationModel = new()
            {
                BaseUrl = string.Format(@"{0}://{1}", Request.Scheme, Request.Host),
                ReceivedBy = string.IsNullOrWhiteSpace(userFullName) ? user.Email : userFullName
            };

            await _emailService.SendEmailByTemplateAsync(user.Email, "Welcome", TemplateType.NotificationEmail, notificationModel);
        }

        private void GetData(string description, StatusCodeEnum type = StatusCodeEnum.InternalServer, string title = "User Confirmed")
        {
            ErrorDetail = new Error()
            {
                Title = title,
                Description = description,
                Status = type,
                ButtonName = "Login",
                RedirectURL = "/login"
            };
        }
    }
}
