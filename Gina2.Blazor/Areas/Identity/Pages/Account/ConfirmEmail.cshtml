﻿@page "/ConfirmEmail"
@model ConfirmEmailModel
@{
    ViewData["Title"] = "Confirm email";
      Layout = "/Areas/Identity/Pages/_Layout.cshtml";
}


@{
    ViewData["Title"] = "Log in";
    Layout = "/Areas/Identity/Pages/_Layout.cshtml";
}

<div class="notf-1 container">
    <div class="notfount _flexcenter">
        <img src="img/svg/@string.Concat(Convert.ToInt16(Model.ErrorDetail.Status) ,".svg")" />
    </div>
</div>
<div class="container-fluid p-0">
      <div class="allbanner _loginindex-0" Style="background-image: url(../img/Search.png);">
        <div class="container notf-0">
            <div Class="container text-center pt-5">
                <h3 class="color-w text-center">@Model.ErrorDetail.Title</h3>
                <p Class="color-w text-center pb-4">
                    @Model.ErrorDetail.Description</p>
                        <a href="@Model.ErrorDetail.RedirectURL" Class="but-yellow" class="btn btn-primary">@Model.ErrorDetail.ButtonName</a>
            </div>
        </div>
    </div>
</div>
