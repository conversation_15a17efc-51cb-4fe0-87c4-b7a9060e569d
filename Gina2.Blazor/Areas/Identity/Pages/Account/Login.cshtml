﻿@page "/login"
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@model Gena2.Blazor.Identity.Areas.Identity.Pages.Account.LoginModel;
@{
    //ViewData["Title"] = "Log in";
    Layout = "/Areas/Identity/Pages/_Layout.cshtml";
}<div class="login _flexcenter">
    <div class="container">
        <div class="row">


            <div class="col-sm-12 col-md-6 col-lg-6 login-r mob-login _flexcenter">
                <Div class="login-box">
                    <Div class="text-center logo">
                        <NavLink Match="NavLinkMatch.All">
                            <img src="@Model.ImageUrl" alt="logo" />
                        </NavLink>
                        <p>Welcome to GIFNA, Please put your login credentials below to start using portal information</p>
                    </Div>
                    <div class="_flexcenter">
                        @*<button onclick="togglelogin()"><img src="img/login-icon.png" />Log in</button>*@
                        <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="form-horizontal w-100">
                            @foreach (var provider in Model.ExternalLogins)
                            {
                                @*<button><img src="img/login-icon.png" /> <a href="MicrosoftIdentity/Account/SignIn">Log in</a></button>*@
                                <button type="submit" name="provider" data-cy="SignUpPageLogin" value="@provider.Name" title="Log in using your @provider.DisplayName account"><img src="img/Microsoft_Azure.png" alt="logo" /> @provider.DisplayName</button>
                            }

                        </form>
                    </div>

                    <div class="whologin" id="whoLogin">
                        <br />
                        <input asp-for="@Model.Key" type="hidden" />
                        <div asp-validation-summary="All" class="text-danger"></div>
                        <div class="Login-divider">
                            Sign up
                        </div>
                        <form method="post" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-sm-12 col-md-6 col-lg-6">
                                    <div>
                                        <label>First Name <span>*</span></label>
                                        <input asp-for="SignUpModel.FirstName" data-cy="SignUpPageFirstName" placeholder="Enter your first name" class="form-control" autocomplete="new-password" aria-required="true" />
                                        <span asp-validation-for="SignUpModel.FirstName" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-6 col-lg-6">
                                    <div>
                                        <label>Last Name <span>*</span></label>
                                        <input asp-for="SignUpModel.LastName" data-cy="SignUpPageLastName" placeholder="Enter your last name" class="form-control" autocomplete="new-password" aria-required="true" />
                                        <span asp-validation-for="SignUpModel.LastName" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 col-md-6 col-lg-6">
                                    <div>
                                        <label>Email id <span>*</span></label>
                                        <input asp-for="SignUpModel.Email" data-cy="SignUpPageEmail" placeholder="Enter your email id" class="form-control" autocomplete="username" aria-required="true" />
                                        <span asp-validation-for="SignUpModel.Email" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-6 col-lg-6">
                                    <div>
                                        <label>Organization <span>*</span></label>
                                        <input asp-for="SignUpModel.Organization" data-cy="SignUpPageOrganization" placeholder="Enter your organization" class="form-control" autocomplete="new-password" aria-required="true" />
                                        <span asp-validation-for="SignUpModel.Organization" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                @*@Html.ReCaptcha("Submit Invisible ReCaptcha", className: "btn btn-primary")*@
                                <input asp-for="SignUpModel.CaptchaToken" data-cy="SignUpPageCaptchaToken" Organization type="hidden" />
                               
                            </div>

                            <br />
                            <span class="text-success">
                                @Html.DisplayFor(e=>e.SuccuessMessage)
                            </span>
                            <div style="padding-top:5px;">
                                <button type="submit" class="loginbut" title="SignUp" data-cy="SignUpPagesubmit">Sign Up</button>
                            </div>
                        </form>
                    </div>
                </Div>
            </div>



            <div class="col-sm-12 col-md-6 col-lg-6 login-r _flexcenter">
                <Div Class="mo-b textsize">
                    <h2>“</h2>
                    <h3>GIFNA</h3>
                    <h4>An interactive platform for sharing standardized information on nutrition policies and actions</h4>
                </Div>
            </div>
        </div>
    </div>
</div>

    @{
        var EnableRecaptch = Convert.ToBoolean(Configuration["ReCaptcha:EnableRecaptcha"] ?? "false");
    }


    @if (EnableRecaptch)
    {
        <script src="https://www.google.com/recaptcha/api.js?render=@(Model.Key)" asp-add-nonce="true"></script>
        <script src="~/js/googlecapcha.js"></script>
    }
   
    <script src="~/js/library/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/js/library/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    
