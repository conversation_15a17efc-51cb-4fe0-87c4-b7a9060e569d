﻿using AntDesign;
using AutoMapper;
using Blazorise.Extensions;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core;
using Gina2.Core.Enums;
using Gina2.Core.Interface;
using Gina2.DbModels;
using Gina2.Services.LayoutCustomization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;
using NuGet.Packaging;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Net;
using static Gina2.Core.Constants;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace Gena2.Blazor.Identity.Areas.Identity.Pages.Account
{
    //[ValidateReCaptcha]
    public class LoginModel : PageModel
    {
        public class RegisterUser
        {
            [Required]
            [EmailAddress]
            public string Email { get; set; }
            [Required]
            public string FirstName { get; set; }
            [Required]
            public string LastName { get; set; }
            [Required]
            public string Organization { get; set; }
            public string CaptchaToken { get; set; }
        }

        [Inject]
        public ProtectedSessionStorage SessionStorage { get; set; }
        public string Key { get; set; }
        public string ImageUrl { get; set; }
        private readonly GenaAppIdentityContext _dbContext;

        public string SuccuessMessage { get; set; }
        [BindProperty]
        public RegisterUser SignUpModel { get; set; }
        private SignInManager<ApplicationUser> _signInManager { get; set; }
        public UserManager<ApplicationUser> _userManager { get; }
        public IList<AuthenticationScheme> ExternalLogins { get; set; }
        public string ReturnUrl { get; set; } = String.Empty;
        private readonly ILogger<LoginModel> _logger;
        private readonly IConfiguration _configuration;
        private readonly IEmailServices _emailServices;
        private readonly ILayoutCustomizationService _layoutCustomizationService;
        private readonly IMemoryCache _memoryCache;
        private readonly IMapper _mapper;
        public LoginModel(SignInManager<ApplicationUser> signInManager, UserManager<ApplicationUser> userManager, ILogger<LoginModel> logger, IConfiguration configuration, GenaAppIdentityContext dbContext, IEmailServices emailServices, IMemoryCache memoryCache, ILayoutCustomizationService layoutCustomizationService, IMapper mapper, ProtectedSessionStorage sessionStorage)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _logger = logger;
            _configuration = configuration;
            _dbContext = dbContext;
            _emailServices = emailServices;
            Key = configuration["ReCaptcha:SiteKey"];
            _memoryCache = memoryCache;
            _layoutCustomizationService = layoutCustomizationService;
            _mapper = mapper;
            SessionStorage = sessionStorage;
        }

        public async Task<IActionResult> OnGetAsync(string returnUrl = null)
        {
            var url =   _memoryCache.Get<string>("url");
            returnUrl ??= url;
            if (User.Identity.IsAuthenticated)
            {
                return LocalRedirect(returnUrl);
            }
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
            await GetLayoutSetting();
            ReturnUrl = returnUrl;
            return Page();
        }
        public async Task<IActionResult> OnPostAsync()
        {
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
            ReturnUrl ??= "~/";
            await GetLayoutSetting();
            if (ModelState.IsValid)
            {
                if (await IsEmailAlreadyExists(SignUpModel.Email))
                {
                    ModelState.AddModelError(string.Empty, "Email id already exists");
                    return Page();
                }
                var EnableRecaptch =Convert.ToBoolean(_configuration["ReCaptcha:EnableRecaptcha"] ?? "false");
                if (EnableRecaptch && !ReCaptchaPassed(SignUpModel.CaptchaToken, Key))
                {
                    ModelState.AddModelError(string.Empty, "Validation failed");
                    return Page();
                }
                
                var user = new ApplicationUser()
                {
                    Email = SignUpModel.Email,
                    UserName = SignUpModel.Email,
                    FirstName = SignUpModel.FirstName,
                    LastName = SignUpModel.LastName,
                    Organization = SignUpModel.Organization,
                    Status = "Pending",
                    CreatedDate = DateTime.UtcNow,
                    EmailConfirmed = true,
                    IsNewRegister = true,
                };

                var userInfo = await _userManager.CreateAsync(user);
                if (userInfo.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, RolesConstant.ContributorRoleName);
                    SuccuessMessage = "You have successfully registered as a user. Please wait for an approval e-mail and accept the invite to be able to login to the platform.";
                    return Page();
                }

                else
                {
                    ModelState.AddModelError(string.Empty, "Something went wrong");
                    return Page();
                }
            }
            
            return Page();
        }
        public async Task<bool> IsEmailAlreadyExists(string email)
        {
            var existingUser = await _userManager.FindByEmailAsync(email);
            return existingUser != null;
        }

        private  bool ReCaptchaPassed(string gRecaptchaResponse,string key)
        {
           using HttpClient httpClient = new HttpClient();

         using var res = httpClient.GetAsync($"{_configuration["ReCaptcha:ApiUrl"]}?secret={_configuration["ReCaptcha:SecretKey"]}&response={gRecaptchaResponse}").Result;

            if (res.StatusCode != HttpStatusCode.OK)
            {
                return false;
            }
            string JSONres = res.Content.ReadAsStringAsync().Result;
            dynamic JSONdata = JObject.Parse(JSONres);

            if (JSONdata.success != "true" || JSONdata.score <= 0.5m)
            {
                return false;
            }

            return true;
        }

        private async Task GetLayoutSetting()
        {
            var layoutCache = _memoryCache.Get<SiteCustomizationDto>(Cachekeys.CustomLayout);

            if (layoutCache == null)
            {
                var siteLayoutSettingFromDB = await _layoutCustomizationService.GetPublishedSetting();

                _memoryCache.Set(Cachekeys.CustomLayout, _mapper.Map<LayoutCustomization, SiteCustomizationDto>(siteLayoutSettingFromDB));
                layoutCache = _memoryCache.Get<SiteCustomizationDto>(Cachekeys.CustomLayout);
            }
            ImageUrl = "img/" +layoutCache.FooterImage;
        }
    }
}
