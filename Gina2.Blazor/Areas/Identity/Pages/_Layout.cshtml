﻿@using Microsoft.AspNetCore.Components.Web
@namespace Gina2.Blazor.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <Title value="The Global database on the Implementation of Food and Nutrition Action (GIFNA) "></Title>
    <link rel="icon" type="image/png" href="img/gina-logo.png" />

    <base href="~/" />


    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link href="~/css/font.css" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/responsive.css" rel="stylesheet" />

    <!-- map library -->
    <script src="~/js/library/jquery-3.6.2.min.js"></script>

</head>
<body>
    @RenderBody()
    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
   
    @RenderSection("Scripts", required: false)
</body>
</html>
