﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Areas.Identity.Data
{
    public class ApplicationUserClaim : IClaimsTransformation
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly GenaAppIdentityContext _dbContext;
        public ApplicationUserClaim(UserManager<ApplicationUser> userManager, GenaAppIdentityContext dbContext)
        {
            _userManager = userManager;
            _dbContext = dbContext;
        }

        public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
        {
            var identity = principal.Identities.FirstOrDefault(c => c.IsAuthenticated);
            if (identity == null) return principal;
            var email = identity.Claims?.FirstOrDefault(c => c.Type== ClaimTypes.Name)?.Value;
            if (email == null)
            {
                return principal;
            }
            if (!principal.Claims.Any(e => e.Type.Contains("country")))
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null) return principal;
                // Add or replace identity.Claims.
                identity.AddClaim(new Claim("status", user?.Status ?? String.Empty));
                identity.AddClaim(new Claim("country", user?.Country ?? String.Empty));
                identity.AddClaim(new Claim("firstName", user?.FirstName ?? String.Empty));
                identity.AddClaim(new Claim("lastName", user?.LastName ?? String.Empty));
                var role = identity.Claims?.FirstOrDefault(c => c.Type.Contains("role"))?.Value;
                if (!string.IsNullOrEmpty(role) && role.Equals(RolesConstant.ApproverRoleName))
                {
                    var countryInfo = await _dbContext.UserApproverCountries
                                    .Where(e => e.UserId == user.Id)
                                    .Select(e => e.CountryCode).ToListAsync();

                    if (countryInfo != null)
                    {
                        identity.AddClaim(new Claim("approverCountryCode", String.Join(",", countryInfo)));
                    }
                }
            }
            return new ClaimsPrincipal(identity);
        }
    }
}
