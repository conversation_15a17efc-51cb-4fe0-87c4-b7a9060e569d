﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gina2.Blazor.Areas.Identity.Data
{
    public class ApplicationUser : IdentityUser
    {
        public ApplicationUser()
        {
            UserApproverCountries = new List<UserApproverCountry>();
        }
        public string Status { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Country { get; set; }
        public string Organization { get; set; }
        public DateTime? LastAccessDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsNewRegister { get; set; }
        public string UserToken { get; set; }
        public string UserRefreshToken { get; set; }
        public virtual ICollection<UserApproverCountry> UserApproverCountries { get; set; }
        public virtual ICollection<ApplicationUserIntertestedTopic> ApplicationUserIntertestedTopics { get; set; }
        public virtual ICollection<ApplicationUserIntertestedCountry> ApplicationUserIntertestedCountries { get; set; }

        [NotMapped]
        private string fullName;

        [NotMapped]
        public string FullName
        {
            get
            {
                return fullName;
            }
            set
            {
                fullName = FirstName + " " + LastName;
            }
        }

        [NotMapped]
        public string FullNameOrEmail => !string.IsNullOrWhiteSpace($"{FirstName} {LastName}") ? $"{FirstName} {LastName}" : Email;
    }
}
