﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Areas.Identity.Data
{
    public class GenaAppIdentityContext : IdentityDbContext<ApplicationUser>
    {
        public DbSet<UserApproverCountry> UserApproverCountries { get; set; }
        public DbSet<ApplicationUserIntertestedCountry> ApplicationUserIntertestedCountries { get; set; }
        public DbSet<ApplicationUserIntertestedTopic> ApplicationUserIntertestedTopic { get; set; }
        public GenaAppIdentityContext(DbContextOptions<GenaAppIdentityContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            SetupUserApproverCountries(builder);
            SetupApplicationUser(builder);
            SetupApplicationUserCountries(builder);
            SetupApplicationUserTopics(builder);

            SeedRoles(builder);
            base.OnModelCreating(builder);
        }
        public void SeedRoles(ModelBuilder builder)
        {
            builder.Entity<IdentityRole>().HasData(
                new IdentityRole { Id = RolesConstant.ContributorRoleId, Name = RolesConstant.ContributorRoleName, NormalizedName = RolesConstant.ContributorRoleName.ToUpper() },
                new IdentityRole { Id = RolesConstant.ApproverRoleId, Name = RolesConstant.ApproverRoleName, NormalizedName = RolesConstant.ApproverRoleName.ToUpper() },
                new IdentityRole { Id = RolesConstant.AdminRoleId, Name = RolesConstant.AdminRoleName, NormalizedName = RolesConstant.AdminRoleName.ToUpper() }
                );
        }
        private static void SetupApplicationUserTopics(ModelBuilder modelBuilder)
        {
            var builder = modelBuilder.Entity<ApplicationUserIntertestedTopic>();
            builder.HasKey(t => t.Id);
            builder.Property(t => t.Id).ValueGeneratedOnAdd();
            builder
               .HasOne(sc => sc.User)
               .WithMany(s => s.ApplicationUserIntertestedTopics)
               .HasForeignKey(sc => sc.UserId);
        }
        private static void SetupApplicationUserCountries(ModelBuilder modelBuilder)
        {
            var builder = modelBuilder.Entity<ApplicationUserIntertestedCountry>();
            builder.HasKey(t => t.Id);
            builder.Property(t => t.Id).ValueGeneratedOnAdd();
            builder
               .HasOne(sc => sc.User)
               .WithMany(s => s.ApplicationUserIntertestedCountries)
               .HasForeignKey(sc => sc.UserId);
        }
        private static void SetupUserApproverCountries(ModelBuilder modelBuilder)
        {
            var builder = modelBuilder.Entity<UserApproverCountry>();
            builder.HasKey(t => t.Id);
            builder.Property(s => s.CountryCode).IsRequired(false);
            builder.Property(s => s.Country).IsRequired(false);
            builder.Property(t => t.Id).ValueGeneratedOnAdd();
            builder
               .HasOne<ApplicationUser>(sc => sc.User)
               .WithMany(s => s.UserApproverCountries)
               .HasForeignKey(sc => sc.UserId);
        }
        private static void SetupApplicationUser(ModelBuilder modelBuilder)
        {
            var builder = modelBuilder.Entity<ApplicationUser>();
            builder.Property(s => s.Country).IsRequired(false);
            builder.Property(s => s.Organization).IsRequired(false);
            builder.Property(s => s.LastName).IsRequired(false);
        }
    }
}
