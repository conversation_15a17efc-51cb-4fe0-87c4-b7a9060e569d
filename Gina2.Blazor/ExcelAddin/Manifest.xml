﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="TaskPaneApp">
	<Id>05c2e1c9-3e1d-406e-9a91-e9ac64854143</Id>
	<Version>*******</Version>
	<DefaultLocale>en-US</DefaultLocale>
	<DisplayName DefaultValue="WHO PROD"/>
	<Description DefaultValue="A template to get started."/>
	<IconUrl DefaultValue="https://localhost:7123/img/scorecard-logo.png"/>
	<HighResolutionIconUrl DefaultValue="https://localhost:7123/img/scorecard-logo.png"/>
	<SupportUrl DefaultValue="https://www.contoso.com/help"/>
	<AppDomains>
		<AppDomain>https://login.microsoftonline.com/</AppDomain>
	</AppDomains>
	<Hosts>
		<Host Name="Workbook"/>
	</Hosts>
	<DefaultSettings>
		<SourceLocation DefaultValue="https://localhost:7123/login?returnUrl=/admin/plug-in/home"/>
	</DefaultSettings>
	<Permissions>ReadWriteDocument</Permissions>
	<VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
		<Hosts>
			<Host xsi:type="Workbook">
				<DesktopFormFactor>
					<GetStarted>
						<Title resid="GetStarted.Title"/>
						<Description resid="GetStarted.Description"/>
						<LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
					</GetStarted>
					<FunctionFile resid="Commands.Url" />
					<ExtensionPoint xsi:type="PrimaryCommandSurface">
						<OfficeTab id="TabHome">
							<Group id="CommandsGroup">
								<Label resid="CommandsGroup.Label" />
								<Icon>
									<bt:Image size="16" resid="Icon.16x16" />
									<bt:Image size="32" resid="Icon.32x32" />
									<bt:Image size="80" resid="Icon.80x80" />
								</Icon>
								<Control xsi:type="Button" id="TaskpaneButton">
									<Label resid="TaskpaneButton.Label" />
									<Supertip>
										<Title resid="TaskpaneButton.Label" />
										<Description resid="TaskpaneButton.Tooltip" />
									</Supertip>
									<Icon>
										<bt:Image size="16" resid="Icon.16x16" />
										<bt:Image size="32" resid="Icon.32x32" />
										<bt:Image size="80" resid="Icon.80x80" />
									</Icon>
									<Action xsi:type="ShowTaskpane">
										<TaskpaneId>ButtonId1</TaskpaneId>
										<SourceLocation resid="Taskpane.Url" />
									</Action>
								</Control>
							</Group>
						</OfficeTab>
					</ExtensionPoint>
				</DesktopFormFactor>
			</Host>
		</Hosts>
		<Resources>
			<bt:Images>
				<bt:Image id="Icon.16x16" DefaultValue="https://localhost:7123/img/scorecard-logo.png"/>
				<bt:Image id="Icon.32x32" DefaultValue="https://localhost:7123/img/scorecard-logo.png"/>
				<bt:Image id="Icon.80x80" DefaultValue="https://localhost:7123/img/scorecard-logo.png"/>
			</bt:Images>
			<bt:Urls>
				<bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://go.microsoft.com/fwlink/?LinkId=276812" />
				<bt:Url id="Commands.Url" DefaultValue="https://localhost:7123/login?returnUrl=/admin/plug-in/home" />
				<bt:Url id="Taskpane.Url" DefaultValue="https://localhost:7123/login?returnUrl=/admin/plug-in/home" />
			</bt:Urls>
			<bt:ShortStrings>
				<bt:String id="GetStarted.Title" DefaultValue="Get started with your sample add-in!" />
				<bt:String id="CommandsGroup.Label" DefaultValue="Commands Group" />
				<bt:String id="TaskpaneButton.Label" DefaultValue="Gifna" />
			</bt:ShortStrings>
			<bt:LongStrings>
				<bt:String id="GetStarted.Description" DefaultValue="Your sample add-in loaded succesfully. Go to the HOME tab and click the 'Show Taskpane' button to get started." />
				<bt:String id="TaskpaneButton.Tooltip" DefaultValue="Click to Show a Taskpane" />
			</bt:LongStrings>
		</Resources>
	</VersionOverrides>
</OfficeApp>