﻿using Gina2.Blazor.CacheService;
using Gina2.Core.Constant;
using Gina2.DbModels;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Models;

namespace Gina2.Blazor.Models
{
    public sealed class SearchHistory
    {
        private readonly PageConfigrationCache _pageConfigrationCache;
        public SearchHistory(PageConfigrationCache pageConfigrationCache)
        {
            _pageConfigrationCache = pageConfigrationCache;
        }
        public int Id { get; set; }
        public List<string> DataTypes { get; set; }
        public string SelectedRegionCode { get; set; }
        public string SelectedIncomeGroup { get; set; }
        public int? StartYear { get; set; }
        public int? EndYear { get; set; }
        public string SearchKeyword { get; set; }
        public List<string> SelectedCountries { get; set; }
        public int Order { get; set; }

        //public List<PolicyTopicMap> PolicyTopicList { get; set; }
        public List<DbModels.Topic> Topics { get; set; }

        public List<string> LastSelectedNutritions { get; set; } = new();
        public IEnumerable<int> SelectedTopicIds { get; set; }
        public List<SelectedTopicsTree> SelectedPolicyTopicIds { get; set; }
        
        public List<SelectedTopicsTree> SelectedMechanismTopicIds { get; set; }
        
        public List<SelectedTopicsTree> SelectedActionTopicIds { get; set; }
        public Dictionary<int, List<int>> SelectedSearchTopicIds { get; set; }
        public IEnumerable<string> SelectedSearchTopicIdsOnSearchTree { get; set; }
        public IEnumerable<int> SelectedPolicyTopicIdsOnSearchTree { get; set; }
        public IEnumerable<int> SelectedActionTopicIdsOnSearchTree { get; set; }
        public IEnumerable<int> SelectedMechanismTopicIdsOnSearchTree { get; set; }
        public IEnumerable<int> PolicyTypeIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> PolicyTopicIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> MechanismTypeIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> MechanismTopicIds { get; set; } = Enumerable.Empty<int>();
        public Dictionary<int, List<int>> PartnerIds { get; set; } = new Dictionary<int, List<int>>();
        public IEnumerable<int> LanguageIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> ProgramTypeIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> ProgramTopicIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> FundingSourceIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> TargetGroupIds = Enumerable.Empty<int>();
        public Dictionary<string, List<string>> CountryGroupSelectedCountries { get; set; } = new();
        public Dictionary<string, List<string>> CountryRegionSelectedCountries { get; set; } = new();
        public IEnumerable<int> DeliveryChannelIds { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> ProblemIds { get; set; } = Enumerable.Empty<int>();
        public Dictionary<int, List<int>> ICN2Ids { get; set; } = new();
        public string SelectedSearchTopicName { get; set; }
        public string SelectedPolicyTopicName {get;set;}
        public string SelectedActionTopicName {get;set;}
        public string SelectedMechanismTopicName {get;set;}
        public string SelectedPartners{get; set;}
        public string SelectedPolicyTypes{get; set;}
        public string SelectedMechanismTypes{get; set;}
        public string SelectedProgramTypes{get; set;}
        public string SelectedFundingSource{get; set;}
        public string SelectedTargetGroup{get; set;}
        public string SelectedDeliveryChannels{get; set;}
        public string SelectedProblemsAndSolutions{get; set;}
        public string SelectedICN2Actions{get; set;}
        public string SelectedLanguages{get; set;}
        public List<string> SelectedTopicNames
        {
            get
            {
                return Topics.Where(t => SelectedTopicIds.Contains(t.Id)).Select(t => t.Name).ToList();
            }
        }

        public GlobalSearchRequest SearchRequest { get; set; } = new();


        public string Name
        {
            get
            {
                string name = string.Empty;

                if (DataTypes.Count > 0)
                {
                    string dataTypeNames = string.Join(", ", DataTypes);
                    name = string.IsNullOrWhiteSpace(name) ? dataTypeNames : $"{name}, {dataTypeNames}";
                }

                if (!string.IsNullOrWhiteSpace(SelectedRegionCode))
                {
                    name = string.IsNullOrWhiteSpace(name) ? SelectedRegionCode : $"{name}, {SelectedRegionCode}";
                }

                if (!string.IsNullOrWhiteSpace(SelectedIncomeGroup))
                {
                    name = string.IsNullOrWhiteSpace(name) ? SelectedIncomeGroup : $"{name}, {SelectedIncomeGroup}";
                }

                if (SelectedCountries.Any())
                {
                    string countryString = string.Join(",", SelectedCountries);
                    name = string.IsNullOrWhiteSpace(name) ? countryString : $"{name}, {countryString}";
                }

                if (StartYear.HasValue)
                {
                    string startYearString = $"Start:{StartYear.Value}";
                    name = string.IsNullOrWhiteSpace(name) ? startYearString : $"{name}, {startYearString}";
                }

                if (EndYear.HasValue)
                {
                    string endYearString = $"End:{EndYear.Value}";
                    name = string.IsNullOrWhiteSpace(name) ? endYearString : $"{name}, {endYearString}";
                }

                if (!string.IsNullOrWhiteSpace(SearchKeyword))
                {
                    string searchKeywordString = $"keyword:{SearchKeyword}";
                    name = string.IsNullOrWhiteSpace(name) ? searchKeywordString : $"{name}, {searchKeywordString}";
                }

                if (LastSelectedNutritions.Count > 0)
                {
                    string lastValue = LastSelectedNutritions.LastOrDefault();
                    name = string.IsNullOrWhiteSpace(name) ? lastValue : $"{name}, {lastValue }";
                }

                if(SelectedSearchTopicIds!=null && SelectedSearchTopicIds.Any())
                {
                    var value = _pageConfigrationCache.GetRecordValueAsync("search", SearchPageConfigurationKey.SearchTopics).Result;
                    string selectedTopics = $"{value}:{ SelectedSearchTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if(SelectedPolicyTopicName!=null && SelectedPolicyTopicIds.Any())
                {
                    string selectedTopics = $"{"Policy topics"}:{ SelectedPolicyTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if(SelectedActionTopicName!=null && SelectedActionTopicIds.Any())
                {
                    string selectedTopics = $"{"Action topics"}:{ SelectedActionTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if(SelectedMechanismTopicName!=null && SelectedMechanismTopicIds.Any())
                {
                    string selectedTopics = $"{"Mechanism topics"}:{ SelectedMechanismTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if (!string.IsNullOrEmpty(SelectedPolicyTypes) && PolicyTypeIds != null && PolicyTypeIds.Any())
                {
                    var value = "Policy types";
                    string key = $"{value}:{ SelectedPolicyTypes}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedMechanismTypes) && MechanismTypeIds != null && MechanismTypeIds.Any())
                {
                    var value = "Mechanism types";
                    string key = $"{value}:{ SelectedMechanismTypes}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedPartners) && PartnerIds != null && PartnerIds.Any())
                {
                    var value = "Partners";
                    string key = $"{value}:{ SelectedPartners}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedLanguages) && LanguageIds != null && LanguageIds.Any())
                {
                    var value = "Languages";
                    string key = $"{value}:{ SelectedLanguages}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedProgramTypes) && ProgramTypeIds != null && ProgramTypeIds.Any())
                {
                    var value = "Program types";
                    string key = $"{value}:{ SelectedProgramTypes}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedFundingSource) && FundingSourceIds != null && FundingSourceIds.Any())
                {
                    var value = "Funding source";
                    string key = $"{value}:{ SelectedFundingSource}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedTargetGroup) && TargetGroupIds != null && TargetGroupIds.Any())
                {
                    var value = "Target group";
                    string key = $"{value}:{ SelectedTargetGroup}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedDeliveryChannels) && DeliveryChannelIds != null && DeliveryChannelIds.Any())
                {
                    var value = "Delivery channel";
                    string key = $"{value}:{ SelectedDeliveryChannels}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedProblemsAndSolutions) && ProblemIds != null && ProblemIds.Any())
                {
                    var value = "Problem & Solution";
                    string key = $"{value}:{ SelectedProblemsAndSolutions}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                if (!string.IsNullOrEmpty(SelectedICN2Actions) && ICN2Ids != null && ICN2Ids.Any())
                {
                    var value = "ICN2 actions";
                    string key = $"{value}:{ SelectedICN2Actions}";
                    name = string.IsNullOrWhiteSpace(name) ? key : $"{name}, {key}";
                }
                return name;
            }
        }

        public string UniqueName
        {
            get
            {
                string name = string.Empty;

                if (DataTypes.Count > 0)
                {
                    string dataTypeNames = string.Join(",", DataTypes);
                    name = string.IsNullOrWhiteSpace(name) ? dataTypeNames : $"{name}, {dataTypeNames}";
                }

                if (!string.IsNullOrWhiteSpace(SelectedRegionCode))
                {
                    name = string.IsNullOrWhiteSpace(name) ? SelectedRegionCode : $"{name}, {SelectedRegionCode}";
                }

                if (!string.IsNullOrWhiteSpace(SelectedIncomeGroup))
                {
                    name = string.IsNullOrWhiteSpace(name) ? SelectedIncomeGroup : $"{name}, {SelectedIncomeGroup}";
                }

                if (SelectedCountries.Any())
                {
                    string countryString = string.Join(",", SelectedCountries);
                    name = string.IsNullOrWhiteSpace(name) ? countryString : $"{name}, {countryString}";
                }

                if (StartYear.HasValue)
                {
                    name = string.IsNullOrWhiteSpace(name) ? StartYear.Value.ToString() : $"{name}, {StartYear.Value.ToString()}";
                }

                if (EndYear.HasValue)
                {
                    name = string.IsNullOrWhiteSpace(name) ? EndYear.Value.ToString() : $"{name}, {EndYear.Value.ToString()}";
                }

                if (!string.IsNullOrWhiteSpace(SearchKeyword))
                {
                    name = string.IsNullOrWhiteSpace(name) ? SearchKeyword : $"{name}, {SearchKeyword}";
                }

                if (LastSelectedNutritions.Count > 0)
                {
                    string topicNames = string.Join(",", LastSelectedNutritions);
                    name = string.IsNullOrWhiteSpace(name) ? topicNames : $"{name}, {topicNames}";
                }

                if (SelectedSearchTopicIds != null && SelectedSearchTopicIds.Any())
                {
                    string searchTopicName = $"search topic:{SelectedSearchTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? searchTopicName : $"{name}, {searchTopicName}";
                }
                if(SelectedPolicyTopicName!=null && SelectedPolicyTopicIds.Any())
                {
                    string selectedTopics = $"{"Policy topics"}:{ SelectedPolicyTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if(SelectedActionTopicName!=null && SelectedActionTopicIds.Any())
                {
                    string selectedTopics = $"{"Action topics"}:{ SelectedActionTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if(SelectedMechanismTopicName!=null && SelectedMechanismTopicIds.Any())
                {
                    string selectedTopics = $"{"Mechanism topics"}:{ SelectedMechanismTopicName}";
                    name = string.IsNullOrWhiteSpace(name) ? selectedTopics : $"{name}, {selectedTopics}";
                }
                if (MechanismTypeIds != null && MechanismTypeIds.Any())
                {
                    string searchKey = $"Mechanism types";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (PartnerIds != null && PartnerIds.Any())
                {
                    string searchKey = "Partners";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (LanguageIds != null && LanguageIds.Any())
                {
                    string searchKey = "Languages";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (ProgramTypeIds != null && ProgramTypeIds.Any())
                {
                    string searchKey = "Program types";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (FundingSourceIds != null && FundingSourceIds.Any())
                {
                    string searchKey = "Funding source";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (TargetGroupIds != null && TargetGroupIds.Any())
                {
                    string searchKey = "Target group";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (DeliveryChannelIds != null && DeliveryChannelIds.Any())
                {
                    string searchKey = "Delivery channel";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (ProblemIds != null && ProblemIds.Any())
                {
                    string searchKey = "Problem & Solution";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (ICN2Ids != null && ICN2Ids.Any())
                {
                    string searchKey = "ICN2 actions";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (CountryGroupSelectedCountries != null && CountryGroupSelectedCountries.Any())
                {
                    string searchKey = "Country group";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                if (CountryRegionSelectedCountries != null && CountryRegionSelectedCountries.Any())
                {
                    string searchKey = "Country region";
                    name = string.IsNullOrWhiteSpace(name) ? searchKey : $"{name}, {searchKey}";
                }
                return name;
            }
        }
    }
}