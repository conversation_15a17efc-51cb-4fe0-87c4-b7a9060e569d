﻿
using Gina2.DbModels;

namespace Gina2.Blazor.Models
{
    public class TermReference
    {
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Link { get; set; }
        public ICollection<TermReferenceDetail> TermDetails { get; set; }
    }

    public class TermReferenceDetail
    { 
        public int? Id { get; set; }
        public string Title { get; set; }
        public string CountryCodeList { get; set; }
        public string RegionCodeList { get; set; }
        public string CountryCode { get; set; }
        public string Link { get; set; }
        public string Type { get; set; }
        public string StartYear { get; set; }
    }
}
