﻿namespace Gina2.Blazor.Models
{
    public class PolicyCsv
    {
        public int? Policy_Id { get; set; }
        public string Title { get; set; }
        public string Iso3_Code { get; set; }
        public string Country_Names { get; set; }
        public string English_Title { get; set; }
        public string Subnational_GeographicArea { get; set; }
        public int? Policy_TypeId { get; set; }
        public string Other_Policy_Type { get; set; }
        public int? Language_Id { get; set; }
        public int? Start_Month { get; set; }
        public int? Start_Year { get; set; }
        public int? End_Month { get; set; }
        public int? End_Year { get; set; }
        public string Published_By { get; set; }
        public int? Published_Month { get; set; }
        public int? Published_Year { get; set; }
        public bool? Adopted { get; set; }
        public int? Adopted_Month { get; set; }
        public int? Adopted_Year { get; set; }
        public string Adopted_By { get; set; }
        public string Topics { get; set; }
        public string Policy_Extract_By_Original_Language { get; set; }
        public string Url { get; set; }
        public string Further_Notes { get; set; }
        public string References { get; set; }
        public string Pdfs { get; set; }
    }
}
