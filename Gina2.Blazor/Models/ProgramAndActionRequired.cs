﻿using Gina2.DbModels;
using System.ComponentModel.DataAnnotations;

namespace Gina2.Blazor.Models
{
    public class ProgramAndActionRequired
    {
        [Required(ErrorMessage = "Title is required")]
        public string Title { get; set; }

        [Required(ErrorMessage = "Program Type is required")]
        public int TypeId { get; set; }
        public ICollection<ProgrammeCountryMapItem> ProgrammeCountryMap { get; set; }

    }
}
