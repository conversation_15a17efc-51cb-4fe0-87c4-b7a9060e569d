﻿namespace Gina2.Blazor.Models
{
    public class TermDoubleTree
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public string ParentName { get; set; }
        public bool IsTopicParent { get; set; } = true;
        public bool IsPlusIcon { get; set; }
        public bool IsCheck { get; set; }
        public string ParentIdString { get; set; }
        public string ExistingChildIdString { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<TermDoubleTree> Child { get; set; } = new List<TermDoubleTree>();
        public int DragAndDropKey { get; set; }
    }
}
