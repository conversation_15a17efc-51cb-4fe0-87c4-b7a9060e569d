﻿namespace Gina2.Blazor.Models
{
    public class HomeFeatureDto
    {
        public int? Id { get; set; }
        public int? DragAndDropKey { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string BackgroundImage { get; set; }
        public string Base64 { get; set; }
        public string RedirectionUrl { get; set; }
        public  int HomeDraftId { get; set; }
        public bool IsLoading { get; set; } = false;
        public string ElementId => $"feature_Image_{Id}";
        public string DetailLink => $"/scorecards/{Id}";
        public byte[] ImageData { get; set; }
        public string ImageDivId => $"image_div_{ElementId}";
    }
}