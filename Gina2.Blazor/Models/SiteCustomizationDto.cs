﻿namespace Gina2.Blazor.Models
{
    public class SiteCustomizationDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string HeaderImage { get; set; }
        public string FooterImage { get; set; }
        public string Status { get; set; }
        public bool IsCurrentPublished { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string FooterText { get; set; }
        public string OrganizationLink { get; set; }
        public string ContactLink { get; set; }
        public string RefCreatedBy { get; set; }
        public string RefUpdatedBy { get; set; }

        public string HeaderImageFullPath { get; set; }
        public string FooterImageFullPath { get; set; }

        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdateDateTime { get; set; }
    }
}
