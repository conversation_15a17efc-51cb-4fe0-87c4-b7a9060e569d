﻿

using Newtonsoft.Json;

namespace Gina2.Blazor.Models.Pdf
{
    public class PolicyPdf
    {
        public string CombinedTitle { get; set; }
        
    }

    public class PolicyPdfData
    {
        public string Start_Date { get; set; }
        public string End_Date { get; set; }
        public string Published_by { get; set; }
        public string Published_Date { get; set; }
        public string Is_the_policy_document_adopted { get; set; }
        public string Adopted_Date { get; set; }
        public string Adopted_By { get; set; }
        public string Type_of_policy { get; set; }
    }

    public class PolicyPdfExtract
    { 
        public string PolicyTypeName { get; set; }
        public string PolicyExtractByOriginalLanguage { get; set; }

    }

    public class PolicyPdfDetails
    {
        public string URL_link { get; set; }
        public string Further_Notes { get; set; }
        public string File_upload { get; set; }
        public string Reference { get; set; }

    }
}
