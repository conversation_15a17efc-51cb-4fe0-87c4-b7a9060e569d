﻿namespace Gina2.Blazor.Models.Pdf
{
    public class CommitmentPdf
    {
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public string MinistryDepartment { get; set; }
        public string EndosedBy { get; set; }
        public string Event { get; set; }
        public string Description { get; set; }
        public string PlannedProgressMonitoring { get; set; }
        public string ResourceAllocation { get; set; }
        public string Links { get; set; }
        public string Notes { get; set; }
        public string Pdfs { get; set; }
    }
    public class SmartCommitmentPdf
    {
        public string Title { get; set; }
        public string EnglishTranslate { get; set; }
        public string Extract { get; set; }
        public int? Month { get; set; }
        public int? Year { get; set; }
    }
}
