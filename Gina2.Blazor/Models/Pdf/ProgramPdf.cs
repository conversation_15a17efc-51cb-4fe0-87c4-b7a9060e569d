﻿using Gina2.DbModels;

namespace Gina2.Blazor.Models.Pdf
{
    public class ProgramPdf
    {
        public string Title { get; set; }
        public string EnglishTranslatedTitle { get; set; }
        //public virtual ProgramType ProgramType { get; set; }
        public string TypeOther { get; set; }
        //public virtual Language Language { get; set; }
        public string Location { get; set; }
        public string BriefDescription { get; set; }
        public string References { get; set; }
        public string NewPolicy { get; set; }
        public string Cost { get; set; }
        public string Links { get; set; }
    }
    public class ActionPdf
    {

        private string programIdString { get; set; }

        private string topicIdString { get; set; }
        public string NewTopic { get; set; }
        public string StatusId { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public int? EndMonth { get; set; }
        public int? EndYear { get; set; }
        public string MicronutrientCompound { get; set; }
        public string AgeGroup { get; set; }
        public string Place { get; set; }
        public string OtherDelivery { get; set; }
        public string ImplementationDetails { get; set; }
        public string ImpactIndicators { get; set; }
        public string MeSystem { get; set; }
        public string TargetPopulation { get; set; }
        public string CoveragePercent { get; set; }
        public string CoverageTypeId { get; set; }
        public string Baseline { get; set; }
        public string PostIntervention { get; set; }
        public string SocialOther { get; set; }
        public string ElenaLink { get; set; }
        public string Solution0 { get; set; }
        public string Solution1 { get; set; }
        public string Solution2 { get; set; }
        public string Solution3 { get; set; }
        public string Solution4 { get; set; }
        public string Solution5 { get; set; }
        public string Solution6 { get; set; }
        public string Solution7 { get; set; }
        public string Solution8 { get; set; }
        public string Solution9 { get; set; }
        public string OtherProblems { get; set; }
        public string OtherLessons { get; set; }
        public string PersonalStory { get; set; }
    }
}
