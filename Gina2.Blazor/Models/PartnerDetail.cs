﻿namespace Gina2.Blazor.Models
{
    public sealed class PartnerDetail
    {
        public string PartnerName { get; set; }
        public int? PartnerCategoryId { get; set; }
        public List<PartnerOrder> PartnerData { get; set; }
        public string PartnerDetails { get; set; }
        public int Key { get; set; }

    }

    public sealed class PartnerOrder
    {
        public string Name { get; set; }
        public int DragAndDropKey { get; set; }

    }
}
