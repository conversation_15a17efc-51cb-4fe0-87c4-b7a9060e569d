﻿using CsvHelper.Configuration.Attributes;
using Gina2.DbModels;
using Newtonsoft.Json;

namespace Gina2.Blazor.Models.Scorecard
{
    public sealed class ScorecardScore
    {
        public string Score { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string ColorCode { get; set; }
        public string StyleClass { get; set; }
        public int? Pattern { get; set; }
        public int ScorecardCountryMapId { get; set; }
        public int? ScoreOrder { get; set; }
        public List<CountryColor> CountryColors { get; set; } = new List<CountryColor>();
        public List<CountryIso> countryIsoCode { get; set; }
        public List<CountryWiseEntity> CountryWisePolicies { get; set; }

        //public string CountriesString => string.Join(",", Countries);
    }

    public class CountryIso
    {
        public string Countries { get; set; }
        public string Iso3Code { get; set; }
    }

    public class ScorecardDownload
    {

        [JsonProperty(PropertyName = "Scorecard")]
        [Name("Scorecard")]
        public string ScorecardName { get; set; }


        [JsonProperty(PropertyName = "Title")]
        [Name("Title")]
        public string EntityTitle { get; set; }

        [JsonProperty(PropertyName = "Type")]
        [Name("Type")]
        public string EntityType { get; set; }

        public string Link { get; set; }

        [Name("WHO Region")]
        [JsonProperty(PropertyName = "WHO Region")]
        public string WhoRegion { get; set; }

        [Name("Country name")]
        [JsonProperty(PropertyName = "Country name")]
        public string CountryName { get; set; }

        [Name("ISO 3")]
        [JsonProperty(PropertyName = "ISO 3")]
        public string CountryIso3 { get; set; }

        [Name("Score")]
        public string Score { get; set; }

        [Name("Criteria")]
        public string Criteria { get; set; }

    }

    public class CountryColor
    {
        public string ISOCode { get; set; }
        public string ColorCode { get; set; }
        public string Country { get; set; }
        public int StripePattern { get; set; }

    }

    public class CountryWiseEntity
    {
        public string CountryName { get; set; }
        public string CountryCode { get; set; }
        public List<MapEntity> MapEntities { get; set; }
    }

    public class MapEntity
    {
        public int EntityId { get; set; }
        public string EntityTitle { get; set; }
        public string EntityType { get; set; }
    }
}
