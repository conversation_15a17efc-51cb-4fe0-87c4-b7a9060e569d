﻿namespace Gina2.Blazor.Models.Dashboard
{
    public class StatusWiseUser
    {
        public string UserId { get; set; }
        public string WorkFlowStatus { get; set; }
    }

    public class MyDraft
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Country { get; set; }
        public string Year { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Title { get; set; }
        public string Type { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
    }

    public class MyDelegate
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Country { get; set; }
        public string Year { get; set; }

        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }

        public string Type { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
    }

    public class OthersDraftsInReview
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
        public bool ShowUserList { get; set; } = false;
    }

    public class OthersDraftsInDelegate
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
        public bool ShowUserList { get; set; } = false;
    }

    public class OthersDraftsImReviewing
    {
        public int Id { get; set; }
        public int PolicyId { get; set; }
        public string PolicyVersionId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
        public bool ShowUserList { get; set; } = false;
    }

    public class OthersDraftsIPublished
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Lastupdate { get; set; }
        public string ModerateState { get; set; }
        public bool ShowUserList { get; set; } = false;
    }

    public class OthersDraftsSendForCorrection
    {
        public int Id { get; set; }
        public string PolicyVersionId { get; set; }
        public int PolicyId { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Country { get; set; }
        public string Notetcorrection { get; set; }
        public string Lastupdate { get; set; }
        public string Status { get; set; }
        public string Year { get; set; }
        public string ModerateState { get; set; }
        public bool ShowUserList { get; set; } = false;
    }
    public class DraftsCorrection
    {
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string Type { get; set; }
        public string Createdby { get; set; }
        public string Country { get; set; }
        public string Notetcorrection { get; set; }
        public string Lastupdate { get; set; }
        public string Status { get; set; }
        public string Year { get; set; }
        public bool ShowUserList { get; set; } = false;

    }

}
