﻿using Gina2.DbModels;

namespace Gina2.Blazor.Models
{
    public class HomeSliderDto
    {
        public int? Id { get; set; }
        public int? DragAndDropKey { get; set; }
        public string FirstTitle { get; set; }
        public string LastTitle { get; set; }
        public string SliderTitle { get; set; }
        public string BackgroundImage { get; set; }
        public string Base64 { get; set; }
        public string RedirectionUrl { get; set; }
        public string FirstColor { get; set; }
        public string LastColor { get; set; }
        public string SliderColor { get; set; }
        public int HomeDraftId { get; set; }
        public byte[] ImageData { get; set; }
        public string ElementId => $"slider_Image_{Id}";
        public string ImageDivId => $"image_div_{ElementId}";

        public static implicit operator HomeSliderDto(SliderItem v)
        {
            throw new NotImplementedException();
        }
    }
}
