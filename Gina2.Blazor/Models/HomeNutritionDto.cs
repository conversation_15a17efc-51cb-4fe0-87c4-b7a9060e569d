﻿namespace Gina2.Blazor.Models
{
    public class HomeNutritionDto
    {
        public int? Id { get; set; }
        public int? DragAndDropKey { get; set; }
        public string SliderTitle { get; set; }
        public string Description { get; set; }
        public string BackgroundImage { get; set; }
        public string Base64 { get; set; }
        public string RedirectionUrl { get; set; }
        public byte[] ImageData { get; set; }
        public int HomeDraftId { get; set; }
        public string ElementId => $"nutrient_Image_{Id}";
        public string ImageDivId => $"image_div_{ElementId}";
    }
}
