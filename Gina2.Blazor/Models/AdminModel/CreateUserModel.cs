﻿using Gina2.Blazor.Areas.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Models.AdminModel
{
    public class CreateUserModel
    {

        public string Id { get; set; }
        [Required]
        public string Role { get; set; }
        public IEnumerable<string> ApproverRegion { get; set; }
        [RequiredIfRoleIsApproverAttribute("Role")]
        public IEnumerable<string> ApproverCountries { get; set; }
    }

    public class RequiredIfRoleIsApproverAttribute : ValidationAttribute
    {
        private readonly string _otherProperty;
        public RequiredIfRoleIsApproverAttribute(string otherProperty)
        {
            _otherProperty = otherProperty;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var property = validationContext.ObjectType.GetProperty(_otherProperty);
            if (property == null)
            {
                return null;
            }
            var otherPropertyValue = property.GetValue(validationContext.ObjectInstance, null);

            if (otherPropertyValue as string == "Approver")
            {
                if (value == null || (value as IEnumerable<string>).Count() == 0 )
                {
                    var test = new ValidationResult("Please Select Country(ies)");
                    return new ValidationResult("Please Select Country(ies)", new[] { "ApproverCountries" });
                }
            }

            return null;
        }
    }

}