﻿using CsvHelper.Configuration.Attributes;
using System.Text.Json.Serialization;

namespace Gina2.Blazor.Models.AdminModel
{
    public class AppUserModel
    {
        public string Id { get; set; }
        [Ignore]
        public bool Check { get; set; }
        [Name("Last_Name")]
        public string LastName { get; set; }
        [Name("First_Name")]
        public string FirstName { get; set; }
        [JsonIgnore]
        public string FullName
        {
            get
            {
                return $"{FirstName} {LastName}";
            }
        }
        [Name("User_Name")]
        public string UserName { get; set; }
        public string Email { get; set; }
        public string Organization { get; set; }
        public string Status { get; set; }
        [Name("User_Roles")]
        public string UserRoles { get; set; }
        [Name("Approver_Region")]
        public string ApproverRegion { get; set; }
        [Name("Approver_Assigned_Country")]
        public string ApproverAssignedCountry { get; set; }
        [Name("Created_Date")]
        public DateTime CreatedDate { get; set; }
        public bool IsNewRegister { get; set; }
        public List<string> InterestedCountryList { get; set; } = new List<string>();
        public string InterestedCountry
        {
            get
            {
                if (InterestedCountryList.Any())
                {
                    return string.Join(",", InterestedCountryList.Take(2).ToList());
                }
                return string.Empty;
            }
        }
    }


    public class ApplicationUserCSVModel
    {
        [Name("First_Name")]
        public string FirstName { get; set; }
        [Name("Last_Name")]
        public string LastName { get; set; }
        [Name("User_Name")]
        public string UserName { get; set; }
        public string Email { get; set; }
        public string Organization { get; set; }
        public string Status { get; set; }
        [Name("User_Roles")]
        public string UserRoles { get; set; }
        [Name("Approver_Region")]
        public string ApproverRegion { get; set; }
        [Name("Approver_Assigned_Country")]
        public string ApproverAssignedCountry { get; set; }
        [Name("User_Interested_Country")]
        public string UserInterestedCountry { get; set; }
        [Name("Created_Date")]
        public DateTime CreatedDate { get; set; }
    }
    public class ApplicationUserSearchRequestModel
    {
        public string Search { get; set; }
        public string SerachContent
        {
            get
            {
                if (string.IsNullOrEmpty(Search))
                {
                    return null;
                }
                return Search?.ToLower();
            }
        }
        public string Status { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public int PageSize { get; set; } = 10;
        public int PageNo { get; set; } = 1;
        public string SortingColumn { get; set; } = "FullName";
        public string IsDescendingOrderType { get; set; }
        public bool IsDescendingOrder { get; set; } = false;
        public int TotalFilteredCount { get; set; }
    }

    public class UserModel
    {
        public string Id { get; set; }
        public string UserName { get; set; }
        public string DisplayName { get; set; }
    }

    public class NewRegisterUser
    {

        [Name("Email address to invite [inviteeEmail] Required")]
        public string Email { get; set; }

        [Name("Redirection url [inviteRedirectURL] Required")]
        public string ApplicationUrl { get; set; }

        [Name("Send invitation message (true or false) [sendEmail]")]
        public bool SendEmail { get; set; } = true;

        [Name("Customized invitation message [customizedMessageBody]")]
        public string MessageBody { get; set; }

    }

}
