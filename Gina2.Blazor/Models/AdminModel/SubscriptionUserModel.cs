﻿using Gina2.Core.Enums;
using Gina2.Core.Methods;
using Newtonsoft.Json;

namespace Gina2.Blazor.Models.AdminModel
{
    public class SubscriptionRequestModel
    {
        public SubscriptionType SubscriptionType { get; set; } 
        public DateTime?[] SelectedDateRange { get; set; }
        public bool IncludeRevisiedContent { get; set; }
    }
    public class SubscriptionUserModel
    {
        public string Email { get; set; }
        public List<string> TopicId { get; set; } = new List<string>();
        public List<string> CountryISO { get; set; } = new List<string>();
        public DateTime?[] SelectedDateRange { get; set; }
        public bool IncludeRevisiedContent { get; set; }
    }

    public class SubscriptionUserEmail
    {
        public string Email { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string SubscriptionLink { get; set; }

    }

    public class SubscriptionUserRequestModel
    {
        public string Email { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
    }
}
