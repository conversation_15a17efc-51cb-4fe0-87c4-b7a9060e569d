﻿using AntDesign;
using Gina2.Core.Methods;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Gina2.Blazor.Models.AdminModel
{
    public class ProfileUserModel
    {
        public string Id { get; set; }
        [Required(ErrorMessage = "First name is required")]
        [MinLength(2, ErrorMessage ="Name is too short.")]
        [StringLength(250, ErrorMessage = "Name is too long.")]
        [CustomValidation]
        public string FirstName { get; set; }
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(250, ErrorMessage = "Last Name is too long.")]
        [CustomValidation]
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        [Required(ErrorMessage = "Organization is required")]
        [StringLength(250, ErrorMessage = "Organization is too long.")]
        [CustomValidation]
        public string Organization { get; set; }
        public IEnumerable<string> UserInterestCountries { get; set; }
        public IEnumerable<int> UserInterestTopics { get; set; } = new List<int>();

        [Required(ErrorMessage = "Nationality is required")]
        public string Country { get; set; }
        public IEnumerable<string> UserInterestRegion { get; set; } = new List<string>();
        public List<string> ValidateCountries { get; set; }
        public List<int> ValidateTopics { get; set; }
        public string ValidateFirstName{ get; set; }
        public string ValidateLastName { get; set; }
        public string UserName { get; set; }
    }

    public class CustomValidationAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                string inputValue = value.ToString();
                //
                bool containsSpecialCharacters = RegexHelper.IsRegexMatch(inputValue, @"[^a-zA-Z0-9]");

                // Use a regular expression to check if the string contains HTML tags
                bool containsHtml = RegexHelper.IsRegexMatch(inputValue, @"<[^>]+>");

                if (containsHtml && containsSpecialCharacters)
                {
                    return new ValidationResult("HTML tags are not allowed.", new[] { validationContext.MemberName });
                }
            }
            // Implement your custom validation logic here
            // Return ValidationResult.Success if the validation passes
            // Return a new ValidationResult instance with an error message if the validation fails

            return ValidationResult.Success; // or ValidationResult.ErrorMessage
        }
    }

}
