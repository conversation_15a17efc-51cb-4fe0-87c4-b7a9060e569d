﻿using System.ComponentModel.DataAnnotations;

namespace Gina2.Blazor.Models.AdminModel
{
    public class IndicatorTypeModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Indicator code field is required")]
        [CustomValidation]
        public string IndicatorCode { get; set; }
        [Required(ErrorMessage = "Indicator name field is required")]
        [CustomValidation]
        public string IndicatorName { get; set; }
        [Required(ErrorMessage = "Filter criteria is required")]
        [CustomValidation]
        public string FilterCriteria { get; set; }
    }
}
