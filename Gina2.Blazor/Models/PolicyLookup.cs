﻿
namespace Gina2.Blazor.Models
{
    public class PolicyLookup
    {
        public bool IsChecked { get; set; }
        public string Name { get; set; }
        public string Symbol { get; set; }
        public static List<PolicyLookup> GetPolicyFilter()
        {
            return new List<PolicyLookup>() {
                new PolicyLookup() { IsChecked=true ,Name = "Policies",Symbol="P" },
                new PolicyLookup() { IsChecked=false ,Name = "Programmes and actions",Symbol="A" },
                new PolicyLookup() { IsChecked=false ,Name = "Mechanisms",Symbol="M" },
                new PolicyLookup() {IsChecked =false ,Name = "SMART commitments",Symbol="C" }
                };
        }
        public static List<string> GetPolicyLooksUp()
        {
            return new List<string>() {
                 "Policies",
                 "Programmes and actions",
                 "Mechanisms",
                 "SMART commitments" 
                };
        }
    }
    public class MapLegend
    {
        public string Color { get; set; }
        public string Value { get; set; }

        public static readonly string[] LegendData = {
            "Policies",
            "Programmes and actions",
            "Mechanisms",
            "SMART commitments"
        };

        public static readonly string[] LegendColor = {
            "mapLegendForward.png",
            "mapLegendBackward.png",
            "mapLegendmech.png",
            "mapLegendmechCommitments.png",
            "#FFFFFF"
        };
        public static List<MapLegend> GetLegendsData()
        {
            List<MapLegend> legends = new List<MapLegend>();
            for (int i = 0; i < LegendData.Count(); i++)
            {
                legends.Add(new MapLegend()
                {
                    Color = LegendColor.ElementAt(i),
                    Value = LegendData.ElementAt(i)

                });
            }
            return legends;
        }
    }
}
