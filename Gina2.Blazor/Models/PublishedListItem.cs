﻿namespace Gina2.Blazor.Models
{
    public sealed class PublishedListItem
    {
        public int Key { get; set; }
        public string Title { get; set; }
        public string EnglishTitle { get; set; }
        public string CombinedTitle { get { return Title + (string.IsNullOrEmpty(EnglishTitle) ? "" : $" [{EnglishTitle}]"); } }
        public string StartYear { get; set; }
        public string EndYear { get; set; }
        public string Category { get; set; }
        public string CategoryName { get; set; }
    }
}
