﻿namespace Gina2.Blazor.Models
{
    public class ProgramCsv
    {
        public int Id { get; set; }
        public int ProgramId { get; set; }
        public int? TopicId { get; set; }
        public string NewTopic { get; set; }
        public string StatusId { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public int? EndMonth { get; set; }
        public int? EndYear { get; set; }
        public string MicronutrientCompound { get; set; }
        public string AgeGroup { get; set; }
        public string Place { get; set; }
        public string OtherDelivery { get; set; }
        public string ImplementationDetails { get; set; }
        public string ImpactIndicators { get; set; }
        public string MeSystem { get; set; }
        public string TargetPopulation { get; set; }
        public string CoveragePercent { get; set; }
        public string CoverageTypeId { get; set; }
        public string Baseline { get; set; }
        public string PostIntervention { get; set; }
        public string SocialOther { get; set; }
        public string ElenaLink { get; set; }
        public string OtherProblems { get; set; }
        public string OtherLessons { get; set; }
        public string PersonalStory { get; set; }
        public string ProgrammeTitle { get; set; }
        public string EnglishTitle { get; set; }
        public string ProgrammeTypeIdString { get; set; }
        //public string OtherPolicyType { get; set; }
        public string LanguageIdString { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public string NewPolicy { get; set; }
        public string Cost { get; set; }
        public string Link { get; set; }
        public string Partner_Government_Details { get; set; }
        public string Partner_Un { get; set; }
        public string Partner_Un_Details { get; set; }
        public string Partner_Ngo { get; set; }
        public string Partner_Ngo_Details { get; set; }
        public string Partner_Donors { get; set; }
        public string Partner_Donors_Details { get; set; }
        public string Partner_Intergov { get; set; }
        public string Partner_Intgov_Details { get; set; }
        public string Partner_National_Ngo { get; set; }
        public string Partner_Nat_Ngo_Details { get; set; }
        public string Partner_Research { get; set; }
        public string Partner_Research_Details { get; set; }
        public string Partner_Private { get; set; }
        public string Partner_Private_Details { get; set; }
        public string Partner_Other { get; set; }
        public string Partner_Other_Details { get; set; }
    }
}
