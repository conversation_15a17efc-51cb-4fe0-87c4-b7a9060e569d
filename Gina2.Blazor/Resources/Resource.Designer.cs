﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Gina2.Blazor.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Gina2.Blazor.Resources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add {0}.
        /// </summary>
        public static string Master_Add {
            get {
                return ResourceManager.GetString("Master.Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  {0} deleted successfully!.
        /// </summary>
        public static string Master_Delete_Success {
            get {
                return ResourceManager.GetString("Master.Delete.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  saved successfully!.
        /// </summary>
        public static string Master_Save_Success {
            get {
                return ResourceManager.GetString("Master.Save.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mechanism added successfully!.
        /// </summary>
        public static string Mechanism_Add_Success {
            get {
                return ResourceManager.GetString("Mechanism.Add.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        public static string Mechanism_Delete_Confirmation {
            get {
                return ResourceManager.GetString("Mechanism.Delete.Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the Mechanism?.
        /// </summary>
        public static string Mechanism_Delete_Confirmation_Message {
            get {
                return ResourceManager.GetString("Mechanism.Delete.Confirmation.Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mechanism deleted successfully!.
        /// </summary>
        public static string Mechanism_Delete_Success {
            get {
                return ResourceManager.GetString("Mechanism.Delete.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Mechanism.
        /// </summary>
        public static string Mechanism_Edit_Title {
            get {
                return ResourceManager.GetString("Mechanism.Edit.Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mechanism updated successfully!.
        /// </summary>
        public static string Mechanism_Update_Success {
            get {
                return ResourceManager.GetString("Mechanism.Update.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy added successfully!.
        /// </summary>
        public static string Policy_Add_Success {
            get {
                return ResourceManager.GetString("Policy.Add.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        public static string Policy_Delete_Confirmation {
            get {
                return ResourceManager.GetString("Policy.Delete.Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the Policy?.
        /// </summary>
        public static string Policy_Delete_Confirmation_Message {
            get {
                return ResourceManager.GetString("Policy.Delete.Confirmation.Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy deleted successfully!.
        /// </summary>
        public static string Policy_Delete_Success {
            get {
                return ResourceManager.GetString("Policy.Delete.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Policy.
        /// </summary>
        public static string Policy_Edit_Title {
            get {
                return ResourceManager.GetString("Policy.Edit.Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy updated successfully!.
        /// </summary>
        public static string Policy_Update_Success {
            get {
                return ResourceManager.GetString("Policy.Update.Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome.
        /// </summary>
        public static string Welcome_Title {
            get {
                return ResourceManager.GetString("Welcome.Title", resourceCulture);
            }
        }
    }
}
