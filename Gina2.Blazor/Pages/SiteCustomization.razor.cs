﻿using Blazorise.DataGrid;
using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Helpers;
using Gina2.DbModels;
using Gina2.Services.LayoutCustomization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using Microsoft.JSInterop;
using Gina2.Core.Interface;

namespace Gina2.Blazor.Pages
{
    public partial class SiteCustomization: PageConfirgurationComponent
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private ILayoutCustomizationService _layoutCustomizationService { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }

        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        private bool IsLoading = false;

        private List<LayoutCustomization> layoutDrafts = new ();
        [Inject]
        private TimeZoneService timeZoneService { get; set; }
        
        string selectedTab = "Drafts";
        bool _visible = false;
        bool deleteDraftModalVisible = false;
        int draftToBeDeleted;
        public string Headerprev { get; set; }
        protected override async Task OnInitializedAsync()
        {
            await Task.Run(() => IsLoading = true);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                await GetAllDrafts();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;
            return Task.CompletedTask;
        }

        private async Task CreateDraft()
        {
            IsLoading = true;
            int newDraftId= await _layoutCustomizationService.CreateNewDraft(CurrentUserService.UserId);
            if (newDraftId>0)
            {
                NavigationManager.NavigateTo($"Layout/Drafts/{newDraftId}/Edit");
            }
            else
            {
                await OpenErrorToaster("Failed to edit layout page, please try agian.");
            }
            IsLoading = false;
        }

        private async Task<List<LayoutCustomization>> GetAllDrafts()
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);
            layoutDrafts = await _layoutCustomizationService.GetAllDrafts();
            using var _dbContext = DbFactory.CreateDbContext();
            var users = await _dbContext.Users.ToListAsync();

            foreach (var item in layoutDrafts)
            {
                var createbyUserObj = String.IsNullOrEmpty(item.CreatedBy) ? null : users.FirstOrDefault(w => w.Id == item.CreatedBy);
                var updatedbyUserObj = String.IsNullOrEmpty(item.UpdatedBy) ? null : users.FirstOrDefault(w => w.Id == item.UpdatedBy);

                item.RefCreatedBy = createbyUserObj == null ? "Admin" : createbyUserObj.FirstName + " " + createbyUserObj.LastName;
                item.RefUpdatedBy = updatedbyUserObj == null ? "Admin" : updatedbyUserObj.FirstName + " " + updatedbyUserObj.LastName;
                item.CreatedDateTime = await timeZoneService.GetLocalDateTime(item.DateCreated);
                item.UpdateDateTime = await timeZoneService.GetLocalDateTime(item.DateUpdated);
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
            return layoutDrafts;
        }

        private async Task DeleteDraft(int draftId)
        {
            deleteDraftModalVisible = false;
            var currentPublishedDraft = await _layoutCustomizationService.GetPublishedDraft();
            if (currentPublishedDraft.Id == draftId)
            {
                await InvokeAsync(StateHasChanged);
                await OpenInfoToaster("Published layout cannot be deleted");
            }
            else
            {
                bool success = await _layoutCustomizationService.DeleteDraft(draftId);
                await GetAllDrafts();
                await InvokeAsync(StateHasChanged);

                if (success)
                {
                    await OpenSuccessToaster("Draft deleted successfully");
                }
                else
                {
                    await OpenErrorToaster("Draft delete failed");
                }
            }
            
            await InvokeAsync(StateHasChanged);
        }

        private void ShowDeleteDraftModal(int draftId)
        {
            deleteDraftModalVisible = true;
            draftToBeDeleted = draftId;
        }
        private void HandleCancel(MouseEventArgs e)
        {
            Headerprev = "position:fixed;";
            _visible = false;
        }
        private async Task ShowPreview(int draftId)
        {
            Headerprev = "position:relative;";
            _visible = true;
            var siteLayoutSetting = await _layoutCustomizationService.GetById(draftId);
            await JsRuntime.InvokeAsync<object>("SetLayoutPreview", siteLayoutSetting);
            await InvokeAsync(StateHasChanged);

        }

        private void OnRowStyling(LayoutCustomization homeDraft, DataGridRowStyling styling)
        {
            if (homeDraft.IsCurrentPublished)
                styling.Style = "color: green;background: rgb(239 245 231); border: 1px solid rgb(255, 255, 255)!important;";
        }
    }
}