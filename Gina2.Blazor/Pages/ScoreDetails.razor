@page "/admin/Scorecards/Details/{ScorecardCountryMapId:int}/{IsoCode}"

@using Gina2.DbModels;

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3">@ScorecardCountry.FirstOrDefault()?.CountryName</Heading>
                    <Paragraph class="color-w">
                       @ScorecardCountry.FirstOrDefault()?.Score
                    </Paragraph>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="gina-70">

<Div Class="DataGrids">
            <DataGrid
              Class="_checkgrid"
              FixedHeaderDataGridMaxHeight="500px"
              FixedHeaderDataGridHeight="450px"
              TItem="@ScorecardCountyMappingItem"
              Data="@ScorecardCountry">
                <EmptyTemplate>
                    <Div>No data found.</Div>
                </EmptyTemplate>

                <DataGridColumns>
                    <DataGridColumn Caption="Title" Sortable="true" Field="@nameof(ScorecardCountyMappingItem.EntityTitle)" Width="50%">
                        <DisplayTemplate>
                            <NavLink href="@($"/policies/{context.EntityId}")">@context?.EntityTitle</NavLink>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn Caption="Type" Sortable="true" Field="@nameof(ScorecardCountyMappingItem.EntityType)" Width="50%" />

                </DataGridColumns>
            </DataGrid>
    
</Div>

</Container>
