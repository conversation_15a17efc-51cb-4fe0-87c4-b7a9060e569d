﻿@page "/admin/scorecards/scoretopic"
@using Gina2.Core.Models;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Domain.Terms;
@using Gina2.Core.Methods;
@inherits PageConfirgurationComponent
<Loader IsLoading="@IsLoading" />
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.HeadingPage).ConvertHtmlToPlainText())</PageTitle>
<Modal @bind-Visible="@SaveVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Do you want save selected topics?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@SaveScorecardTopics">Yes</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@ChangingType">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-3 pb-3">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Class="h-title" Size="HeadingSize.Is3"data-cy="ScoreCardPageHeader">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.HeadingPage))
                        <AdminEditbut Key="@ScoreCardTopicPageConfigurationKey.HeadingPage" />
                    </Heading>
                    <Paragraph Class="color-w subtitleediticon" data-cy="ScoreCardPageDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.PageDescription))
                        <AdminEditbut Key="@ScoreCardTopicPageConfigurationKey.PageDescription" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                         <BreadcrumbItem>
                             <BreadcrumbLink To="/" data-cy="HomeLink">Home</BreadcrumbLink>
                         </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Scorecard</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Score Topics</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Fluid Class="newdraft mt-3" Padding="Padding.Is0">
    <Container>
        <Div Class="form-newd">
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="ScoreCardScorecardLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardScorecardLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="ScoreCardScorecardToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardScorecardTooltip)">
                            <Button data-cy="ScoreCardScorecardToolTipBtn" Class="but-info _tooltip">
                                <Icon data-cy="ScoreCardScorecardIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ScoreCardTopicPageConfigurationKey.ScoreCardScorecardGroup" />
                    </FieldLabel>
                    <Select data-cy="SelectScoreCardOptions" TValue="int" SelectedValue="@ScorecardId" SelectedValueChanged="@OnScoreChaging">
                        @*<SelectItem data-cy="SelectScoreCard" Value="0">Select score card</SelectItem>*@
                        <Repeater Items="@ScorecardList">
                            <SelectItem data-cy=@($"{context.Title.Split(" ")[0]}Title") Value="@context.Id">@context.Title</SelectItem>
                        </Repeater>
                    </Select>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="ScoreCardTypeLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardTypeLabel)
                        <Tooltip data-cy="ScoreCardTypeToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardTypeTooltip)">
                            <Button data-cy="ScoreCardTypeToolTipBtn" Class="but-info _tooltip">
                                <Icon data-cy="ScoreCardTypeIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ScoreCardTopicPageConfigurationKey.ScoreCardTypeGroup" />

                    </FieldLabel>
                    <Select data-cy="SelectTypesOptions" TValue="int" SelectedValue="@TypeId" SelectedValueChanged="@OnTypeChaging">
                        @*<SelectItem Value="0" data-cy="SelectType">Select Type</SelectItem>*@
                        <Repeater Items="@TopicsType">
                            <SelectItem data-cy=@($"{context.Name.Split(" ")[0]}TopicName") Value="@context.Id">@context.Name</SelectItem>
                        </Repeater>
                    </Select>
                </Field>
            </Fields>
            <Div Class="_topicescorecard">
                <Div Class="_heading" Flex="Flex.JustifyContent.Between.AlignItems.Center">
                    <Heading data-cy="ScoreCardTopicHeading">
                        @PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardTopicLabel)
                        <Tooltip data-cy="ScoreCardTopicTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ScoreCardTopicPageConfigurationKey.ScoreCardTopicTooltip)">
                            <Button data-cy="ScoreCardTopicToolTipBtn" Class="but-info _tooltip">
                                <Icon data-cy="ScoreCardTopicIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ScoreCardTopicPageConfigurationKey.ScoreCardTopicGroup" />
                    </Heading>
                    @*<Button Class="but-clear" >Clear</Button>*@
                </Div>
                <Div Class="_body _Autocomplete">
                    @if (TopicList.Count > 0)
                    {
                        <Div Flex="Flex.JustifyContent.Between.AlignItems.Start" Class="pb-3">
                            <Autocomplete TItem="Term"
                                      TValue="Term"
                                      Data="@SearchingTerms"
                                      TextField="@(( item ) => item.Name)"
                                      ValueField="@(( item ) => item)"
                                      SelectedValueChanged="@(item => SelectedChangeTopic(item))"
                                      Placeholder="Search"
                                      SearchChanged="@OnSearchTopic"
                                      Filter="AutocompleteFilter.StartsWith"
                                      FreeTyping
                                      CustomFilter="@(( item, searchValue ) => item.Name.IndexOf( searchValue, 0, StringComparison.CurrentCultureIgnoreCase ) >= 0 )">
                                <NotFoundContent> Sorry... @context was not found! </NotFoundContent>
                                <ItemContent >
                                    @context.Item.Name
                                </ItemContent>
                            </Autocomplete>

                            <Div Class="pl-0">
                                <Button data-cy="ScoreCardSearchBtn" Class="but-yellow pl-2 pr-2 ml-1" Clicked="@GetSearchingTopicData">Search</Button>
                            </Div>
                        </Div>
                        @*<AntDesign.Search  @bind-Value="@searchKey" Placeholder="Search.." />*@
                        @*<Div Class="_taxon content _customTree" id="_searchElement">*@
                        <AntDesign.Tree @ref="topicTree"
                                    ShowIcon
                                    DefaultCheckedKeys="topicKeys"
                                    MatchedClass="site-tree-search-value"
                                    DataSource="TopicList"
                                    TItem="GTreeNode"
                                    TitleExpression="x => x.DataItem.Title"
                                    ChildrenExpression="x => x.DataItem.Children"
                                    KeyExpression="x => x.DataItem.Key"
                                    Checkable>
                            <TitleTemplate Context="ScoreData">
                                <Div data-cy=@($"{ScoreData.Title.Split(" ")[0]}ScoreDataTitle") id="@(!string.IsNullOrEmpty(SearchTopic.Name) && SearchTopic.Name == ScoreData.Title ? $"_searchscroll{SearchTopic.Id}" : "")"
                                 Style="@(!string.IsNullOrEmpty(SearchTopic.Name) && SearchTopic.Name == ScoreData.Title ? "color:orange" : "")">
                                    @ScoreData.Title
                                </Div>
                            </TitleTemplate>
                        </AntDesign.Tree>
                        @*</Div>*@

                    }
                </Div>
            </Div>
        </Div>
    </Container>
    <Container Class="mt-4 pb-6">
        <Button data-cy="ScoreCardSaveBtn" Class="but-blues" Clicked="@SaveScorecardTopics">Save</Button>
        <Button data-cy="ScoreCardExportBtn" Loading="@ExportLoading" Class="but-blues" Clicked="@DownloadScoreExcel">Export</Button>
        @*<Button Class="but-reset" >Reset</Button>*@
    </Container>
</Container>

<style>
    .site-tree-search-value {
        color: #f50;
    }
</style>