﻿using Gina2.Blazor.Models;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Gina2.Blazor.Pages
{
    public partial class ProgrammeAndActionList
    {
        [Inject]
        private IProgrammeService ProgrammeAndActionService { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        public readonly string Title = "Programmes and actions";
        public readonly string TabTitle = "programmes and actions";
        public readonly string TypeofTitle = "View by action area";
        public readonly string TableTitle = "Programme / action  - target group";
        public readonly string AdminTitle = "Programmes and actions";

        public readonly string DetailUrlContext = "programmes-and-actions";
        public string CsvFields = "Programme_Id,Iso3code,Programme_Title";

        private List<PublishedListItem> actions = new();
        private List<TypeList> actionTypes = new();
        public string CsvKeys { get; set; }
        public List<string> CsvData { get; set; } = new();
        public string FileName = "gina2_programmesandactions";

        private bool isLoading = true;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                var actionList = await ProgrammeAndActionService.GetProgrammeAndActionsAsync(CountryCode);
                List<ProgramCsv> ProgrammeactionCsv = actionList.Select(x => new ProgramCsv()
                {
                    ProgramId = x.ProgramId,
                    TopicId = x.TopicId,
                    NewTopic = x.NewTopic,
                    StatusId = x.StatusId,
                    StartMonth = x.StartMonth,
                    StartYear = x.StartYear,
                    EndMonth = x.EndMonth,
                    EndYear = x.EndYear,
                    MicronutrientCompound = x.MicronutrientCompound,
                    AgeGroup = x.AgeGroup,
                    Place = x.Place,
                    OtherDelivery = x.OtherDelivery,
                    ImplementationDetails = x.ImplementationDetails,
                    ImpactIndicators = x.ImpactIndicators,
                    MeSystem = x.MeSystem,
                    TargetPopulation = x.TargetPopulation,
                    CoveragePercent = x.CoveragePercent,
                    CoverageTypeId = x.CoverageTypeId,
                    Baseline = x.Baseline,
                    PostIntervention = x.Baseline,
                    SocialOther = x.SocialOther,
                    ElenaLink = string.IsNullOrEmpty(x.ElenaLink) ? "-" : $"\"{x.ElenaLink}\"",
                    OtherProblems = string.IsNullOrEmpty(x.OtherProblems) ? "-" : $"\"{x.OtherProblems}\"",
                    OtherLessons = string.IsNullOrEmpty(x.OtherLessons) ? "-" : $"\"{x.OtherLessons}\"",
                    PersonalStory = string.IsNullOrEmpty(x.PersonalStory) ? "-" : $"\"{x.PersonalStory}\"",
                    ProgrammeTitle = string.IsNullOrEmpty(x.Program.Title) ? "-" : $"\"{x.Program.Title}\"",
                    EnglishTitle = string.IsNullOrEmpty(x.Program.EnglishTranslatedTitle) ? "-" : $"\"{x.Program.EnglishTranslatedTitle}\"",
                    ProgrammeTypeIdString = x.Program.TypeId.ToString(),
                    LanguageIdString = x.Program.LanguageId.ToString(),
                    Location = string.IsNullOrEmpty(x.Program.Location) ? "-" : $"\"{x.Program.Location}\"",
                    Description = string.IsNullOrEmpty(x.Program.BriefDescription) ? "-" : $"\"{x.Program.BriefDescription}\"",
                    NewPolicy = x.Program.NewPolicy,
                    Cost = x.Program.Cost,
                    Link = x.Program.Links

                }).ToList();

                if (ProgrammeactionCsv.Count > 0)
                {
                    var dictionary = JObject.FromObject(ProgrammeactionCsv[0]).ToObject<Dictionary<string, string>>();
                    CsvKeys = string.Join(",", dictionary.Select(a => $"\"{a.Key}\""));
                    foreach (var item in ProgrammeactionCsv)
                    {
                        var dictionaryvalues = JObject.FromObject(item).ToObject<Dictionary<string, string>>();
                        string CsvValue = string.Join(",", dictionaryvalues.Select(a => $"{a.Value}"));
                        CsvData.Add(CsvValue);
                    }
                }
                var ProgrammeAndActionTypes = await ProgrammeAndActionService.GetProgrammeActionTypesAsync();

                foreach (var item in actionList)
                {
                    if(item.ProgramId != 0 && item.Program.TypeId != null) {
                        string title = await GetProgramAndActionTitle(item);
                        actions.Add(new PublishedListItem()
                        {
                            Key = item.Id,
                            Title = title,
                            StartYear = item.StartYear?.ToString() ?? "-",
                            EndYear = item.EndYear?.ToString() ?? "-",
                            Category = item.Program.TypeId.ToString(),
                            CategoryName = ProgrammeAndActionTypes.FirstOrDefault(e => e.Id == item.Program.TypeId)?.Name,
                        });
                    }
                    
                }
                actions = actions.OrderByDescending(a => a.StartYear).ToList();
                foreach (var item in ProgrammeAndActionTypes)
                {
                    actionTypes.Add(new TypeList()
                    {
                        Id = item.Id,
                        Name = item.Name
                    });

                }

                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }
        private async Task<string> GetProgramAndActionTitle(Gina2.DbModels.Action actionItem)
        {
            StringBuilder title = new StringBuilder(); ;
            title.Append($"{actionItem.Program.Title} / {actionItem.Topic?.Name}");
            foreach (var item in actionItem.ActionTargetGroups)
            {
                if (item.TargetGroup != null) {
                    title.Append($"-{item.TargetGroup.Name}");
                }
                        
            }

            return title.ToString();

        }
    }
}
