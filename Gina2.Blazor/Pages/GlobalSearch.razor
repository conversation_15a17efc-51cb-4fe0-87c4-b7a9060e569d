﻿@page "/search"
@using Domain.PolicyTypes
@using Domain.Search
@using Domain.Terms
@using Gina2.Blazor.Helpers;
@using Gina2.Core.Constant
@using Json.Net;
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Blazor.Models;
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
@using System.Text.RegularExpressions;
@inherits PageConfirgurationComponent
@inject IAppState AppState;
@inject ProtectedSessionStorage ProtectedSessionStore;
@using Gina2.Core.Methods;
@*   <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem href="admin/contents/search-edit">Edit</DropdownItem>
                <DropdownItem href="#">Translate</DropdownItem>
            </DropdownMenu>
        </Dropdown>*@
<PageTitle>GIFNA @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SearchHeading).ConvertHtmlToPlainText())</PageTitle>
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SearchHeading))
                        <AdminEditbut Key="@SearchPageConfigurationKey.SearchHeading" />
                    </Heading>
                    <CardText Class="color-w">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SubHeader))
                        <AdminEditbut Key="@SearchPageConfigurationKey.SubHeader" />
                    </CardText>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container>
    <Layout Sider Class="search-box aboutus pt-4 pb-7 mob-layout">
        <Layout Class="left-layout pr-3" Style="position:relative;">
            <LayoutContent>
                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex">
                    <Div Class="item1">
                        <Heading Size="HeadingSize.Is3">
                            <span Class="_SearchResults">Search result - </span>
                            @if (string.IsNullOrWhiteSpace(TotalRecordMessage))
                            {
                                <div class="loader"></div>
                            }
                            else
                            {

                                <span>@TotalRecordMessage</span>

                            }
                        </Heading>
                    </Div>
                    <Div Class="item2 _but_hig">
                        @* <Button Class="but-yellow mr-1 pl-1 pr-1 mob-t-hide wid-mnin" Clicked="@OnToggleSelectAll">
                        @(searchRequest.IsAllSelected ? "Clear" : "Select All")
                        </Button>*@
                        <Button Class="but-by-yellow text-by-yellow pl-1 pr-1 mob-t-hide" Clicked="@OnMapExportClicked"><Icon Class="fa-solid fa-map" /> Display result in map</Button>
                    </Div>
                </Div>
                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex">
                    @if (SearchHistories.Any())
                    {
                        <Breadcrumb Class="_Search bread-crumb">
                            Search history :-&nbsp;
                            @{
                                charCount = 0;
                            }
                            <Repeater Items="@SearchHistories.OrderBy(w=>w.Order)" Context="historyContext">
                                @{
                                    charCount = charCount + @historyContext.Name.Length;
                                    string history = Regex.Replace(historyContext.Name, "<.*?>", String.Empty);
                                }
                                @if (charCount < 200)
                                {
                                    <BreadcrumbItem>
                                        <BreadcrumbLink onclick="@(() => LoadSearchHistory(historyContext.Order))">@history</BreadcrumbLink>
                                    </BreadcrumbItem>
                                }
                            </Repeater>
                            @if (SearchHistories.Select(w => w.Name.Length).Sum() > 200)
                            {
                                <button class="_more" @onclick="@Toggle">...</button>
                            }
                            <Div hidden="@(!HideMore)" Class="hidemore">
                                <button class="_more _lessmore" @onclick="@Toggle">show less</button>
                                <Breadcrumb Class="_Search bread-crumb">
                                    <Repeater Items="@SearchHistories" Context="historyContext">
                                        @{
                                            string history = Regex.Replace(historyContext.Name, "<.*?>", String.Empty);

                                        }
                                        <BreadcrumbItem>
                                            <BreadcrumbLink onclick="@(() => LoadSearchHistory(historyContext.Order))">@history</BreadcrumbLink>
                                        </BreadcrumbItem>
                                    </Repeater>
                                </Breadcrumb>
                                <button class="_more _lessmore" @onclick="@Toggle">show less</button>
                            </Div>
                        </Breadcrumb>
                    }
                </Div>
                @* <Div Class="_Togglelessmore" hidden="@(!HideMore)" @onclick="@Toggle"></Div> *@
                <DataGridforSearch @ref="DataGridforSearchchild"
                                   FileType="SearchPage"
                                   SendDataForFileDownloadEvent="@GetDataFromDataGrid"
                                   SendCountInforomSpToParentEvent="@GetDataCount">
                    @* SendDataFromSpToParentEvent="@GetDataFromSpFromChild"*@
                    @*  <DataGridforSearch @ref="DataGridforSearchchild"
                    FileType="SearchPage"
                    SendDataToParentEvent="@GetDataFromChild"
                    SendCountInfoToParentEvent="@GetDataCount"
                    SendDataForFileDownloadEvent="@GetDataFromDataGrid"
                    SendDataFromSpToParentEvent="@GetDataFromSpFromChild">*@
                </DataGridforSearch>
                <FileDownload FileType="SearchPage" @ref="FileDownloadChild" SearchHistories="@SearchHistories" ShowCSVPanel="@ShowCSVPanel">
                </FileDownload>
                <Typemap />
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>
                <Div Class="HedFilters _adfilter" id="GeneralFilter">
                    <AdminEditbut Key="@SearchPageConfigurationKey.GeneralFilters" />
                    <Heading Flex="Flex.JustifyContent.Between" onclick="@(()=>ToggleGeneralFilterMode())">
                        <span class="_goto">
                            @(!isGeneralFilter ? "Go to " : "") @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.GeneralFilters))
                        </span>
                        @if (!isGeneralFilter)
                        {
                            <Icon Class="fas fa-arrow-right" />
                        }
                    </Heading>
                </Div>
                <Loading_progress loaderVisibility="@searchLoaderVisibility" />
                @if (!isGeneralFilter)
                {
                    <Div Class="HedFilters _adfilter mt-1" id="AdvancedFilter">
                        <AdminEditbut Key="@SearchPageConfigurationKey.AdvancedFilters" />
                        <Heading Flex="Flex.JustifyContent.Between">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.AdvancedFilters))
                            </span>
                        </Heading>
                    </Div>
                }

                <Div Class="filters-Search" id="accordion2Example">
                    <Div class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.DataType))
                                <AdminEditbut Key="@SearchPageConfigurationKey.DataType" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"><Icon Clicked="@OnClearDataTypesClicked" Name="IconName.Redo" Class="_editicon" id="GeneralDataReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsethree" aria-expanded="true" aria-controls="collapsethree"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapsethree" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion2Example">
                            <Div Class="accordion-body">
                                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                    <span>Data (items)</span>
                                    <span>Countries</span>
                                </Div>
                                <AntDesign.Menu Class="_mapforoverlay _datatypes">
                                    @foreach (var item in PolicyLookups)
                                    {
                                        <Check Class="check-light-blue" TValue="bool" Checked="item.IsChecked"
                                               CheckedChanged="(e) => ToggleDataTypeSelection(item.Name,e)">
                                            <Span Flex="Flex.JustifyContent.Start">
                                                <Span Class="_nowrap">@item.Name &nbsp;</Span>
                                                <Span Flex="Flex.JustifyContent.Between" Class="w-100"><Span>(@GetDataTypeCount(item.Symbol))</Span><Span>@GetDataTypeCountryCount(item.Symbol)</Span></Span>
                                            </Span>
                                        </Check>
                                    }
                                </AntDesign.Menu>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                @*general filter *@
                <Div Class="filters-Search" id="accordionExample">
                    <Div class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Regions))
                                <AdminEditbut Key="@SearchPageConfigurationKey.Regions" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearRegionsClicked" Name="IconName.Redo" Class="_editicon" id="GeneralRegionsReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                            <Div Class="accordion-body">

                                <AntDesign.Select TItemValue="string"
                                                  DataSource="@AllRegions"
                                                  Placeholder="Select a WHO region"
                                                  TItem="CountryWithRegion"
                                                  ValueName="RegionCode"
                                                  LabelName="RegionCode"
                                                  OnSelectedItemChanged="@OnRegionChanged"
                                                  @bind-Value="searchRequest.SelectedRegionCode"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 13px; margin-top:8px;">
                                    <ItemTemplate Context="regionContext">
                                        <span>
                                            @GetRegionName(regionContext.RegionCode) @(!string.IsNullOrWhiteSpace(regionContext.RegionDescription) ? "- " + regionContext.RegionDescription : "")
                                        </span>
                                    </ItemTemplate>
                                </AntDesign.Select>

                                <AntDesign.Select TItem="string"
                                                  Placeholder="Select an income group"
                                                  TItemValue="string"
                                                  DataSource="@AllIncomGroups"
                                                  @bind-Value="@searchRequest.SelectedIncomeCode"
                                                  OnSelectedItemChanged="OnIncomeGroupChanged"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 8px;">
                                    <ItemTemplate Context="inconeGroupContext">
                                        <span>
                                            @GetIncomeGroup(inconeGroupContext)
                                        </span>
                                    </ItemTemplate>
                                </AntDesign.Select>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                <Div Class="filters-Search" id="accordion4Example">
                    <Div class="accordion">

                        <Div class="_alltopic-header">
                            <span>@((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Countries)) <AdminEditbut Key="@SearchPageConfigurationKey.Countries" /></span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearCountryClicked" Name="IconName.Redo" Class="_editicon" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsefive" aria-expanded="true" aria-controls="collapsefive"> </button></span>
                            </span>
                        </Div>

                        <Div id="collapsefive" class="accordion-collapse collapse show" aria-labelledby="headingfive" data-bs-parent="#accordion4Example">
                            <Div Class="accordion-body">
                                <AntDesign.Select @key="@fullCountryDetail.Count"
                                                  DataSource="@fullCountryDetail"
                                                  Mode="multiple"
                                                  TItemValue="string"
                                                  TItem="CountryWithRegion"
                                                  LabelName="@nameof(CountryWithRegion.CountryWithDescription)"
                                                  ValueName="@nameof(CountryWithRegion.CountryName)"
                                                  OnSelectedItemsChanged="OnSelectedCountries"
                                                  Placeholder="Select one or more countries"
                                                  AllowClear
                                                  Values="searchRequest.SelectedCountries"
                                                  EnableSearch>
                                </AntDesign.Select>

                            </Div>
                        </Div>
                    </Div>
                </Div>

                <Div Class="filters-Search" id="accordion1Example" disabled="@IsTopicDDDisabled">
                    <Div Class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.PublishedYear))
                                <AdminEditbut Key="@SearchPageConfigurationKey.PublishedYear" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearPublishedYearClicked" Name="IconName.Redo" Class="_editicon" id="GeneralPublichedReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapsetwo" Class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion1Example">
                            <Div class="accordion-body" disabled="true">
                                <Div Flex="Flex.JustifyContent.Between">
                                    <AntDesign.DatePicker Class="w-48" TValue="DateTime?" Picker="year" Placeholder="@Startplace" Value="@searchRequest.StartDate" ValueChanged="e=>ChangeStartYear(e)" DisabledDate="@(date => date >= searchRequest.EndDate)" />
                                    <AntDesign.DatePicker Class="w-48" TValue="DateTime?" Picker="year" Placeholder="@Endplace" Value="@searchRequest.EndDate" ValueChanged="e=>ChangeEndYear(e)" DisabledDate="@(date => date <= searchRequest.StartDate)" />
                                </Div>

                            </Div>
                        </Div>
                    </Div>
                </Div>

                @if (TopicsInSearch.Any())
                {
                    <Div Class="filters-Search" id="accordionSerchTree">
                        <Div class="accordion">
                            <Div class="_alltopic-header">
                                <span style="font-weight: 600;">
                                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SearchTopics))
                                    <AdminEditbut Key="@SearchPageConfigurationKey.SearchTopics"></AdminEditbut>
                                </span>
                                <span class="flex-center">
                                    <Tooltip Text="Reset"><Icon Clicked="@OnClearSearchTopics" Name="IconName.Redo" Class="_editicon" id="GeneralTopicReset" /></Tooltip>
                                    <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSearchTree" aria-expanded="true" aria-controls="collapsethree"> </button></span>
                                </span>
                            </Div>
                            <Div id="collapseSearchTree" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionSerchTree">
                                <Div Class="accordion-body">
                                    <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata" Style="margin-bottom:3px;">
                                        <span>Data (items)</span>
                                        <span>Countries </span>
                                    </Div>
                                    <AntDesign.Tree ShowIcon
                                                    @ref="searchTree"
                                                    MatchedClass="site-tree-search-value"
                                                    DataSource="TopicsInSearch"
                                                    KeyExpression="x => x.DataItem.Key"
                                                    TItem="GTreeNode"
                                                    OnCheck="x => SearchTreeCheckboxClicked(x)"
                                                    TitleExpression="x =>
                                                            {
                                                            return x.DataItem.Title;
                                                            }"
                                                    ChildrenExpression="x => x.DataItem.Children"
                                                    Checkable>
                                        <TitleTemplate Context="basicTopicContext">
                                            <Div Class="_gsearchitem">
                                                <Span>
                                                    @basicTopicContext.DataItem.Title
                                                    @if (!basicTopicContext.DataItem.IsLoading)
                                                    {
                                                        <Span>
                                                            @($" ({(GetBasicTopicCount(basicTopicContext.DataItem.TopicId, basicTopicContext.ParentNode))})")
                                                        </Span>
                                                    }
                                                </Span>
                                                @if (basicTopicContext.DataItem.IsLoading)
                                                {
                                                    <Span> <AntDesign.Icon Type="loading" Theme="outline" /></Span>
                                                }
                                                else
                                                {
                                                    <Span> @GetBasicTopicCountryCount(basicTopicContext.DataItem.TopicId, basicTopicContext.ParentNode)</Span>
                                                }
                                            </Div>
                                        </TitleTemplate>
                                    </AntDesign.Tree>
                                </Div>
                            </Div>
                        </Div>
                    </Div>
                }
                @if (EnableAdvancedSearch)
                {
                    <Div Class="HedFilters _adfilter" id="AdvancedFilter">
                        <AdminEditbut Key="@SearchPageConfigurationKey.AdvancedFilters" />

                        <Heading Flex="Flex.JustifyContent.Between" onclick="@(()=>ToggleAdvanceFilterMode())">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.AdvancedFilters))
                            </span> <Icon Class="fas fa-arrow-right" />
                        </Heading>
                    </Div>
                }
            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Container>