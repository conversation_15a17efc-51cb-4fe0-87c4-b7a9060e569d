﻿h@page "/home-moderate"

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">History of About GIFNA</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="#">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Moderate</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
                <Div Class="item2">
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Class="aboutmenu" Split />
                        <DropdownMenu>
                            <DropdownItem href="home-edit">Edit</DropdownItem>
                            <DropdownItem href="home-moderate">Moderate</DropdownItem>
                            <DropdownItem href="home-translate">Translate</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="gina-70">

<Div Class="table-responsive">
                    <Table Class="table-nth">
                        <TableHeader ThemeContrast="ThemeContrast.Dark">
                            <TableRow>
                                <TableHeaderCell>Revision</TableHeaderCell>
                                <TableHeaderCell>Title</TableHeaderCell>
                                <TableHeaderCell>Date</TableHeaderCell>
                                <TableHeaderCell>Revision Action</TableHeaderCell>
                                <TableHeaderCell>Moderation Action</TableHeaderCell>
                            </TableRow>
                        </TableHeader>
                         <TableBody>
        <TableRow>
            <TableRowCell>139193</TableRowCell>
            <TableRowCell><Heading Size="HeadingSize.Is3">Home</Heading>
                          <Paragraph>Edited by <Span>engesveenk</Span></Paragraph>
                          <Paragraph>Revised by <NavLink href="#">engesveenk</NavLink></Paragraph>
            </TableRowCell>
            <TableRowCell>06/04/2021 - 08:43</TableRowCell>
            <TableRowCell>
            <Tooltip Text="View">
                <Button><Icon Name="IconName.Eye" /></Button>
            </Tooltip>
            <Tooltip Text="New Draft">
                <Button> <Icon Name="IconName.Edit" /></Button>
            </Tooltip>

            </TableRowCell>
            <TableRowCell><Heading Size="HeadingSize.Is3">This is the published revision.</Heading>
            <Paragraph Class="alink"><NavLink href="#">Unpublish</NavLink></Paragraph>
            <Paragraph Class="date">From Draft --> Published on 10/05/2021 - 14:19</Paragraph>
            <Paragraph>by <NavLink href="#">engesveenk</NavLink></Paragraph>
            </TableRowCell>
        </TableRow>
    </TableBody>
                    </Table>
                </Div>
</Container>
