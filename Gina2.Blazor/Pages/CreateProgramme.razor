﻿@page "/admin/programme/create"
@using Gina2.Blazor.Helpers.PageConfigrationData
<PageTitle>GIFNA Enter programme / action(s)</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                 <Container Class="ginasearch pt-5 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item0 pl-1">
                            <Heading Size="HeadingSize.Is3" data-cy="ProgrammeTitle">
                                Enter programme / action(s)
                                @*@((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.Title))
                                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.Title" />*@
                            </Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink data-cy="ProgrammeHomeLink" To="/">Home</BreadcrumbLink>
                                </BreadcrumbItem>
                                 <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#" data-cy="BreadcrumbLinkContent">Content</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink data-cy="ProgrammeLink" To="#">Programme / action(s)</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Padding="Padding.Is0">
            <ProgramCreateOrEdit />
        </Container>
    </Authorized>
</AuthorizeView>
