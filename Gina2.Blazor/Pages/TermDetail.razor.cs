using AutoMapper;
using Gina2.Blazor.Models;
using Microsoft.AspNetCore.Components;
using Domain.Terms;
using Gina2.Services.Topic;
using Gina2.DbModels;
using Gina2.MySqlRepository.Models;
using Gina2.Core;
using Gina2.Services.Vocabulary;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.AspNetCore.Authorization;
using Blazorise.DataGrid;
using Gina2.Services.Models;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Dynamic;
using Gina2.Core.Methods;
using System.Linq;
using Gina2.Services.Programme;
using Blazorise.Utilities;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class TermDetail
    {
        [Inject] 
        private IMapper _mapper { get; set; }

        [Inject]
        private ITopicService TopicService { get; set; }

        [Inject]
        private IProgrammeService programService { get; set; }
        [Inject]
        private IVocabularyService vocabularyService { get; set; }
        [Parameter]
        public string Vocabulary { get; set; }

        [Parameter]
        public string Id { get; set; }

        private TermReference termReferences { get; set; } = new();
        private List<TermReferenceDetail> Detail { get; set; } = new();
        private List<TopicReference> TopicReferences { get; set; } = new();
        private List<TopicReference> TopicReferencesData { get; set; } = new();
        private List<TopicReference> DefaultTopicReferencesData { get; set; } = new();
        public List<Term> Terms { get; set; } = new();
        //private IEnumerable<Topic> AllTopics { get; set; }
        private IEnumerable<TopicParent> AllParentTopics { get; set; }
        private IEnumerable<Gina2.DbModels.PolicyTopic> PolicyTopics { get; set; }
        private IEnumerable<Gina2.DbModels.MechanismTopic> MechanismTopics { get; set; }
        private int TopicId { get; set; }
        private bool isLoadingTerms = true;
        private int CurrentPage = 1;
        private int PageSize = 50;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                TopicReferences = DefaultTopicReferencesData = await GetTermReferenceAsync();
                termReferences.Name = TopicReferences.FirstOrDefault()?.VocabularyName?? await GetTermNameAsync()?? string.Empty;
                Pagination();
                isLoadingTerms = false;
                StateHasChanged();
            }
        }

        public async Task<List<TopicReference>> GetTermReferenceAsync()
        {
            return await vocabularyService.GetDetailsRefernceAsync(Vocabulary, Id);
        }

        public async Task<string> GetTermNameAsync()
        {
            return Vocabulary switch
            {
                Constants.VocabularyTables.Coordination => await vocabularyService.GetNameById<Coordination>(int.Parse(Id)),
                Constants.VocabularyTables.Monitoring => await vocabularyService.GetNameById<Monitoring>(int.Parse(Id)),
                Constants.VocabularyTables.Country => await vocabularyService.GetNameById<Country>(Id),
                Constants.VocabularyTables.Region => await vocabularyService.GetNameById<Region>(Id),
                Constants.VocabularyTables.Delivery => await vocabularyService.GetNameById<Delivery>(int.Parse(Id)),
                Constants.VocabularyTables.Icn2Category => await vocabularyService.GetNameById<Icn2Category>(int.Parse(Id)),
                Constants.VocabularyTables.Icn2 => await vocabularyService.GetNameById<Icn2>(int.Parse(Id)),
                Constants.VocabularyTables.Language => await vocabularyService.GetNameById<Language>(int.Parse(Id)),
                Constants.VocabularyTables.MechanismType => await vocabularyService.GetNameById<MechanismType>(int.Parse(Id)),
                Constants.VocabularyTables.Micronutrient => await vocabularyService.GetNameById<Micronutrient>(int.Parse(Id)),
                Constants.VocabularyTables.Partners => await vocabularyService.GetNameById<Partner>(int.Parse(Id)),
                Constants.VocabularyTables.PartnerCategory => await vocabularyService.GetNameById<PartnerCategory>(int.Parse(Id)),
                Constants.VocabularyTables.ProgramType => await vocabularyService.GetNameById<ProgramType>(int.Parse(Id)),
                Constants.VocabularyTables.ProblemType => await vocabularyService.GetNameById<ProblemType>(int.Parse(Id)),
                Constants.VocabularyTables.Area => Id,
                Constants.VocabularyTables.Sdg => await vocabularyService.GetNameById<Sdg>(int.Parse(Id)),
                Constants.VocabularyTables.TargetGroup => await vocabularyService.GetNameById<TargetGroup>(int.Parse(Id)),
                Constants.VocabularyTables.Topic => await vocabularyService.GetNameById<DbModels.Topic>(int.Parse(Id)),
                Constants.VocabularyTables.PolicyType => await vocabularyService.GetNameById<PolicyType>(int.Parse(Id)),
                Constants.VocabularyTables.IncomeGroup => await vocabularyService.GetNameById<IncomeGroup>(Id),
                Constants.VocabularyTables.CountryGroup => await vocabularyService.GetNameById<CountryGroup>(int.Parse(Id)),
                _ => string.Empty,
            };
        }


        private async Task OnReadData(DataGridReadDataEventArgs<TopicReference> e)
        {
            isLoadingTerms = true;
            CurrentPage = e.Page;
            PageSize= e.PageSize;
            //TopicReferences = TopicReferences.OrderBy(d => d[e.Columns].ToString()).ToList();
            Pagination();
            isLoadingTerms = false;
            await InvokeAsync(StateHasChanged);
            //}
        }

        private void Pagination()
        {
            TopicReferencesData = TopicReferences.Skip((Convert.ToInt16(CurrentPage) - 1) * PageSize)
                        .Take(PageSize)
                       .ToList();
        }

        void SortingTermRefernce(DataGridSortChangedEventArgs e)
        {
            isLoadingTerms = true;
            if (e.SortDirection == Blazorise.SortDirection.Default)
            {
                TopicReferences = DefaultTopicReferencesData;
            }
            else {
                bool descending = e.SortDirection == Blazorise.SortDirection.Descending ? true : false;
                TopicReferences = TopicReferences.OrderByDynamic(e.FieldName, descending).ToList();
            }
            
        }
    }
}