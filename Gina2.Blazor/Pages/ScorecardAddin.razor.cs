using Microsoft.AspNetCore.Components;
using Gina2.Services.Policy;
using Gina2.Services.Topic;
using Gina2.DbModels;
using Gina2.Core.Models;
using Gina2.Services.ScoreCarad;
using AntDesign;
using Blazorise.Snackbar;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Services.Vocabulary;
using Domain.Terms;
using Microsoft.JSInterop;
using ClosedXML.Excel;
using Newtonsoft.Json;
using static Gina2.Core.Constants;
using Domain.CsvDetails;
using Gina2.Services.Programme;
using Gina2.Services.Mechanism;
using Gina2.Services.Commitments;
using static Gina2.Core.Enums.ScorecardHelper;
using Gina2.Core.Enums;
using Microsoft.AspNetCore.Authorization;
using System.Data;
using System.IO;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class ScorecardAddin : PageConfirgurationComponent
    {
        [Inject]
        private ICommitmentService CommitmentService { get; set; }
        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Inject]
        private IProgrammeService ProgramService { get; set; }

        [Inject]

        private IWebHostEnvironment Environment { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        public IVocabularyService VocabularyService { get; set; }

        [Inject]
        private IScoreCardService ScorecardService { get; set; }

        [Inject]
        private IPolicyService PolicyService { get; set; }
        [Inject]
        public ITopicService TopicService { get; set; }

        [Inject]
        private IScorecardTopicService scorecardTopicService { get; set; }

        [Inject]
        private IScorecardCountryService scorecardCountryService { get; set; }

        private bool SaveVisible { get; set; }
        public List<DbModels.Topic> TopicsType { get; set; }

        private List<Topic> AllTopics = new();
        private List<Term> TypeTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private List<GTreeNode> TopicList = new List<GTreeNode>();
        private int TypeId { get; set; }
        private Tree<GTreeNode> topicTree;

        private int ScorecardId { get; set; } = 0;
        private string[] topicKeys;
        private string[] ChecktopicKeys;
        private string searchKey;
        private bool ExportLoading { get; set; }
        private List<ScorecardTopic> AddTopics { get; set; } = new();
        private List<ScorecardTopic> RemoveTopics { get; set; } = new();
        public List<DbModels.Scorecard> ScorecardList { get; set; }
        public List<Term> SearchingTerms { get; set; } = new();
        private bool IsScorllSearch { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (IsScorllSearch)
            {
                await JsRuntime.InvokeVoidAsync("ScoreToView", $"_searchscroll{SearchTopic.Id}");
                IsScorllSearch = false;
            }
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                ScorecardList = await ScorecardService.GetAllAsync();
                TopicsType = await TopicService.GetAllTopicGrandParentAsync();
                var topicSearch = TopicsType.FirstOrDefault(t => t.Name.Equals("Search topics"));
                TopicsType.Remove(topicSearch);
                TypeId = TopicsType.Count > 0 ? TopicsType.FirstOrDefault(t => t.Name == "Policy").Id : 0;
                ScorecardId = ScorecardList.Count > 0 ? (int)ScorecardList?.FirstOrDefault()?.Id : 0;
                var selectedTopics = await scorecardTopicService.ScorecardTopics(ScorecardId, TopicsType.FirstOrDefault(t => t.Name == "Policy")?.Name);
                topicKeys = selectedTopics?.Select(t => $"{t.ParentId}-{t.TopicId}").ToArray();
                ChecktopicKeys = selectedTopics?.Select(t => $"{t.ParentId}-{t.TopicId}").ToArray();
                await GetTopicsAsync();

                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            _ = base.OnAfterRenderAsync(firstRender);
        }

        
        private async Task OnTypeChaging(int typeId)
        {
            TypeId = typeId;
            if (AddTopics.Any() || RemoveTopics.Any())
            {
                SaveScorecardTopics();
            }
            await ChangingType();

            //if (AddTopics.Any())
            //{
            //    SaveVisible = true;
            //}
            //else
            //{
            // await ChangingType();
            //}
        }

        private async Task ChangingType()
        {
            IsLoading = true;
            TopicList = new();
            await GetTopicAndParent();
            var First = AllParentTopics.Where(x => x.ParentId.Equals(TypeId)).OrderBy(t => t.OrderKey);
            var selectedTopics = await scorecardTopicService.ScorecardTopics(ScorecardId, TopicsType.FirstOrDefault(t => t.Id == TypeId).Name);
            topicKeys = selectedTopics?.Select(t => $"{t.ParentId}-{t.TopicId}").ToArray();
            await GetTopicTreeView(First);
            IsLoading = false;
        }

        private async Task OnScoreChaging(int scoreId)
        {
            IsLoading = true;
            ScorecardId = scoreId;
            TopicList = new();
            var selectedTopics = await scorecardTopicService.ScorecardTopics(ScorecardId, TopicsType.FirstOrDefault(t => t.Id == TypeId).Name);
            topicKeys = selectedTopics?.Select(t => $"{t.ParentId}-{t.TopicId}").ToArray();
            await GetTopicsAsync();
            IsLoading = false;
            await InvokeAsync(StateHasChanged);

        }
        private async Task GetTopicsAsync()
        {
            //TopicList = new();
            await GetTopicAndParent();
            var First = AllParentTopics.Where(x => x.ParentId.Equals(TypeId)).OrderBy(t => t.OrderKey);
            await GetTopicTreeView(First);
        }

        private async Task GetTopicAndParent()
        {
            AllTopics = await PolicyService.GetTopicsOnlyAsync();
            AllParentTopics = await PolicyService.GetParentTopics();
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> First)
        {
            foreach (var item in First)
            {
                TypeTopics.Add(new Term()
                {
                    Name = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Id = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Id
                });
                TopicList.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Key = $"{item.ParentId}-{item.TopicId}",
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId.ToString()),
                    IsSelected = false
                });
            }
        }

        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<TopicParent> First, string parentKey)
        {
            List<GTreeNode> child = new List<GTreeNode>();
            foreach (var item in First)
            {
                TypeTopics.Add(new Term()
                {
                    Name = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Id = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Id
                });
                GTreeNode itemchild = new GTreeNode();
                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Key = $"{parentKey}-{item.TopicId}",
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId.ToString()),
                    IsSelected = false
                });
            }
            return child;
        }


        private async Task SaveScorecardTopics()
        {
            List<string> selectedTopics = topicTree.CheckedKeys.ToList();
            var scorecardTopics = selectedTopics.Select(x => new ScorecardTopic()
            {
                ScorecardId = ScorecardId,
                TopicId = int.Parse(x.Split("-")[1]),
                ParentId = int.Parse(x.Split("-")[0]),
                CreatedBy = "",
                EntityType = TopicsType.FirstOrDefault(t => t.Id == TypeId).Name,
                DateCreated = DateTimeOffset.UtcNow,
                DateUpdated = DateTimeOffset.UtcNow,
            }).ToList();
            bool isTopicSave = await scorecardTopicService.SaveTopic(scorecardTopics, ScorecardId);
            AddTopics = new();
            RemoveTopics = new();
            SaveVisible = false;
            if (isTopicSave)
            {
                await OpenSuccessToaster("Topics saved successfully");
            }
            //await ChangingType();
        }

        private async Task OnSearchTopic(string name)
        {
            if (!string.IsNullOrEmpty(name))
            {
                SearchingTerms = TypeTopics.Where(t => t.Name.Contains(name)).DistinctBy(x => x.Name).ToList();
            }
        }

        private Term SearchTopic { get; set; } = new Term();
        private async Task SelectedChangeTopic(Term term)
        {
            if (term != null)
            {
                SearchTopic = term;
            }

        }
        private void GetSearchingTopicData()
        {
            topicTree.SearchValue = SearchTopic == null ? string.Empty : SearchTopic.Name;
            IsScorllSearch = true;

        }
        private async Task DownloadScoreExcel()
        {
            ExportLoading = true;
            var path = Path.Combine(Environment.WebRootPath, "Export", "Template.xlsx");
            if (ScorecardId != 0)
            {
                ScoreExcelSheets create = new ScoreExcelSheets();
                var patterns = ScorecardHelper.patterns;
                using (var workbook = new XLWorkbook(path))
                {
                    var name = ScorecardList.FirstOrDefault(s => s.Id == ScorecardId);
                    var scorecardExcelDetails = await scorecardTopicService.GetScorecardExcelDeatil(ScorecardId);
                    var scoreColor = scorecardExcelDetails.DistinctBy(x => x.Score).ToList();
                    int scoreIndex = 1;
                    foreach (var item in create.SheetNameList)
                    {
                        var worksheet = workbook.Worksheet(item.Name);
                        worksheet.ConditionalFormats.RemoveAll();
                        if (item.Name == "GIFNA population count")
                        {
                            worksheet.Cell(1, 1).Value = name.PopulationPercentage / 100;
                            worksheet.Cell(1, 2).Value = name.PopulationDescription;
                        }

                        if (item.Name == "GIFNA map display")
                        {
                            var dt = await scorecardCountryService.GetMapColorsByScore(ScorecardId);
                           
                            if (dt.Item3.Any())
                            {
                                for (int row = dt.Item3.Count+2; row <= 20; row++)
                                {
                                    worksheet.Row(row).Clear();
                                }
                            }

                            int scoreMapIndex = 0;
                            int colorIndex = 0;
                            int patternIndex = 0;
                            if (scorecardExcelDetails.Any())
                            {
                                foreach (var score in dt.Item3)
                                {
                                    worksheet.Cell(scoreMapIndex + 2, 1).Value = score;
                                    scoreMapIndex++;
                                }
                            }
                            foreach (var color in dt.Item1)
                            {
                                worksheet.Cell(colorIndex + 2, 2).Value = color;
                                colorIndex++;

                            }

                            foreach (var pattern in dt.Item2)
                            {
                                worksheet.Cell(patternIndex + 2, 3).Value = string.IsNullOrEmpty(pattern) || pattern == "0" ? "" : ScorecardHelper.patterns[int.Parse(pattern) - 1];
                                patternIndex++;

                            }
                        }

                        if (item.Name == "GIFNA CSV table")
                        {
                            var scoreCountryTable = await scorecardCountryService.GetCountryTableContent(ScorecardId);

                            if (scoreCountryTable != null)
                            {
                                var _scoreCountry = JsonConvert.DeserializeObject<DataTable>(scoreCountryTable.Content);
                                var _scoreCountryheader = JsonConvert.DeserializeObject<List<string>>(scoreCountryTable.Header);

                                List<string> header = _scoreCountryheader != null ? _scoreCountryheader : new List<string>();
                                int index = 0;

                                foreach (var column in header)
                                {
                                    index++;
                                    worksheet.Row(1).Row(index, index).Value = header[index - 1];
                                }
                                worksheet.Cell(2, 1).InsertData(_scoreCountry.AsEnumerable());
                            }
                            

                        }

                        if (item.Name == "Table to display")
                        {
                            var (header, dt) = await scorecardCountryService.GetCountriesByScore(ScorecardId);
                                int index = 0;

                                foreach (var column in header)
                                {
                                    index++;
                                    worksheet.Row(1).Row(index, index).Value = header[index - 1];
                                }
                                worksheet.Cell(2, 1).InsertData(dt);
                            
                            worksheet.CellsUsed().Style.Alignment.WrapText = true;
                        }

                        if (item.Name == "GIFNA main table CSV")
                        {
                            var dt = await scorecardCountryService.GetTypeDetailsByScore(ScorecardId);
                            worksheet.Cell(2, 1).InsertData(dt);

                        }

                        if (item.Name == "Data input 1")
                        {
                            List<string> Row1 = new List<string>() {
                            "Field", "Row1", "Row2", "Row3", "Row4", "Row5", "Row6", "Row7"
                            };
                            List<string> Row2 = new List<string>() {
                            "Data", "policyid", "Iso3CountryCode", "CountryName", "PolicyTitle", "PolicyType",
                            "StartYear", "StartMonth"
                            };

                            List<string> Row3 = new List<string>() {
                            "Condition", "", "", "", "", "",
                            "", ""
                            };
                            List<string> Row4 = new List<string>() {
                            "TaxonomyId", "", "", "", "", "",
                            "", ""
                            };
                            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(ScorecardId, "Policy");
                            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).Distinct().ToList();
                            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).Distinct().ToList();

                            for (int i = 0; i < ScorecardTopicsNames.Count; i++)
                            {
                                Row1.Add("Column");
                                Row2.Add("topic");
                                Row3.Add(ScorecardTopicsNames[i]);
                                Row4.Add(scorecardTopics[i].ToString());
                            }
                            int index = 0;
                            foreach (var column in Row2)
                            {
                                index++;
                                worksheet.Row(1).Row(index, index).Value = Row1[index - 1];
                                worksheet.Row(2).Row(index, index).Value = Row2[index - 1];
                                worksheet.Row(3).Row(index, index).Value = Row3[index - 1];
                                worksheet.Row(4).Row(index, index).Value = Row4[index - 1];
                            }
                            var dt = await PolicyService.CreatePoliciesDataTable(scorecardTopics, ScorecardTopicsNames);
                            worksheet.Cell(5, 1).InsertData(dt);

                        }

                        if (item.Name == "Data input 2")
                        {
                            List<string> Row1 = new List<string>() {
                            "Field", "Row1", "Row2", "Row3", "Row4", "Row5", "Row6", "Row7"
                            };
                            List<string> Row2 = new List<string>() {
                            "Data", "action_id", "Iso3CountryCode", "CountryName", "programme_title", "programme_type",
                            "StartYear", "StartMonth"
                            };

                            List<string> Row3 = new List<string>() {
                            "Condition", "", "", "", "", "",
                            "", ""
                            };
                            List<string> Row4 = new List<string>() {
                            "TaxonomyId", "", "", "", "", "",
                            "", ""
                            };
                            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(ScorecardId, "Action");
                            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).ToList();
                            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).ToList();

                            for (int i = 0; i < ScorecardTopicsNames.Count; i++)
                            {
                                Row1.Add("Column");
                                Row2.Add("topic");
                                Row3.Add(ScorecardTopicsNames[i]);
                                Row4.Add(scorecardTopics[i].ToString());
                            }
                            int index = 0;
                            foreach (var column in Row2)
                            {
                                index++;
                                worksheet.Row(1).Row(index, index).Value = Row1[index - 1];
                                worksheet.Row(2).Row(index, index).Value = Row2[index - 1];
                                worksheet.Row(3).Row(index, index).Value = Row3[index - 1];
                                worksheet.Row(4).Row(index, index).Value = Row4[index - 1];
                            }
                            var dt = await ProgramService.CreateActionDataTable(scorecardTopics, ScorecardTopicsNames);
                            worksheet.Cell(5, 1).InsertData(dt);
                        }

                        if (item.Name == "Data input 3")
                        {
                            List<string> Row1 = new List<string>() {
                            "Field", "Row1", "Row2", "Row3", "Row4", "Row5", "Row6", "Row7"
                            };
                            List<string> Row2 = new List<string>() {
                            "Data", "mechanism_id", "iso3code", "countryname", "mechanism_title", "mechanism_type", "start_year", "start_month"
                            };

                            List<string> Row3 = new List<string>() {
                            "Condition", "", "", "", "", "",
                            "", ""
                            };
                            List<string> Row4 = new List<string>() {
                            "TaxonomyId", "", "", "", "", "",
                            "", ""
                            };
                            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(ScorecardId, "Mechanism");
                            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).ToList();
                            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).ToList();

                            for (int i = 0; i < ScorecardTopicsNames.Count; i++)
                            {
                                Row1.Add("Column");
                                Row2.Add("topic");
                                Row3.Add(ScorecardTopicsNames[i]);
                                Row4.Add(scorecardTopics[i].ToString());
                            }
                            int index = 0;
                            foreach (var column in Row2)
                            {
                                index++;
                                worksheet.Row(1).Row(index, index).Value = Row1[index - 1];
                                worksheet.Row(2).Row(index, index).Value = Row2[index - 1];
                                worksheet.Row(3).Row(index, index).Value = Row3[index - 1];
                                worksheet.Row(4).Row(index, index).Value = Row4[index - 1];
                            }
                            var dt = await MechanismService.CreateMechanismDataTable(scorecardTopics, ScorecardTopicsNames);

                            worksheet.Cell(5, 1).InsertData(dt);
                        }

                        if (item.Name == "Data input 4")
                        {
                            List<string> Row1 = new List<string>() {
                            "Field", "Row1", "Row2", "Row3", "Row4", "Row5", "Row6"
                            };
                            List<string> Row2 = new List<string>() {
                            "Data", "CommitmentId", "iso3code", "countryname", "commitment_title", "start_year", "start_month"
                            };

                            List<string> Row3 = new List<string>() {
                            "Condition", "", "", "", "", "",
                            ""
                            };
                            List<string> Row4 = new List<string>() {
                            "TaxonomyId", "", "", "", "", "",
                            ""
                            };
                            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(ScorecardId, "Commitment");
                            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).ToList();
                            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).ToList();

                            for (int i = 0; i < ScorecardTopicsNames.Count; i++)
                            {
                                Row1.Add("Column");
                                Row2.Add("topic");
                                Row3.Add(ScorecardTopicsNames[i]);
                                Row4.Add(scorecardTopics[i].ToString());
                            }
                            int index = 0;
                            foreach (var column in Row2)
                            {
                                index++;
                                worksheet.Row(1).Row(index, index).Value = Row1[index - 1];
                                worksheet.Row(2).Row(index, index).Value = Row2[index - 1];
                                worksheet.Row(3).Row(index, index).Value = Row3[index - 1];
                                worksheet.Row(4).Row(index, index).Value = Row4[index - 1];
                            }
                            var dt = await CommitmentService.CreateCommitmentDataTable(scorecardTopics, ScorecardTopicsNames);

                            worksheet.Cell(5, 1).InsertData(dt);
                        }



                    }
                    var workbookBytes = new byte[0];
                    using (var ms = new MemoryStream())
                    {
                        ms.Position = 0;
                        workbook.SaveAs(ms);
                        workbookBytes = ms.ToArray();
                        string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
                        await JsRuntime.InvokeVoidAsync("ExcelDownload", $"GIFNA-{name.Title}-{currentDate}.xlsx", Convert.ToBase64String(workbookBytes, 0, workbookBytes.Length));

                    }
                }
            }
            ExportLoading = false;
        }
    }
}