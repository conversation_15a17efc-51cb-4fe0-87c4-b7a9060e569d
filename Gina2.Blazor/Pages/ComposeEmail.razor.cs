﻿using AntDesign;
using DocumentFormat.OpenXml.Wordprocessing;
using Domain.Programme;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.CacheService;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Blazor.Shared;
using Gina2.Core.Enums;
using Gina2.Core.Interface;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.Services.Models;
using Gina2.Services.Search;
using Gina2.SPs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using NuGet.Packaging.Licenses;
using System;
using System.Buffers.Text;
using System.Data.Entity.Core.Objects;
using System.Globalization;
using System.Net;
using System.Security.Cryptography.Xml;
using System.Text;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class ComposeEmail : PageConfirgurationComponent
    {
        [Inject]
        private IWebHostEnvironment environment { get; set; }
        [Inject]
        public ISearchService SearchService { get; set; }
        [Inject]
        public IEmailServices EmailServices { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }
        [Inject]
        public NavigationManager _navigationManager { get; set; }

        [Inject]
        public IConfiguration Configuration { get; set; }
        public string Emails { get; set; }
        public bool IsEmailsError { get; set; }

        public bool EnableEmailSection { get; set; }
        public bool IsLoading { get; set; }
        public SubscriptionRequestModel SubscriptionRequestModel { get; set; } = new SubscriptionRequestModel();
        public List<SubscriptionUserModel> SubscriptionUserModels { get; set; } = new List<SubscriptionUserModel>();
        public bool IsSubscriptionTypeError { get; set; }

        public List<SubscriptionUserEmail> SubscriptionUserEmails { get; set; } = new List<SubscriptionUserEmail>();
        public List<CombinedSearchResult> SubscriberContentResult { get; set; } = new List<CombinedSearchResult>();
        private GlobalSearchRequest searchRequest = new();

        private _quillEditor quillEditorRef;
        public string inputValue { get; set; }
        private bool IsSelectedDateRangeError;
        private bool IsEmailSubjectError;

        private bool ThemesVisible { get; set; }
        private string EmailSubject { get; set; }

        private int ElementToShow { get; set; }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                StateHasChanged();
            }
        }
        private async Task ToggleDataTypeSelection(bool isChecked)
        {
            SubscriptionRequestModel.IncludeRevisiedContent = isChecked;
        }

        void HandleChange(SubscriptionType subscriptionType)
        {
            SubscriptionRequestModel.SubscriptionType = subscriptionType;
            if (SubscriptionRequestModel.SubscriptionType == SubscriptionType.Other)
            {
                SubscriptionRequestModel.IncludeRevisiedContent = false;
                for (int i = 0; i < SubscriptionRequestModel.SelectedDateRange.Length; i++)
                {
                    SubscriptionRequestModel.SelectedDateRange[i] = null;
                }
                StateHasChanged();
            }
        }
        private async Task PreviewBtnClick()
        {
            IsEmailsError = IsEmailSubjectError = false;
            if (string.IsNullOrEmpty(Emails))
            {
                IsEmailsError = true;
                StateHasChanged();
                return;
            }
            if (string.IsNullOrEmpty(EmailSubject))
            {
                IsEmailSubjectError = true;
                StateHasChanged();
                return;
            }
            var body = await quillEditorRef.GetHTML();

            GenerateSampleContentData(body);
            ThemesVisible = true;
        }
        private void PreviewBtnClicked()
        {
            if (ElementToShow != 0)
            {
                --ElementToShow;
                StateHasChanged();
            }
        }
        private void NextBtnClicked()
        {
            if (ElementToShow != SubscriptionUserEmails.Count)
            {
                ++ElementToShow;
                StateHasChanged();
            }
        }

        private void GenerateSampleContentData(string body)
        {
            IEnumerable<CombinedSearchResult> results = [];
            
            if (SubscriptionType.Other == SubscriptionRequestModel.SubscriptionType)
            {
                SubscriptionUserModels = Emails.Split(';').Select(email => new SubscriptionUserModel
                {
                    Email = email.Trim(),
                }).ToList();
            }
            
            foreach (var item in SubscriptionUserModels)
            {
                StringBuilder templete = new();
                templete.Append(body);
                
                switch (SubscriptionRequestModel.SubscriptionType)
                {
                    case SubscriptionType.Country:
                        results = SubscriberContentResult.Where(e => item.CountryISO.Contains(e.CountryList)).Take(5);
                        break;
                    case SubscriptionType.Topic:
                        results = SubscriberContentResult.Where(e => item.TopicId.Contains(e.TopicIdList)).Take(5);
                        break;
                }
                
                if (results.Any())
                {
                    templete.Append(GenerateHtmlTable(results));
                }
                
                SubscriptionUserEmails.Add(new SubscriptionUserEmail
                {
                    Email = item.Email,
                    Subject = EmailSubject,
                    Body = templete.ToString(),
                    SubscriptionLink = SerializeUrl(item.CountryISO,
                                                    item.TopicId,
                                                    SubscriptionRequestModel.SelectedDateRange,
                                                    SubscriptionRequestModel.IncludeRevisiedContent)
                });
            }

        }        
        
        private async Task SendToAllBtnClicked()
        {
            IsLoading = true;

            try
            {
                string baseUrl = Configuration["App:SelfUrl"];
                              
                foreach (var item in SubscriptionUserEmails)
                {
                    SubscriptionEmailModel emailModel = new()
                    {
                        BaseUrl = baseUrl,
                        EmailBody = item.Body,
                        SubscriptionLink = SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType ? item.SubscriptionLink : string.Empty,                                        
                        ContactAddress = string.Empty
                    };

                    await EmailServices.SendEmailByTemplateAsync(item.Email, item.Subject, TemplateType.Subscription, emailModel);
                }

                await OpenSuccessToaster("email send successfully");
                HideModal();

                IsLoading = false;
            }
            catch (Exception)
            {
                throw;
            }                       
        }

        private async Task SendToSampleBtnClicked()
        {
            IsLoading = true;
            var item = SubscriptionUserEmails.ElementAt(ElementToShow);

            SubscriptionEmailModel emailModel = new()
            {
                BaseUrl = Configuration["App:SelfUrl"],
                EmailBody = item.Body,
                SubscriptionLink = SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType ? item.SubscriptionLink : string.Empty,
                ContactAddress = string.Empty
            };

            await EmailServices.SendEmailByTemplateAsync(Configuration["DeafulEmailAdressForsubscription"],
                                                         item.Subject,
                                                         TemplateType.Subscription,
                                                         emailModel);            
            await OpenSuccessToaster("email send successfully");

            IsLoading = false;
        }
        
        private async Task SendToBtnClicked()
        {
            IsLoading = true;
            var item = SubscriptionUserEmails.ElementAt(ElementToShow);

            SubscriptionEmailModel emailModel = new()
            {
                BaseUrl = Configuration["App:SelfUrl"],
                EmailBody = item.Body,
                SubscriptionLink = SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType ? item.SubscriptionLink : string.Empty,
                ContactAddress = string.Empty
            };

            await EmailServices.SendEmailByTemplateAsync(item.Email, item.Subject, TemplateType.Subscription, emailModel);
            await OpenSuccessToaster("email send successfully");

            IsLoading = false;            
        }
        
        private string SerializeUrl(List<string> countryISO, List<string> topicId, DateTime?[] selectedDateRange, bool IncludeRevisiedContent)
        {
            SubscriptionUserModel subscriptionUserModel = new SubscriptionUserModel()
            {
                CountryISO = countryISO,
                TopicId = topicId,
                SelectedDateRange = selectedDateRange,
                IncludeRevisiedContent = IncludeRevisiedContent
            };
            // Create a dictionary to hold your parameters
            string encodedStr = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(subscriptionUserModel,
                                                                                                          Formatting.Indented,
                                                                                                          new SpecialDateTimeConverter())));

            return $"For full list please <a href='{string.Format(@"{0}content/{1}", _navigationManager.BaseUri, WebUtility.UrlEncode(encodedStr))}'>click</a> here";
        }
        private async Task OnfitlerBtnClicked()
        {

            SubscriptionUserModels = new List<SubscriptionUserModel>();
            SubscriberContentResult = new List<CombinedSearchResult>();
            SubscriptionUserEmails = new List<SubscriptionUserEmail>();

            IsSubscriptionTypeError = IsSelectedDateRangeError = false;
            if (!Enum.IsDefined(typeof(SubscriptionType), SubscriptionRequestModel.SubscriptionType))
            {
                IsSubscriptionTypeError = true;
                StateHasChanged();
                return;
            }

            if ((SubscriptionRequestModel.SelectedDateRange == null || SubscriptionRequestModel.SelectedDateRange.Any(e => e == null)) && SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType)
            {
                IsSelectedDateRangeError = true;
                StateHasChanged();
                return;
            }
            IsLoading = true;
            GetSubscribeUserinformation();
            await GetContentData();
            if (!SubscriberContentResult.Any() && SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType)
            {
                _ = OpenErrorToaster("No new content available for selected date range");
            }
            else
            {
                EnableEmailSection = true;
            }
            IsLoading = false;
        }
        static StringBuilder GenerateHtmlTable(IEnumerable<CombinedSearchResult> data)
        {
            StringBuilder htmlTable = new StringBuilder();
            // Start the HTML table
            htmlTable.Append("<table class='_emailCompose' style ='border-collapse: collapse;'>");
            // Add table headers
            htmlTable.Append("<tr>");
            htmlTable.Append("<th style='border: 1px solid black;'>WHO region</th>");
            htmlTable.Append("<th style='border: 1px solid black;'>Country</th>");
            htmlTable.Append("<th style='border: 1px solid black;'>Title</th>");
            htmlTable.Append("<th style='border: 1px solid black;'>Type</th>");
            htmlTable.Append("<th style='border: 1px solid black;'>Start year</th>");
            htmlTable.Append("</tr>");

            foreach (var item in data)
            {
                htmlTable.Append("<tr>");
                htmlTable.Append("<td style='border: 1px solid black;'>").Append(item.RegionCode).Append("</td>");
                htmlTable.Append("<td style='border: 1px solid black;'>").Append(item.CountryList).Append("</td>");
                htmlTable.Append("<td style='border: 1px solid black;'>").Append($"<a href='{item.DetailsUrl}'>{item.Title}</a>").Append("</td>");
                htmlTable.Append("<td style='border: 1px solid black;'>").Append(item.Type).Append("</td>");
                htmlTable.Append("<td style='border: 1px solid black;'>").Append(item.StartYear).Append("</td>");
                htmlTable.Append("</tr>");
            }
            // End the HTML table
            htmlTable.Append("</table>");
            return htmlTable;
        }

        private async Task GetContentData()
        {
            try
            {
                if (SubscriptionType.Other != SubscriptionRequestModel.SubscriptionType)
                {
                    var countryList = SubscriptionUserModels.SelectMany(e => e.CountryISO).ToList();
                    var topicIds = SubscriptionUserModels.SelectMany(e => e.TopicId).ToList();
                    SubscriberContentResult = await SearchService.GetContentListAsync(countryList, topicIds, SubscriptionRequestModel.SelectedDateRange, SubscriptionRequestModel.IncludeRevisiedContent);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private void GetSubscribeUserinformation()
        {
            switch (SubscriptionRequestModel.SubscriptionType)
            {
                case SubscriptionType.Country:
                    SubscriptionUserModels = GetSubscribeCountryUserList();
                    break;
                case SubscriptionType.Topic:
                    SubscriptionUserModels = GetSubscribeTopicUserList();
                    break;
            }
            if (SubscriptionUserModels.Any())
            {
                Emails = string.Join(";", SubscriptionUserModels.Select(e => e.Email).ToList());
            }
        }
        private List<SubscriptionUserModel> GetSubscribeCountryUserList()
        {
            using var _dbContext = DbFactory.CreateDbContext();

            var subscriptionUsers = _dbContext.Users
            .Join(_dbContext.ApplicationUserIntertestedCountries,
                user => user.Id,
                userCountry => userCountry.UserId,
                (user, userCountry) => new { user, userCountry })
            .GroupBy(result => result.user.Id)
            .Select(group => new SubscriptionUserModel
            {
                Email = group.First().user.Email,
                CountryISO = group.Select(result => result.userCountry.Country).ToList()
            }).ToList();

            return subscriptionUsers;
        }
        private List<SubscriptionUserModel> GetSubscribeTopicUserList()
        {
            using var _dbContext = DbFactory.CreateDbContext();

            var subscriptionUsers = _dbContext.Users
            .Join(_dbContext.ApplicationUserIntertestedTopic,
                user => user.Id,
                topic => topic.UserId,
                (user, topic) => new { user, topic })
            .GroupBy(result => result.user.Id)
            .Select(group => new SubscriptionUserModel
            {
                Email = group.First().user.Email,
                TopicId = group.Select(result => result.topic.TopicId.ToString()).ToList()
            }).ToList();

            return subscriptionUsers;
        }
        private void HideModal()
        {
            ElementToShow = 0;
            ThemesVisible = false;
            StateHasChanged();
        }
        private string getTemplete(string fileName)
        {
            var path = Path.Combine(environment.WebRootPath, "templete", fileName);
            string templete = File.ReadAllText(path);
            return templete;
        }
    }
}
