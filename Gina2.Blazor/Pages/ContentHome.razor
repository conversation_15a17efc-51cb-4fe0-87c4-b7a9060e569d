﻿@page "/admin/contents/home"
@using ChartJs.Blazor.Common
@using ChartJs.Blazor.PieChart
@using ChartJs.Blazor.Util
@using AntDesign.Select
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Core.Enums
@using Gina2.DbModels.HomeDrafts
@inherits PageConfirgurationComponent
<PageTitle>GIFNA Home</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">
        @{
            RenderFragment footer = @<AntDesign.Template>
            </AntDesign.Template>;
        }

        <Div Class="_live-preview">
            <AntDesign.Modal Title="@title"
                             Visible="@_visible"
                             DestroyOnClose=true
                             Closable=true
                             OnCancel="@HandleCancel" Footer="@footer" Width="1200">
                @if (_visible)
                {
                    <Index previewDraftId="@previewDraftId" rerenderTokenId="@rerenderTokenId" />
                }
            </AntDesign.Modal>
        </Div>
        <Loader IsLoading="@IsLoading" />
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
                <Container Class="pt-5 pb-5">
                    <Heading Class="h-title">Home</Heading>
                </Container>
            </Card>
        </Container>
        <Container Fluid Class="bg-trdot pt-3">
            <Container Class="Dashboard">
                <Row Class="mt-4 mb-4">
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_mydrafts _HomeCurrent">
                        <Tabs SelectedTab="@selectedTab" SelectedTabChanged="@OnSelectedTabChanged">
                            <Items>
                                <Div>
                                    <Tab Name="Drafts">Drafts</Tab>
                                    <Tab Name="Published">Published contents</Tab>
                                </Div>
                                <Div Class="adddraft">
                                    <Button Disabled="@(homeDrafts.Where(w=>w.Status==HomeDraftModerationState.Draft.ToString()).Any())" Class="but-yellow pl-2 pr-2" Clicked="CreateDraft"><Icon Name="IconName.Pen" /> Edit home page</Button>
                                </Div>
                            </Items>
                            <Content>
                                <TabPanel Name="Drafts">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth _actions"
                                                      TItem="@HomeDraft"
                                                      Data="@homeDrafts.Where(w=>w.Status!=HomeDraftModerationState.Published.ToString())"
                                                      PageSize="5"
                                                      TotalItems="@SearchModel.TotalFilteredCount"
                                                      CurrentPage="SearchModel.PageNo"
                                                      ShowPageSizes
                                                      ShowPager
                                                      Responsive
                                                      SortMode="DataGridSortMode.Single">
                                                <EmptyTemplate>
                                                    <Div>No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Field="@nameof(HomeDraft.RefCreatedBy)" Caption="Created by" Width="22%" TextAlignment="TextAlignment.Start" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.CreatedDateTime)" Caption="Created date" Width="19%" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.RefUpdatedBy)" Caption="Last updated by" Width="22%" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.UpdateDateTime)" Caption="Last update" Width="19%" />
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                        {
                                                            <Button>
                                                                <Tooltip Text="Edit">
                                                                    <Icon Clicked="@(()=>{ NavigationManager.NavigateTo($"admin/contents/home/<USER>/{currentRow.Id}/edit",true);  })" Name="IconName.Pen" />
                                                                </Tooltip>
                                                            </Button>
                                                        }
                                                            <Button>
                                                                <Tooltip Text="Preview" style="z-index:0" >
                                                                    <Icon Clicked="async (e)=>await ShowPreview(currentRow.Id)" Name="IconName.Eye">

                                                                    </Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                        {
                                                            <Button>
                                                                <Tooltip Text="Delete">
                                                                    <Icon Name="IconName.Delete" Clicked="e=> ShowDeleteDraftModal(currentRow.Id)" />
                                                                </Tooltip>
                                                            </Button>
                                                        }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                                <TotalItemsTemplate Context="row">
                                                    <Badge TextColor="TextColor.Dark">
                                                        @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                                                    </Badge>
                                                </TotalItemsTemplate>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                                <TabPanel Name="Published">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth"
                                                      TItem="@HomeDraft"
                                                      Data="@homeDrafts.Where(w=>w.Status==HomeDraftModerationState.Published.ToString())"
                                                      PageSize="5"
                                                      TotalItems="@SearchModel.TotalFilteredCount"
                                                      CurrentPage="SearchModel.PageNo"
                                                      ShowPageSizes
                                                      ShowPager
                                                      Responsive
                                                      SortMode="DataGridSortMode.Single"
                                                      RowStyling="@OnRowStyling">
                                                <EmptyTemplate>
                                                    <Div>No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Field="@nameof(HomeDraft.RefCreatedBy)" Caption="Created by" Width="22%" TextAlignment="TextAlignment.Start" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.CreatedDateTime)" Caption="Created date" Width="19%" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.RefUpdatedBy)" Caption="Last Updated by" Width="22%" />
                                                    <DataGridColumn Field="@nameof(HomeDraft.UpdateDateTime)" Caption="Last update" Width="19%" />
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Button>
                                                                <Tooltip Text="Edit">
                                                                    <Icon Clicked="@(()=>{ NavigationManager.NavigateTo($"admin/contents/home/<USER>/{currentRow.Id}/edit",true);  })" Name="IconName.Pen" />
                                                                </Tooltip>
                                                            </Button>
                                                            <Button>
                                                                <Tooltip Text="Preview">
                                                                    <Icon Clicked="(e)=>ShowPreview(currentRow.Id)" Name="IconName.Eye"></Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                        {
                                                            <Button>
                                                                <Tooltip Text="Delete">
                                                                    <Icon Name="IconName.Delete" Clicked="e=> ShowDeleteDraftModal(currentRow.Id)" />
                                                                </Tooltip>
                                                            </Button>
                                                        }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                                <TotalItemsTemplate Context="row">
                                                    <Badge TextColor="TextColor.Dark">
                                                        @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                                                    </Badge>
                                                </TotalItemsTemplate>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                            </Content>
                        </Tabs>
                    </Column>

                </Row>
            </Container>
        </Container>

        <Modal @bind-Visible="@deleteDraftModalVisible" Class="modals-lg _modalcenter">
            <ModalContent Centered Class="forms">
                <ModalHeader>
                    <ModalTitle>Are you sure want to Delete this draft?</ModalTitle>
                </ModalHeader>
                <ModalFooter>
                    <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteHomeDraft(draftToBeDeleted);} )">Delete</Button>
                    <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteDraftModalVisible = false)">Cancel</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

    </Authorized>
</AuthorizeView>

