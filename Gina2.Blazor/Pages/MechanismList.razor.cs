﻿using Gina2.Blazor.Models;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json.Linq;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Mechanism;

namespace Gina2.Blazor.Pages
{
    public partial class MechanismList
    {
        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        public readonly string Title = "Mechanisms";
        public readonly string TabTitle = "mechanisms";
        public readonly string TypeofTitle = "View by mechanism type";
        public readonly string TableTitle = "Mechanisms";
        public readonly string AdminTitle = "Mechanisms";
        public readonly string DetailUrlContext = "mechanisms";
        public string CsvKeys { get; set; }
        public List<string> CsvData { get; set; } = new();
        public string FileName = "gina2_mechanisms";

        private List<PublishedListItem> mechanisms = new();
        private List<TypeList> mechanismTypes = new();

        protected override async Task OnInitializedAsync()
        {
            var MechanismNames = await MechanismService.GetMechanismsAsync(CountryCode);
            List<MechanismCsv> MechanismCsv = MechanismNames.Select(x => new MechanismCsv()
            {
                Title = !string.IsNullOrEmpty(x.Title) ? $"\"{x.Title}\"" : "-",
                EnglishTitle = !string.IsNullOrEmpty(x.EnglishTitle) ? $"\"{x.EnglishTitle}\"" : "-",
                Mandate = !string.IsNullOrEmpty(x.Mandate) ? $"\"{x.Mandate}\"" : "-",
                MechanismOtherType = x.MechanismOtherType,
                LeadGovernmentAgency = !string.IsNullOrEmpty(x.LeadGovernmentAgency) ? $"\"{x.LeadGovernmentAgency}\"" : "-",
                StartMonth = x.StartMonth,
                StartYear = x.StartYear,
                OtherTopics = !string.IsNullOrEmpty(x.OtherTopics) ? $"\"{x.OtherTopics}\"" : "-",
                LessonsLearnt = !string.IsNullOrEmpty(x.LessonsLearnt) ? $"\"{x.LessonsLearnt}\"" : "-",
                Url = x.Url,
                Notes = !string.IsNullOrEmpty(x.Notes) ? $"\"{x.Notes}\"" : "-",
                References = !string.IsNullOrEmpty(x.References) ? $"\"{x.References}\"" : "-"
            }).ToList();
            if (MechanismCsv.Count > 0)
            {
                var dictionary = JObject.FromObject(MechanismCsv[0]).ToObject<Dictionary<string, string>>();
                CsvKeys = string.Join(",", dictionary.Select(a => $"\"{a.Key}\""));
                foreach (var item in MechanismCsv)
                {
                    var dictionaryvalues = JObject.FromObject(item).ToObject<Dictionary<string, string>>();
                    string CsvValue = string.Join(",", dictionaryvalues.Select(a => $"{a.Value}"));
                    CsvData.Add(CsvValue);
                }
            }
            var MechanismTypes = await MechanismService.GetMechanismTypes();
            foreach (var item in MechanismNames)
            {
                if (item.MechanismTypeId != 0)
                {
                    mechanisms.Add(new PublishedListItem()
                    {
                        Key = item.Id,
                        Title = item.Title,
                        StartYear = item.StartYear.HasValue ? item.StartYear.Value.ToString() : "-",
                        EndYear = "-",
                        Category = String.IsNullOrEmpty(item.MechanismTypeId.ToString()) ? String.Empty : item.MechanismTypeId.ToString(),
                        CategoryName = MechanismTypes.FirstOrDefault(e => e.Id == item.MechanismTypeId)?.Name
                    });
                }
            }
            foreach (var item in MechanismTypes)
            {
                mechanismTypes.Add(new TypeList()
                {
                    Id = item.Id,
                    Name = item.Name
                });

            }
        }
    }
}
