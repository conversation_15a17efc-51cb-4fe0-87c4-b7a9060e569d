﻿@using Microsoft.AspNetCore.Components.Web
@namespace Gina2.Blazor.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title value="The Global database on the Implementation of Food and Nutrition Action (GIFNA)"></title>
    <link rel="icon" type="image/png" href="img/gina-logo.png" />

    <base href="~/" />
    <!-- New Start -->

    <link href="~/css/default.css" rel="stylesheet" />
    <link href="_content/Blazorise.Snackbar/blazorise.snackbar.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/css/<EMAIL>" rel="stylesheet">
    <link rel="stylesheet" href="~/css/all.css">

    <link rel="stylesheet" href="_content/Radzen.Blazor/css/default.css">

    <link href="_content/Blazorise.Icons.FontAwesome/v6/css/all.min.css" rel="stylesheet">

    <link href="_content/Blazorise/blazorise.css?v=*******" rel="stylesheet" />

    <link href="Gina2.Blazor.styles.css" rel="stylesheet" />
    <link href="~/css/font.css" rel="stylesheet" />
    <link href="_content/AntDesign/css/ant-design-blazor.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://js.arcgis.com/4.23/esri/css/main.css" asp-add-nonce="true">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="_content/Blazorise.Icons.FontAwesome/v6/css/all.min.css" rel="stylesheet">

    <link href="_content/Blazorise/blazorise.css?v=*******" rel="stylesheet" />
    <link href="_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css?v=*******" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/responsive.css" rel="stylesheet" />
    <link href="~/css/quill.snow.css" rel="stylesheet" />
    <script src="~/js/quill.js"></script>
    <script src="~/js/image-resize.min.js"></script>
    <script src="~/js/blazorquill.js"></script>
    <script src="~/js/quill-image-compress.js"></script>
    <!-- map library -->
    <script src="~/js/library/jquery-3.6.2.min.js"></script>
    <script src="~/js/library/popper.min.js"></script>
    <script src="~/js/library/tippy.js"></script>
    <script src="~/js/library/bootstrap.min.js"></script>
    <script src="~/js/library/bootstrap.bundle.min.js"></script>
    <script src="~/js/library/jspdf.min.js"></script>
    <script src="~/js/library/jspdf.plugin.autotable.min.js"></script>
    <script src="~/js/library/moment.min.js"></script>
    <script src="~/js/library/mobile-drag-drop-index.min.js"></script>
    <script src="~/js/library/scroll-behaviour.min.js"></script>

    <script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>

    <script src="_content/AntDesign/js/ant-design-blazor.js"></script>
    <script src="~/js/HistoryBefore.js"></script>
    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js" asp-add-nonce="true"></script>
    <script src="~/js/HistoryAfter.js"></script>
    <script src="~/js/clarity.js"></script>
    <component type="typeof(HeadOutlet)" render-mode="Server" />
</head>
<body>
    @RenderBody()
    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <!-- This is the glue between Blazor and Chart.js -->
    <script src="_content/ChartJs.Blazor.Fork/ChartJsBlazorInterop.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>
    <script src="_framework/blazor.server.js"></script>
    <script type="text/javascript" src="~/js/Office.js"></script>
    <script type="text/javascript" src="~/js/index.js"></script>
    <script type="text/javascript" src="~/js/ginav2.js"></script>
    <script type="text/javascript" src="~/js/SaveAsFile.js"></script>    
    <script type="text/javascript" src="~/js/ScorecardMap.js"></script>
    <script src="~/js/util.js"></script>
     <script src="~/js/GoogleCapcha.js"></script>
    <script src="~/js/Draggable.js" defer="defer"></script>
</body>
</html>