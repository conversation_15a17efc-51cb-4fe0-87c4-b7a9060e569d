﻿using AntDesign;
using AutoMapper;
using Domain.ActionTopic;
using Domain.MechanismTopics;
using Domain.PolicyTopics;
using Domain.Terms;
using Force.DeepCloner;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core.Constant;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Country;
using Gina2.Services.ICN2;
using Gina2.Services.Language;
using Gina2.Services.Mechanism;
using Gina2.Services.Models;
using Gina2.Services.PartnerCategorys;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Gina2.Services.TargetGroups;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Net;
using System.Text;
using Domain.PolicyTypes;
using Domain.Search;
using Newtonsoft.Json.Linq;
using Microsoft.Graph.Models;
using System.Linq;
using DocumentFormat.OpenXml.Office2010.Excel;
using Gina2.Services.Search;
using Blazorise;
using AutoMapper.Configuration.Conventions;
using Google.Protobuf.WellKnownTypes;
using System.Diagnostics;
using Gina2.Blazor.Utilities;
using Gina2.MySqlRepository.Repositories;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;

namespace Gina2.Blazor.Pages
{
    public partial class AdvanceSearch : PageConfirgurationComponent
    {
        
        #region Services
        [Inject]
        private IProgrammeService ProgrammeService { get; set; }
        [Inject]
        private ISearchService SearchService { get; set; }
        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Inject]
        private ITopicService TopicService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Inject]
        private IPartnerCategoryService PartnerCategoryService { get; set; }

        [Inject]
        private ITargetGroupService TargetGroupService { get; set; }

        [Inject]
        private IProgrammeService ProgramSevice { get; set; }
        [Inject]
        private IIcn2Service Icn2Service { get; set; }
        [Inject]
        private ILanguageService LanguageService { get; set; }

        [Inject]
        private IMapper mapper { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private ProtectedSessionStorage ProtectedSessionStore { get; set; }
        #endregion Services

        #region Declarations

        private GlobalSearchRequest searchRequest = new();
        private GlobalSearchRequest previousSearchRequest = null;

        private readonly List<Domain.Search.SearchResult> selectedEntries = new();
        public List<PolicyLookup> PolicyLookups { get; set; } = new List<PolicyLookup>();
        private bool IsAllSelected { get; set; } = false;
        
        private Tree<GTreeNode> policyTopicTree;
        private Tree<GTreeNode> mechanismTopicTree;
        private Tree<GTreeNode> actionTopicTree;
        private Tree<GTreeNode> searchTree;


        private Tree<TermTreeNode> refCoutryGroupTree;
        private Tree<TermTreeNode> refCoutryRegionTree;
        private Tree<TermTreeNode> refPartnerTree;
        private Tree<PolicyTypeViewModel> refPolicyTypeTree;
        private Tree<ProgramType> refProgramTypeTree;

        private Tree<Term> refProgramTopicTree;
        private Tree<PartnerCategory> refFundingSource;
        private Tree<TargetGroup> refTargetGroupTree;
        private Tree<Delivery> refDeliveryTree;
        private Tree<ProblemType> refProblemTypeTree;
        private Tree<MechanismType> refMechanismTypeTree;
        private Tree<TermTreeNode> refICN2Tree;
        private Tree<Language> refLanguageTree;
        private List<string> lastSelectedNutritions = new();
        // private Dictionary<int, List<int>> SelectedSearchTopicList = new();
        private Dictionary<int, List<int>> SelectedPolicyTopicList = new();
        // private List<int> SelectedSearchTopicListUIOnly = new();
        private List<int> SelectedPolicyTopicOnSearchTree = new();
        List<int> SelectedActionTopicsOnSearchTree = new List<int>();
        List<int> SelectedMechanismTopicsOnSearchTree = new List<int>();
        private Dictionary<int, List<int>> PolicyTopicParentChilds = new();
        private Dictionary<int, List<int>> MechanismTopicParentChilds = new();
        private Dictionary<int, List<int>> ActionTopicParentChilds = new();
        private Dictionary<int, List<int>> BasicTopicParentChilds = new();

        private Dictionary<int, List<int>> AllPolicyTopicParentChilds = new();
        private Dictionary<int, List<int>> AllMechanismTopicParentChilds = new();
        private Dictionary<int, List<int>> AllActionTopicParentChilds = new();
        private Dictionary<int, List<int>> AllBasicTopicParentChilds = new();
        // private string SelectedSearchTopicName = string.Empty;
        private string SelectedPolicyTopicName = string.Empty;
        private string SelectedActionTopicName = string.Empty;
        private string SelectedMechanismTopicName = string.Empty;
        private string SelectedPartners = string.Empty;
        private string SelectedPolicyTypes = string.Empty;
        private string SelectedMechanismTypes = string.Empty;
        private string SelectedProgramTypes = string.Empty;
        private string SelectedFundingSource = string.Empty;
        private string SelectedTargetGroup = string.Empty;
        private string SelectedDeliveryChannels = string.Empty;
        private string SelectedProblemsAndSolutions = string.Empty;
        private string SelectedICN2Actions = string.Empty;
        private string SelectedLanguages = string.Empty;
        private bool isGeneralFilter = true;
        private List<int> ExistingTopicCountryCounts { get; set; } = new();
        private List<CountryWithRegion> AllCountryWithRegion { get; set; } = new();
        private readonly DebounceDispatcher _debouncer = new DebounceDispatcher(TimeSpan.FromSeconds(2));

        #endregion Declarations

        #region Properties
        private bool SearchCountsLoading = false;
        private Visibility searchLoaderVisibility = Visibility.Invisible;
        private IEnumerable<CountryWithRegion> AllRegions = Enumerable.Empty<CountryWithRegion>();
        private static IEnumerable<string> AllIncomGroups = Enumerable.Empty<string>();
        private List<CountryWithRegion> fullCountryDetail = new();
        private List<CountryWithRegion> allCountryDetail = new();
        public bool IsSearchedClicked { get; set; } = false;
        private readonly List<GTreeNode> PolicyTopicTreeNodes = new();
        private readonly List<GTreeNode> MechanismTopicTreeNodes = new();
        private readonly List<GTreeNode> ActionTopicTreeNodes = new();
        private readonly List<GTreeNode> OtherPrentTopicTreeNodes = new();
        private readonly List<GTreeNode> TopicsInSearch = new();
        private readonly List<TermTreeNode> CountryGroupsTree = new();
        private readonly List<TermTreeNode> CountryRegionGroupTree = new();
        private readonly List<TermTreeNode> PartnerTree = new();
        private readonly List<TermTreeNode> ICN2Tree = new();
        private List<Gina2.DbModels.Topic> AllTopics = new();
        private List<Gina2.DbModels.TopicParent> AllParentTopics = new();
        public int NewPolicyId { get; set; }

        private readonly string[] Startplace = new string[] { "Earliest start year" };
        private readonly string[] Endplace = new string[] { "Latest start year" };
        public bool IsTopicDDDisabled { get; set; } = false;
        public int RegionId { get; set; }
        private DataGridforSearch DataGridforSearchchild { get; set; }
        private SearchResultCounts SearchDataCount { get; set; } = new();
        private FileDownload FileDownloadChild { get; set; }
        public bool CanDownload => IsAllSelected || selectedEntries.Any();
        public string TotalRecordMessage { get; set; } = string.Empty;
        public bool ShowCSVPanel { get; set; } = false;
        public bool ShowTopic { get; set; } = true;
        public bool AdFilter { get; set; } = false;
        public bool AdFilterdisa { get; set; } = true;
        public bool AdFilterdisabled { get; set; } = true;
        private bool HideMore { get; set; } = false;
        private bool hasPolicyTopics = false;
        private bool hasMechanismTopics = false;
        private bool hasActionTopics = false;
        private void Toggle()
        {
            HideMore = !HideMore;
        }

        public List<SearchHistory> SearchHistories { get; set; } = new();
        public SearchChangeSet SearchChangeSet { get; set; } = new();
        private List<TopicDataTypeInformation> TopicDataList { get; set; } = new();
        private List<CountryGroup> CountryGroups { get; set; } = new();
        private List<PartnerCategory> PartnerCategories { get; set; } = new();
        private List<Domain.PolicyTypes.PolicyTypeViewModel> PolicyTypes { get; set; } = new();
        private List<ProgramType> ProgramTypes { get; set; } = new();

        private List<Term> ProgramTopics { get; set; } = new();
        private List<TargetGroup> TargetGroups { get; set; } = new();
        private List<Delivery> Deliveries { get; set; } = new();
        private List<ProblemType> ProblemTypes { get; set; } = new();
        private List<MechanismType> MechanismTypes { get; set; } = new();
        private List<Icn2Category> Icn2Categories { get; set; } = new();
        private List<Language> Languages { get; set; } = new();
        bool CollLangFilters = false;
        bool CollPolicyData = true;
        private int charCount = 0;

        #endregion Properties

        #region Methods

        #region Methods - ComponentBase Overrides

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await GetTopicsAsync();
        }
        public string GetPageURL()
        {
            string page = "searchpage";
            if (NavigationManager.Uri.Contains("advanced-search"))
            {
                page = "advancesearch";
            }
            return page;
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {

            if (firstRender)
            {
                _ = ToggleSearchFiltersLoader(Visibility.Visible);
                searchRequest.DefaultPage = GetPageURL();
                if (!string.IsNullOrEmpty(Configuration["EnableSearchPage"]) && !Convert.ToBoolean(Configuration["EnableSearchPage"]))
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                if (!string.IsNullOrEmpty(Configuration["EnableAdvancedSearch"]) && !Convert.ToBoolean(Configuration["EnableAdvancedSearch"]))
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                _ = JSRuntime.InvokeVoidAsync("loadJs", Configuration["ReCaptcha:GoogleRecaptchaUrl"].ToString() + Configuration["ReCaptcha:SiteKey"].ToString());
                isGeneralFilter = !NavigationManager.Uri.Contains("advanced-search");
                PolicyLookups = PolicyLookup.GetPolicyFilter();
                _ = GetSearchResultCount();
                
                _ = GetAdvanceSearchFilters();
                IsLoading = false;
                // AddSearchHistory();
                _ = ToggleSearchFiltersLoader(Visibility.Invisible);
                //previousSearchRequest = searchRequest.DeepClone();
                
            }
        }
        public async Task ToggleSearchFiltersLoader(Visibility value)
        {
            searchLoaderVisibility = value;
            await InvokeAsync(StateHasChanged);
        }
        private async Task GetAdvanceSearchFilters()
        {
            if (!isGeneralFilter)
            {
                if (searchRequest.IsPolicyDataTypeSelected)
                {
                    _ = GetPolicyTypeFilter();
                }
                if (searchRequest.IsPragrammesAndActionsDataTypeSelected)
                {
                    _ = GetProgramTypeFilter();
                    _ = GetTargetGroupsAsyncFilter();
                    _ = GetDeliveriesAsyncFilter();
                    _ = GetProblemTypesAsyncFilter();
                }
                if (searchRequest.IsMechanicalDataTypeSelected)
                {
                    _ = GetMechanismTypesAsyncFilter();
                }
                if (searchRequest.IsCommitmentsDataTypeSelected)
                {
                    _ = GetIcn2CategoriesAsyncFilter();
                }

                _ = GeLanguagesAsyncFilter();
                _ = GetCountryGroupsAsyncFilter();
                _ = GetPartnerCategoriesAsyncFilter();
                
            }
        }
        private async Task GetAllCountryAsyncFilter()
        {
            if (!AllCountryWithRegion.Any())
            {
                AllCountryWithRegion = await CountryService.GetAllCountryWithRegion();
            }
        }
        private async Task GetCountryGroupsAsyncFilter()
        {
            if (!CountryGroups.Any())
            {
                CountryGroups = (await CountryService.GetAllCountryGroups()).OrderBy(c => c.Name).ToList();
                foreach (var item in CountryGroups)
                {
                    CountryGroupsTree.Add(new TermTreeNode()
                    {
                        Iso3Code = Convert.ToString(item.Id),
                        Name = item.Name,
                        Children = mapper.Map<List<CountryGroupMapping>, List<TermTreeNode>>(item.CountryGroupMappings.ToList())
                    });
                }
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetPartnerCategoriesAsyncFilter()
        {
            if (!PartnerCategories.Any())
            {
                PartnerCategories = await PartnerCategoryService.GetPartnerCategoryPartnersAsync();
                foreach (PartnerCategory partnerCategory in PartnerCategories)
                {
                    PartnerTree.Add(new TermTreeNode()
                    {
                        Id = partnerCategory.Id,
                        Name = partnerCategory.Name,
                        Children = mapper.Map<List<Partner>, List<TermTreeNode>>(partnerCategory.Partners.ToList())
                    });

                }
                await InvokeAsync(StateHasChanged);
            }
        }
        private async Task GeLanguagesAsyncFilter()
        {
            if (!Languages.Any())
            {
                Languages = await LanguageService.GetLanguageTypesAsync();
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetDeliveriesAsyncFilter()
        {
            if (!Deliveries.Any())
            {
                Deliveries = await ProgramSevice.GetDeliveryAsync();
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetProblemTypesAsyncFilter()
        {
            if (!ProblemTypes.Any())
            {
                ProblemTypes = await ProgramSevice.GetProblemTypesAsync();
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetMechanismTypesAsyncFilter()
        {
            if (!MechanismTypes.Any())
            {
                MechanismTypes = await MechanismService.GetMechanismTypes();
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetIcn2CategoriesAsyncFilter()
        {
            if (!Icn2Categories.Any())
            {

                Icn2Categories = await Icn2Service.GetIc2nCategoriesAsync();
                foreach (var icn2Category in Icn2Categories)
                {
                    ICN2Tree.Add(new TermTreeNode()
                    {
                        Id = icn2Category.Id,
                        Name = icn2Category.Name,
                        Children = mapper.Map<List<Icn2>, List<TermTreeNode>>(icn2Category.Icn2s.ToList())
                    });
                }
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetTargetGroupsAsyncFilter()
        {
            if (!TargetGroups.Any())
            {
                TargetGroups = await TargetGroupService.GetTargetGroupsAsync();
                await InvokeAsync(StateHasChanged);

            }
        }
        private async Task GetProgramTypeFilter()
        {
            if (!ProblemTypes.Any())
            {
                ProgramTypes = await ProgramSevice.GetProgrammeActionTypesAsync();
                await InvokeAsync(StateHasChanged);


            }
        }

        private async Task GetPolicyTypeFilter()
        {
            if (!PolicyTypes.Any())
            {
                PolicyTypes = (await PolicyService.GetPolicyTypes()).ToList();
                await InvokeAsync(StateHasChanged);

            }
        }
        public async Task RefreshGrid()
        {
            // DataGridforSearchchild.IsAccordianLoading = true;
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = DataGridforSearchchild.RefreshDataGrid();
        }
        public async Task GetSearchResultCount()
        {
            // Stopwatch stopwatch = new Stopwatch();
            // stopwatch.Start();
            var searchResponseFromSp = await SearchService.GetCombinedSearchResultCounts(searchRequest);
            // stopwatch.Stop();
            // Console.WriteLine("GetSearchResultCount grid calling time", stopwatch);
            IsSearchedClicked = true;
            //SearchResultCounts.TotalRecordCount = searchResponse.TotalFilteredCount;
            //pass the data to parent component
            //SendDataToParentEvent.InvokeAsync(SearchResultCounts).ConfigureAwait(false);
            //SendDataFromSpToParentEvent.InvokeAsync(searchResponseFromSp).ConfigureAwait(false);
            _ = GetDataCount(searchResponseFromSp.SearchResultCounts);
             _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        private async Task EmptyTotalRecordMessage()
        {
            TotalRecordMessage = string.Empty;
            await InvokeAsync(StateHasChanged);   
        }
        public async Task SearchGlobally(bool initialCall = false)
        {
            _ = EmptyTotalRecordMessage();
            _ = RefreshGrid();
            _ = GetSearchResultCount();
            
            StateHasChanged();
        }
        public async Task OnClearClicked()
        {
            searchRequest = new GlobalSearchRequest();
            PolicyLookups.ForEach(x => x.IsChecked = false);
            PolicyLookups.FirstOrDefault(e => e.Name == "Policies").IsChecked = true;
            policyTopicTree?.UncheckAll();
            mechanismTopicTree?.UncheckAll();
            SetDataType();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        private void ToggleAdvanceFilterMode()
        {
            if (isGeneralFilter)
            {
                NavigationManager.NavigateTo("/advanced-search", true);
            }
        }
        private void ToggleGeneralFilterMode()
        {
            if (!isGeneralFilter)
            {
                NavigationManager.NavigateTo("/search", true);
            }
        }
        public async Task OnClearDataTypesClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            PolicyLookups.ForEach(x => x.IsChecked = false);
            PolicyLookups.FirstOrDefault(e => e.Name == "Policies").IsChecked = true;
            SetDataType();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        // public async Task OnClearSearchTopics()
        // {
        //     _ = ToggleSearchFiltersLoader(Visibility.Visible);
        //     searchTree.UncheckAll();
        //     SelectedSearchTopicList.Clear();
        //     SelectedSearchTopicName = string.Empty;
        //     DataGridforSearchchild.SearchRequest = searchRequest;
        //     _ = SearchGlobally(true);
        //     _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        // }
        public async Task OnClearRegionsClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            searchRequest.SelectedRegionCode = String.Empty;
            searchRequest.SelectedIncomeCode = String.Empty;
            searchRequest.SelectedCountries = Enumerable.Empty<string>();

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        public async Task OnClearCountryClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            
            searchRequest.SelectedCountries = Enumerable.Empty<string>();

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally();
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        public async Task OnClearPublishedYearClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            searchRequest.StartDate = null;
            searchRequest.EndDate = null;

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        public async Task OnClearSearchTextClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            searchRequest.SearchText = String.Empty;

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
           _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        public async Task OnClearTopicsClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            policyTopicTree?.UncheckAll();
            mechanismTopicTree?.UncheckAll();
            searchRequest.SelectedTopcis = Enumerable.Empty<int>();
            searchRequest.SelectedPolicyTopics = new();
            searchRequest.SelectedMechanismTopics = new List<SelectedTopicsTree>();
            searchRequest.SelectedActionTopics = new List<SelectedTopicsTree>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        public async Task OnClearCountryGroupRegion()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refCoutryGroupTree.UncheckAll();
            refCoutryRegionTree.UncheckAll();

            searchRequest.CountryGroupSelectedCountries.Clear();
            searchRequest.CountryRegionSelectedCountries.Clear();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearPartner()
        {
            refPartnerTree.UncheckAll();
            searchRequest.PartnerIds = new Dictionary<int, List<int>>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearPolicyType()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refPolicyTypeTree.UncheckAll();
            searchRequest.PolicyTypeIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearMechanismFilter()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refMechanismTypeTree.UncheckAll();
            searchRequest.MechanismTypeIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearCommitmentFilter()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refICN2Tree.UncheckAll();
            searchRequest.MechanismTypeIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearNutritionTopics()
        {

            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            if (policyTopicTree != null)
            {
                policyTopicTree.UncheckAll();
                searchRequest.SelectedPolicyTopics = new();
            }
            else
            if (mechanismTopicTree != null)
            {
                mechanismTopicTree.UncheckAll();
                searchRequest.SelectedMechanismTopics = new List<SelectedTopicsTree>();
            }
            else
            if (actionTopicTree != null)
            {
                actionTopicTree.UncheckAll();
                searchRequest.SelectedActionTopics = new List<SelectedTopicsTree>();
            }

            searchRequest.MechanismTypeIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearActionFilters()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refProgramTypeTree.UncheckAll();
            refFundingSource.UncheckAll();
            refTargetGroupTree.UncheckAll();
            refDeliveryTree.UncheckAll();
            refProblemTypeTree.UncheckAll();
            searchRequest.ProgramTypeIds = Enumerable.Empty<int>();
            searchRequest.FundingSourceIds = Enumerable.Empty<int>();
            searchRequest.TargetGroupIds = Enumerable.Empty<int>();
            searchRequest.DeliveryChannelIds = Enumerable.Empty<int>();
            searchRequest.ProblemIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        public async Task OnClearLanguageFilters()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            refLanguageTree.UncheckAll();
            searchRequest.LanguageIds = Enumerable.Empty<int>();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        //public async Task GetDataFromChild(Domain.Search.SearchResultCounts value)
        //{
        //    SearchDataCount = value;

        //    //StateHasChanged();
        //}

        public async Task GetDataCount(Domain.Search.SearchResultCounts value)
        {
            SearchDataCount = value;
            HashSet<string> countryList = value.CountryCount.Select(s => s.Key).ToHashSet();
            await GetAllCountryAsyncFilter();
            var countryDetailforProgramAction = AllCountryWithRegion.Where(w => countryList.Contains(w.CountryCode)).ToList();// await CountryService.GetCountriesWithRegionProgramAction(countryList);
            TotalRecordMessage = value.TotalRecordCount.ToString();
            FileDownloadChild.RefreshDataofFileDownload(value, searchRequest);
            GetCountryPanelDetail(countryDetailforProgramAction);
            //StateHasChanged();
        }

        public int GetDataTypeCount(string id)
        {
            return SearchDataCount.DataTypeCount.FirstOrDefault(w => w.Key == id).Value;
        }

        public int GetDataTypeCountryCount(string id)
        {
            return SearchDataCount.DataTypeWiseCountryCount.FirstOrDefault(w => w.Key == id).Value;
        }

        public int GetCountryGroupCountryCount(string code, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)// as current item doesnt have parent so it's the parent node
            {
                HashSet<string> childs = CountryGroups.First(w => w.Id == Convert.ToInt32(code))
                            .CountryGroupMappings.Select(s => s.Iso3Code).ToHashSet();

                return SearchDataCount.CountryCount.Where(s => childs.Contains(s.Key))
                                                    .Sum(s => s.Value);
            }
            else
            {
                return SearchDataCount.CountryCount.ContainsKey(code) ? SearchDataCount.CountryCount[code] : 0;

            }
        }

        public int GetCountryRegionCountryCount(string code, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)// as current item doesnt have parent so it's the parent node
            {
                HashSet<string> childs = fullCountryDetail.Where(w => w.RegionCode == code).Select(s => s.CountryCode).ToHashSet();

                return SearchDataCount.CountryCount.Where(s => childs.Contains(s.Key))
                                                    .Sum(s => s.Value);
            }
            else
            {
                return SearchDataCount.CountryCount.ContainsKey(code) ? SearchDataCount.CountryCount[code] : 0;
            }
        }

        public int GetLanguageCount(int id)
        {
            if (SearchDataCount.LanguageCount.TryGetValue(id, out int value))
            {
                return value;
            }
            return 0;
        }

        public int GetLanguageCountryCount(int id)
        {
            if (SearchDataCount.LanguageCountryCount.TryGetValue(id, out int value))
            {
                return value;
            }
            return 0;
        }

        public int GetPartnerCount(int partnerId, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)// as current item doesnt have parent so it's the parent node
            {
                if (PartnerCategories.Any(p => p.Id == partnerId))
                {
                    var category = PartnerCategories.First(p => p.Id == partnerId);
                    HashSet<int> childs = category.Partners.Select(c => c.Id).ToHashSet();
                    childs.Add(partnerId);
                    return SearchDataCount.PartnerCount
                        .Where(s => childs.Contains(s.Key))
                        .SelectMany(s => s.Value)
                        .Distinct()
                        .Count();
                }
            }
            else
            {
                var partnerCount = SearchDataCount.PartnerCount.FirstOrDefault(w => w.Key == partnerId).Value;
                return partnerCount == null ? 0 : partnerCount.Count();
            }
            return 0;
        }
        public int GetPartnerCountryCount(int partnerId, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)
            {
                if (PartnerCategories.Any(p => p.Id == partnerId))
                {
                    var category = PartnerCategories.First(p => p.Id == partnerId);
                    // var partnerChilds = PartnerCategories.
                    HashSet<int> childs = category.Partners.Select(c => c.Id).ToHashSet();
                    childs.Add(partnerId);
                    return SearchDataCount.BasicPartnerCountryList
                        .Where(s => childs.Contains(s.Key))
                        .SelectMany(s => s.Value)
                        .Distinct()
                        .Count();
                }
            }
            else
            {
                var countryCount = SearchDataCount.BasicPartnerCountryList.FirstOrDefault(w => w.Key == partnerId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
            return 0;

        }

        public int GetMechanismTypeCountryCount(int mechanismTypeId)
        {
            return SearchDataCount.MechanismTypeCountryCount.FirstOrDefault(w => w.Key == mechanismTypeId).Value;
        }
        // new implementt started there
        public int GetBasicTopicCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            try
            {
                if (parentNode != null)
                {

                }
                if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
                {
                    if (AllBasicTopicParentChilds.ContainsKey(topicId))
                    {
                        HashSet<int> childs = AllBasicTopicParentChilds[topicId].ToHashSet();
                        var childsCount = SearchDataCount.BasicTopicCountList
                                    .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                                    .Distinct().Count();

                        return childsCount + SearchDataCount.BasicTopicCount
                                            .FirstOrDefault(w => w.Key == topicId).Value;
                    }
                    return 0;
                }
                else
                {
                    var childsCount = SearchDataCount.BasicTopicCountList
                                    .Where(s => s.Key.Equals(topicId)).SelectMany(s => s.Value)
                                    .Distinct().Count();

                    return childsCount + SearchDataCount.BasicTopicCount.FirstOrDefault(w => w.Key == topicId).Value;

                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public int GetBasicTopicCountryCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                HashSet<int> childs = new HashSet<int>();

                if (AllBasicTopicParentChilds.ContainsKey(topicId))
                {
                    childs = AllBasicTopicParentChilds[topicId].ToHashSet();
                    childs.Add(topicId);
                }

                return SearchDataCount.BasicTopicCountryList
                               .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                               .Distinct().Count();
            }
            else
            {
                var countryCount = SearchDataCount.BasicTopicCountryList.FirstOrDefault(w => w.Key == topicId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
        }
        // new implementt end here

        public int GetPolicyTopicCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            
            try
            {
                if (parentNode != null)
                {

                }
                if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
                {
                    HashSet<int> childs = AllPolicyTopicParentChilds[topicId].ToHashSet();
                    var childsCount = SearchDataCount.TopicWisePolicyIdList
                                    .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                                    .Distinct().Count();
                    var childsCounts = childsCount + SearchDataCount.PolicyTopicCount
                                            .FirstOrDefault(w => w.Key == topicId).Value;
                    if (AllPolicyTopicParentChilds.ContainsKey(topicId))
                    {
                        

                        return childsCounts;
                    }
                    return 0;
                }
                else
                {
                    var childsCount = SearchDataCount.TopicWisePolicyIdList
                                    .Where(s => s.Key.Equals(topicId)).SelectMany(s => s.Value)
                                    .Distinct().Count();

                    // return childsCount + SearchDataCount.PolicyTopicCount.FirstOrDefault(w => w.Key == topicId).Value;
                    return childsCount;

                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public int GetPolicyTopicCountryCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                HashSet<int> childs = AllPolicyTopicParentChilds[topicId].ToHashSet();
                childs.Add(topicId);
                return SearchDataCount.TopicWisePolicyCountryList
                            .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                            .Distinct().Count();
            }
            else
            {
                var countryCount = SearchDataCount.TopicWisePolicyCountryList.FirstOrDefault(w => w.Key == topicId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
        }

        public int GetMechanismTopicCountryCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                HashSet<int> childs = AllMechanismTopicParentChilds[topicId].ToHashSet();
                childs.Add(topicId);
                return SearchDataCount.TopicWiseMechanismCountryList
                            .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                            .Distinct().Count();
            }
            else
            {
                var countryCount = SearchDataCount.TopicWiseMechanismCountryList.FirstOrDefault(w => w.Key == topicId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
        }

        public int GetActionTopicCountryCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                HashSet<int> childs = AllActionTopicParentChilds[topicId].ToHashSet();
                childs.Add(topicId);
                var s = SearchDataCount.TopicWiseActionCountryList
                            .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                            .Distinct();
                return SearchDataCount.TopicWiseActionCountryList
                            .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                            .Distinct().Count();
            }
            else
            {
                var countryCount = SearchDataCount.TopicWiseActionCountryList.FirstOrDefault(w => w.Key == topicId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
        }

        public int GetMechanismTopicCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                if (AllMechanismTopicParentChilds.ContainsKey(topicId))
                {
                    HashSet<int> childs = AllMechanismTopicParentChilds[topicId].ToHashSet();
                    var childsCount = SearchDataCount.TopicWiseMechanismList
                                .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                                .Distinct().Count();

                    return childsCount + SearchDataCount.MechanismTopicCount
                                        .FirstOrDefault(w => w.Key == topicId).Value;
                }
                return 0;
            }
            else
            {
                    var childsCount = SearchDataCount.TopicWiseMechanismList
                                    .Where(s => s.Key.Equals(topicId)).SelectMany(s => s.Value)
                                    .Distinct().Count();
                    // var result = childsCount + SearchDataCount.MechanismTopicCount.FirstOrDefault(w => w.Key == topicId).Value;

                    return childsCount;
            }
        }

        public int GetActionTopicCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                if (AllActionTopicParentChilds.ContainsKey(topicId))
                {
                    HashSet<int> childs = AllActionTopicParentChilds[topicId].ToHashSet();
                    var childsCount = SearchDataCount.TopicWiseActionList
                                .Where(s => childs.Contains(s.Key)).SelectMany(s => s.Value)
                                .Distinct().Count();

                    return childsCount + SearchDataCount.ActionTopicCount
                                        .FirstOrDefault(w => w.Key == topicId).Value;
                }
                return 0;
            }
            else
            {
                var childsCount = SearchDataCount.TopicWiseActionList
                                    .Where(s => s.Key.Equals(topicId)).SelectMany(s => s.Value)
                                    .Distinct().Count();

                // return childsCount + SearchDataCount.ActionTopicCount.FirstOrDefault(w => w.Key == topicId).Value;
                return childsCount;
            }
        }

        public int GetPolicyTypeCount(int id)
        {
            return SearchDataCount.PolicyTypeCount.FirstOrDefault(w => w.Key == id).Value;
        }

        public int GetPolicyTypeCountryCount(int id)
        {
            return SearchDataCount.PolicyTypeCountryCount.FirstOrDefault(w => w.Key == id).Value;
        }

        public int GetProgramTypeCount(int id)
        {
            var data = SearchDataCount.ProgramTypeCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetProgramTypeCountryCount(int ProgramTypeId)
        {
            return SearchDataCount.ProgramTypeCountryCount.FirstOrDefault(w => w.Key == ProgramTypeId).Value;
        }

        public int GetFundingSourceContextCount(int id)
        {
            var data = SearchDataCount.FundingSourceCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetFundingSourceCountryCount(int partnerId)
        {
            return SearchDataCount.FundingSourceCountryCount.FirstOrDefault(w => w.Key == partnerId).Value;
                
        }

        public int GetTargetGroupCount(int id)
        {
            var data = SearchDataCount.TargetGroupCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetTargetGroupCountryCount(int targetGroupId)
        {
            return SearchDataCount.TargetGroupCountryCount.FirstOrDefault(w => w.Key == targetGroupId).Value;
        }
        public int GetDeliveryChnlContextCount(int id)
        {
            var data = SearchDataCount.DeliveryChnlCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetDeliveryChnlCountryCount(int deliveryChnlId)
        {
            return SearchDataCount.DeliveryChnlCountryCount.FirstOrDefault(w => w.Key == deliveryChnlId).Value;
        }
        public int GetProblemTypeCount(int id)
        {
            var data = SearchDataCount.ProblemTypeCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetProblemTypeCountryCount(int ProblemTypeId)
        {
            return SearchDataCount.ProblemTypeCountryCount.FirstOrDefault(w => w.Key == ProblemTypeId).Value;
        }

        public int GetMechanismTypeCount(int id)
        {
            var data = SearchDataCount.MechanismTypeCount.FirstOrDefault(w => w.Key == id);
            return data.Value;
        }

        public int GetICN2Count(int id, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)// as current item doesnt have parent so it's the parent node
            {
                if (Icn2Categories.Any(p => p.Id == id))
                {
                    var category = Icn2Categories.First(p => p.Id == id);
                    HashSet<int> childs = category.Icn2s.Select(c => c.Id).ToHashSet();
                    childs.Add(id);
                    return SearchDataCount.ICN2Count
                        .Where(s => childs.Contains(s.Key))
                        .SelectMany(s => s.Value)
                        .Distinct()
                        .Count();
                }
            }
            else
            {
                var icn2Count = SearchDataCount.ICN2Count.FirstOrDefault(w => w.Key == id).Value;
                return icn2Count == null ? 0 : icn2Count.Count();
            }
            return 0;
        }
        
        public int GetICN2CountryCount(int icn2Id, AntDesign.TreeNode<TermTreeNode> parentNode)
        {
            if (parentNode == null)
            {
                if (Icn2Categories.Any(p => p.Id == icn2Id))
                {
                    var category = Icn2Categories.First(p => p.Id == icn2Id);
                    // var partnerChilds = PartnerCategories.
                    HashSet<int> childs = category.Icn2s.Select(c => c.Id).ToHashSet();
                    childs.Add(icn2Id);
                    return SearchDataCount.BasicICN2CountryList
                        .Where(s => childs.Contains(s.Key))
                        .SelectMany(s => s.Value)
                        .Distinct()
                        .Count();
                }
            }
            else
            {
                var countryCount = SearchDataCount.BasicICN2CountryList.FirstOrDefault(w => w.Key == icn2Id).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
            return 0;

        }


        public async Task GetDataFromDataGrid(Dictionary<string, object> value)
        {
            FileDownloadChild.RefreshDataofSelectList(value);
            searchRequest.IsAllSelected = Convert.ToBoolean(value["IsAllSelected"]);
            TotalRecordMessage = value.ToString();
        }

        private void GetCountryPanelDetail(List<CountryWithRegion> countryWithRegions)
        {
            allCountryDetail.AddRange(countryWithRegions);
            allCountryDetail = allCountryDetail.DistinctBy(e => new { e.DataType, e.CountryCode }).ToList();
            RemovedRegionDetail();

        }
        private void RemovedRegionDetail()
        {
            var listofSelectedData = PolicyLookups.Where(e => e.IsChecked == true).Select(e => e.Name.First()).ToList();
            fullCountryDetail = allCountryDetail.OrderBy(e => e.CountryName).ToList();

            AllRegions = allCountryDetail.Where(e => e.RegionCode != null).OrderBy(e => e.RegionOrder).ThenBy(e => e.RegionCode)
                .GroupBy(d => d.RegionCode).Select(d => d.OrderByDescending(d => d.RegionDescription)
                .First()).Distinct().ToList();

            if (!CountryRegionGroupTree.Any())
            {
                foreach (var region in fullCountryDetail.Where(e => e.RegionCode != null).GroupBy(w => w.RegionCode))
                {
                    CountryRegionGroupTree.Add(new TermTreeNode()
                    {
                        Id = 0,
                        RegionCode = region.Key,
                        Name = region.Key,
                        Iso3Code = region.Key,
                        Children = mapper.Map<List<CountryWithRegion>, List<TermTreeNode>>(region.Select(s => s).ToList())
                    });
                }
            }

            AllIncomGroups = allCountryDetail.Where(e => e.IncomeGroupCode != null)
                .OrderBy(e => e.IncomeOrder)
                .Select(e => e.IncomeGroupCode)
                .Distinct().ToList();
            FilterCountries();
            StateHasChanged();
        }

        private void PolicyTypeClicked()
        {

        }
        #endregion Methods - ComponentBase Overrides

        #region Methods - Helpers

        private GTreeNode GetTreeNodeByTopicId(int topicId, List<GTreeNode> nodes)
        {
            var node = nodes.FirstOrDefault(t => t.TopicId == topicId);

            if (node == null)
            {
                foreach (var childNode in nodes)
                {
                    node = GetTreeNodeByTopicId(topicId, childNode.Children);

                    if (node != null)
                    {
                        break;
                    }
                }
            }

            return node;
        }

        private async Task OnKeywordSearch()
        {
            if (!string.IsNullOrWhiteSpace(searchRequest.SearchText) || previousSearchRequest.SearchText != searchRequest.SearchText)
            {
                _ = OnSearchClicked();
            }
        }


        private async Task OnSearchClicked()
        {
            if (!await VerifyRecaptcha())
            {
                return;
            }

            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            SetDataType();
            searchRequest.PageNo = 1;
            // searchRequest.SelectedSearchTopics = SelectedSearchTopicList;

            ClearSearchParams();

            DataGridforSearchchild.SearchRequest = searchRequest;

            _ = SearchGlobally(true);
            searchRequest.SelectedParentNutritions = lastSelectedNutritions;

            AddSearchHistory();
            previousSearchRequest = searchRequest.DeepClone();
            
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        private async Task<bool> VerifyRecaptcha()
        {
            var token = await JSRuntime.InvokeAsync<string>("runCaptcha", Configuration["ReCaptcha:SiteKey"].ToString());
            var data = VerifyToken(token);
            if (data)
            {
                return true;
            }
            return false;
        }

        private void AddSearchHistory()
        {
            SearchHistory history = new(pageConfigrationCache)
            {
                DataTypes = GetSelectedDataTypesString(),
                SelectedRegionCode = searchRequest.SelectedRegionCode,//(previousSearchRequest?.SelectedRegionCode != searchRequest.SelectedRegionCode || string.IsNullOrEmpty(previousSearchRequest.SelectedRegionCode)) ? searchRequest.SelectedRegionCode : "",
                SelectedIncomeGroup = searchRequest.SelectedIncomeCode,// previousSearchRequest?.SelectedIncomeCode != searchRequest.SelectedIncomeCode ? searchRequest.SelectedIncomeCode : "",
                StartYear = searchRequest.StartYear,//previousSearchRequest?.StartYear != searchRequest.StartYear ? searchRequest.StartYear : null,
                EndYear = searchRequest.EndYear,// previousSearchRequest?.EndYear != searchRequest.EndYear ? searchRequest.EndYear : null,
                SearchKeyword = previousSearchRequest?.SearchText != searchRequest.SearchText ? searchRequest.SearchText : "",
                SelectedCountries = searchRequest.SelectedCountries.ToList(),
                Topics = AllTopics,
                LastSelectedNutritions = GetNutritionDifference(),
                SelectedTopicIds = searchRequest.SelectedTopcis,
                
                SelectedPolicyTopicIds = searchRequest.SelectedPolicyTopics.DeepClone(),
                SelectedPolicyTopicIdsOnSearchTree = searchRequest.SelectedPolicyTopicsUIOnly.DeepClone(),
                SelectedMechanismTopicIds = searchRequest.SelectedMechanismTopics,
                SelectedActionTopicIdsOnSearchTree = SelectedActionTopicsOnSearchTree,
                SelectedActionTopicIds = searchRequest.SelectedActionTopics,
                SelectedMechanismTopicIdsOnSearchTree = SelectedMechanismTopicsOnSearchTree,
                Order = SearchHistories.Any() ? SearchHistories.Max(w => w.Order) + 1 : 1,
                SelectedSearchTopicIds = searchRequest.SelectedSearchTopics.DeepClone(),
                SelectedSearchTopicIdsOnSearchTree = searchRequest.SelectedSearchTopicsUIOnly.DeepClone(),
                // SelectedSearchTopicName = SelectedSearchTopicName,
                SelectedPolicyTopicName = SelectedPolicyTopicName,
                
                SelectedActionTopicName = SelectedActionTopicName,
                SelectedMechanismTopicName = SelectedMechanismTopicName,
                PolicyTypeIds = searchRequest.PolicyTypeIds.DeepClone(),
                MechanismTypeIds = searchRequest.MechanismTypeIds.DeepClone(),
                DeliveryChannelIds = searchRequest.DeliveryChannelIds.DeepClone(),
                FundingSourceIds = searchRequest.FundingSourceIds.DeepClone(),
                ICN2Ids = searchRequest.ICN2Ids.DeepClone(),
                LanguageIds = searchRequest.LanguageIds.DeepClone(),
                PartnerIds = searchRequest.PartnerIds.DeepClone(),
                ProblemIds = searchRequest.ProblemIds.DeepClone(),
                ProgramTypeIds = searchRequest.ProgramTypeIds.DeepClone(),
                TargetGroupIds = searchRequest.TargetGroupIds.DeepClone(),
                SelectedPartners = SelectedPartners,
                SelectedPolicyTypes = SelectedPolicyTypes,
                SelectedMechanismTypes = SelectedMechanismTypes,
                SelectedProgramTypes = SelectedProgramTypes,
                SelectedFundingSource = SelectedFundingSource,
                SelectedTargetGroup = SelectedTargetGroup,
                SelectedDeliveryChannels = SelectedDeliveryChannels,
                SelectedProblemsAndSolutions = SelectedProblemsAndSolutions,
                SelectedICN2Actions = SelectedICN2Actions,
                SelectedLanguages = SelectedLanguages,
                CountryGroupSelectedCountries = searchRequest.CountryGroupSelectedCountries.DeepClone(),
                CountryRegionSelectedCountries = searchRequest.CountryRegionSelectedCountries.DeepClone(),
                SearchRequest = searchRequest.DeepClone(),

            };

            if (!string.IsNullOrWhiteSpace(history.Name))
            {
                if (SearchHistories.Any(h => h.UniqueName.Equals(history.UniqueName)))
                {
                    SearchHistories = SearchHistories.Where(h => !h.UniqueName.Equals(history.UniqueName)).ToList();
                }

                SearchHistories.Add(history);
            }
        }

        private void ClearSearchParams()
        {
            if (!searchRequest.IsPolicyDataTypeSelected)
            {
                searchRequest.PolicyTypeIds = Enumerable.Empty<int>();
                searchRequest.SelectedPolicyTopics = new();
            }
            if (!searchRequest.IsMechanicalDataTypeSelected)
            {
                searchRequest.MechanismTypeIds = Enumerable.Empty<int>();
                searchRequest.SelectedMechanismTopics = new List<SelectedTopicsTree>();
            }
            if (!searchRequest.IsPragrammesAndActionsDataTypeSelected)
            {
                searchRequest.ProgramTypeIds = Enumerable.Empty<int>();
                searchRequest.ProblemIds = Enumerable.Empty<int>();
                searchRequest.DeliveryChannelIds = Enumerable.Empty<int>();
                searchRequest.FundingSourceIds = Enumerable.Empty<int>();
            }
            if (!searchRequest.IsCommitmentsDataTypeSelected)
            {
                searchRequest.ICN2Ids = new();
            }
        }

        private async Task LoadSearchHistory(int order)
        {
            var history = SearchHistories.Where(w => w.Order == order).First();
            SearchHistories.RemoveAll(w => w.Order > history.Order);

            searchRequest = history.SearchRequest.DeepClone();

            if (policyTopicTree != null)
            {
                policyTopicTree.UncheckAll();
                foreach (var item in history.SelectedPolicyTopicIdsOnSearchTree)
                {
                    var policyTopicTOBeChecked = policyTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item, true);
               
                    policyTopicTOBeChecked.CheckAllChildren();
                }
            }
            if (actionTopicTree != null)
            {
                actionTopicTree.UncheckAll();
                foreach (var item in history.SelectedActionTopicIdsOnSearchTree)
                {
                    actionTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item, true).CheckAllChildren();
                }
            }
            if (mechanismTopicTree != null)
            {
                mechanismTopicTree.UncheckAll();
                foreach (var item in history.SelectedMechanismTopicIdsOnSearchTree)
                {
                    mechanismTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item, true).CheckAllChildren();
                }
            }

            if (refCoutryGroupTree != null)
            {
                refCoutryGroupTree.UncheckAll();
                foreach (var item in history.CountryGroupSelectedCountries)
                {
                    var parentNode = refCoutryGroupTree.FindFirstOrDefaultNode(s => s.DataItem.Iso3Code == item.Key);

                    foreach (var data in item.Value)
                    {
                        refCoutryGroupTree.FindFirstOrDefaultNode(w => w.DataItem.Iso3Code == data && w.ParentNode.DataItem.Iso3Code == item.Key, true).Checked = true;
                    }

                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                }
            }

            if (refCoutryRegionTree != null)
            {
                refCoutryRegionTree.UncheckAll();
                foreach (var item in history.CountryRegionSelectedCountries)
                {
                    var parentNode = refCoutryRegionTree.FindFirstOrDefaultNode(s => s.DataItem.RegionCode == item.Key);

                    foreach (var data in item.Value)
                    {
                        refCoutryRegionTree.FindFirstOrDefaultNode(w => w.DataItem.Iso3Code == data && w.ParentNode.DataItem.RegionCode == item.Key, true).Checked = true;
                    }

                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                }
            }

            if (refPartnerTree != null)
            {
                refPartnerTree.UncheckAll();
                foreach (var item in history.PartnerIds)
                {
                    var parentNode = refPartnerTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).ParentNode;
                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                    refPartnerTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).CheckAllChildren();
                }
            }


            if (refPolicyTypeTree != null)
            {
                refPolicyTypeTree.UncheckAll();
                foreach (var item in history.PolicyTypeIds)
                {
                    refPolicyTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }


            if (refMechanismTypeTree != null)
            {
                refMechanismTypeTree.UncheckAll();
                foreach (var item in history.MechanismTypeIds)
                {
                    refMechanismTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refProgramTypeTree != null)
            {
                refProgramTypeTree.UncheckAll();
                foreach (var item in history.ProgramTypeIds)
                {
                    refProgramTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refFundingSource != null)
            {
                refFundingSource.UncheckAll();
                foreach (var item in history.FundingSourceIds)
                {
                    refFundingSource.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refLanguageTree != null)
            {
                refLanguageTree.UncheckAll();
                foreach (var item in history.LanguageIds)
                {
                    refLanguageTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }


            if (refTargetGroupTree != null)
            {
                refTargetGroupTree.UncheckAll();
                foreach (var item in history.TargetGroupIds)
                {
                    refTargetGroupTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refDeliveryTree != null)
            {
                refDeliveryTree.UncheckAll();
                foreach (var item in history.DeliveryChannelIds)
                {
                    refDeliveryTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refProblemTypeTree != null)
            {
                refProblemTypeTree.UncheckAll();
                foreach (var item in history.ProblemIds)
                {
                    refProblemTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).CheckAllChildren();
                }
            }

            if (refICN2Tree != null)
            {
                refICN2Tree.UncheckAll();
                foreach (var item in history.ICN2Ids)
                {
                    var parentNode = refICN2Tree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).ParentNode;
                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                    refICN2Tree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).CheckAllChildren();
                }
            }

            if (searchTree != null)
            {
                searchTree.UncheckAll();
                foreach (var item in history.SelectedSearchTopicIdsOnSearchTree)
                {
                    searchTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == int.Parse(item.Split('-').ElementAt(1)), true).Checked = true;
                }
            }


            PolicyLookups.ForEach(pl =>
            {
                if (pl.Name.Equals("Policies"))
                {
                    pl.IsChecked = searchRequest.IsPolicyDataTypeSelected;
                }

                if (pl.Name.Equals("Programmes and actions"))
                {
                    pl.IsChecked = searchRequest.IsPragrammesAndActionsDataTypeSelected;
                }

                if (pl.Name.Equals("Mechanisms"))
                {
                    pl.IsChecked = searchRequest.IsMechanicalDataTypeSelected;
                }

                if (pl.Name.Equals("SMART commitments"))
                {
                    pl.IsChecked = searchRequest.IsCommitmentsDataTypeSelected;
                }
            });

            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));

            if (searchRequest.SelectedCountries.Any())
            {
                CountryVisible = true;
            }

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            StateHasChanged();
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }
        private List<string> GetNutritionDifference()
        {
            if (!searchRequest.SelectedParentNutritions.Any())
            {
                return new();
            }

            var addedNutritions = searchRequest.SelectedParentNutritions.Except(previousSearchRequest.SelectedParentNutritions).ToList();

            if (addedNutritions.Any())
            {
                return addedNutritions;
            }

            var removedNutritions = previousSearchRequest.SelectedParentNutritions.Except(searchRequest.SelectedParentNutritions).ToList();

            if (removedNutritions.Any())
            {
                return searchRequest.SelectedParentNutritions;
            }

            return new();
        }
        private List<string> GetSelectedDataTypesString()
        {
            List<string> dataTypes = new();

            if (searchRequest.IsPolicyDataTypeSelected && searchRequest.IsPragrammesAndActionsDataTypeSelected &&
                    searchRequest.IsMechanicalDataTypeSelected && searchRequest.IsCommitmentsDataTypeSelected)
            {
                dataTypes.Add("All data types");
                return dataTypes;
            }

            if (searchRequest.IsPolicyDataTypeSelected)
            {
                dataTypes.Add("Policies");
            }

            if (searchRequest.IsPragrammesAndActionsDataTypeSelected)
            {
                dataTypes.Add("Programmes and actions");
            }

            if (searchRequest.IsMechanicalDataTypeSelected)
            {
                dataTypes.Add("Mechanisms");
            }

            if (searchRequest.IsCommitmentsDataTypeSelected)
            {
                dataTypes.Add("Commitments");
            }

            return dataTypes;
        }

        private void SetDataType()
        {
            searchRequest.IsPragrammesAndActionsDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Programmes and actions").IsChecked;
            searchRequest.IsPolicyDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Policies").IsChecked;
            searchRequest.IsMechanicalDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Mechanisms").IsChecked;
            searchRequest.IsCommitmentsDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "SMART commitments").IsChecked;
        }

        private async Task ToggleDataTypeSelection(string name, bool isChecked)
        {
            var listofSelectedData = PolicyLookups.FirstOrDefault(e => e.Name == name).IsChecked = isChecked;
            await ToggleSearchFiltersLoader(Visibility.Invisible);
            if (name == "Policies")
            {
                searchRequest.IsPolicyDataTypeSelected = isChecked;
                await GetAdvanceSearchFilters();
                await OnSearchClicked();
                
                if (!isChecked)
                {
                    RemovedRegionDetail();
                }
            }

            if (name == "Mechanisms")
            {
                searchRequest.IsMechanicalDataTypeSelected = isChecked;
                await GetAdvanceSearchFilters();
                await OnSearchClicked();
                

            }
            if (name == "Programmes and actions")
            {
                searchRequest.IsPragrammesAndActionsDataTypeSelected = isChecked;
                await GetAdvanceSearchFilters();
                await OnSearchClicked();
                
            }
            if (name == "SMART commitments")
            {
                searchRequest.IsCommitmentsDataTypeSelected = isChecked;
                await GetAdvanceSearchFilters();
                await OnSearchClicked();
            }

            
            PolicyLookups.Where(w => w.Name == name).FirstOrDefault().IsChecked = isChecked;
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms" || w.Name == "Actions"));
            //CheckSearchButtonAvailability();
            await ToggleSearchFiltersLoader(Visibility.Invisible);
            // await InvokeAsync(StateHasChanged);
        }

        private async void CheckSearchButtonAvailability()
        {
            if (searchRequest.IsPolicyDataTypeSelected || searchRequest.IsPragrammesAndActionsDataTypeSelected || searchRequest.IsMechanicalDataTypeSelected)
            {
                IsTopicDDDisabled = false;
            }
            else
            {
                IsTopicDDDisabled = true;
            }

            await Task.Run(async () => await InvokeAsync(StateHasChanged));
        }

        public void ChangeStartYear(DateTime? dateTime)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            searchRequest.StartDate = dateTime;
            OnSearchClicked();
        }

        public void ChangeEndYear(DateTime? dateTime)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            searchRequest.EndDate = dateTime;
            OnSearchClicked();
        }

        public bool CountryVisible { get; set; } = false;

        private void OnRegionChanged(CountryWithRegion value)
        {
            if (value == null)
            {
                searchRequest.SelectedRegionCode = String.Empty;
            }
            else
            {
                searchRequest.SelectedRegionCode = value.RegionCode;
            }
            FilterCountries();
            OnSearchClicked();
        }
        private void OnSelectedCountries(IEnumerable<CountryWithRegion> values)
        {

            searchRequest.SelectedCountries = values?.Select(v => v.CountryName).ToList() ?? new List<string>();
            OnSearchClicked();
        }


        private void OnIncomeGroupChanged(string value)
        {
            searchRequest.SelectedIncomeCode = value;
            ResetSelectedCountries();
            FilterCountries();
            OnSearchClicked();
        }
        private void ResetSelectedCountries()
        {
            searchRequest.SelectedCountries = Enumerable.Empty<string>();
        }

        private void FilterCountries()
        {
            var listofSelectedData = PolicyLookups.Where(e => e.IsChecked == true).Select(e => e.Name.First());
            if(string.IsNullOrEmpty(searchRequest.SelectedIncomeCode) && string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                AllIncomGroups = allCountryDetail.Where(e => e.IncomeGroupCode != null)
                    .OrderBy(e => e.IncomeOrder)
                    .Select(e => e.IncomeGroupCode)
                    .Distinct().ToList();
                fullCountryDetail = fullCountryDetail.DistinctBy(e => e.CountryName).ToList();
            }
            else if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeCode) && !string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                AllIncomGroups = allCountryDetail.Where(e => !string.IsNullOrEmpty(e.IncomeGroupCode) && !string.IsNullOrEmpty(e.RegionCode))
                             .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                             .OrderBy(e => e.IncomeOrder)
                             .Select(e => e.IncomeGroupCode)
                             .Distinct().ToList();

                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrEmpty(e.IncomeGroupCode) && !string.IsNullOrEmpty(e.RegionCode)).Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode) && e.IncomeGroupCode.Equals(searchRequest.SelectedIncomeCode))
                    .DistinctBy(e => e.CountryCode).OrderBy(e => e.CountryName).DistinctBy(e => e.CountryName).ToList();

            }
            else if (!string.IsNullOrEmpty(searchRequest.SelectedRegionCode))
            {
                AllIncomGroups = allCountryDetail.Where(e => !string.IsNullOrEmpty(e.RegionCode) && !string.IsNullOrEmpty(e.IncomeGroupCode))
                             .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                             .OrderBy(e => e.IncomeOrder)
                             .Select(e => e.IncomeGroupCode)
                             .Distinct().ToList();
                //IncomeGroupSwap();

                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrEmpty(e.RegionCode)).Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                    .DistinctBy(e => e.CountryCode).OrderBy(e => e.CountryName).DistinctBy(e => e.CountryName).ToList();
            }
            else if (!string.IsNullOrEmpty(searchRequest.SelectedIncomeCode))
            {
                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrEmpty(e.IncomeGroupCode)).Where(e => e.IncomeGroupCode.Equals(searchRequest.SelectedIncomeCode))
                    .DistinctBy(e => e.CountryCode).OrderBy(e => e.CountryName).DistinctBy(e => e.CountryName).ToList();
            }
            fullCountryDetail = fullCountryDetail.DistinctBy(e => e.CountryName).ToList();
            StateHasChanged();
        }

        private async Task GetTopicsAsync()
        {
            AllTopics = await PolicyService.GetTopicsAsync();
            var DataTopicsParentsDictionary = AllTopics.Where(t=>t.Name.Equals("Policy") || t.Name.Equals("Mechanism") || t.Name.Equals("Action")).ToDictionary(e=>e.Name, e=>e.Id);
            AllParentTopics = await PolicyService.GetParentTopics();
            var AllPranetTopicsDictionary = AllParentTopics.GroupBy(parent=>parent.ParentId).ToDictionary(group=>group.Key, group => group.ToList());
            var policyParentTopicId =DataTopicsParentsDictionary["Policy"];

            hasPolicyTopics = policyParentTopicId > 0;

            if (hasPolicyTopics)
            {
                var policyTopics = AllPranetTopicsDictionary[policyParentTopicId].OrderBy(x => x.OrderKey).ToList();

                await BuildPolicyTopicTreeView(policyTopics);
                await RecursivelyGetPolicyTopicChilds();
            }

            var mechanismParentTopicId = DataTopicsParentsDictionary["Mechanism"];


            var actionParentTopicId =  DataTopicsParentsDictionary["Action"];
            hasMechanismTopics = mechanismParentTopicId > 0;
            hasActionTopics = actionParentTopicId > 0;

            if (hasMechanismTopics)
            {
                var mechanismTopics = AllPranetTopicsDictionary[mechanismParentTopicId].OrderBy(x => x.OrderKey).ToList();

                await BuildMechanismTopicTreeView(mechanismTopics);
                await RecursivelyGetMechanismTopicChilds();
            }
            if (hasActionTopics)
            {
                var actionTopics = AllPranetTopicsDictionary[actionParentTopicId].OrderBy(x => x.OrderKey).ToList();

                await BuildActionTopicTreeView(actionTopics);
                await RecursivelyGetActionTopicChilds();
            }

           
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            StateHasChanged();
        }


        public async Task RecursivelyGetPolicyTopicChilds()
        {
            try
            {
                foreach (var item in PolicyTopicParentChilds)
                {
                    if (!AllPolicyTopicParentChilds.ContainsKey(item.Key))
                    {
                        AllPolicyTopicParentChilds.Add(item.Key, item.Value);
                    }
                    foreach (var childId in item.Value.ToList())
                    {
                        await AddTopicChildRecusively(item.Key, childId, PolicyTopicParentChilds, AllPolicyTopicParentChilds);
                    }
                }
            }

            catch (Exception e)
            {

            }
        }

        public async Task RecursivelyGetMechanismTopicChilds()
        {
            try
            {
                foreach (var item in MechanismTopicParentChilds)
                {
                    if (!AllMechanismTopicParentChilds.ContainsKey(item.Key))
                    {
                        AllMechanismTopicParentChilds.Add(item.Key, item.Value);
                    }
                    foreach (var childId in item.Value.ToList())
                    {
                        await AddTopicChildRecusively(item.Key, childId, MechanismTopicParentChilds, AllMechanismTopicParentChilds);
                    }
                }
            }

            catch (Exception e)
            {

            }
        }

        public async Task RecursivelyGetActionTopicChilds()
        {
            foreach (var item in ActionTopicParentChilds)
            {
                if (!AllActionTopicParentChilds.ContainsKey(item.Key))
                {
                    AllActionTopicParentChilds.Add(item.Key, item.Value);
                }
                foreach (var childId in item.Value.ToList())
                {
                    await AddTopicChildRecusively(item.Key, childId, ActionTopicParentChilds, AllActionTopicParentChilds);
                }
            }
        }

        public async Task AddTopicChildRecusively(int paretnId, int childId, Dictionary<int, List<int>> directParentChild, Dictionary<int, List<int>> parentChildAllLevel)
        {
            if (directParentChild.ContainsKey(childId))
            {
                parentChildAllLevel[paretnId].AddRange(directParentChild[childId]);
                foreach (var item in directParentChild[childId])
                {
                    await AddTopicChildRecusively(paretnId, item, directParentChild, parentChildAllLevel);
                }
            }
        }


        // private string ParentName { get; set; }
        private async Task BuildPolicyTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                var parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId))
                                                .OrderBy(t => t.OrderKey)
                                                .ToList();

                if (parentTopics.Any())
                {
                    if (PolicyTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        PolicyTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId));
                    }
                    else
                    {
                        PolicyTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                    }
                }

                var topic = await GetChildTopicTreeView(parentTopics, Gina2.Core.Constants.Policy, 1); // Start at level 1

                PolicyTopicTreeNodes.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = item.Topic.Name,
                    Key = $"0:{item.ParentId}-{item.TopicId}|{item.Topic.Name}", // Level 0
                    Children = topic,
                    IsSelected = false
                });
            }
        }

        private async Task BuildMechanismTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                var parentTopics = new List<DbModels.TopicParent>();

                parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList();

                if (parentTopics.Any())
                {
                    if (MechanismTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        MechanismTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId));
                    }
                    else
                    {
                        MechanismTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                    }
                }

                var topic = await GetChildTopicTreeView(parentTopics, Gina2.Core.Constants.Mechanism, 1);

                MechanismTopicTreeNodes.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = item.Topic.Name,
                    Key = $"0:{item.ParentId}-{item.TopicId}|{item.Topic.Name}", // Level 0
                    Children = topic,
                    IsSelected = false
                });
            }
        }
        private async Task BuildActionTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                var parentTopics = new List<DbModels.TopicParent>();

                parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList();

                if (parentTopics.Any())
                {
                    if (ActionTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        ActionTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId));
                    }
                    else
                    {
                        ActionTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                    }
                }

                var topic = await GetChildTopicTreeView(parentTopics, Gina2.Core.Constants.Action, 1);

                ActionTopicTreeNodes.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = item.Topic.Name,
                    Key = $"0:{item.ParentId}-{item.TopicId}|{item.Topic.Name}", // Level 0
                    Children = topic,
                    IsSelected = false
                });
            }
        }
        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First, string entityType, int level)
        {

            List<GTreeNode> child = new();

            // Convert AllTopics and AllParentTopics to dictionaries for faster lookups
            var allTopicsDictionary = AllTopics.ToDictionary(t => t.Id);
            var allParentTopicsLookup = AllParentTopics.ToLookup(x => x.ParentId);

            foreach (var item in First)
            {
                // Retrieve child topics
                var parentTopics = allParentTopicsLookup[item.TopicId]
                                        .OrderBy(t => t.OrderKey)
                                        .ToList();

                // Handle entity-specific behavior for Policy and others
                if (parentTopics.Any() && entityType == Gina2.Core.Constants.Policy)
                {
                    if (PolicyTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        PolicyTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId));
                    }
                    else
                    {
                        PolicyTopicParentChilds[item.TopicId] = parentTopics.Select(s => s.TopicId).ToList();
                    }
                }

                // Recursive call for child topics
                var childTopics = await GetChildTopicTreeView(parentTopics, entityType, level + 1);

                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = item.Topic.Name,
                    Key = $"{level}:{item.ParentId}-{item.TopicId}|{item.Topic.Name}",
                    Children = childTopics,
                    IsSelected = false
                });
            }

            return child;
        }


        private async Task PolicyTopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            (SelectedPolicyTopicName,SelectedPolicyTopicOnSearchTree) = GetCheckedTopicsNames(checkedValue);

            searchRequest.SelectedPolicyTopics = GetParentChildDictionary(checkedValue.Tree.CheckedKeys);
            searchRequest.SelectedPolicyTopicsUIOnly = SelectedPolicyTopicOnSearchTree;
            _ = OnSearchClicked();
        }

        private (string,List<int>) GetCheckedTopicsNames(TreeEventArgs<GTreeNode> checkedValue)
        {
            SelectedPolicyTopicName = string.Empty;
            var listofTopicIdAndName = checkedValue.Tree.CheckedKeys.Select(e => e.Split('-').ElementAt(1)).ToList();
            var listofTopicIds = listofTopicIdAndName.Select(e => int.Parse( e.Split('|').ElementAt(0))).ToList();
            var listofTopicNames = listofTopicIdAndName.Select(e => e.Split('|').ElementAt(1)).ToList();
            return (string.Join(",", listofTopicNames), listofTopicIds);
        }

        public static List<SelectedTopicsTree> GetParentChildDictionary(string[] values)
        {
            // Step 1: Create a dictionary to hold parent-child relationships.
            var parentChildDict = new Dictionary<int, SelectedTopicsTree>();

            // Step 2: Populate the dictionary.
            foreach (var value in values)
            {
                // Split by ':' to separate level and the rest
                var parts = value.Split(':');
                int level = int.Parse(parts[0]);

                // Split by '-' to separate parent and topic
                var parentAndTopic = parts[1].Split('-');
                int parentId = int.Parse(parentAndTopic[0]);

                // Split by '|' to separate topicId and topicName
                var topicAndName = parentAndTopic[1].Split('|');
                int topicId = int.Parse(topicAndName[0]);
                string topicName = topicAndName[1];

                // Create a new topic node
                var topic = new SelectedTopicsTree
                {
                    Level = level,
                    TopicId = topicId,
                    TopicName = topicName
                };

                // Add or update the dictionary entry for the parent
                if (!parentChildDict.ContainsKey(parentId))
                {
                    parentChildDict[parentId] = new SelectedTopicsTree
                    {
                        Level = level - 1, // Parent level is one less than the child level
                        TopicId = parentId,
                        ChildTopics = new List<SelectedTopicsTree>()
                    };
                }
                parentChildDict[parentId].ChildTopics.Add(topic);

                // Ensure the topic itself is stored in the dictionary
                if (!parentChildDict.ContainsKey(topicId))
                {
                    parentChildDict[topicId] = topic;
                }
            }

            // Step 3: Exclude parents that are also children.
            var result = parentChildDict.Values
                .Where(topic => !parentChildDict.Values.Any(parent => parent.ChildTopics.Any(child => child.TopicId == topic.TopicId)))
                .ToList();

            return result;
        }
        private async Task ActionTopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            (SelectedActionTopicName,SelectedActionTopicsOnSearchTree) = GetCheckedTopicsNames(checkedValue);
            searchRequest.SelectedActionTopics = GetParentChildDictionary(checkedValue.Tree.CheckedKeys);
            
            _ = OnSearchClicked();
        }
        private async Task MechanismTopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            (SelectedMechanismTopicName,SelectedMechanismTopicsOnSearchTree) = GetCheckedTopicsNames(checkedValue);
            searchRequest.SelectedMechanismTopics = GetParentChildDictionary(checkedValue.Tree.CheckedKeys);
            
            _ = OnSearchClicked();

        }
        private void OnToggleSelectAll()
        {
            searchRequest.IsAllSelected = !searchRequest.IsAllSelected;
            DataGridforSearchchild.SelectDeselectCheckBox(searchRequest);
            StateHasChanged();
        }
        private async Task OnMapExportClicked()
        {
            SetDataType();
            searchRequest.DefaultPage = "map";
            await ProtectedSessionStore.SetAsync("config", Serialize());
            //NavigationManager.NavigateTo("map/redirect_filter_data", true);
            await JSRuntime.InvokeVoidAsync("open", $"map/redirect_filter_data", "_blank");
        }
        private string Serialize()
        {
           string encodedStr = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(searchRequest, Formatting.Indented, new SpecialDateTimeConverter())));
            return encodedStr;
        }

        private bool IsUserClickedEnterToSearch = false;

        private bool IsNonFunctionKey(string key)
        {
            // List of common function keys that should not trigger debounce
            var functionKeys = new HashSet<string>
            {
                "Enter", "Tab", "Shift", "Control", "Alt", "CapsLock", "Escape",
                "PageUp", "PageDown", "End", "Home", "ArrowLeft", "ArrowUp", 
                "ArrowRight", "ArrowDown", "Insert", "Delete", "Meta", "ContextMenu",
                "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"
            };

            return !functionKeys.Contains(key);
        }

        private async Task OnSearchTextEnter(KeyboardEventArgs e)
        {
            if (e.Key == "Enter")
            {
                IsUserClickedEnterToSearch = true;
                
                // Execute search logic directly
                _ = ToggleSearchFiltersLoader(Visibility.Visible);
                await OnSearchClicked();
                _ = ToggleSearchFiltersLoader(Visibility.Invisible);

                IsUserClickedEnterToSearch = false; // Reset the flag after execution
            }
            else if (IsNonFunctionKey(e.Key))
            {
                _debouncer.Dispatch(async () =>
                {
                    if (!IsUserClickedEnterToSearch)
                    {
                        
                        _ = ToggleSearchFiltersLoader(Visibility.Visible);
                        await OnSearchClicked();
                        _ = ToggleSearchFiltersLoader(Visibility.Invisible);
                    }
                });
            }

        }


        private async Task<List<GTreeNode>> GetChildTopicInSearch(IEnumerable<Gina2.DbModels.TopicParent> First, int grandParentId)
        {
            List<GTreeNode> child = new List<GTreeNode>();
            foreach (var item in First)
            {
                var parentTopics = new List<DbModels.TopicParent>();
                parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList();
                //if (parentTopics.Any())
                //{
                if (BasicTopicParentChilds.ContainsKey(item.TopicId))
                {
                    BasicTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId).ToList());
                }
                else
                {
                    BasicTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                }
                //}
                if (AllTopics.Where(w => w.IsSelected).Select(w => w.Id).Contains(item.TopicId))
                {
                    GTreeNode itemchild = new GTreeNode();
                    child.Add(new GTreeNode()
                    {
                        TopicId = item.TopicId,
                        ParentId = item.ParentId,
                        Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        Key = $"{item.ParentId}-{item.TopicId}",
                        Children = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId),
                        IsSelected = false
                    });
                }
                else
                {
                    var childList = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId);
                }

            }
            return child;
        }

        private void GetTopicListForParentTopic(int parentId, List<int> ParentAllTopicIds)
        {
            foreach (var item in ParentAllTopicIds)
            {
                // get topic id if topic has some parent,
                var GetAllTopicsforParentTopic2 = AllParentTopics.Where(e => item == e.ParentId).Select(e => e.TopicId).Distinct().ToList();
                if (!GetAllTopicsforParentTopic2.Any())
                {
                    PapulateSelectedTopicList(SelectedPolicyTopicList, parentId, item.ToString());
                }
                else
                {
                    GetTopicListForParentTopic(parentId, GetAllTopicsforParentTopic2);
                }
            }
        }

        private void PapulateSelectedTopicList(Dictionary<int, List<int>> selectedTopicList, int parentId, string item)
        {
            if (!selectedTopicList.ContainsKey(parentId))
            {
                selectedTopicList.Add(parentId, new List<int>());
                selectedTopicList[parentId].Add(int.Parse(item));
            }
            if (selectedTopicList.ContainsKey(parentId) && !selectedTopicList[parentId].Any(e => e == int.Parse(item)))
            {
                selectedTopicList[parentId].Add(int.Parse(item));
            }
        }

        private async void PolicyTypeTreeCheckboxClicked(TreeEventArgs<PolicyTypeViewModel> checkedValue)
        {
            searchRequest.PolicyTypeIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));
            SelectedPolicyTypes = checkedValue.Node.DataItem.Name;
            _ = OnSearchClicked();
        }

        private async Task CountryGroupCheckboxClicked(TreeEventArgs<TermTreeNode> checkedValue)
        {
            string parentId = string.Empty;
            List<string> childIds = new();

            if (checkedValue.Node.ParentNode != null)//checked item is a child
            {
                parentId = checkedValue.Node.ParentNode.DataItem.Iso3Code;
                childIds.Add(checkedValue.Node.DataItem.Iso3Code);
            }
            else
            {
                parentId = checkedValue.Node.DataItem.Iso3Code;
                childIds.AddRange(checkedValue.Node.GetParentChildDataItems().Where(s => s.Iso3Code == parentId).First().Children.Select(s => s.Iso3Code));
            }

            if (checkedValue.Node.Checked)
            {
                if (searchRequest.CountryGroupSelectedCountries.ContainsKey(parentId))
                {
                    searchRequest.CountryGroupSelectedCountries[parentId].AddRange(childIds);
                }
                else
                {
                    searchRequest.CountryGroupSelectedCountries.Add(parentId, childIds);
                }
            }
            else
            {
                if (searchRequest.CountryGroupSelectedCountries.ContainsKey(parentId))
                {
                    searchRequest.CountryGroupSelectedCountries[parentId].RemoveAll(w => childIds.Contains(w));
                    if (searchRequest.CountryGroupSelectedCountries[parentId].Count == 0)
                    {
                        searchRequest.CountryGroupSelectedCountries.Remove(parentId);
                    }
                }
                else
                {
                    searchRequest.CountryGroupSelectedCountries.Remove(parentId);
                }
            }

            _ = OnSearchClicked();
        }

        private async Task CountryRegionCheckboxClicked(TreeEventArgs<TermTreeNode> checkedValue)
        {
            string parentId = string.Empty;
            List<string> childIds = new();

            if (checkedValue.Node.ParentNode != null)//checked item is a child
            {
                parentId = checkedValue.Node.ParentNode.DataItem.RegionCode;
                childIds.Add(checkedValue.Node.DataItem.Iso3Code);
            }
            else
            {
                parentId = checkedValue.Node.DataItem.RegionCode;
                childIds.AddRange(checkedValue.Node.GetParentChildDataItems().Where(s => s.RegionCode == parentId).First().Children.Select(s => s.Iso3Code));
            }

            if (checkedValue.Node.Checked)
            {
                if (searchRequest.CountryRegionSelectedCountries.ContainsKey(parentId))
                {
                    searchRequest.CountryRegionSelectedCountries[parentId].AddRange(childIds);
                }
                else
                {
                    searchRequest.CountryRegionSelectedCountries.Add(parentId, childIds);
                }
            }
            else
            {
                if (searchRequest.CountryRegionSelectedCountries.ContainsKey(parentId))
                {
                    searchRequest.CountryRegionSelectedCountries[parentId].RemoveAll(w => childIds.Contains(w));
                    if (searchRequest.CountryRegionSelectedCountries[parentId].Count == 0)
                    {
                        searchRequest.CountryRegionSelectedCountries.Remove(parentId);
                    }
                }
                else
                {
                    searchRequest.CountryRegionSelectedCountries.Remove(parentId);
                }
            }

            _ =  OnSearchClicked();
        }


        private async void MechanismTypeTreeCheckboxClicked(TreeEventArgs<MechanismType> checkedValue)
        {
            searchRequest.MechanismTypeIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));
            
            SelectedMechanismTypes = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }
        private async void ProgramTypeTreeCheckboxClicked(TreeEventArgs<ProgramType> checkedValue)
        {
            searchRequest.ProgramTypeIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));

            SelectedProgramTypes = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }
        private async void FundingSourceTreeCheckboxClicked(TreeEventArgs<PartnerCategory> checkedValue)
        {
            searchRequest.FundingSourceIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));

            SelectedFundingSource = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }

        private async Task TargetGroupTreeCheckboxClicked(TreeEventArgs<TargetGroup> checkedValue)
        {
            searchRequest.TargetGroupIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));

            SelectedTargetGroup = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }
        private async void DeliveryChnlTreeCheckboxClicked(TreeEventArgs<Delivery> checkedValue)
        {
            searchRequest.DeliveryChannelIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));

            SelectedDeliveryChannels = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }

        private async void ProblemTypeTreeCheckboxClicked(TreeEventArgs<ProblemType> checkedValue)
        {
            searchRequest.ProblemIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));

            SelectedProblemsAndSolutions = checkedValue.Node.DataItem.Name;

            _ =  OnSearchClicked();
        }

        private async void ICN2TreeCheckboxClicked(TreeEventArgs<TermTreeNode> checkedValue)
        {
            Dictionary<int, List<int>> selectedICN2Ids = new Dictionary<int, List<int>>();
            if (checkedValue.Node.Checked)
            {
                if(checkedValue.Node.DataItem.Children != null && checkedValue.Node.DataItem.Children.Any())
                {
                    selectedICN2Ids.Add(checkedValue.Node.DataItem.Id,checkedValue.Node.DataItem.Children.Select(c=>c.Id).ToList<int>());
                }
                else
                {
                    selectedICN2Ids.Add(checkedValue.Node.DataItem.Id,new List<int>());
                }
            }
            
            searchRequest.ICN2Ids = selectedICN2Ids;
            
            if(checkedValue.Node.DataItem.Children != null && checkedValue.Node.DataItem.Children.Any())
            {
                var allSelectedChildrenNames =checkedValue.Node.DataItem.Children.Select(node => node.Name);
                SelectedICN2Actions = string.Join(",", allSelectedChildrenNames);
            }
            else
            {
                SelectedICN2Actions = checkedValue.Node.DataItem.Name;
            }

            _ = OnSearchClicked();
        }

        private async void LanguageCheckboxClicked(TreeEventArgs<Language> checkedValue)
        {
            searchRequest.LanguageIds = checkedValue.Tree.CheckedKeys.Select(s => Convert.ToInt32(s));
            
            SelectedLanguages = checkedValue.Node.DataItem.Name;
            
            _ =  OnSearchClicked();
        }

        private async void PartnerTreeCheckboxClicked(TreeEventArgs<TermTreeNode> checkedValue)
        {
            Dictionary<int, List<int>> selectedPartner = new Dictionary<int, List<int>>();
            if (checkedValue.Node.Checked)
            {
                if(checkedValue.Node.DataItem.Children != null && checkedValue.Node.DataItem.Children.Any())
                {
                    selectedPartner.Add(checkedValue.Node.DataItem.Id,checkedValue.Node.DataItem.Children.Select(c=>c.Id).ToList<int>());
                }
                else
                {
                    selectedPartner.Add(checkedValue.Node.DataItem.Id,new List<int>());
                }
            }
            
            searchRequest.PartnerIds = selectedPartner;
            
            if(checkedValue.Node.DataItem.Children != null && checkedValue.Node.DataItem.Children.Any())
            {
                var allSelectedChildrenNames =checkedValue.Node.DataItem.Children.Select(node => node.Name);
                SelectedPartners = string.Join(",", allSelectedChildrenNames);
            }
            else
            {
                SelectedPartners = checkedValue.Node.DataItem.Name;
            }

            _ = OnSearchClicked();
        }
        

        private async Task GetChildSelectedTopicInSearch(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                var childs = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList();
                if (childs.Any())
                {
                    await GetChildSelectedTopicInSearch(childs);
                }
                //else
                //{
                //    PapulateSelectedTopicList(item.ParentId,item.TopicId.ToString());
                //}
            }
        }

        
        public bool VerifyToken(string _Token)
        {
            using HttpClient httpClient = new HttpClient();
            using var res = httpClient.GetAsync($"https://www.google.com/recaptcha/api/siteverify?secret={Configuration["ReCaptcha:SecretKey"]}&response={_Token}").Result;

            if (res.StatusCode != HttpStatusCode.OK)
            {
                return false;
            }
            string JSONres = res.Content.ReadAsStringAsync().Result;
            dynamic JSONdata = JObject.Parse(JSONres);

            if (JSONdata.success != "true" || JSONdata.score <= 0.5m)
            {
                //OpenErrorToaster($"score {JSONdata.score} is too low ");
                return false;
            }

            return true;

        }

        private string GetIncomeGroup(string incomeGroupCode)
        {
            string name = string.Empty;
            switch (incomeGroupCode)
            {
                case "LIC":
                    name = "LIC - Low-income countries";
                    break;
                case "LMC":
                    name = "LMC - Lower-middle income countries";
                    break;
                case "UMC":
                    name = "UMC - Upper-middle income countries";
                    break;
                case "HIC":
                    name = "HIC - High-income countries";
                    break;
                default:
                    break;
            }
            return name;
        }

        private string GetRegionName(string regionCode)
        {
            string name = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    name = "AFR - African Region";
                    break;
                case "AMR":
                    name = "AMR - Region of the Americas";
                    break;
                case "EMR":
                    name = "EMR - Eastern Mediterranean Region";
                    break;
                case "EUR":
                    name = "EUR - European Region";
                    break;
                case "SEAR":
                    name = "SEAR - South-East Asia Region";
                    break;
                case "WPR":
                    name = "WPR - Western Pacific Region";
                    break;
                default:
                    break;
            }
            return name;
        }
        #endregion Methods - Helpers

        #endregion Methods
        
    }
}