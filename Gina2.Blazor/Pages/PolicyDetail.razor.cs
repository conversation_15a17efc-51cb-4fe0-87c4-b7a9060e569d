﻿using Gina2.Blazor.Helpers.Authenticate;
using Gina2.Core.Interface;
using Gina2.MySqlRepository.Repositories;
using Microsoft.AspNetCore.Components;
using PolicyLog = Gina2.DbModels.PolicyDrafts.PolicyLog;

namespace Gina2.Blazor.Pages
{
    public partial class PolicyDetail
    {
        [Inject]
        private IAuthenticateHelper AuthenticateHelper { get; set; }

        [Inject]
        private ICurrentUserService currentUserService { get; set; }

        [Inject]
        private IPolicyRepository policyRepository { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        [Parameter]
        public int PolicyCode { get; set; }

        [Parameter]
        public int VersionId { get; set; }

        private bool isAuthenticated;
        private string role;
        private string LoginUser;
        private string PublishedUser;
        private bool IsPublished = true;

        protected override async Task OnInitializedAsync()
        {
            isAuthenticated = true;            
            (role, LoginUser) = await AuthenticateHelper.IsAuthenticated();

            List<PolicyLog> policyLogs = await AuthenticateHelper.IsPublished<PolicyLog>(p => p.PolicyId == PolicyCode);
            if (policyLogs is null || policyLogs.Count == 0)
            {
                isAuthenticated = false;
                return;
            }

            if (CanAccessForPublishedLog(policyLogs))
                return;

            if (await CanAccessForLoginUserOrRole(policyLogs))
                return;

            isAuthenticated = false;
        }

        private bool CanAccessForPublishedLog(List<PolicyLog> policyLogs)
        {
            PolicyLog publishedLog = VersionId > 0
                        ? policyLogs.FirstOrDefault(v => v.PolicyVId == VersionId)
                        : policyLogs.FirstOrDefault(v => v.IsPublished);

            IsPublished = publishedLog?.IsPublished ?? false;
            PublishedUser = publishedLog?.UserName;

            if (IsPublished) 
                return true;

            return false;
        }

        private async Task<bool> CanAccessForLoginUserOrRole(List<PolicyLog> policyLogs)
        {
            var revisionLog = policyLogs.FirstOrDefault(v => v.PolicyVId == VersionId);                     
            
            if (revisionLog?.UserName == LoginUser || revisionLog?.DelegatedUserName == LoginUser)
            {
                return true;
            }

            if (string.Equals(role, "admin", StringComparison.OrdinalIgnoreCase))
                return true;

            if (role.Equals("approver", StringComparison.OrdinalIgnoreCase) && revisionLog?.UserName != LoginUser)
            {
                var policyCountries = await policyRepository
                    .GetPolicyCountriesByPolicyIdAndRevisionId(PolicyCode, VersionId);
                var approverCountries = currentUserService.ApproverCountryCode;

                if(approverCountries.Any(country => policyCountries.Contains(country)))
                {
                    return true;
                }
            }                

            return false;
        }
    }
}
