﻿@page "/nutritionpartners/"
@using Plk.Blazor.DragDrop;
<Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-7 pb-7">
                    <Div Class="downl-flex">
                            <Heading Size="HeadingSize.Is3"></Heading>
                            <Heading Class="h-title">Convening nutrition partners</Heading>
                        </Div>
                </Container>
            </Card>
</Container>
<Container Class="pt-5 pb-5">
  <DataGrid Class="table-nth" 
                          TItem="@MyDrafts"
                          Data="@MyDraftsResults"
                          PageSize="3"
                          ShowPageSizes
                          ShowPager
                          Responsive
                          SortMode="DataGridSortMode.Single">
                            <EmptyTemplate>
                                <Div>No data found for the search criteria.</Div>
                            </EmptyTemplate>
                            <DataGridColumns>
                                <DataGridColumn Field="@nameof(MyDrafts.Id)" Caption="Id" Width="6%" />
                                <DataGridColumn Field="@nameof(MyDrafts.Visible)" Caption="Visible" />
                                <DataGridColumn Caption="Actions" Width="10%">
                                    <DisplayTemplate>
                                        <Button>
                                       <Tooltip Text="Edit">
                                        <Icon Name="IconName.Eye"/>
                                        </Tooltip>
                                        </Button>
                                    </DisplayTemplate>
                                </DataGridColumn>
                            </DataGridColumns>
                        </DataGrid>
                        </Container>
@code {
    public class MyDrafts
        {
            public string Id { get; set; }
            public string Visible { get; set; }
        }
        private IEnumerable<MyDrafts> MyDraftsResults = new List<MyDrafts>()
        {
            new MyDrafts() { Id="#344", Visible="Coordination mechanism for food safety",},
            new MyDrafts() { Id="#344", Visible="National food and nutrition consulting committee",},
            new MyDrafts() { Id="#344", Visible="Action Nutrition Program",},
            new MyDrafts() { Id="#344", Visible="ZimASSET Food and Nutrition Security Cluster",},
            new MyDrafts() { Id="#344", Visible="Coordination of 1000 Most Critical Days response",},
            new MyDrafts() { Id="#344", Visible="Inter-ministerial Committee for Healthy nutrition and physical activity",},
        };
        
} 
