﻿using AutoMapper;
using Blazorise;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core.Enums;
using Gina2.Core.Interface;
using Gina2.Core.Methods;
using Gina2.DbModels.HomeDrafts;
using Gina2.Services.HomeDraftServices;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using SixLabors.ImageSharp.PixelFormats;
using System.Text.RegularExpressions;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Pages
{
    public partial class HomeEdit : PageConfirgurationComponent
    {
        [Inject]
        private IConfiguration Configuration { get; set; }

        [Inject]
        private IHomeDraftService HomeDraftService { get; set; }

        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IMapper Mapper { get; set; }

        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }

        [Inject]
        private NavigationManager navigationManager { get; set; }

        [Parameter]
        public int homeDraftId { get; set; }

        private HomeSliderDraft SliderData { get; set; } = new HomeSliderDraft();
        private ScorecardDraft ScoreCardData { get; set; } = new();
        private HomeNutritionDraft NutritionData { get; set; } = new();

        private const string EmojiErrorText = "The text is required or contains Emoji(s).";
        private List<HomeSliderDraft> HomeSliderList = new();
        private List<ScorecardDraft> HomeScoreCardList { get; set; } = new();
        private List<HomeNutritionDraft> HomeNutritionList { get; set; } = new();
        private int? UpdateKey { get; set; }
        private string BackgroundImage { get; set; }
        private Validations SliderValidations;
        private Validations FeatureValidations;
        private HomeSliderDraft DeleteSlider { get; set; } = new HomeSliderDraft();
        private ScorecardDraft DeleteFeatureSlider { get; set; } = new ScorecardDraft();
        private HomeNutritionDraft DeleteNutritionSlider { get; set; } = new HomeNutritionDraft();
        private bool SliderModal;
        private bool FeatureModal;
        private bool NutritionVisible;
        private bool DeleteVisible;
        private bool DeleteFeatureVisible;
        private bool DeleteNutritionVisible;
        private string ImageType { get; set; }
        private long ImageSizeLimit { get; set; } = 2000000;
        private string ImageError { get; set; }
        private string UrlError { get; set; }
        private FileEdit fileEdit { get; set; }

        private bool isUploadingSlider = false;
        private bool isSliderSaving = false;

        private bool isUploadingFeature = false;
        private bool isFeatureSaving = false;

        private bool isUploadingNutrient = false;
        private bool isNutrientSaving = false;

        private bool isLoadingSlider = true;
        private bool isLoadingNutrients = true;
        private bool isLoadingFeatures = true;

        private double sliderUplodedPercentage = 0;
        private double scorecardUplodedPercentage = 0;
        private double nutritionUplodedPercentage = 0;
        private bool modifyPublishedDraft = false;
        string currentUserId = string.Empty;
        string title = "Live Preview";
        bool _visible = false;
        private string featureTitleError = string.Empty;
        private string featureDescriptionError = string.Empty;
        private string nutritionTitleError = string.Empty;

        private bool canSaveSlider = true;
        private bool canSaveFeature = true;
        private bool canSaveNutrition = true;

        private int previewDraftId { get; set; }
        private string rerenderTokenId { get; set; }//to force reload the preview

        HomeDraft draftByIdToEdit = new HomeDraft();
        public bool ShowSliderFirstTitle { get; set; } = false;
        public bool ShowSliderSliderTitle { get; set; } = false;
        public bool ShowSliderLastTitle { get; set; } = false;
        public bool ShowFeatureTitleError { get; set; } = false;
        public bool ShowFeatureDescriptionError { get; set; } = false;
        public bool ShowNutritionTitleErrorError { get; set; } = false;
        private string latestAddition = string.Empty;
        private string originalLatestAddition = string.Empty;
        private bool canSaveLatestAddition = false;
        private _quillEditor quillSliderFirstTitleRef;
        private _quillEditor quillScoreCardDataTitleRef;
        private _quillEditor quillEditorScoreCardDataDescriptionRef;
        private _quillEditor quillEditorNutritionDataRef;
        private _quillEditor quillLatestAdditionRef;

        public CloudBlobContainer CloudBlobContainer
        {
            get
            {
                string blobStorageConnectionString = new AppSettingsHelper(Configuration).GetBlobStorageConnectionString();
                if (string.IsNullOrWhiteSpace(blobStorageConnectionString))
                {
                    return null;
                }

                CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
                CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
                return cloudBlobClient.GetContainerReference(Gina2.Core.Constants.BlobStorage.ContainerName);
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                currentUserId = CurrentUserService.UserId;
                var currentPublisheVersion = await HomeDraftService.GetPublishedDraft();

                draftByIdToEdit = await HomeDraftService.GetById(homeDraftId);
                if (draftByIdToEdit == null)
                {
                    navigationManager.NavigateTo("NotFound");
                    return;
                }
                if (currentPublisheVersion.Id == homeDraftId)
                {
                    modifyPublishedDraft = true;
                }

                if (draftByIdToEdit.LatestAdditions != null)
                {
                    originalLatestAddition = draftByIdToEdit.LatestAdditions.OrderByDescending(la => la.DateCreated).FirstOrDefault()?.Title;
                    latestAddition = string.IsNullOrEmpty(originalLatestAddition) ? "" : originalLatestAddition;
                }

                LoadSlidersAsync();
                LoadNutrientsAsync();
                LoadFeaturesAsync();
                IsLoading = false;
                StateHasChanged();
            }

            _ = base.OnAfterRenderAsync(firstRender);
        }

        private void LoadSlidersAsync()
        {
            isLoadingSlider = true;
            HomeSliderList = draftByIdToEdit.HomeSliderDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            if (modifyPublishedDraft)
            {
                var sliderCache = MemoryCache.Get<IEnumerable<HomeSliderDto>>(Core.Constants.Cachekeys.Slider);

                HomeSliderList.ForEach(hs =>
                {
                    var cache = sliderCache.FirstOrDefault(sc => sc.Id == hs.Id);

                    if (cache != null)
                    {
                        hs.ImageData = cache.ImageData;
                    }
                });
            }

            isLoadingSlider = false;
        }

        private void LoadNutrientsAsync()
        {
            isLoadingNutrients = true;
            HomeNutritionList = draftByIdToEdit.HomeNutritionDrafts
                                .OrderByDescending(x => x.DragAndDropKey).ToList();

            if (modifyPublishedDraft)
            {
                var nutrientCache = MemoryCache.Get<IEnumerable<HomeNutritionDto>>(Core.Constants.Cachekeys.Nutrient) ?? Enumerable.Empty<HomeNutritionDto>();

                HomeNutritionList.ForEach(hs =>
                {
                    var cache = nutrientCache.FirstOrDefault(sc => sc.Id == hs.Id);

                    if (cache != null)
                    {
                        hs.ImageData = cache.ImageData;
                    }
                });
            }
            isLoadingNutrients = false;
        }

        private void LoadFeaturesAsync()
        {
            isLoadingFeatures = true;
            HomeScoreCardList = draftByIdToEdit.ScoreCardDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            if (modifyPublishedDraft)
            {
                var scorecardCache = MemoryCache.Get<IEnumerable<HomeFeatureDto>>(Core.Constants.Cachekeys.Scorecard);

                HomeScoreCardList.ForEach(hs =>
                {
                    var cache = scorecardCache.FirstOrDefault(sc => sc.Id == hs.Id);

                    if (cache != null)
                    {
                        hs.ImageData = cache.ImageData;
                    }
                });
            }
            isLoadingFeatures = false;
        }


        private async Task AddSliderImageAsync(FileChangedEventArgs args)
        {
            isUploadingSlider = true;

            try
            {
                var files = args.Files;
                foreach (var file in files)
                {
                    if (file.Size < ImageSizeLimit)
                    {
                        await using MemoryStream fs = new();
                        await file.OpenReadStream(maxAllowedSize: ImageSizeLimit).CopyToAsync(fs);

                        fs.Position = 0; // Reset the stream position to the beginning before reading
                        using (var image = SixLabors.ImageSharp.Image.Load<Rgba32>(fs))
                        {
                            // Check dimensions
                            if (image.Width == 1920 && image.Height == 728)
                            {
                                fs.Position = 0; // Reset stream position for re-use
                                SliderData.ImageData = GetBytes(fs);

                                string filename = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                                SliderData.BackgroundImage = $"{Guid.NewGuid():N}-{filename}";
                                ImageError = string.Empty;
                            }
                            else
                            {
                                SliderData.BackgroundImage = string.Empty;
                                ImageError = "Image dimension should be 1920x728px";
                            }
                        }
                    }
                    else
                    {
                        ImageError = "Image Size is too large. Maximum size is 2MB.";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }

            isUploadingSlider = false;
        }


        private async Task AddFeatureImageAsync(FileChangedEventArgs args)
        {
            isUploadingFeature = true;

            try
            {
                var files = args.Files;
                foreach (var file in files)
                {
                    if (file.Size < ImageSizeLimit)
                    {
                        await using MemoryStream fs = new();
                        await file.OpenReadStream(maxAllowedSize: ImageSizeLimit).CopyToAsync(fs);

                        fs.Position = 0; // Reset the stream position to the beginning
                        using (var image = SixLabors.ImageSharp.Image.Load<Rgba32>(fs))
                        {
                            // Check dimensions
                            if (image.Width == 450 && image.Height == 410)
                            {
                                fs.Position = 0; // Reset the stream position for re-use
                                ScoreCardData.ImageData = GetBytes(fs);

                                string filename = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                                ScoreCardData.BackgroundImage = $"{Guid.NewGuid():N}-{filename}";
                                ImageError = string.Empty;
                            }
                            else
                            {
                                ScoreCardData.BackgroundImage = string.Empty;
                                ImageError = "Image dimension should be 450x410px";
                            }
                        }
                    }
                    else
                    {
                        ImageError = "Image size is too large.";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }

            isUploadingFeature = false;
        }


        private async Task AddNutrientImageAsync(FileChangedEventArgs args)
        {
            isUploadingNutrient = true;

            try
            {
                var files = args.Files;
                foreach (var file in files)
                {
                    if (file.Size < ImageSizeLimit)
                    {
                        await using MemoryStream fs = new();
                        await file.OpenReadStream(maxAllowedSize: ImageSizeLimit).CopyToAsync(fs);

                        fs.Position = 0; // Reset the stream position to the beginning before reading
                        using (var image = SixLabors.ImageSharp.Image.Load<Rgba32>(fs))
                        {
                            // Check dimensions
                            if ((image.Width == 329 && image.Height == 256) || (image.Width * image.Height == 84224 || image.Width * image.Height == 84480))
                            {
                                fs.Position = 0; // Reset the stream position for re-use
                                NutritionData.ImageData = GetBytes(fs);

                                string filename = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                                NutritionData.BackgroundImage = $"{Guid.NewGuid():N}-{filename}";
                                ImageError = string.Empty;
                            }
                            else
                            {
                                NutritionData.BackgroundImage = string.Empty;
                                ImageError = "Image dimension should be 329x256px";
                            }
                        }
                    }
                    else
                    {
                        ImageError = "Image size is too large.";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }

            isUploadingNutrient = false;
        }


        public static byte[] GetBytes(Stream stream)
        {
            var bytes = new byte[stream.Length];
            stream.Seek(0, SeekOrigin.Begin);
            stream.ReadAsync(bytes, 0, bytes.Length);
            stream.Dispose();
            return bytes;
        }

        private async Task SaveImageToAzureStorage(string backgroundImage, byte[] imageBytes)
        {
            CloudBlockBlob cloudBlockBlob = CloudBlobContainer.GetBlockBlobReference(backgroundImage);

            if (!(await cloudBlockBlob.ExistsAsync()) && imageBytes?.Length > 0)
            {
                cloudBlockBlob.Properties.ContentType = ImageType;
                await cloudBlockBlob.UploadFromByteArrayAsync(imageBytes, 0, imageBytes.Length);
            }
        }

        private async Task SaveAndUpdateSliderDetail()
        {
            await Task.Run(() => isSliderSaving = true);
            await InvokeAsync(StateHasChanged);
            ImageError = string.IsNullOrEmpty(SliderData.BackgroundImage) ? "Image is Required" : string.Empty;
            string toastMessage = string.Empty;
            SliderData.FirstTitle = await quillSliderFirstTitleRef.GetHTML();
            if (CheckSliderCanSave() && !string.IsNullOrEmpty(SliderData.BackgroundImage) && !string.IsNullOrEmpty(SliderData.FirstTitle))
            {
                bool hasUploadedSlider = HomeSliderList.Any(hs => hs.Id == SliderData.Id && hs.BackgroundImage != SliderData.BackgroundImage) && SliderData.ImageData != null;

                if (hasUploadedSlider)
                {
                    await SaveImageToAzureStorage(SliderData.BackgroundImage, SliderData.ImageData);
                }

                if (UpdateKey != null)
                {
                    SliderData.Id = (int)UpdateKey;
                    toastMessage = "updated";
                    await HomeDraftService.SaveOrUpdateHomeSlideAsync(SliderData, currentUserId);
                    SliderModal = false;
                }
                else
                {
                    SliderData.DragAndDropKey = HomeSliderList.Count + 1;
                    toastMessage = "created";
                    await HomeDraftService.SaveOrUpdateHomeSlideAsync(SliderData, currentUserId);
                    SliderModal = false;
                    //await OpenToaster("Success", "Slider added successfully");
                }
                await UpdateObjectsFromLatest();

                if (SliderData.ImageData != null)
                {
                    var updatedItem = HomeSliderList.Single(i => i.Id == SliderData.Id);
                    updatedItem.BackgroundImage = SliderData.BackgroundImage;
                    updatedItem.ImageData = SliderData.ImageData;

                    if (modifyPublishedDraft)
                    {
                        var sliderCacheList = MemoryCache.Get<IEnumerable<HomeSliderDto>>(Gina2.Core.Constants.Cachekeys.Slider).ToList();

                        foreach (var item in HomeSliderList.Where(i => i.Id != SliderData.Id))
                        {
                            var cacheItem = sliderCacheList.FirstOrDefault(i => i.Id == item.Id);

                            if (cacheItem != null && item.Id != SliderData.Id)
                            {
                                item.ImageData = cacheItem.ImageData;
                            }
                        }

                        MemoryCache.Set(Core.Constants.Cachekeys.Slider, Mapper.Map<List<HomeSliderDraft>, List<HomeSliderDto>>(HomeSliderList));
                    }

                }
                else
                {
                    if (modifyPublishedDraft)
                    {
                        MemoryCache.Set(Core.Constants.Cachekeys.Slider, Mapper.Map<List<HomeSliderDraft>, List<HomeSliderDto>>(new()));
                    }
                }
                SliderData = new HomeSliderDraft();
                UpdateKey = null;
                BackgroundImage = string.Empty;
                ImageError = string.Empty;
                UrlError = string.Empty;
                await OpenToaster("Success", $"Slider {toastMessage} successfully");
                _ = fileEdit.Reset().AsTask();
            }
            await Task.Run(() => isSliderSaving = false);
            await InvokeAsync(StateHasChanged);
        }

        private async Task UpdateObjectsFromLatest()
        {
            var homeSliderDraft = await HomeDraftService.GetById(homeDraftId);
            HomeSliderList = homeSliderDraft.HomeSliderDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();
            HomeNutritionList = homeSliderDraft.HomeNutritionDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();
            HomeScoreCardList = homeSliderDraft.ScoreCardDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveAndUpdatFeatureDetail()
        {
            await Task.Run(() => isFeatureSaving = true);
            await InvokeAsync(StateHasChanged);
            ScoreCardData.Title = await quillScoreCardDataTitleRef.GetHTML();
            ScoreCardData.Description = await quillEditorScoreCardDataDescriptionRef.GetHTML();
            if (!string.IsNullOrEmpty(ScoreCardData.BackgroundImage)
                && string.IsNullOrEmpty(ImageError)
                && string.IsNullOrEmpty(UrlError))
            {
                bool hasUploadedFeature = (ScoreCardData.Id == 0 && !string.IsNullOrEmpty(ScoreCardData.BackgroundImage)) || HomeScoreCardList.Any(hs => hs.Id == ScoreCardData.Id && hs.BackgroundImage != ScoreCardData.BackgroundImage) && ScoreCardData.ImageData != null;

                if (hasUploadedFeature)
                {
                    await SaveImageToAzureStorage(ScoreCardData.BackgroundImage, ScoreCardData.ImageData);
                }

                if (UpdateKey != null)
                {
                    ScoreCardData.Id = (int)UpdateKey;
                    await HomeDraftService.SaveOrUpdateHomeFutureAsync(ScoreCardData, currentUserId);
                    await OpenToaster("Success", "Feature updated successfully");
                }
                else
                {
                    ScoreCardData.DragAndDropKey = HomeScoreCardList.Count + 1;
                    await HomeDraftService.SaveOrUpdateHomeFutureAsync(ScoreCardData, currentUserId);
                    await OpenToaster("Success", "Feature added successfully");
                }

                await UpdateObjectsFromLatest();

                if (ScoreCardData.ImageData != null)
                {
                    if (modifyPublishedDraft)
                    {
                        var currentScoreCard = HomeScoreCardList.FirstOrDefault(i => i.Id == ScoreCardData.Id);
                        if (currentScoreCard != null)
                        {
                            currentScoreCard.ImageData = ScoreCardData.ImageData;
                        }
                        else
                        {
                            currentScoreCard.ImageData = null;
                        }

                        var scorecardCache = MemoryCache.Get<IEnumerable<HomeFeatureDto>>(Core.Constants.Cachekeys.Scorecard).ToList();

                        foreach (var item in HomeScoreCardList.Where(i => i.Id != ScoreCardData.Id))
                        {
                            var cacheItem = scorecardCache.FirstOrDefault(i => i.Id == item.Id);

                            if (cacheItem != null && item.Id != ScoreCardData.Id)
                            {
                                item.ImageData = cacheItem.ImageData;
                            }
                        }

                        MemoryCache.Set(Core.Constants.Cachekeys.Scorecard, Mapper.Map<List<ScorecardDraft>, List<HomeFeatureDto>>(HomeScoreCardList));
                    }

                }
                else
                {
                    if (modifyPublishedDraft)
                    {
                        MemoryCache.Set(Core.Constants.Cachekeys.Scorecard, Mapper.Map<List<ScorecardDraft>, List<HomeFeatureDto>>(new()));
                    }

                }

                UpdateKey = null;
                FeatureModal = false;
                ScoreCardData = new ScorecardDraft();
                BackgroundImage = string.Empty;
                UrlError = string.Empty;
                _ = fileEdit.Reset().AsTask();
            }
            else
            {
                ImageError = string.IsNullOrEmpty(ScoreCardData.BackgroundImage) ? "Image is Required" : string.Empty;
            }

            await Task.Run(() => isFeatureSaving = false);
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveAndUpdatNutritionDetail()
        {
            await Task.Run(() => isNutrientSaving = true);
            await InvokeAsync(StateHasChanged);
            NutritionData.SliderTitle = await quillEditorNutritionDataRef.GetHTML();
            if (!string.IsNullOrEmpty(NutritionData.BackgroundImage)
                && string.IsNullOrEmpty(ImageError)
                && string.IsNullOrEmpty(UrlError))
            {
                await SaveImageToAzureStorage(NutritionData.BackgroundImage, NutritionData.ImageData);

                if (UpdateKey != null)
                {
                    NutritionData.Id = (int)UpdateKey;
                    await HomeDraftService.SaveOrUpdateHomeNutritionAsync(NutritionData, currentUserId);
                    await OpenToaster("Success", "Nutrition updated successfully");
                }
                else
                {
                    NutritionData.DragAndDropKey = HomeNutritionList.Count + 1;
                    await HomeDraftService.SaveOrUpdateHomeNutritionAsync(NutritionData, currentUserId);
                    await OpenToaster("Success", "Nutrition added successfully");
                }
                await UpdateObjectsFromLatest();

                if (NutritionData.ImageData != null)
                {
                    if (modifyPublishedDraft)
                    {
                        HomeNutritionList.Single(i => i.Id == NutritionData.Id).ImageData = NutritionData.ImageData;

                        var nutrientCache = MemoryCache.Get<IEnumerable<HomeNutritionDto>>(Core.Constants.Cachekeys.Nutrient).ToList();

                        foreach (var item in HomeNutritionList.Where(i => i.Id != NutritionData.Id))
                        {
                            var cacheItem = nutrientCache.FirstOrDefault(i => i.Id == item.Id);

                            if (cacheItem != null && item.Id != NutritionData.Id)
                            {
                                item.ImageData = cacheItem.ImageData;
                            }
                        }

                        MemoryCache.Set(Core.Constants.Cachekeys.Nutrient, Mapper.Map<List<HomeNutritionDraft>, List<HomeNutritionDto>>(HomeNutritionList));
                    }

                }
                else
                {
                    if (modifyPublishedDraft)
                    {
                        MemoryCache.Set(Core.Constants.Cachekeys.Nutrient, Mapper.Map<List<HomeNutritionDraft>, List<HomeNutritionDto>>(new()));
                    }

                }

                UpdateKey = null;
                NutritionVisible = false;
                NutritionData = new HomeNutritionDraft();
                BackgroundImage = string.Empty;
                UrlError = string.Empty;
                _ = fileEdit.Reset().AsTask();
            }
            else
            {
                ImageError = string.IsNullOrEmpty(NutritionData.BackgroundImage) ? "Image is Required" : string.Empty;
            }

            await Task.Run(() => isNutrientSaving = false);
            await InvokeAsync(StateHasChanged);
        }

        private Task ThemesModal()
        {
            FeatureModal = true;
            ScoreCardData = new ScorecardDraft()
            {
                Title = string.Empty,
                Description = string.Empty,
                RedirectionUrl = string.Empty,
                BackgroundImage = string.Empty,
            };
            StateHasChanged();
            ScoreCardData.HomeDraftId = homeDraftId;
            return Task.CompletedTask;
        }

        private void ShowModal()
        {
            SliderData.HomeDraftId = homeDraftId;
            SliderModal = true;
        }

        private Task ShowNutrition()
        {
            NutritionVisible = true;
            NutritionData = new HomeNutritionDraft()
            {
                SliderTitle = string.Empty,
                Description = string.Empty,
                BackgroundImage = string.Empty,
                RedirectionUrl = string.Empty,
            };
            NutritionData.HomeDraftId = homeDraftId;
            return Task.CompletedTask;
        }

        private void HideModal(bool value)
        {
            value = false;
            UpdateKey = null;
            ScoreCardData = new ScorecardDraft();
            SliderData = new HomeSliderDraft();
            NutritionData = new HomeNutritionDraft();
            ImageType = null;
            ImageError = string.Empty;
            BackgroundImage = string.Empty;
            UrlError = string.Empty;
            fileEdit.Reset().AsTask();
            featureTitleError = featureDescriptionError = nutritionTitleError = string.Empty;
            canSaveSlider = canSaveFeature = canSaveNutrition = true;
            Task.Run(async () => await SliderValidations.ClearAll());
            Task.Run(async () => await FeatureValidations.ClearAll());

        }

        public async Task OnDropItem(HomeSliderDraft slider)
        {
            int key = HomeSliderList.Count;
            foreach (var item in HomeSliderList)
            {
                key--;
                item.DragAndDropKey = key;
            }
            await HomeDraftService.SaveDragandDropHomeSlideAsync(HomeSliderList, currentUserId);

        }

        public async Task OnDropFeature(ScorecardDraft slider)
        {
            int key = HomeSliderList.Count;
            foreach (var item in HomeScoreCardList)
            {
                key--;
                item.DragAndDropKey = key;
            }
            await HomeDraftService.SaveDragandDropHomeFeatureAsync(HomeScoreCardList, currentUserId);
            if (modifyPublishedDraft)
            {
                MemoryCache.Set(Core.Constants.Cachekeys.Scorecard, Mapper.Map<List<ScorecardDraft>, List<HomeFeatureDto>>(HomeScoreCardList));
            }
            await OpenToaster("Success", "Feature updated successfully");
        }
        public async Task OnDropNutrition(HomeNutritionDraft slider)
        {
            int key = HomeNutritionList.Count;
            foreach (var item in HomeNutritionList)
            {
                key--;
                item.DragAndDropKey = key;
            }
            await HomeDraftService.SaveDragandDropHomeNutritionAsync(HomeNutritionList, currentUserId);
            if (modifyPublishedDraft)
            {
                MemoryCache.Set(Core.Constants.Cachekeys.Nutrient, Mapper.Map<List<HomeNutritionDraft>, List<HomeNutritionDto>>(HomeNutritionList));
            }
            await OpenToaster("Success", "Nutrition updated successfully");
        }
        private void ShowFeatureDelete(ScorecardDraft data)
        {
            if (modifyPublishedDraft && !CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                _ = OpenInfoToaster("You are not allowed to modify published home page content");
            }
            else
            {
                DeleteFeatureSlider = data;
                DeleteFeatureVisible = true;
            }
        }
        private void ShowNutritionDelete(HomeNutritionDraft data)
        {
            if (modifyPublishedDraft && !CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                _ = OpenInfoToaster("You are not allowed to modify published home page content");
            }
            else
            {
                DeleteNutritionSlider = data;
                DeleteNutritionVisible = true;
            }
        }

        private async Task DeleteHomeSlider()
        {
            await HomeDraftService.DeleteHomeSliderAsync(DeleteSlider, currentUserId);
            MemoryCache.Remove(DeleteSlider);
            DeleteVisible = false;
            await OpenToaster("Success", "Deleted slider successfully");
        }

        private async Task DeleteFeature()
        {
            await HomeDraftService.DeleteFeatureSliderAsync(DeleteFeatureSlider, currentUserId);

            await UpdateObjectsFromLatest();

            if (modifyPublishedDraft)
            {
                var scorecardCache = MemoryCache.Get<IEnumerable<HomeFeatureDto>>(Core.Constants.Cachekeys.Scorecard).ToList();

                foreach (var item in HomeScoreCardList)
                {
                    var cacheItem = scorecardCache.FirstOrDefault(i => i.Id == item.Id);

                    if (cacheItem != null)
                    {
                        item.ImageData = cacheItem.ImageData;
                    }
                }
                MemoryCache.Set(Core.Constants.Cachekeys.Scorecard, Mapper.Map<List<ScorecardDraft>, List<HomeFeatureDto>>(HomeScoreCardList));
            }

            DeleteFeatureVisible = false;
            await OpenToaster("Success", "Deleted feature successfully");
        }

        private async Task DeleteNutrition()
        {
            await HomeDraftService.DeleteNutritionSliderAsync(DeleteNutritionSlider, currentUserId);

            await UpdateObjectsFromLatest();

            if (modifyPublishedDraft)
            {
                var nutrientCache = MemoryCache.Get<IEnumerable<HomeNutritionDto>>(Core.Constants.Cachekeys.Nutrient).ToList();

                foreach (var item in HomeNutritionList)
                {
                    var cacheItem = nutrientCache.FirstOrDefault(i => i.Id == item.Id);

                    if (cacheItem != null)
                    {
                        item.ImageData = cacheItem.ImageData;
                    }
                }
                MemoryCache.Set(Core.Constants.Cachekeys.Nutrient, Mapper.Map<List<HomeNutritionDraft>, List<HomeNutritionDto>>(HomeNutritionList));
            }

            DeleteNutritionVisible = false;
            await OpenToaster("Success", "Deleted nutrition successfully");
        }

        private async Task ShowEdit(HomeSliderDraft item)
        {
            if (modifyPublishedDraft && !CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                await OpenInfoToaster("You are not allowed to modify published home page content");
            }
            else
            {
                UpdateKey = item.Id;
                sliderUplodedPercentage = 0;

                SliderData = HomeSliderList.Where(x => x.Id == UpdateKey).Select(x => new HomeSliderDraft()
                {
                    Id = x.Id,
                    SliderTitle = x.SliderTitle,
                    FirstTitle = x.FirstTitle,
                    LastTitle = x.LastTitle,
                    BackgroundImage = x.BackgroundImage,
                    RedirectionUrl = x.RedirectionUrl,
                    ImageData = x.ImageData,
                    HomeDraftId = x.HomeDraftId
                }).FirstOrDefault();
                BackgroundImage = SliderData.BackgroundImage;
                SliderModal = true;
            }
        }

        private async Task ShowFeatureEdit(ScorecardDraft item)
        {
            if (modifyPublishedDraft && !CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                await OpenInfoToaster("You are not allowed to modify published home page content");
            }
            else
            {
                UpdateKey = item.Id;
                scorecardUplodedPercentage = 0;

                ScoreCardData = HomeScoreCardList.Where(x => x.Id == UpdateKey).Select(x => new ScorecardDraft()
                {
                    Id = x.Id,
                    Title = x.Title,
                    Description = x.Description,
                    BackgroundImage = x.BackgroundImage,
                    RedirectionUrl = x.RedirectionUrl,
                    ImageData = x.ImageData,
                    HomeDraftId = x.HomeDraftId
                }).FirstOrDefault();
                BackgroundImage = ScoreCardData.BackgroundImage;
                FeatureModal = true;
            }

        }

        private async Task ShowNutritionEdit(HomeNutritionDraft item)
        {
            if (modifyPublishedDraft && !CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                await OpenInfoToaster("You are not allowed to modify published home page content");
            }
            else
            {
                UpdateKey = item.Id;
                nutritionUplodedPercentage = 0;

                NutritionData = HomeNutritionList.Where(x => x.Id == UpdateKey).Select(x => new HomeNutritionDraft()
                {
                    Id = x.Id,
                    SliderTitle = x.SliderTitle,
                    Description = x.Description,
                    BackgroundImage = x.BackgroundImage,
                    RedirectionUrl = x.RedirectionUrl,
                    ImageData = x.ImageData,
                    HomeDraftId = x.HomeDraftId
                }).FirstOrDefault();
                BackgroundImage = NutritionData.BackgroundImage;
                NutritionVisible = true;
            }

        }

        private void OnSliderUrlChanged(string value)
        {
            SliderData.RedirectionUrl = value;
            bool IsUrl = RegexHelper.IsRegexMatch(value, @"(http|https):\/\/.*\..*");
            UrlError = IsUrl || string.IsNullOrEmpty(value) ? string.Empty : "Enter correct URL";
        }

        private void OnFeatureUrlChanged(string value)
        {
            ScoreCardData.RedirectionUrl = value;
            bool IsUrl = RegexHelper.IsRegexMatch(value, @"(http|https):\/\/.*\..*");
            UrlError = IsUrl || string.IsNullOrEmpty(value) ? string.Empty : "Enter correct URL";
        }

        private void OnNutritionUrlChanged(string value)
        {
            NutritionData.RedirectionUrl = value;
            bool IsUrl = RegexHelper.IsRegexMatch(value, @"(http|https):\/\/.*\..*");
            UrlError = IsUrl || string.IsNullOrEmpty(value) ? string.Empty : "Enter correct URL";
        }

        private void OnSliderProgressed(FileProgressedEventArgs e)
        {
            sliderUplodedPercentage = e.Percentage;
        }

        private void OnNutrientUploadProgressed(FileProgressedEventArgs e)
        {
            nutritionUplodedPercentage = e.Percentage;
        }

        private void OnScorecardUplaodProgressed(FileProgressedEventArgs e)
        {
            scorecardUplodedPercentage = e.Percentage;
        }
        private async Task PublishDraft(int draftId)
        {
            IsLoading = true;
            bool success = await HomeDraftService.PublishDraft(draftId, currentUserId);

            if (success)
            {
                var currentPublished = await HomeDraftService.GetById(draftId);
                if (currentPublished != null)
                {
                    await SetSliderCathe(currentPublished);
                    await SetFeaturesCache(currentPublished);
                    await SetNutritionCache(currentPublished);
                    modifyPublishedDraft = true;
                }
                IsLoading = false;
                navigationManager.NavigateTo("");
            }
            else
            {
                IsLoading = false;
                _ = OpenToaster("Error", "Draft publish failed", AntDesign.NotificationType.Error);
            }

        }

        private async Task SetSliderCathe(HomeDraft currentPublishedDraft)
        {
            List<HomeSliderDto> SlidersList = new();
            List<HomeSliderDraft> sliders = currentPublishedDraft.HomeSliderDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            foreach (var item in sliders)
            {
                HomeSliderDto slider = new()
                {
                    Id = item.Id,
                    SliderTitle = item.SliderTitle,
                    RedirectionUrl = item.RedirectionUrl,
                    FirstTitle = item.FirstTitle,
                    LastTitle = item.LastTitle,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId
                };

                SlidersList.Add(slider);
            }

            List<Task> tasks = new();
            foreach (var item in SlidersList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }

            await Task.WhenAll(tasks);
            bool hasSliders = SlidersList.Any(s => s.ImageData != null);
            MemoryCache.Set(Cachekeys.Slider, SlidersList);
        }

        private async Task SetFeaturesCache(HomeDraft currentPublishedDraft)
        {
            List<HomeFeatureDto> FeaturesList = new();
            List<ScorecardDraft> scorecards = currentPublishedDraft.ScoreCardDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            var latestScorecards = scorecards.Take(3);

            foreach (ScorecardDraft item in latestScorecards)
            {
                HomeFeatureDto feature = new()
                {
                    Id = item.Id,
                    Title = item.Title,
                    Description = item.Description,
                    RedirectionUrl = item.RedirectionUrl,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId
                };

                FeaturesList.Add(feature);
            }

            List<Task> tasks = new();
            foreach (var item in FeaturesList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }

            await Task.WhenAll(tasks);
            MemoryCache.Set(Cachekeys.Scorecard, FeaturesList);
        }

        private async Task SetNutritionCache(HomeDraft currentPublishedDraft)
        {
            List<HomeNutritionDto> nutrientList = new();
            List<HomeNutritionDraft> nutritiions = currentPublishedDraft.HomeNutritionDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            var latestNutrients = nutritiions.Take(15);

            foreach (HomeNutritionDraft item in latestNutrients)
            {
                HomeNutritionDto nutrition = new()
                {
                    Id = item.Id,
                    SliderTitle = item.SliderTitle,
                    Description = item.Description,
                    RedirectionUrl = item.RedirectionUrl,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId,
                };

                nutrientList.Add(nutrition);
            }

            List<Task> tasks = new();
            foreach (var item in nutrientList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }
            await Task.WhenAll(tasks);
            MemoryCache.Set(Cachekeys.Nutrient, nutrientList);
        }

        private async Task SetBlobBytesAsync(HomeSliderDto item)
        {
            try
            {
                item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
            }
            catch (Exception)
            {
                // No need to handle here.
            }
        }
        private async Task SetBlobBytesAsync(HomeNutritionDto item)
        {
            try
            {
                item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
            }
            catch (Exception)
            {
                // No need to handle here.
            }
        }
        private async Task SetBlobBytesAsync(HomeFeatureDto item)
        {
            try
            {
                item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
            }
            catch (Exception)
            {
                // No need to handle here.
            }
        }

        private async Task<byte[]> GetBlobBytesAsync(string backgroundImage)
        {
            CloudBlockBlob cloudBlockBlob = CloudBlobContainer.GetBlockBlobReference(backgroundImage);
            await cloudBlockBlob.FetchAttributesAsync();
            byte[] arr = new byte[cloudBlockBlob.Properties.Length];
            await cloudBlockBlob.DownloadToByteArrayAsync(arr, 0);
            return arr;
        }

        private async Task ShowPreview(int draftId)
        {
            await Task.Run(() =>
            {
                IsLoading = true;
            });

            await InvokeAsync(StateHasChanged);
            previewDraftId = draftId;
            rerenderTokenId = Guid.NewGuid().ToString();
            _visible = true;
            await Task.Run(() =>
            {
                IsLoading = false;
            });
            await InvokeAsync(StateHasChanged);
        }

        private void HandleCancel(MouseEventArgs e)
        {
            _visible = false;
        }

        private void ChangeSliderFirstTitle(string value)
        {
            SliderData.FirstTitle = value;
            CheckSliderCanSave();
        }
        private void ChangeSliderTitle(string value)
        {
            SliderData.SliderTitle = value;
            CheckSliderCanSave();
        }

        private void ChangeSliderLastTitle(string value)
        {
            SliderData.LastTitle = value;
            CheckSliderCanSave();
        }

        private bool CheckSliderCanSave()
        {
            var isEmoji = RegexHelper.IsRegexMatch(SliderData.FirstTitle, EmojiConstant.EmojiPattern);
            //bool hasValidTitle = ShowSliderSliderTitle = string.IsNullOrEmpty(SliderData.SliderTitle) || SliderData.SliderTitle.Contains("<br>") || RegexHelper.IsRegexMatch(SliderData.SliderTitle, EmojiConstant.EmojiPattern);
            //bool hasValidLastTitle = ShowSliderLastTitle = RegexHelper.IsRegexMatch(SliderData.LastTitle, EmojiConstant.EmojiPattern);
            ShowSliderFirstTitle = isEmoji || string.IsNullOrEmpty(SliderData.FirstTitle);
            return !ShowSliderFirstTitle;
        }

        private void ValidateFeatureTitle(ValidatorEventArgs e)
        {
            CheckFeatureCanSave();
        }
        private void ValidateFeatureDescription(ValidatorEventArgs e)
        {
            CheckFeatureCanSave();
        }
        private void ChangeFeatureTitle(string value)
        {
            ScoreCardData.Title = value;
            CheckFeatureCanSave();
        }

        private void ChangeFeatureDesciption(string value)
        {
            ScoreCardData.Description = value;
            CheckFeatureCanSave();
        }

        private void CheckFeatureCanSave()
        {
            bool hasValidDescription = ShowFeatureDescriptionError = !string.IsNullOrEmpty(ScoreCardData.Description) ? RegexHelper.IsRegexMatch(ScoreCardData.Description, EmojiConstant.EmojiPattern) : false;
            bool hasValidTitle = ShowFeatureTitleError = string.IsNullOrEmpty(ScoreCardData.Title) || ScoreCardData.Title.Contains("<br>") || RegexHelper.IsRegexMatch(ScoreCardData.Title, EmojiConstant.EmojiPattern);
            canSaveFeature = !hasValidTitle && !hasValidDescription;
        }

        private void ChangeNutritionTitle(string value)
        {
            NutritionData.SliderTitle = value;
            CheckNutritionCanSave();
        }

        private void CheckNutritionCanSave()
        {
            ShowNutritionTitleErrorError = string.IsNullOrEmpty(NutritionData.SliderTitle) || NutritionData.SliderTitle.Contains("<br>") || RegexHelper.IsRegexMatch(NutritionData.SliderTitle, EmojiConstant.EmojiPattern);
            canSaveNutrition = !ShowNutritionTitleErrorError;
        }

        private async Task ChangeLatestAddition(string value)
        {
            latestAddition = value;
            CheckLatestAdditionCanSave();
            await InvokeAsync(StateHasChanged);
        }

        private void CheckLatestAdditionCanSave()
        {
            canSaveLatestAddition = !latestAddition.Equals(originalLatestAddition);
        }

        private async Task SaveLatestAddition()
        {
            latestAddition = await quillLatestAdditionRef.GetHTML();
            var quillInput = Regex.Replace(latestAddition, "<.*?>", String.Empty);
            await HomeDraftService.SaveLatestAddition(homeDraftId, string.IsNullOrEmpty(quillInput) || string.IsNullOrWhiteSpace(quillInput) ? "" : latestAddition);
            originalLatestAddition = latestAddition;
            await OpenToaster("Success", "Updated latest addition");
            CheckLatestAdditionCanSave();
        }

        private void ClearLatestAddition()
        {
            latestAddition = originalLatestAddition;
            CheckLatestAdditionCanSave();
        }
    }
}