﻿@page "/admin/indicatortype"
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Gina2.Blazor.Models.AdminModel;
@using Gina2.DbModels;
@inherits PageConfirgurationComponent;
@using Gina2.Core.Methods;
<Loader IsLoading="@IsLoading" />
<PageTitle>GIFNA @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Modal @bind-Visible="@ThemesVisible" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms adminmobel">
        <ModalHeader Class="ant-header">
            <ModalTitle data-cy="IndicatorTypeTitle">Indicator type </ModalTitle>
            <NavLink class="close" data-cy="IndicatorTypeNavLink" onclick="@(()=> HideModal())"><img src="/img/close.png" /></NavLink>
        </ModalHeader>
        <ModalBody>
            <AntDesign.Form Class="_mapindicator" Layout="@AntDesign.FormLayout.Vertical" Loading="@IsSavingloading" Model="@AddorUpdateIndicatorType"
                            LabelColSpan="8"
                            WrapperColSpan="16"
                            OnFinish="BtnSaveClicked">

                <AntDesign.Row>

                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <LabelTemplate>
                                <label class="pr-1" data-cy="IndicatorCodeLabel" >
                                    @PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorCodeLabel)<span class="pr-1">*</span>
    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorCodeTooltip)">
                                    <Button Class="but-info _tooltip">
                                        <Icon Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@IndicatorTypeConfigurationKey.IndicatorCodeGroup" />
                                </label>
                            </LabelTemplate>
                            <ChildContent>
                                <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorCodePlaceHolder)"
                                                 @bind-Value="@context.IndicatorCode" />
                            </ChildContent>
                        </AntDesign.FormItem>

                    </AntDesign.Col>

                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <label style="margin-bottom:5px;" data-cy="IndicatorNameLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorNameLabel)<span class="pr-1">*</span>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorNameTooltip)">
                                    <Button data-cy="IndicatorNameLabelTooltip" Class="but-info _tooltip">
                                        <Icon data-cy="IndicatorNameLabelIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@IndicatorTypeConfigurationKey.IndicatorNameGroup" />
                            </label>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorNamePlaceHolder)"
                                             @bind-Value="@context.IndicatorName" />

                        </AntDesign.FormItem>

                    </AntDesign.Col>

                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <label style="margin-bottom:5px;"  data-cy="IndicatorFilterCriteriaLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorFilterCriteriaLabel)<span class="pr-1">*</span>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorFilterCriteriaTooltip)">
                                    <Button data-cy="IndicatorFilterCriteriaToolTip" Class="but-info _tooltip">
                                        <Icon data-cy="IndicatorFilterCriteriaicon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@IndicatorTypeConfigurationKey.IndicatorFilterCriteriaGroup" />
                            </label>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.IndicatorCodePlaceHolder)"
                                             @bind-Value="@context.FilterCriteria" />
                        </AntDesign.FormItem>
                    </AntDesign.Col>

                </AntDesign.Row>

                <AntDesign.Button Class="but-blues apply" HtmlType="submit"> Save</AntDesign.Button>
            </AntDesign.Form>
        </ModalBody>
    </ModalContent>
</Modal>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Maskgroup.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3" data-cy="IndicatorTitle">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.Title))
                        <AdminEditbut Key="@IndicatorTypeConfigurationKey.Title" />
                    </Heading>
                    <Paragraph Class="color-w" data-cy="IndicatorPageDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.SubTitle))
                        <AdminEditbut Key="@IndicatorTypeConfigurationKey.SubTitle" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="IndicatorHomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                         <BreadcrumbItem Active>
                            <BreadcrumbLink To="#" data-cy="Indicator">Indicator</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#" data-cy="IndicatorTypeLink">Indicator management</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="pt-4 pl-2 pr-2 adminuser">
    <Layout Class="search-box pb-3 mob-layout">
        <Layout Class="left-layout DataGrids">
            <LayoutContent>
                <Div Class="form-bg _antdesign pl-0 pr-0">

                    <Fields>
                        <Field ColumnSize="ColumnSize.Is8.OnTablet.Is12.OnMobile.Is11.OnDesktop.Is11.OnWidescreen.Is11.OnFullHD" Class="_antdesign">
                            <Heading Size="HeadingSize.Is2" Class="Headingh3" data-cy="IndicatorHeading">
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(IndicatorTypeConfigurationKey.Heading))
                                <Tooltip data-cy="AddMapIndicatorToolTip" Text="Add map indicator">
                                    <Button data-cy="AddMapIndicatorBtn" Class="but-info _tooltip">
                                        <Icon  data-cy="AddMapIndicatorIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@IndicatorTypeConfigurationKey.Heading" />
                            </Heading>
                        </Field>

                        <Field Flex="Flex.JustifyContent.End.AlignItems.End" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is1.OnDesktop.Is1.OnWidescreen.Is1.OnFullHD">
                            <Button data-cy="AddBtn" Class="btn but-yellow w-100" Clicked="@ShowAddForm"><Icon Class="far fa-plus" /> Add</Button>
                        </Field>
                    </Fields>


                </Div>
                <div class="_TypedataGrid">
                <DataGrid FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@IndicatorTypeModel"
                          Responsive
                          Data="@IndicatorType"
                          PageSize="50"
                          PageSizes="new[] { 50,100,250,500,1000 }"
                          ShowPageSizes
                          ShowPager
                          SortMode="DataGridSortMode.Single">
                    <EmptyTemplate>
                        <Div  data-cy="NoData">No data found for the search criteria.</Div>
                    </EmptyTemplate>
                    <DataGridColumns>
                        <DataGridColumn Field="@nameof(IndicatorTypeModel.IndicatorCode)"  Width="20%" Caption="Indicator code" Sortable="true" />
                        <DataGridColumn Field="@nameof(IndicatorTypeModel.IndicatorName)" Width="45%" Caption="Indicator name" Sortable="true" />
                        <DataGridColumn Field="@nameof(IndicatorTypeModel.FilterCriteria)" Caption="Filter criteria" Sortable="true" />
                             <DataGridColumn Caption="Action" Sortable="false" Width="10%">
                                 <DisplayTemplate>
                                     <div style="width:80px;">
                                <Icon Class="_colors-w" Clicked="(e)=>UpdateById(context)" Name="IconName.Pen" />
                                <Icon Class="_colors-w" Clicked="(e)=>ShowDeleteModal(context.Id)" Name="IconName.Delete" />
                                     </div>
                            </DisplayTemplate>
                        </DataGridColumn>
                    </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate>
                        <Badge TextColor="TextColor.Dark">
                            @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
                </div>
            </LayoutContent>
        </Layout>
    </Layout>

</Container>

<Modal @bind-Visible="@deleteModalVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Are you sure want to delete this data?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteById(indicatorToBeDeleted);} )">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteModalVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>
