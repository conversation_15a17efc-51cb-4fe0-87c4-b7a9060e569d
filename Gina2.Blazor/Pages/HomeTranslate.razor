﻿@page "/home-translate"

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">Translations of Home GIFNA</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="#">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Translate</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
                <Div Class="item2">
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Class="aboutmenu" Split />
                        <DropdownMenu>
                            <DropdownItem href="home-edit">Edit</DropdownItem>
                            <DropdownItem href="home-moderate">Moderate</DropdownItem>
                            <DropdownItem href="home-translate">Translate</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="gina-70">

<Div Class="table-responsive">
                    <Table Class="table-nth">
                        <TableHeader ThemeContrast="ThemeContrast.Dark">
                            <TableRow>
                                <TableHeaderCell>Language</TableHeaderCell>
                                <TableHeaderCell>Title</TableHeaderCell>
                                <TableHeaderCell>Status</TableHeaderCell>
                                <TableHeaderCell>Revision Action</TableHeaderCell>
                            </TableRow>
                        </TableHeader>
                         <TableBody>
        <TableRow>
            <TableRowCell>Spanish</TableRowCell>
            <TableRowCell><Heading Size="HeadingSize.Is3"><NavLink To="#">Home GIFNA</NavLink> </Heading>
            </TableRowCell>
            <TableRowCell>Published</TableRowCell>
            <TableRowCell>
            <Tooltip Text="New Draft">
                <Button> <Icon Name="IconName.Edit" /></Button>
            </Tooltip>
            </TableRowCell>
        </TableRow>
     <TableRow>
            <TableRowCell>French</TableRowCell>
            <TableRowCell><Heading Size="HeadingSize.Is3"><NavLink To="#">A propos de GIFNA</NavLink> </Heading>
            </TableRowCell>
            <TableRowCell>Published</TableRowCell>
            <TableRowCell>
            <Tooltip Text="New Draft">
                <Button> <Icon Name="IconName.Edit" /></Button>
            </Tooltip>
            </TableRowCell>
        </TableRow>
         <TableRow>
            <TableRowCell>English (source)</TableRowCell>
            <TableRowCell><Heading Size="HeadingSize.Is3"><NavLink To="#">Home GIFNA</NavLink> </Heading>
            </TableRowCell>
            <TableRowCell>Published</TableRowCell>
            <TableRowCell>
            <Tooltip Text="New Draft">
                <Button> <Icon Name="IconName.Edit" /></Button>
            </Tooltip>
            </TableRowCell>
        </TableRow>
    </TableBody>
                    </Table>
                </Div>
<Heading Class="translh3" Size="HeadingSize.Is3">Select translations for Home GIFNA</Heading>
<Paragraph Class="translp">Alternatively, you can select existing nodes as translations of this one or remove nodes from this translation set. Only nodes that have the right language and don't belong to other translation set will be available here.</Paragraph>
<Field Horizontal Class="translinput">
    <FieldLabel ColumnSize="ColumnSize.Is2">Spanish</FieldLabel>
    <FieldBody ColumnSize="ColumnSize.Is10">
        <TextEdit Placeholder="Home GIFNA [nid:24444]" />
    </FieldBody>
</Field>
<Field Horizontal Class="translinput">
    <FieldLabel ColumnSize="ColumnSize.Is2">French</FieldLabel>
    <FieldBody ColumnSize="ColumnSize.Is10">
        <TextEdit Placeholder="A propos de GIFNA [nid:5967]" />
    </FieldBody>
</Field>
<Button Class="Login pl-3 pr-3 mt-3">Update translations</Button>
</Container>
