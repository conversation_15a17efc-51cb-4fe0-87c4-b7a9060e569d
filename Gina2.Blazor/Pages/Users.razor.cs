using AntDesign;
using Blazorise;
using Blazorise.DataGrid;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Enums;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.Services.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Text;
using System.Linq.Dynamic.Core;
using System.Transactions;
using Gina2.Services.Country;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Core.Extensions;
using Gina2.Core.Interface;
using static Gina2.Core.Constants;
using StackExchange.Redis;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Identity.Client;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Authentication;
using Azure.Identity;
using MySqlX.XDevAPI;
using System.Net.Http;
using NuGet.Configuration;
using Azure.Core;
using System.Net.Http.Headers;
using Newtonsoft.Json;
using System.Text.Json;
using static System.Net.WebRequestMethods;
using Microsoft.Kiota.Abstractions.Authentication;
using System.Net.Mime;
using System;
using Azure;
using Google.Protobuf.WellKnownTypes;
using Gina2.Core.Constant;
using Microsoft.Graph.Education.Classes.Item.Assignments.Item.Submissions.Item.Return;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.AspNetCore.Http;
using Gina2.Blazor.Helpers.RefreshToken;
using System.Linq.Expressions;
using System.Reflection;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class Users : PageConfirgurationComponent
    {
        [Inject]
        private IRefreshToken _refreshToken { get; set; }
        [Inject]
        private ILogger<Users> _logger { get; set; }
        [Inject]
        private ICurrentUserService _currentUserService { get; set; }
        [Inject]
        private IHttpClientFactory _httpClientFactory { get; set; }
        [Inject]
        private IConfidentialClientApplication ConfidentialClientApplication { get; set; }
        [Inject]
        private HttpClient httpClient { get; set; }

        [Inject]
        public IConfiguration Configuration { get; set; }
        [Inject]
        public RoleManager<IdentityRole> _roleManager { get; set; }
        [Inject]
        private IJSRuntime _JSRuntime { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }
        [Inject]
        public UserManager<ApplicationUser> _userManager { get; set; }
        [Inject]
        public ICountryService _countryService { get; set; }
        [Inject]
        public IEmailServices _emailServices { get; set; }

        [Inject]
        public SignInManager<ApplicationUser> _signInManager { get; set; }
        [Inject]
        public NavigationManager _navigationManager { get; set; }
        public CreateUserModel User { get; set; } = new CreateUserModel();
        public static List<MySqlRepository.Models.CountryWithRegion> Regions { get; set; } = new List<MySqlRepository.Models.CountryWithRegion>();
        public static List<string> RegionCode { get; set; } = new List<string>();
        public static List<string> CountryList { get; set; } = new List<string>();
        private GlobalSearchRequest SearchRequest = new();
        public bool DisabledUserStatus { get; set; } = true;
        public bool DisabledInviateUserStatus { get; set; } = true;
        private DataGrid<AppUserModel> SearchDataGrid;
        private List<AppUserModel> UserResults { get; set; }
        public string UpdateUserStatus { get; set; }
        public bool IsUserDownloading { get; set; } = false;
        public bool IsNewUserExportDownloading { get; set; } = false;
        public bool IsSearchClicked { get; set; } = false;
        public bool IsUserStatusClicked { get; set; } = false;
        public bool IsSavingloading { get; set; } = false;
        public bool IsInviationSending { get; set; } = false;
        private bool ThemesVisible;
        private TokenProvider provider = new TokenProvider();
        public bool AllowtoSearch { get; set; } = true;
        public ApplicationUserSearchRequestModel SearchModel { get; set; } = new ApplicationUserSearchRequestModel();


        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await _JSRuntime.InvokeVoidAsync("resetRecaptcha");
                await Loader(false);
                StateHasChanged();
            }
        }
        private string GetRegionName(string regionCode)
        {
            string name = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    name = "AFR - African Region";
                    break;
                case "AMR":
                    name = "AMR - Region of the Americas";
                    break;
                case "EMR":
                    name = "EMR - Eastern Mediterranean Region";
                    break;
                case "EUR":
                    name = "EUR - European Region";
                    break;
                case "SEAR":
                    name = "SEAR - South-East Asia Region";
                    break;
                case "WPR":
                    name = "WPR - Western Pacific Region";
                    break;
                default:
                    break;
            }
            return name;
        }
        private async Task GetLookupData()
        {
            if (!RegionCode.Any() || !CountryList.Any())
            {
                Regions = await _countryService.GetCountriesWithRegion();
                RegionCode = Regions.Where(x => x.RegionCode != null).DistinctBy(e => e.RegionCode).Select(e => e.RegionCode).ToList();
                CountryList = Regions.Distinct().Select(e => e.CountryName).ToList();
            }
        }
        public void OnRegionChanged(IEnumerable<string> regionCode)
        {
            User.ApproverCountries = new List<string>();
            if (regionCode != null)
            {
                User.ApproverRegion = new List<string>(regionCode.Contains("all") ? RegionCode : regionCode);

                User.ApproverCountries = Regions
                                .Where(e => regionCode.Contains(e.RegionCode))
                               .Select(e => e.CountryName).ToList();
            }

        }

        public async Task Loader(bool value)
        {
            await Task.Run(() =>
            {
                IsLoading = value;
            });
            StateHasChanged();
        }
        private async Task OpenAddOrUpdateModel()
        {
            await GetLookupData();
            ThemesVisible = true;
            StateHasChanged();
        }
        private void HideModal()
        {
            User = new();
            ThemesVisible = false;
            StateHasChanged();
        }
        public async Task OnSearchClicked()
        {
            SearchModel.PageNo = 1;
            AllowtoSearch = IsSearchClicked = true;
            await OnSearchResult();
            IsSearchClicked = false;
        }
        private async Task OnReadData(DataGridReadDataEventArgs<AppUserModel> e)
        {
            if (!e.CancellationToken.IsCancellationRequested)
            {
                if (e.ReadDataMode is DataGridReadDataMode.Paging)
                {
                    SearchModel.PageNo = e.Page;
                    SearchModel.PageSize = e.PageSize;
                    AllowtoSearch = true;
                }
                if (AllowtoSearch)
                {
                    await OnSearchResult();
                    await InvokeAsync(StateHasChanged);
                }

            }
        }
        private async Task OnSortChanged(DataGridSortChangedEventArgs e)
        {
            IsLoading = true;
            SearchModel.SortingColumn = e.FieldName;
            SearchModel.IsDescendingOrder = true;
            SearchModel.IsDescendingOrderType = e.SortDirection.ToString();
            await InvokeAsync(StateHasChanged);

        }
        public async Task OnSearchResult()
        {
            if (AllowtoSearch)
            {
                AllowtoSearch = false;
                using var _dbContext = DbFactory.CreateDbContext();
                var query = from user in _dbContext.Users
                            join userRole in _dbContext.UserRoles
                           on user.Id equals userRole.UserId
                            join role in _dbContext.Roles
                            on userRole.RoleId equals role.Id
                            where user.Status == (SearchModel.Status ?? user.Status)
                            && (user.Email.ToLower().StartsWith(SearchModel.SerachContent ?? user.Email)
                            || user.Organization.ToLower().StartsWith(SearchModel.SerachContent ?? user.Organization)
                            || user.FirstName.ToLower().StartsWith(SearchModel.SerachContent ?? user.FirstName)
                            || user.LastName.ToLower().StartsWith(SearchModel.SerachContent ?? user.LastName)
                             ) && role.Name == (SearchModel.Role ?? role.Name)
                            && user.Email.Equals(SearchModel.Email ?? user.Email)
                            select new AppUserModel()
                            {
                                Id = user.Id,
                                FirstName = user.FirstName,
                                LastName = user.LastName,
                                UserName = user.UserName,
                                Email = user.Email,
                                Organization = user.Organization,
                                Status = user.Status,
                                IsNewRegister = user.IsNewRegister,
                                UserRoles = role.Name,
                                CreatedDate = user.CreatedDate,
                                InterestedCountryList = _dbContext.ApplicationUserIntertestedCountries.Where(e => e.UserId == user.Id).Select(e => e.Country).ToList()
                            };

                // Apply Sorting
                query = await Sorting(query);

                SearchModel.TotalFilteredCount = await query.CountAsync();

                UserResults = await query.Skip(SearchModel.PageSize * (SearchModel.PageNo - 1))
                                .Take(SearchModel.PageSize).ToListAsync();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }


        private async Task<IQueryable<T>> Sorting<T>(IQueryable<T> query) where T : class
        {
            if (!string.IsNullOrEmpty(SearchModel.SortingColumn)
                && !string.IsNullOrEmpty(SearchModel.IsDescendingOrderType)
                && SearchModel.IsDescendingOrderType.ToLower() != "default")
            {
                bool isAscending = SearchModel.IsDescendingOrderType.ToLower() == "ascending";

                // Get the property dynamically
                var parameter = Expression.Parameter(typeof(T), "x");
                var property = typeof(T).GetProperty(SearchModel.SortingColumn, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                if (property != null)
                {
                    Expression propertyAccess = Expression.Property(parameter, property);
                    System.Type propertyType = property.PropertyType;
                    LambdaExpression orderByExpression = Expression.Lambda(propertyAccess, parameter);

                    string methodName = isAscending ? "OrderBy" : "OrderByDescending";
                    var orderByMethod = typeof(Queryable)
                        .GetMethods()
                        .Where(m => m.Name == methodName && m.GetParameters().Length == 2)
                        .Single()
                        .MakeGenericMethod(typeof(T), propertyType);

                    query = (IQueryable<T>)orderByMethod.Invoke(null, new object[] { query, orderByExpression });
                }
            }

            return query;
        }





        public async Task ExportUsers()
        {
            IsUserDownloading = true;
            using var _dbContext = DbFactory.CreateDbContext();
            var query = from user in _dbContext.Users
                        join userRole in _dbContext.UserRoles
                       on user.Id equals userRole.UserId
                        join role in _dbContext.Roles
                        on userRole.RoleId equals role.Id
                        where user.Status == (SearchModel.Status ?? user.Status)
                 && (user.Email.ToLower().StartsWith(SearchModel.SerachContent ?? user.Email)
                  || user.Organization.ToLower().StartsWith(SearchModel.SerachContent ?? user.Organization)
                  || user.FirstName.ToLower().StartsWith(SearchModel.SerachContent ?? user.FirstName)
                  || user.LastName.ToLower().StartsWith(SearchModel.SerachContent ?? user.LastName)
                   ) && role.Name == (SearchModel.Role ?? role.Name)
                        select new ApplicationUserCSVModel()
                        {
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            UserName = user.UserName,
                            Email = user.Email,
                            Organization = user.Organization,
                            Status = user.Status,
                            UserRoles = role.Name,
                            UserInterestedCountry = string.Join("|", _dbContext.ApplicationUserIntertestedCountries.Where(e => e.UserId == user.Id).Select(e => e.Country).ToList()),
                            ApproverAssignedCountry = string.Join("|", _dbContext.UserApproverCountries.Where(e => e.UserId == user.Id).Select(e => e.Country).ToList()),
                            CreatedDate = user.CreatedDate
                        };
            query = await Sorting(query);

            if (query.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(query.ToList());
                await _JSRuntime.InvokeVoidAsync("saveAsFile", "gifna_Users.csv", fileData);
            }
            IsUserDownloading = false;
            await InvokeAsync(StateHasChanged);
        }
        public Task ExportUsersClicked()
        {
            if (IsUserDownloading)
                return Task.CompletedTask;
            _ = ExportUsers();
            return Task.CompletedTask;
        }

        public async Task OnSavedClicked()
        {
            IsSavingloading = true;
            await UpdateUser(User);
            await OpenToaster("User Update", "User update Successfully", AntDesign.NotificationType.Success);
            HideModal();
            AllowtoSearch = true;
            _ = OnSearchResult();
            IsSavingloading = false;
        }

        private async Task UpdateUser(CreateUserModel user)
        {
            var applicationUser = await _userManager.Users.Where(e => e.Id == user.Id).FirstOrDefaultAsync();
            if (applicationUser != null)
            {
                var getuserRole = await _userManager.GetRolesAsync(applicationUser);
                var deleteRole = await _userManager.RemoveFromRolesAsync(applicationUser, getuserRole);
                var updateRole = await _userManager.AddToRoleAsync(applicationUser, user.Role);
                if (User.Role.Equals("Approver"))
                {
                    await RemoveApproverCountries(applicationUser.Id);
                    await AddApproverCountries(applicationUser);
                }
            }
        }
        private async Task RemoveApproverCountries(string userId)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var approverCountries = await _dbContext.UserApproverCountries
                                    .Where(e => e.UserId == userId)
                                     .ToListAsync();
            _dbContext.UserApproverCountries.RemoveRange(approverCountries);
            await _dbContext.SaveChangesAsync();
        }
        private async Task AddApproverCountries(ApplicationUser ApplicationUser)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var UserApproverCountries = new List<UserApproverCountry>();
            foreach (var item in User?.ApproverCountries)
            {
                UserApproverCountries.Add(new UserApproverCountry()
                {
                    UserId = ApplicationUser.Id,
                    Country = item,
                    CountryCode = Regions.FirstOrDefault(e => e.CountryName == item)?.CountryCode,
                });
            }
            await _dbContext.UserApproverCountries.AddRangeAsync(UserApproverCountries);
            await _dbContext.SaveChangesAsync();
        }
        public async Task InviationBtnClicked(AppUserModel user)
        {
            IsLoading = true;
            await SendInviatationEmail(user);
            AllowtoSearch = true;
            _ = OnSearchResult();
            AllowtoSearch = IsLoading = false;
        }

        public async Task SendNewRegisterEmailCliked()
        {
            if (IsInviationSending)
                return;
            try
            {
                IsInviationSending = true;
                var selectedUsers = UserResults.Where(w => w.Check == true && w.IsNewRegister == true).ToList();
                foreach (var userModel in selectedUsers)
                {
                    await SendInviatationEmail(userModel);
                }
            }
            catch (Exception ex)
            {
            }
            AllowtoSearch = true;
            _ = OnSearchResult();
            AllowtoSearch = IsInviationSending = false;
        }
        private async Task SendInviatationEmail(AppUserModel user)
        {
            try
            {
                string[] scopes = { "https://graph.microsoft.com/.default" };

                var accessToken = await UpdateTokenIfAccessTokenExpired();
                if (string.IsNullOrWhiteSpace(accessToken))
                {
                    _ = OpenInfoToaster("Sorry, You are not authorised to send the invitation.");
                    return;
                }


                TokenProvider provider = new();
                provider.token = accessToken;
                var authenticationProvider = new BaseBearerTokenAuthenticationProvider(provider);
                var graphClient = new GraphServiceClient(authenticationProvider);
                var requestBody = new Invitation
                {
                    InvitedUserEmailAddress = user.Email,
                    InviteRedirectUrl = _navigationManager.BaseUri,
                    SendInvitationMessage = true,
                };
                var result = await graphClient.Invitations.PostAsync(requestBody);

                var applicationUser = await _userManager.Users.Where(e => e.Id == user.Id).FirstOrDefaultAsync();
                applicationUser.IsNewRegister = false;
                applicationUser.Status = "invite sent";

                var userInfo = await _userManager.UpdateAsync(applicationUser);
                _ = OpenSuccessToaster("Invite sent successfully.");
            }
            catch (Exception ex)
            {
                _ = OpenInfoToaster("something went wrong, please contact admin");
                _logger.LogInformation("expection ::{ex}", ex);
            }
        }
        public async Task<string> UpdateTokenIfAccessTokenExpired()
        {
            var logedinUser = await _userManager.Users.FirstOrDefaultAsync(e => e.Email == _currentUserService.Email);
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadJwtToken(logedinUser.UserToken);
            // Get the expiration time from the token's payload
            var expirationTime = jwtToken.ValidTo;
            // Compare the expiration time with the current time
            var isExpired = DateTime.UtcNow > expirationTime;
            if (isExpired)
            {
                logedinUser = await _refreshToken.Refreshtoken(logedinUser);
            }
            return logedinUser.UserToken;
        }

        private async Task ToggleSelectDeselectSelection(string userName, bool isChecked)
        {
            await Loader(true);
            var test = isChecked;
            UserResults.Where(w => w.UserName == userName).FirstOrDefault().Check = isChecked;
            if (UserResults.Any(e => e.Check == true))
            {
                DisabledUserStatus = false;
            }
            else
            {
                DisabledUserStatus = true;
            }
            if (UserResults.Any(e => e.Check == true && e.IsNewRegister == true))
            {
                DisabledInviateUserStatus = false;
            }
            else
            {
                DisabledInviateUserStatus = true;
            }

            await Loader(false);
            StateHasChanged();
        }
        public async Task OnUserStatusClicked()
        {
            IsUserStatusClicked = true;
            var selectedUser = UserResults.Where(w => w.Check == true).Select(e => e.UserName).ToList();
            if (selectedUser.Any())
            {
                var appUser = await _userManager.Users.Where(e => selectedUser.Contains(e.UserName)).ToListAsync();
                appUser.ForEach(c => c.Status = UpdateUserStatus);
                foreach (var item in appUser)
                {
                    var isUpdate = await _userManager.UpdateAsync(item);
                }
            }
            await OpenToaster("Status Change", $"{selectedUser.Count} user Status Changed Successfully", AntDesign.NotificationType.Success);
            IsUserStatusClicked = false;
            DisabledUserStatus = true;
            AllowtoSearch = true;
            await OnSearchResult();
            StateHasChanged();
        }
        public async Task GetUserById(string userId, string role)
        {
            await Loader(true);
            ApplicationUser user = new();
            if (role.Equals(RolesConstant.ApproverRoleName))
            {
                user = await _userManager.Users.Include(e => e.UserApproverCountries)
               .Where(e => e.Id == userId).AsNoTracking().FirstOrDefaultAsync();
            }
            User = new CreateUserModel
            {
                Id = userId,
                //ApproverRegion = user?.UserApproverCountries?.FirstOrDefault()?.Country,
                ApproverCountries = user?.UserApproverCountries.Select(e => e.Country).ToList(),
                Role = role
            };
            await Loader(false);
            await OpenAddOrUpdateModel();
        }
    }

    public class TokenProvider : IAccessTokenProvider
    {
        public string token { get; set; }
        public AllowedHostsValidator AllowedHostsValidator => throw new NotImplementedException();

        public Task<string> GetAuthorizationTokenAsync(Uri uri, Dictionary<string, object>? additionalAuthenticationContext = null,
            CancellationToken cancellation = default)
        {
            return Task.FromResult(token);
        }
    }
}