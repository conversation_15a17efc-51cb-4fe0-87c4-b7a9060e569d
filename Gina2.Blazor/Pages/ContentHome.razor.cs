﻿using Blazorise.DataGrid;
using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.DbModels.HomeDrafts;
using Gina2.Services.HomeDraftServices;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using AntDesign;
using Gina2.Core.Interface;
using Gina2.Blazor.Models.AdminModel;

namespace Gina2.Blazor.Pages
{
    public partial class ContentHome : PageConfirgurationComponent
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private IHomeDraftService _HomeDraftService { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }

        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }
        public ApplicationUserSearchRequestModel SearchModel { get; set; } = new ApplicationUserSearchRequestModel();
        private bool IsLoading = false;

        private List<HomeDraft> homeDrafts = new List<HomeDraft>();
        [Inject]
        private TimeZoneService timeZoneService { get; set; }
        private int previewDraftId { get; set; }
        private string rerenderTokenId { get; set; }
        string title = "Live Preview";
        string selectedTab = "Drafts";
        bool _visible = false;
        bool deleteDraftModalVisible = false;
        int draftToBeDeleted;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsLoading = true;
                await GetAllDrafts();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            _ = base.OnAfterRenderAsync(firstRender);
        }

        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;
            return Task.CompletedTask;
        }


        private async Task ShowPreview(int draftId)
        {
            await Task.Run(() =>
            {
                IsLoading = true;
            });

            await InvokeAsync(StateHasChanged);
            previewDraftId = draftId;
            rerenderTokenId = Guid.NewGuid().ToString();
            _visible = true;
            await Task.Run(() =>
            {
                IsLoading = false;
            });
            await InvokeAsync(StateHasChanged);
        }


        private void HandleCancel(MouseEventArgs e)
        {
            _visible = false;
        }

        private async Task CreateDraft()
        {
            IsLoading = true;
            var draftCreated = await _HomeDraftService.CreateNewDraft(CurrentUserService.UserId);
            if (draftCreated != null)
            {
                NavigationManager.NavigateTo($"admin/contents/home/<USER>/{draftCreated.Id}/edit");
            }
            else
            {
                _ = OpenToaster("Error", "Failed to edit home page.", NotificationType.Error);
            }
            IsLoading = false;
        }

        private async Task<List<HomeDraft>> GetAllDrafts()
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);
            homeDrafts = await _HomeDraftService.GetAllDrafts();
            using var _dbContext = DbFactory.CreateDbContext();
            var users = await _dbContext.Users.ToListAsync();

            foreach (var item in homeDrafts)
            {
                var createbyUserObj = String.IsNullOrEmpty(item.CreatedBy) ? null : users.FirstOrDefault(w => w.Id == item.CreatedBy);
                var updatedbyUserObj = String.IsNullOrEmpty(item.UpdatedBy) ? null : users.FirstOrDefault(w => w.Id == item.UpdatedBy);

                item.RefCreatedBy = createbyUserObj == null ? "Admin" : createbyUserObj.FirstName + " " + createbyUserObj.LastName;
                item.RefUpdatedBy = updatedbyUserObj == null ? "Admin" : updatedbyUserObj.FirstName + " " + updatedbyUserObj.LastName;
                item.CreatedDateTime = await timeZoneService.GetLocalDateTime(item.DateCreated);
                item.UpdateDateTime = await timeZoneService.GetLocalDateTime(item.DateUpdated);
                item.IsCurrentPublished = item.HomePage != null;
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
            return homeDrafts;
        }

        private async Task DeleteHomeDraft(int draftId)
        {
            var currentPublishedDraft = await _HomeDraftService.GetPublishedDraft();
            if (currentPublishedDraft.Id == draftId)
            {
                _ = OpenToaster("Validation", "Published home page cannot be deleted.", NotificationType.Warning);
            }
            else
            {
                bool success = await _HomeDraftService.DeleteDraft(draftId);
                if (success)
                {
                    _ = OpenToaster("Success", "Draft deleted successfully.");
                    await GetAllDrafts();
                }
                else
                {
                    _ = OpenToaster("Error", "Draft delete failed.", NotificationType.Error);
                }
            }
            deleteDraftModalVisible = false;
            await InvokeAsync(StateHasChanged);
        }

        private void ShowDeleteDraftModal(int draftId)
        {
            deleteDraftModalVisible = true;
            draftToBeDeleted = draftId;
        }

        private void OnRowStyling(HomeDraft homeDraft, DataGridRowStyling styling)
        {
            if (homeDraft.IsCurrentPublished)
                styling.Style = "color: green;background: rgb(239 245 231); border: 1px solid rgb(255, 255, 255)!important;";
        }
    }
}