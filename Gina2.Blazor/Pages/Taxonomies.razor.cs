﻿using AutoMapper;
using Blazorise.Snackbar;
using Domain.Terms;
using Domain.Vocabulary;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Gina2.Core.Models;
using Microsoft.Extensions.Caching.Memory;
using Gina2.Services.Vocabulary;
using Gina2.Services.Country;
using Gina2.Services.Topic;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Blazorise;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authorization;
using Gina2.Core.Methods;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class Taxonomies : PageConfirgurationComponent
    {
        [Inject]
        public IVocabularyService vocabularyService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IMapper _mapper { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        private bool showTermsModal = false;
        private bool showAddEditTermModal;
        private bool showUpdateVocabularyModal = false;
        private bool isAddingTermMode = false;

        private bool isLoadingTerms = false;
        private bool isUpdatingVocabulary = false;
        private bool isSavingTerm = false;

        private SnackbarStack SnackbarStack { get; set; }

        private VocabularyModel selectedVocabulary = new();

        public List<VocabularyModel> Vocabularies { get; set; } = new();
        public List<DbModels.Region> Regions { get; set; } = new();
        public List<Term> Terms { get; set; } = new();
        private string NewVocabularyValue { get; set; }

        private Term TermDetail { get; set; } = new();
        private string TableName { get; set; }
        private IEnumerable<Topic> AllTopics { get; set; }
        private IEnumerable<TopicParent> AllParentTopics { get; set; }

        public List<GTreeNode> TopicList { get; set; } = new ();
        private List<string> CountryStatus = new ();
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                Regions = await CountryService.GetRegionsAsync();  
                await vocabularyService.GetAsync();
                await LoadCountryStatus();
                await LoadVocabulariesAsync();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
                _ = JsRuntime.InvokeVoidAsync("SetupIdforDragandDrop", "VocabulariesList");
            }
        }

        private async Task LoadCountryStatus()
        {
            CountryStatus = await CountryService.GetCountryStatus();
        }

        private async Task<List<Term>> GetChildTopicTreeView(List<DbModels.TopicParent> First)
        {

            List<Term> child = new List<Term>();
            foreach (var item in First)
            {
                Term itemchild = new Term();
                child.Add(new Term()
                {
                    Id = item.TopicId,
                    ParentId = item.ParentId,
                    ParentName = AllTopics.Where(t => t.Id == item.ParentId).FirstOrDefault().Name,
                    Name = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Child = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.Topic.DragAndDropKey).ToList())
                });
            }
            return child;
        }
        private async Task LoadVocabulariesAsync()
        {
            Vocabularies = await vocabularyService.GetAsync();
            var icn2 = Vocabularies.Where(i => i.TableName == Gina2.Core.Constants.VocabularyTables.Icn2).First();
            var region = Vocabularies.Where(i => i.TableName == Gina2.Core.Constants.VocabularyTables.Region).First();
            Vocabularies.Remove(icn2);
            Vocabularies.Remove(region);
        }

        private void ListTermsAsync(VocabularyModel vocabulary)
        {
            NavigationManager.NavigateTo($"/admin/taxonomies/{vocabulary.TableName}");
        }

        private void AddNewTerm(String tableName, int parentId = 0)
        {
            TableName = tableName;
            TermDetail = new()
            {
                ParentId = parentId != 0 ? parentId : 0
            };
            showAddEditTermModal = true;
            isAddingTermMode = true;
        }

        private void ShowUpdateVocabularyPopupAsync(VocabularyModel vocabulary)
        {
            selectedVocabulary = vocabulary;
            showUpdateVocabularyModal = true;
        }

        private async Task SaveOrUpdateNewTerms()
        {
            isSavingTerm = true;
            var term = await vocabularyService.AddEditTermsAsync(selectedVocabulary.TableName, TermDetail);
            Terms.Add(term);
            showAddEditTermModal = false;
            TermDetail = new();
            NewVocabularyValue = String.Empty;
            isSavingTerm = false;
            await SnackbarStack.PushAsync("The term is saved.", SnackbarColor.Success, options => options.IntervalBeforeClose = 2000);
        }

        private async Task UpdateVocabularyAsync()
        {
            isUpdatingVocabulary = true;
            if (selectedVocabulary != null)
            {
                await vocabularyService.UpdateVocabularyName(selectedVocabulary);
            }

            isUpdatingVocabulary = false;
            showUpdateVocabularyModal = false;

            await InvokeAsync(StateHasChanged);
            await SnackbarStack.PushAsync("The vocabulary name is updated.", SnackbarColor.Success, options => options.IntervalBeforeClose = 2000);
        }

        private async Task EditTermPopupAsync(Term term)
        {
            var result = new Term();
            result.Id = term.Id;
            result.Name = term.Name;
            result.Description = term.Description;
            result.ParentId = term.ParentId;
            result.Status = term.Status;

            TermDetail = _mapper.Map<Term>(result);
            if (!String.IsNullOrEmpty(TermDetail.Iso3Code) && CountryService != null)
            { 
            TermDetail.Code = await CountryService.GetRegionByCountryAsync(TermDetail.Iso3Code);
            }
            showAddEditTermModal = true;
            isAddingTermMode = false;
        }

        public async Task OnDropVocabulary()
        {
            isSavingTerm = true;
            foreach (var item in Vocabularies)
            {
                item.DragAndDropKey = Vocabularies.IndexOf(item);
            }

            await vocabularyService.SaveDragAndDropVocabularyAsync(Vocabularies);
            isSavingTerm = false;
        }

        private async Task ResetTermsOrderAsync()
        {
            isSavingTerm = true;

            Terms = Terms.OrderBy(item => item.Name).ToList();

            foreach (var item in Terms)
            {
                item.DragAndDropKey = Terms.IndexOf(item);
            }
            await vocabularyService.SaveDragAndDropTermsAsync(TableName, Terms, string.Empty, new List<Term>());
            isSavingTerm = false;
        }

        private async Task ResetVocabularyOrderAsync()
        {
            isSavingTerm = true;

            Vocabularies = Vocabularies.OrderBy(item => item.Name).ToList();

            foreach (var item in Vocabularies)
            {
                item.DragAndDropKey = Vocabularies.IndexOf(item);
            }

            await vocabularyService.SaveDragAndDropVocabularyAsync(Vocabularies);
            isSavingTerm = false;
        }

        public async Task OnDropTerm()
        {
            isSavingTerm = true;
            foreach (var item in Terms)
            {
                item.DragAndDropKey = Terms.IndexOf(item);
                if (item.Icn2s != null && item.Icn2s.Count > 0)
                {
                    foreach (var icn in item.Icn2s)
                    {
                        icn.DragAndDropKey = item.Icn2s.IndexOf(icn);
                    }

                }

                if (item.Partner != null && item.Partner.Count > 0)
                {
                    foreach (var partner in item.Partner)
                    {
                        partner.DragAndDropKey = item.Partner.IndexOf(partner);
                    }
                }
            }

            await vocabularyService.SaveDragAndDropTermsAsync(TableName, Terms, string.Empty, new List<Term>());
            isSavingTerm = false;
        }

        private void ValidateText(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }
    }
}