﻿using AntDesign;
using AutoMapper;
using Domain.Terms;
using Force.DeepCloner;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core.Constant;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Country;
using Gina2.Services.ICN2;
using Gina2.Services.Language;
using Gina2.Services.Mechanism;
using Gina2.Services.Models;
using Gina2.Services.PartnerCategorys;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Gina2.Services.TargetGroups;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Net;
using System.Text;
using Domain.PolicyTypes;
using Domain.Search;
using Newtonsoft.Json.Linq;
using Gina2.Services.Search;
using Blazorise;
using System.Diagnostics;
using System.Net.NetworkInformation;
using Microsoft.Graph.Models;

namespace Gina2.Blazor.Pages
{
    public partial class GlobalSearch : PageConfirgurationComponent
    {
        #region Services
        [Inject]
        private IProgrammeService ProgrammeService { get; set; }
        [Inject]
        private ISearchService SearchService { get; set; }
        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Inject]
        private ITopicService TopicService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Inject]
        private IPartnerCategoryService PartnerCategoryService { get; set; }

        [Inject]
        private ITargetGroupService TargetGroupService { get; set; }

        [Inject]
        private IProgrammeService ProgramSevice { get; set; }
        [Inject]
        private IIcn2Service Icn2Service { get; set; }
        [Inject]
        private ILanguageService LanguageService { get; set; }

        [Inject]
        private IMapper mapper { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        #endregion Services

        #region Declarations

        private GlobalSearchRequest searchRequest = new();
        private GlobalSearchRequest previousSearchRequest = null;

        private readonly List<Domain.Search.SearchResult> selectedEntries = new();
        public List<PolicyLookup> PolicyLookups { get; set; } = new List<PolicyLookup>();
        private bool IsAllSelected { get; set; } = false;
        private Tree<GTreeNode> policyTopicTree;
        private Tree<GTreeNode> mechanismTopicTree;
        private Tree<GTreeNode> actionTopicTree;
        private Tree<GTreeNode> searchTree;

        private Tree<TermTreeNode> refCoutryGroupTree;
        private Tree<TermTreeNode> refCoutryRegionTree;
        private Tree<TermTreeNode> refPartnerTree;
        private Tree<PolicyTypeViewModel> refPolicyTypeTree;
        private Tree<ProgramType> refProgramTypeTree;
        private Tree<PartnerCategory> refFundingSource;
        private Tree<TargetGroup> refTargetGroupTree;
        private Tree<Delivery> refDeliveryTree;
        private Tree<ProblemType> refProblemTypeTree;
        private Tree<MechanismType> refMechanismTypeTree;
        private Tree<TermTreeNode> refICN2Tree;
        private Tree<Language> refLanguageTree;
        private List<string> lastSelectedNutritions = new();
        private Dictionary<int, List<int>> SelectedSearchTopicList = new();
        private List<string> SelectedSearchTopicListUIOnly = new();
        private Dictionary<int, List<int>> PolicyTopicParentChilds = new();
        private Dictionary<int, List<int>> MechanismTopicParentChilds = new();
        private Dictionary<int, List<int>> ActionTopicParentChilds = new();
        private Dictionary<int, List<int>> BasicTopicParentChilds = new();

        private Dictionary<int, List<int>> AllPolicyTopicParentChilds = new();
        private Dictionary<int, List<int>> AllMechanismTopicParentChilds = new();
        private Dictionary<int, List<int>> AllActionTopicParentChilds = new();
        private Dictionary<int, List<int>> AllBasicTopicParentChilds = new();
        private string SelectedSearchTopicName = string.Empty;
        private bool isGeneralFilter = true;
        private List<int> ExistingTopicCountryCounts { get; set; } = new();
        private List<CountryWithRegion> AllCountryWithRegion { get; set; } = new();

        #endregion Declarations

        #region Properties
        private Visibility searchLoaderVisibility = Visibility.Invisible;

        private IEnumerable<CountryWithRegion> AllRegions = Enumerable.Empty<CountryWithRegion>();
        private static IEnumerable<string> AllIncomGroups = Enumerable.Empty<string>();
        private List<CountryWithRegion> fullCountryDetail = new();
        private List<CountryWithRegion> allCountryDetail = new();
        public bool IsSearchedClicked { get; set; } = false;
        private readonly List<GTreeNode> PolicyTopicTreeNodes = new();
        private readonly List<GTreeNode> MechanismTopicTreeNodes = new();
        private readonly List<GTreeNode> ActionTopicTreeNodes = new();
        private readonly List<GTreeNode> OtherPrentTopicTreeNodes = new();
        private readonly List<GTreeNode> TopicsInSearch = new();
        private readonly List<TermTreeNode> CountryGroupsTree = new();
        private readonly List<TermTreeNode> CountryRegionGroupTree = new();
        private readonly List<TermTreeNode> PartnerTree = new();
        private readonly List<TermTreeNode> ICN2Tree = new();
        private List<Gina2.DbModels.Topic> AllTopics = new();
        private List<Gina2.DbModels.TopicParent> AllParentTopics = new();
        public int NewPolicyId { get; set; }

        private readonly string[] Startplace = new string[] { "Earliest start year" };
        private readonly string[] Endplace = new string[] { "Latest start year" };
        public bool IsTopicDDDisabled { get; set; } = false;
        public int RegionId { get; set; }
        private DataGridforSearch DataGridforSearchchild { get; set; }
        private SearchResultCounts SearchDataCount { get; set; } = new();
        private FileDownload FileDownloadChild { get; set; }
        public bool CanDownload => IsAllSelected || selectedEntries.Any();
        public string TotalRecordMessage { get; set; } = string.Empty;
        public bool ShowCSVPanel { get; set; } = false;
        public bool ShowTopic { get; set; } = true;
        public bool AdFilter { get; set; } = false;
        public bool AdFilterdisa { get; set; } = true;
        public bool AdFilterdisabled { get; set; } = true;
        private bool HideMore { get; set; } = false;
        private bool hasPolicyTopics = false;
        private bool hasMechanismTopics = false;
        private bool hasActionTopics = false;
        private void Toggle()
        {
            HideMore = !HideMore;
        }

        public List<SearchHistory> SearchHistories { get; set; } = new();
        public SearchChangeSet SearchChangeSet { get; set; } = new();
        private List<TopicDataTypeInformation> TopicDataList { get; set; } = new();
        private List<CountryGroup> CountryGroups { get; set; } = new();
        private List<PartnerCategory> PartnerCategories { get; set; } = new();
        private List<Domain.PolicyTypes.PolicyTypeViewModel> PolicyTypes { get; set; } = new();
        private List<ProgramType> ProgramTypes { get; set; } = new();
        private List<TargetGroup> TargetGroups { get; set; } = new();
        private List<Delivery> Deliveries { get; set; } = new();
        private List<ProblemType> ProblemTypes { get; set; } = new();
        private List<MechanismType> MechanismTypes { get; set; } = new();
        private List<Icn2Category> Icn2Categories { get; set; } = new();
        private List<Language> Languages { get; set; } = new();

        private int charCount = 0;
        private bool EnableAdvancedSearch;
        #endregion Properties

        #region Methods

        #region Methods - ComponentBase Overrides
        public string GetPageURL()
        {
            string page = "searchpage";
            if (NavigationManager.Uri.Contains("advanced-search"))
            {
                page = "advancesearch";
            }
            return page;
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {

            if (firstRender)
            {
                _ = ToggleSearchFiltersLoader(Visibility.Visible);
                searchRequest.DefaultPage = GetPageURL();
                _ = GetSearchResultCount();
                _ = GetTopicsAsync().ConfigureAwait(false);
                if (!string.IsNullOrEmpty(Configuration["EnableSearchPage"]) && !Convert.ToBoolean(Configuration["EnableSearchPage"]))
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                EnableAdvancedSearch = Convert.ToBoolean(Configuration["EnableAdvancedSearch"]);
                _ = JSRuntime.InvokeVoidAsync("loadJs", Configuration["ReCaptcha:GoogleRecaptchaUrl"].ToString() + Configuration["ReCaptcha:SiteKey"].ToString());
                isGeneralFilter = !NavigationManager.Uri.Contains("advanced-search");
                PolicyLookups = PolicyLookup.GetPolicyFilter();
                IsLoading = false;
                _ = ToggleSearchFiltersLoader(Visibility.Invisible);
                AddSearchHistory();
                //previousSearchRequest = searchRequest.DeepClone();
                await InvokeAsync(StateHasChanged);
            }
        }
        private async Task GetAllCountryAsyncFilter()
        {
            if (!AllCountryWithRegion.Any())
            {
                AllCountryWithRegion = await CountryService.GetAllCountryWithRegion();
            }
        }
        public async Task RefreshGrid()
        {
            _ = DataGridforSearchchild.RefreshDataGrid();
        }
        public async Task ToggleSearchFiltersLoader(Visibility value)
        {
            searchLoaderVisibility = value;
            await InvokeAsync(StateHasChanged);
        }
        public async Task GetSearchResultCount()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var searchResponseFromSp = await SearchService.GetCombinedSearchResultCounts(searchRequest);
            stopwatch.Stop();
            Console.WriteLine($"GetSearchResultCount grid calling time: {stopwatch.Elapsed}");
            IsSearchedClicked = true;
            _ = GetDataCount(searchResponseFromSp.SearchResultCounts);
            _ = ToggleSearchFiltersLoader(Visibility.Invisible);
        }

        private async Task EmptyTotalRecordMessage()
        {
            TotalRecordMessage = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        public async Task SearchGlobally(bool initialCall = false)
        {
            _ = EmptyTotalRecordMessage();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = RefreshGrid();
            _ = GetSearchResultCount();
        }
        private void ToggleAdvanceFilterMode()
        {
            if (isGeneralFilter)
            {
                NavigationManager.NavigateTo("/advanced-search", true);
            }
        }
        private void ToggleGeneralFilterMode()
        {
            if (!isGeneralFilter)
            {
                NavigationManager.NavigateTo("/search", true);
            }
        }
        public async Task OnClearDataTypesClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            PolicyLookups.ForEach(x => x.IsChecked = false);
            PolicyLookups.FirstOrDefault(e => e.Name == "Policies").IsChecked = true;
            SetDataType();
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = OnSearchClicked();
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            StateHasChanged();
        }
        public async Task OnClearSearchTopics()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            searchTree.UncheckAll();
            SelectedSearchTopicList.Clear();
            SelectedSearchTopicName = string.Empty;
            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = OnSearchClicked();
            StateHasChanged();
        }
        public async Task OnClearRegionsClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            searchRequest.SelectedRegionCode = String.Empty;
            searchRequest.SelectedIncomeCode = String.Empty;
            searchRequest.SelectedCountries = Enumerable.Empty<string>();

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = OnSearchClicked();
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            StateHasChanged();
        }
        public async Task OnClearCountryClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            searchRequest.SelectedCountries = Enumerable.Empty<string>();

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = OnSearchClicked();
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            StateHasChanged();
        }
        public async Task OnClearPublishedYearClicked()
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);

            searchRequest.StartDate = null;
            searchRequest.EndDate = null;

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = OnSearchClicked();
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
            StateHasChanged();
        }

        public async Task GetDataCount(Domain.Search.SearchResultCounts value)
        {
            SearchDataCount = value;
            var countryList = value.CountryCount.Select(s => s.Key);
            await GetAllCountryAsyncFilter();
            var countryDetailforProgramAction = AllCountryWithRegion.Where(w => countryList.Contains(w.CountryCode))
                                                                    .ToList();// await CountryService.GetCountriesWithRegionProgramAction(countryList);
            TotalRecordMessage = value.TotalRecordCount.ToString();
            FileDownloadChild.RefreshDataofFileDownload(value, searchRequest);
            GetCountryPanelDetail(countryDetailforProgramAction);
            //StateHasChanged();
        }

        public int GetDataTypeCount(string id)
        {
            return SearchDataCount.DataTypeCount.FirstOrDefault(w => w.Key == id).Value;
        }

        public int GetDataTypeCountryCount(string id)
        {
            return SearchDataCount.DataTypeWiseCountryCount.FirstOrDefault(w => w.Key == id).Value;
        }

        // new implementt started there
        public int GetBasicTopicCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            try
            {
                if (parentNode != null)
                {

                }
                if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
                {
                    if (AllBasicTopicParentChilds.ContainsKey(topicId))
                    {
                        var childs = AllBasicTopicParentChilds[topicId];
                        var childsCount = SearchDataCount.BasicTopicCountList
                                    .Where(s => childs.Contains(s.Key))
                                    .SelectMany(s => s.Value)
                                    .Distinct()
                                    .Count();

                        return childsCount + SearchDataCount.BasicTopicCount.FirstOrDefault(w => w.Key == topicId).Value;
                    }
                    return 0;
                }
                else
                {
                    var childsCount = SearchDataCount.BasicTopicCountList
                                    .Where(s => s.Key.Equals(topicId))
                                    .SelectMany(s => s.Value)
                                    .Distinct()
                                    .Count();

                    return childsCount + SearchDataCount.BasicTopicCount.FirstOrDefault(w => w.Key == topicId).Value;

                }
            }
            catch (Exception)
            {
                throw;
            }

        }

        public int GetBasicTopicCountryCount(int topicId, AntDesign.TreeNode<GTreeNode> parentNode)
        {
            if (AllParentTopics.Where(s => s.ParentId == topicId).Any())
            {
                List<int> childs = new List<int>();

                if (AllBasicTopicParentChilds.ContainsKey(topicId))
                {
                    childs = AllBasicTopicParentChilds[topicId].ToList();
                    childs.Add(topicId);
                }

                var aa = SearchDataCount.BasicTopicCountryList
                               .Where(s => childs.Contains(s.Key))
                               .SelectMany(s => s.Value)
                               .Distinct()
                               .Count();
                return aa;
            }
            else
            {
                var countryCount = SearchDataCount.BasicTopicCountryList.FirstOrDefault(w => w.Key == topicId).Value;
                return countryCount == null ? 0 : countryCount.Count();
            }
        }
        // new implementt end here
        public async Task GetDataFromDataGrid(Dictionary<string, object> value)
        {
            FileDownloadChild.RefreshDataofSelectList(value);
            searchRequest.IsAllSelected = Convert.ToBoolean(value["IsAllSelected"]);
            TotalRecordMessage = value.ToString();
        }

        private void GetCountryPanelDetail(List<CountryWithRegion> countryWithRegions)
        {
            allCountryDetail.AddRange(countryWithRegions);
            allCountryDetail = allCountryDetail.DistinctBy(e => new { e.DataType, e.CountryCode }).ToList();
            RemovedRegionDetail();

        }
        private void RemovedRegionDetail()
        {
            var listofSelectedData = PolicyLookups.Where(e => e.IsChecked == true).Select(e => e.Name.First()).ToList();
            fullCountryDetail = allCountryDetail.OrderBy(e => e.CountryName).ToList();

            AllRegions = allCountryDetail.Where(e => e.RegionCode != null)
                                        .OrderBy(e => e.RegionOrder)
                                        .ThenBy(e => e.RegionCode)
                                        .GroupBy(d => d.RegionCode)
                                        .Select(d => d.OrderByDescending(d => d.RegionDescription).First())
                                        .Distinct()
                                        .ToList();

            if (!CountryRegionGroupTree.Any())
            {
                foreach (var region in fullCountryDetail.Where(e => e.RegionCode != null).GroupBy(w => w.RegionCode))
                {
                    CountryRegionGroupTree.Add(new TermTreeNode()
                    {
                        Id = 0,
                        RegionCode = region.Key,
                        Name = region.Key,
                        Iso3Code = region.Key,
                        Children = mapper.Map<List<CountryWithRegion>, List<TermTreeNode>>(region.Select(s => s).ToList())
                    });
                }
            }

            AllIncomGroups = allCountryDetail.Where(e => e.IncomeGroupCode != null)
                                             .OrderBy(e => e.IncomeOrder)
                                             .Select(e => e.IncomeGroupCode)
                                             .Distinct()
                                             .ToList();
            FilterCountries();
            StateHasChanged();
        }
        #endregion Methods - ComponentBase Overrides

        #region Methods - Helpers

        private GTreeNode GetTreeNodeByTopicId(int topicId, List<GTreeNode> nodes)
        {
            var node = nodes.FirstOrDefault(t => t.TopicId == topicId);

            if (node == null)
            {
                foreach (var childNode in nodes)
                {
                    node = GetTreeNodeByTopicId(topicId, childNode.Children);

                    if (node != null)
                    {
                        break;
                    }
                }
            }

            return node;
        }


        private async Task OnSearchClicked()
        {
            if (!await VerifyRecaptcha())
            {

                return;
            }
            SetDataType();
            searchRequest.PageNo = 1;
            searchRequest.SelectedSearchTopics = SelectedSearchTopicList;
            searchRequest.SelectedSearchTopicsUIOnly = SelectedSearchTopicListUIOnly;
            searchRequest.SelectedParentNutritions = lastSelectedNutritions;
            _ = SearchGlobally(true);
            ClearSearchParams();

            AddSearchHistory();
            previousSearchRequest = searchRequest.DeepClone();
            StateHasChanged();
        }

        private async Task<bool> VerifyRecaptcha()
        {
            var token = await JSRuntime.InvokeAsync<string>("runCaptcha", Configuration["ReCaptcha:SiteKey"].ToString());
            var data = VerifyToken(token);
            if (data)
            {
                return true;
            }
            return false;
        }

        private void AddSearchHistory()
        {
            SearchHistory history = new(pageConfigrationCache)
            {
                DataTypes = GetSelectedDataTypesString(),
                SelectedRegionCode = searchRequest.SelectedRegionCode,//(previousSearchRequest?.SelectedRegionCode != searchRequest.SelectedRegionCode || string.IsNullOrEmpty(previousSearchRequest.SelectedRegionCode)) ? searchRequest.SelectedRegionCode : "",
                SelectedIncomeGroup = searchRequest.SelectedIncomeCode,// previousSearchRequest?.SelectedIncomeCode != searchRequest.SelectedIncomeCode ? searchRequest.SelectedIncomeCode : "",
                StartYear = searchRequest.StartYear,//previousSearchRequest?.StartYear != searchRequest.StartYear ? searchRequest.StartYear : null,
                EndYear = searchRequest.EndYear,// previousSearchRequest?.EndYear != searchRequest.EndYear ? searchRequest.EndYear : null,
                SearchKeyword = previousSearchRequest?.SearchText != searchRequest.SearchText ? searchRequest.SearchText : "",
                SelectedCountries = searchRequest.SelectedCountries.ToList(),
                Topics = AllTopics,
                LastSelectedNutritions = GetNutritionDifference(),
                SelectedTopicIds = searchRequest.SelectedTopcis,
                SelectedPolicyTopicIds = searchRequest.SelectedPolicyTopics,
                SelectedMechanismTopicIds = searchRequest.SelectedMechanismTopics,
                SelectedActionTopicIds = searchRequest.SelectedActionTopics,
                Order = SearchHistories.Any() ? SearchHistories.Max(w => w.Order) + 1 : 1,
                SelectedSearchTopicIds = searchRequest.SelectedSearchTopics.DeepClone(),
                SelectedSearchTopicIdsOnSearchTree = searchRequest.SelectedSearchTopicsUIOnly.DeepClone(),
                SelectedSearchTopicName = SelectedSearchTopicName,
                PolicyTypeIds = searchRequest.PolicyTypeIds.DeepClone(),
                MechanismTypeIds = searchRequest.MechanismTypeIds.DeepClone(),
                DeliveryChannelIds = searchRequest.DeliveryChannelIds.DeepClone(),
                FundingSourceIds = searchRequest.FundingSourceIds.DeepClone(),
                ICN2Ids = searchRequest.ICN2Ids.DeepClone(),
                LanguageIds = searchRequest.LanguageIds.DeepClone(),
                PartnerIds = searchRequest.PartnerIds.DeepClone(),
                ProblemIds = searchRequest.ProblemIds.DeepClone(),
                ProgramTypeIds = searchRequest.ProgramTypeIds.DeepClone(),
                TargetGroupIds = searchRequest.TargetGroupIds.DeepClone(),
                CountryGroupSelectedCountries = searchRequest.CountryGroupSelectedCountries.DeepClone(),
                CountryRegionSelectedCountries = searchRequest.CountryRegionSelectedCountries.DeepClone(),
                SearchRequest = searchRequest.DeepClone(),

            };

            if (!string.IsNullOrWhiteSpace(history.Name))
            {
                if (SearchHistories.Any(h => h.UniqueName.Equals(history.UniqueName)))
                {
                    SearchHistories = SearchHistories.Where(h => !h.UniqueName.Equals(history.UniqueName)).ToList();
                }

                SearchHistories.Add(history);
            }
        }

        private void ClearSearchParams()
        {
            if (!searchRequest.IsPolicyDataTypeSelected)
            {
                searchRequest.PolicyTypeIds = Enumerable.Empty<int>();
                searchRequest.SelectedPolicyTopics = new();
            }
            if (!searchRequest.IsMechanicalDataTypeSelected)
            {
                searchRequest.MechanismTypeIds = Enumerable.Empty<int>();
                searchRequest.SelectedMechanismTopics = new List<SelectedTopicsTree>();
            }
            if (!searchRequest.IsPragrammesAndActionsDataTypeSelected)
            {
                searchRequest.ProgramTypeIds = Enumerable.Empty<int>();
                searchRequest.ProblemIds = Enumerable.Empty<int>();
                searchRequest.DeliveryChannelIds = Enumerable.Empty<int>();
                searchRequest.FundingSourceIds = Enumerable.Empty<int>();
            }
            if (!searchRequest.IsCommitmentsDataTypeSelected)
            {
                searchRequest.ICN2Ids = new();
            }
        }

        private async Task LoadSearchHistory(int order)
        {
            var history = SearchHistories.Where(w => w.Order == order).First();
            SearchHistories.RemoveAll(w => w.Order > history.Order);

            searchRequest = history.SearchRequest.DeepClone();
            SelectedSearchTopicList = searchRequest.SelectedSearchTopics;
            // if (policyTopicTree != null)
            // {
            //     policyTopicTree.UncheckAll();
            //     foreach (var item in history.SelectedPolicyTopicIds)
            //     {
            //         var parentNode = policyTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item, true).ParentNode;
            //         if (parentNode != null)
            //         {
            //             parentNode.Indeterminate = true;
            //         }
            //         policyTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item, true).Checked = true;
            //     }
            // }

            if (refCoutryGroupTree != null)
            {
                refCoutryGroupTree.UncheckAll();
                foreach (var item in history.CountryGroupSelectedCountries)
                {
                    var parentNode = refCoutryGroupTree.FindFirstOrDefaultNode(s => s.DataItem.Iso3Code == item.Key);

                    foreach (var data in item.Value)
                    {
                        refCoutryGroupTree.FindFirstOrDefaultNode(w => w.DataItem.Iso3Code == data && w.ParentNode.DataItem.Iso3Code == item.Key, true).Checked = true;
                    }

                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                }
            }

            if (refCoutryRegionTree != null)
            {
                refCoutryRegionTree.UncheckAll();
                foreach (var item in history.CountryRegionSelectedCountries)
                {
                    var parentNode = refCoutryRegionTree.FindFirstOrDefaultNode(s => s.DataItem.RegionCode == item.Key);

                    foreach (var data in item.Value)
                    {
                        refCoutryRegionTree.FindFirstOrDefaultNode(w => w.DataItem.Iso3Code == data && w.ParentNode.DataItem.RegionCode == item.Key, true).Checked = true;
                    }

                    if (parentNode != null)
                    {
                        parentNode.Indeterminate = true;
                    }
                }
            }

            // if (mechanismTopicTree != null)
            // {
            //     mechanismTopicTree.UncheckAll();
            //     foreach (var item in history.SelectedMechanismTopicIds)
            //     {
            //         var parentNode = mechanismTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item.TopicId, true).ParentNode;
            //         if (parentNode != null)
            //         {
            //             parentNode.Indeterminate = true;
            //         }
            //         mechanismTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item.TopicId, true).Checked = true;
            //     }
            // }

            // if (actionTopicTree != null)
            // {
            //     actionTopicTree.UncheckAll();
            //     foreach (var item in history.SelectedActionTopicIds)
            //     {
            //         var parentNode = actionTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item.TopicId, true).ParentNode;
            //         if (parentNode != null)
            //         {
            //             parentNode.Indeterminate = true;
            //         }
            //         actionTopicTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == item.TopicId, true).Checked = true;
            //     }
            // }

            // if (refPartnerTree != null)
            // {
            //     refPartnerTree.UncheckAll();
            //     foreach (var item in history.PartnerIds)
            //     {
            //         var parentNode = refPartnerTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).ParentNode;
            //         if (parentNode != null)
            //         {
            //             parentNode.Indeterminate = true;
            //         }
            //         refPartnerTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item.Key, true).Checked = true;
            //     }
            // }


            // if (refPolicyTypeTree != null)
            // {
            //     refPolicyTypeTree.UncheckAll();
            //     foreach (var item in history.PolicyTypeIds)
            //     {
            //         refPolicyTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }


            // if (refMechanismTypeTree != null)
            // {
            //     refMechanismTypeTree.UncheckAll();
            //     foreach (var item in history.MechanismTypeIds)
            //     {
            //         refMechanismTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refProgramTypeTree != null)
            // {
            //     refProgramTypeTree.UncheckAll();
            //     foreach (var item in history.ProgramTypeIds)
            //     {
            //         refProgramTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refFundingSource != null)
            // {
            //     refFundingSource.UncheckAll();
            //     foreach (var item in history.FundingSourceIds)
            //     {
            //         refFundingSource.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refLanguageTree != null)
            // {
            //     refLanguageTree.UncheckAll();
            //     foreach (var item in history.LanguageIds)
            //     {
            //         refLanguageTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }


            // if (refTargetGroupTree != null)
            // {
            //     refTargetGroupTree.UncheckAll();
            //     foreach (var item in history.TargetGroupIds)
            //     {
            //         refTargetGroupTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refDeliveryTree != null)
            // {
            //     refDeliveryTree.UncheckAll();
            //     foreach (var item in history.DeliveryChannelIds)
            //     {
            //         refDeliveryTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refProblemTypeTree != null)
            // {
            //     refProblemTypeTree.UncheckAll();
            //     foreach (var item in history.ProblemIds)
            //     {
            //         refProblemTypeTree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            // if (refICN2Tree != null)
            // {
            //     refICN2Tree.UncheckAll();
            //     foreach (var item in history.ICN2Ids)
            //     {
            //         refICN2Tree.FindFirstOrDefaultNode(w => w.DataItem.Id == item, true).Checked = true;
            //     }
            // }

            if (searchTree != null)
            {
                searchTree.UncheckAll();
                foreach (var item in history.SelectedSearchTopicIdsOnSearchTree)
                {
                    searchTree.FindFirstOrDefaultNode(w => w.DataItem.TopicId == int.Parse(item.Split('-').ElementAt(1)), true).Checked = true;
                }
            }


            PolicyLookups.ForEach(pl =>
            {
                if (pl.Name.Equals("Policies"))
                {
                    pl.IsChecked = searchRequest.IsPolicyDataTypeSelected;
                }

                if (pl.Name.Equals("Programmes and actions"))
                {
                    pl.IsChecked = searchRequest.IsPragrammesAndActionsDataTypeSelected;
                }

                if (pl.Name.Equals("Mechanisms"))
                {
                    pl.IsChecked = searchRequest.IsMechanicalDataTypeSelected;
                }

                if (pl.Name.Equals("SMART commitments"))
                {
                    pl.IsChecked = searchRequest.IsCommitmentsDataTypeSelected;
                }
            });

            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));

            DataGridforSearchchild.SearchRequest = searchRequest;
            _ = SearchGlobally(true);
            HideMore = false;
            StateHasChanged();
        }
        private List<string> GetNutritionDifference()
        {
            if (!searchRequest.SelectedParentNutritions.Any())
            {
                return new();
            }

            var addedNutritions = searchRequest.SelectedParentNutritions.Except(previousSearchRequest.SelectedParentNutritions).ToList();

            if (addedNutritions.Any())
            {
                return addedNutritions;
            }

            var removedNutritions = previousSearchRequest.SelectedParentNutritions.Except(searchRequest.SelectedParentNutritions).ToList();

            if (removedNutritions.Any())
            {
                return searchRequest.SelectedParentNutritions;
            }

            return new();
        }
        private List<string> GetSelectedDataTypesString()
        {
            List<string> dataTypes = new();

            if (searchRequest.IsPolicyDataTypeSelected && searchRequest.IsPragrammesAndActionsDataTypeSelected &&
                    searchRequest.IsMechanicalDataTypeSelected && searchRequest.IsCommitmentsDataTypeSelected)
            {
                dataTypes.Add("All data types");
                return dataTypes;
            }

            if (searchRequest.IsPolicyDataTypeSelected)
            {
                dataTypes.Add("Policies");
            }

            if (searchRequest.IsPragrammesAndActionsDataTypeSelected)
            {
                dataTypes.Add("Programmes and actions");
            }

            if (searchRequest.IsMechanicalDataTypeSelected)
            {
                dataTypes.Add("Mechanisms");
            }

            if (searchRequest.IsCommitmentsDataTypeSelected)
            {
                dataTypes.Add("Commitments");
            }

            return dataTypes;
        }

        private void SetDataType()
        {
            searchRequest.IsPragrammesAndActionsDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Programmes and actions").IsChecked;
            searchRequest.IsPolicyDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Policies").IsChecked;
            searchRequest.IsMechanicalDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "Mechanisms").IsChecked;
            searchRequest.IsCommitmentsDataTypeSelected = PolicyLookups.FirstOrDefault(e => e.Name == "SMART commitments").IsChecked;
        }

        private async Task ToggleDataTypeSelection(string name, bool isChecked)
        {
            //DataGridforSearchchild.ToggleSearchFiltersLoader(true);

            var listofSelectedData = PolicyLookups.FirstOrDefault(e => e.Name == name).IsChecked = isChecked;
            if (name == "Policies")
            {
                searchRequest.IsPolicyDataTypeSelected = isChecked;
                if (!isChecked)
                {
                    RemovedRegionDetail();
                }
            }

            if (name == "Mechanisms")
            {
                searchRequest.IsMechanicalDataTypeSelected = isChecked;

            }
            if (name == "Programmes and actions")
            {
                //DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
                searchRequest.IsPragrammesAndActionsDataTypeSelected = isChecked;
            }
            if (name == "SMART commitments")
            {
                searchRequest.IsCommitmentsDataTypeSelected = isChecked;
            }
            PolicyLookups.Where(w => w.Name == name).FirstOrDefault().IsChecked = isChecked;
            //CheckSearchButtonAvailability();
            ToggleSearchFiltersLoader(Visibility.Visible);
            _ = OnSearchClicked();

            await InvokeAsync(StateHasChanged);
        }

        public void ChangeStartYear(DateTime? dateTime)
        {
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            searchRequest.StartDate = dateTime;
            OnSearchClicked();
        }

        public void ChangeEndYear(DateTime? dateTime)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            // _ = DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            searchRequest.EndDate = dateTime;
            _ = OnSearchClicked();
        }

        private void OnRegionChanged(CountryWithRegion value)
        {
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            if (value == null)
            {
                searchRequest.SelectedRegionCode = String.Empty;
            }
            else
            {
                searchRequest.SelectedRegionCode = value.RegionCode;
            }
            ResetSelectedCountries();
            FilterCountries();
            OnSearchClicked();
        }
        private void OnSelectedCountries(IEnumerable<CountryWithRegion> values)
        {
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            searchRequest.SelectedCountries = values?.Select(v => v.CountryName).ToList() ?? new List<string>();
            OnSearchClicked();
        }


        private void OnIncomeGroupChanged(string value)
        {
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            searchRequest.SelectedIncomeCode = value;
            ResetSelectedCountries();
            FilterCountries();
            OnSearchClicked();
        }

        private void ResetSelectedCountries()
        {
            searchRequest.SelectedCountries = Enumerable.Empty<string>();
        }

        private void FilterCountries()
        {
            var listofSelectedData = PolicyLookups.Where(e => e.IsChecked == true).Select(e => e.Name.First());

            if (!string.IsNullOrWhiteSpace(searchRequest.SelectedIncomeCode) 
                    && !string.IsNullOrWhiteSpace(searchRequest.SelectedRegionCode))
            {
                AllIncomGroups = allCountryDetail.Where(e => !string.IsNullOrWhiteSpace(e.IncomeGroupCode) && !string.IsNullOrWhiteSpace(e.RegionCode))
                                                 .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                                                 .OrderBy(e => e.IncomeOrder)
                                                 .Select(e => e.IncomeGroupCode)
                                                 .Distinct()
                                                 .ToList();

                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrWhiteSpace(e.IncomeGroupCode) && !string.IsNullOrWhiteSpace(e.RegionCode))
                                                    .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode) && e.IncomeGroupCode.Equals(searchRequest.SelectedIncomeCode))
                                                    .DistinctBy(e => e.CountryCode)
                                                    .DistinctBy(e => e.CountryName)
                                                    .OrderBy(e => e.CountryName)                                                    
                                                    .ToList();

            }
            else if (!string.IsNullOrWhiteSpace(searchRequest.SelectedRegionCode))
            {
                AllIncomGroups = allCountryDetail.Where(e => !string.IsNullOrWhiteSpace(e.RegionCode) && !string.IsNullOrWhiteSpace(e.IncomeGroupCode))
                                                 .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                                                 .OrderBy(e => e.IncomeOrder)
                                                 .Select(e => e.IncomeGroupCode)
                                                 .Distinct()
                                                 .ToList();

                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrWhiteSpace(e.RegionCode))
                                                    .Where(e => e.RegionCode.Equals(searchRequest.SelectedRegionCode))
                                                    .DistinctBy(e => e.CountryCode)
                                                    .DistinctBy(e => e.CountryName)
                                                    .OrderBy(e => e.CountryName)                                                    
                                                    .ToList();
            }
            else if (!string.IsNullOrWhiteSpace(searchRequest.SelectedIncomeCode))
            {
                fullCountryDetail = allCountryDetail.Where(e => !string.IsNullOrWhiteSpace(e.IncomeGroupCode))
                                                    .Where(e => e.IncomeGroupCode.Equals(searchRequest.SelectedIncomeCode))
                                                    .DistinctBy(e => e.CountryCode)
                                                    .DistinctBy(e => e.CountryName)
                                                    .OrderBy(e => e.CountryName)                                                    
                                                    .ToList();
            }
            else
            {
                fullCountryDetail = fullCountryDetail.DistinctBy(e => e.CountryName).ToList();
            }
            StateHasChanged();
        }

        private async Task GetTopicsAsync()
        {
            var task1 = PolicyService.GetTopicsForBasicSearchAsync();
            var task2 = PolicyService.GetParentForBasicSearchTopics();
            AllTopics = await task1;
            AllParentTopics = await task2;
            await GetTopicInSearchTreeView();
            RecursivelyBasicTopicChilds();
            await InvokeAsync(StateHasChanged);
            ShowTopic = PolicyTopicTreeNodes.Any() && PolicyLookups.Any(w => w.IsChecked && (w.Name == "Policies" || w.Name == "Mechanisms"));
        }
        public void RecursivelyBasicTopicChilds()
        {
            try
            {
                var firstParent = AllParentTopics.Where(w => w.ParentId == SearchTopicConfigurationKey.SearchTopicId);
                foreach (var item in BasicTopicParentChilds)
                {
                    if (!AllBasicTopicParentChilds.ContainsKey(item.Key))
                    {
                        AllBasicTopicParentChilds.Add(item.Key, item.Value);
                    }
                    foreach (var childId in item.Value.ToList())
                    {
                        AddTopicChildRecusively(item.Key, childId, BasicTopicParentChilds, AllBasicTopicParentChilds);
                    }
                }
            }
            catch (Exception e)
            {

            }
        }

        public void AddTopicChildRecusively(int paretnId, int childId, Dictionary<int, List<int>> directParentChild, Dictionary<int, List<int>> parentChildAllLevel)
        {
            if (directParentChild.ContainsKey(childId))
            {
                parentChildAllLevel[paretnId].AddRange(directParentChild[childId]);
                foreach (var item in directParentChild[childId])
                {
                    AddTopicChildRecusively(paretnId, item, directParentChild, parentChildAllLevel);
                }
            }
        }
        private string ParentName { get; set; }
        private async Task<(List<GTreeNode>, int?, int?)> GetChildTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First, string entityType)
        {
            //for child
            int? totalRecord = 0;
            int? totalCountry = 0;
            List<GTreeNode> child = new();
            foreach (var item in First)
            {
                DbModels.Topic total = new();
                total = AllTopics.FirstOrDefault(t => t.Id == item.TopicId);
                int? itemTotalCountry = 0;
                var itemTotalRecord = 0;

                if (ParentName == "Policy")
                {
                    itemTotalCountry = total.PolicyTopics.Select(p => p.Policy).Select(c => c.PolicyCountryMap).FirstOrDefault()?.ToList()?.Count;

                    itemTotalRecord = total.PolicyTopics.Count;
                }

                if (ParentName == "Mechanism")
                {
                    itemTotalCountry = total.MechanismTopics.Select(p => p.Mechanism).Select(c => c.MechanismCountryMap).FirstOrDefault()?.ToList()?.Count;
                    itemTotalRecord = total.MechanismTopics.Count;
                }
                itemTotalCountry = itemTotalCountry != null ? itemTotalCountry : 0;

                total ??= new DbModels.Topic();
                if (itemTotalRecord > 0)
                {
                    totalRecord += itemTotalRecord;
                }

                if (itemTotalCountry != null)
                {
                    totalCountry += itemTotalCountry;
                }
                var parentTopic = new List<DbModels.TopicParent>();

                parentTopic = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList();

                if (parentTopic.Any())
                {
                    if (entityType == Gina2.Core.Constants.Policy)
                    {

                        if (PolicyTopicParentChilds.ContainsKey(item.TopicId))
                        {
                            PolicyTopicParentChilds[item.TopicId].AddRange(parentTopic.Select(s => s.TopicId));
                        }
                        else
                        {
                            PolicyTopicParentChilds.Add(item.TopicId, parentTopic.Select(s => s.TopicId).ToList());
                        }
                    }
                }
                else
                if (entityType == Gina2.Core.Constants.Mechanism)
                {
                    if (MechanismTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        MechanismTopicParentChilds[item.TopicId].AddRange(parentTopic.Select(s => s.TopicId));
                    }
                    else
                    {
                        MechanismTopicParentChilds.Add(item.TopicId, parentTopic.Select(s => s.TopicId).ToList());
                    }
                }
                else
                if (entityType == Gina2.Core.Constants.Action)
                {
                    if (ActionTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        ActionTopicParentChilds[item.TopicId].AddRange(parentTopic.Select(s => s.TopicId));
                    }
                    else
                    {
                        ActionTopicParentChilds.Add(item.TopicId, parentTopic.Select(s => s.TopicId).ToList());
                    }
                }

                var topic = await GetChildTopicTreeView(parentTopic, entityType);
                totalRecord += topic.Item2;
                totalCountry += topic.Item3;
                GTreeNode itemchild = new();
                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    //Title = $"{item.Topic.Name} ({itemTotalRecord + topic.Item2}) {itemTotalCountry + topic.Item3}",
                    Title = item.Topic.Name,
                    Children = topic.Item1,
                    IsSelected = false
                });
            }
            return (child, totalRecord, totalCountry);
        }

        private async Task OnMapExportClicked()
        {
            SetDataType();
            searchRequest.DefaultPage = "map";
            await ProtectedSessionStore.SetAsync("config", Serialize());
            //NavigationManager.NavigateTo("map/redirect_filter_data", true);
            searchRequest.DefaultPage = "searchpage";

            await JSRuntime.InvokeVoidAsync("open", $"map/redirect_filter_data", "_blank");
        }
        private string Serialize()
        {
            string encodedStr = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(searchRequest, Formatting.Indented, new SpecialDateTimeConverter())));
            return encodedStr;
        }

        private async Task GetTopicInSearchTreeView()
        {
            TopicsInSearch.Clear();
            var firstParent = AllParentTopics.Where(w => w.ParentId == SearchTopicConfigurationKey.SearchTopicId).OrderBy(t => t.OrderKey);
            var actualTopicsInSearch = new List<GTreeNode>();

            foreach (var item in firstParent)
            {
                var parentTopics = new List<DbModels.TopicParent>();
                parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList();

                if (parentTopics.Any())
                {
                    if (BasicTopicParentChilds.ContainsKey(item.TopicId))
                    {
                        BasicTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId).ToList());
                    }
                    else
                    {
                        BasicTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                    }
                }

                if (AllTopics.Where(w => w.IsSelected).Select(w => w.Id).Contains(item.TopicId))
                {

                    TopicsInSearch.Add(new GTreeNode()
                    {
                        TopicId = item.TopicId,
                        ParentId = item.ParentId,
                        Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        Key = $"{0}-{item.TopicId}",
                        Children = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId),
                        IsSelected = false,

                    });

                }
                else
                {
                    var childList = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), item.TopicId);
                }
            }
        }

        private async Task<List<GTreeNode>> GetChildTopicInSearch(IEnumerable<Gina2.DbModels.TopicParent> First, int grandParentId)
        {
            List<GTreeNode> child = new List<GTreeNode>();
            foreach (var item in First)
            {
                var parentTopics = new List<DbModels.TopicParent>();
                parentTopics = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList();
                //if (parentTopics.Any())
                //{
                if (BasicTopicParentChilds.ContainsKey(item.TopicId))
                {
                    BasicTopicParentChilds[item.TopicId].AddRange(parentTopics.Select(s => s.TopicId).ToList());
                }
                else
                {
                    BasicTopicParentChilds.Add(item.TopicId, parentTopics.Select(s => s.TopicId).ToList());
                }
                //}
                if (AllTopics.Where(w => w.IsSelected).Select(w => w.Id).Contains(item.TopicId))
                {
                    GTreeNode itemchild = new GTreeNode();
                    child.Add(new GTreeNode()
                    {
                        TopicId = item.TopicId,
                        ParentId = item.ParentId,
                        Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        Key = $"{grandParentId}-{item.TopicId}",
                        Children = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), grandParentId),
                        IsSelected = false
                    });
                }
                else
                {
                    var childList = await GetChildTopicInSearch(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList(), grandParentId);
                }

            }
            return child;
        }

        private async Task SearchTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            _ = ToggleSearchFiltersLoader(Visibility.Visible);
            // DataGridforSearchchild.ToggleVisibility(Visibility.Visible);
            SelectedSearchTopicList.Clear();
            SelectedSearchTopicListUIOnly.Clear();
            SelectedSearchTopicName = string.Empty;
            var listofTopicId = checkedValue.Tree.CheckedKeys.Select(e => e.Split('-').ElementAt(1)).ToList();
            var listOfAllKeyNames = AllTopics.Where(s => listofTopicId.Contains(Convert.ToString(s.Id))).Select(w => w.Name).Distinct();
            SelectedSearchTopicName = string.Join(",", listOfAllKeyNames);
            foreach (var item in checkedValue.Tree.CheckedKeys)
            {
                var key = item.Split('-');
                int ParentId = int.Parse(key.ElementAt(0));
                int topicId = int.Parse(key.ElementAt(1));

                if (item.StartsWith(0 + "-"))
                {
                    var alltopic = AllParentTopics.Where(x => x.ParentId == Convert.ToInt32(topicId)).OrderBy(t => t.OrderKey).Select(e => e.TopicId).ToList();
                    if (alltopic == null || !alltopic.Any())
                    {
                        GetTopicListForParentTopic(topicId, new List<int>() { topicId });

                    }
                    GetTopicListForParentTopic(topicId, alltopic);
                }
                else
                {
                    SelectedSearchTopicListUIOnly.Add(item);

                    var firstParent = AllParentTopics.Where(w => w.ParentId == SearchTopicConfigurationKey.SearchTopicId).OrderBy(t => t.OrderKey).ToList();
                    var parent = AllParentTopics.Where(x => x.ParentId == Convert.ToInt32(topicId)).OrderBy(t => t.OrderKey).ToList();

                    if (parent.Any())
                    {
                        var ParentAllTopicIds = parent.Select(e => e.TopicId).ToList();
                        // await GetChildSelectedTopicInSearch(parent);
                        GetTopicListForParentTopic(ParentId, ParentAllTopicIds);
                        // not requried to get from db
                        //foreach (var item in collection)
                        //{
                        //    PapulateSelectedTopicList(item.ParentId, item.TopicId.ToString());
                        //}
                    }
                    else
                    {
                        PapulateSelectedTopicList(ParentId, topicId.ToString());
                    }
                }
            }
            _ = OnSearchClicked();
        }

        private void GetTopicListForParentTopic(int parentId, List<int> ParentAllTopicIds)
        {
            foreach (var item in ParentAllTopicIds)
            {
                // get topic id if topic has some parent,
                var GetAllTopicsforParentTopic2 = AllParentTopics.Where(e => item == e.ParentId).Select(e => e.TopicId).Distinct().ToList();
                if (!GetAllTopicsforParentTopic2.Any())
                {
                    PapulateSelectedTopicList(parentId, item.ToString());
                }
                else
                {
                    GetTopicListForParentTopic(parentId, GetAllTopicsforParentTopic2);
                }
            }
        }

        private void PapulateSelectedTopicList(int parentId, string item)
        {
            if (!SelectedSearchTopicList.ContainsKey(parentId))
            {
                SelectedSearchTopicList.Add(parentId, new List<int>());
                SelectedSearchTopicList[parentId].Add(int.Parse(item));
            }
            if (SelectedSearchTopicList.ContainsKey(parentId) && !SelectedSearchTopicList[parentId].Any(e => e == int.Parse(item)))
            {
                SelectedSearchTopicList[parentId].Add(int.Parse(item));
            }
        }

        private async Task GetChildSelectedTopicInSearch(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                var childs = AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList();
                if (childs.Any())
                {
                    await GetChildSelectedTopicInSearch(childs);
                }
                //else
                //{
                //    PapulateSelectedTopicList(item.ParentId,item.TopicId.ToString());
                //}
            }
        }

        public bool VerifyToken(string _Token)
        {
            using HttpClient httpClient = new HttpClient();
            using var res = httpClient.GetAsync($"https://www.google.com/recaptcha/api/siteverify?secret={Configuration["ReCaptcha:SecretKey"]}&response={_Token}").Result;

            if (res.StatusCode != HttpStatusCode.OK)
            {
                return false;
            }
            string JSONres = res.Content.ReadAsStringAsync().Result;
            dynamic JSONdata = JObject.Parse(JSONres);

            if (JSONdata.success != "true" || JSONdata.score <= 0.5m)
            {
                //OpenErrorToaster($"score {JSONdata.score} is too low ");
                return false;
            }

            return true;

        }

        private string GetIncomeGroup(string incomeGroupCode)
        {
            string name = string.Empty;
            switch (incomeGroupCode)
            {
                case "LIC":
                    name = "LIC - Low-income countries";
                    break;
                case "LMC":
                    name = "LMC - Lower-middle income countries";
                    break;
                case "UMC":
                    name = "UMC - Upper-middle income countries";
                    break;
                case "HIC":
                    name = "HIC - High-income countries";
                    break;
                default:
                    break;
            }
            return name;
        }

        private string GetRegionName(string regionCode)
        {
            string name = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    name = "AFR - African Region";
                    break;
                case "AMR":
                    name = "AMR - Region of the Americas";
                    break;
                case "EMR":
                    name = "EMR - Eastern Mediterranean Region";
                    break;
                case "EUR":
                    name = "EUR - European Region";
                    break;
                case "SEAR":
                    name = "SEAR - South-East Asia Region";
                    break;
                case "WPR":
                    name = "WPR - Western Pacific Region";
                    break;
                default:
                    break;
            }
            return name;
        }
        #endregion Methods - Helpers

        #endregion Methods
    }
}