﻿@page "/advanced-search"

@using Domain.PolicyTypes
@using Domain.Search
@using Domain.Terms
@using Gina2.Blazor.Helpers;
@using Gina2.Core.Constant
@using Json.Net;
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using Gina2.Blazor.Helpers.PageConfigrationData
@using System.Text.RegularExpressions;
@inherits PageConfirgurationComponent

@*   <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem href="admin/contents/search-edit">Edit</DropdownItem>
                <DropdownItem href="#">Translate</DropdownItem>
            </DropdownMenu>
        </Dropdown>*@

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SearchHeading))
                        <AdminEditbut Key="@SearchPageConfigurationKey.SearchHeading" />
                    </Heading>
                    <CardText Class="color-w">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.SubHeader))
                        <AdminEditbut Key="@SearchPageConfigurationKey.SubHeader" />
                    </CardText>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container>
    <Layout Sider Class="search-box aboutus pt-4 pb-7 mob-layout">
        <Layout Class="left-layout pr-3" Style="position:relative;">
            <LayoutContent>
                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex">
                    <Div Class="item1">
                        <Heading Size="HeadingSize.Is3">
                            <span Class="_SearchResults" >Search result - </span>
                            @if (string.IsNullOrWhiteSpace(TotalRecordMessage))
                            {
                                <div class="loader"></div>
                            }
                            else
                            {
                                
                                    <span>@TotalRecordMessage</span>
                                
                            }
                        </Heading>
                    </Div>
                    <Div Class="item2 _but_hig">
                        @* <Button Class="but-yellow mr-1 pl-1 pr-1 mob-t-hide wid-mnin" Clicked="@OnToggleSelectAll">
                        @(searchRequest.IsAllSelected ? "Clear" : "Select All")
                        </Button>*@
                        <Button Class="but-by-yellow text-by-yellow pl-1 pr-1 mob-t-hide" Clicked="@OnMapExportClicked"><Icon Class="fa-solid fa-map" /> Display result in map</Button>
                    </Div>
                </Div>
                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex">
                    @if (SearchHistories.Any())
                    {
                        <Breadcrumb Class="_Search bread-crumb">
                            Search history :-&nbsp;
                            @{
                                charCount = 0;
                            }
                            <Repeater Items="@SearchHistories.OrderBy(w=>w.Order)" Context="historyContext">
                                @{
                                    charCount = charCount + @historyContext.Name.Length;
                                    string history = Regex.Replace(historyContext.Name, "<.*?>", String.Empty);
                                }
                                @if (charCount < 200)
                                {
                                    <BreadcrumbItem>
                                        <BreadcrumbLink onclick="@(() => LoadSearchHistory(historyContext.Order))">@history</BreadcrumbLink>
                                    </BreadcrumbItem>
                                }
                            </Repeater>
                            @if (SearchHistories.Select(w => w.Name.Length).Sum() > 200)
                            {
                                <button class="_more" @onclick="@Toggle">...</button>
                            }
                            <Div hidden="@(!HideMore)" Class="hidemore">
                                <button class="_more _lessmore" @onclick="@Toggle">show less</button>
                                <Breadcrumb Class="_Search bread-crumb">
                                    <Repeater Items="@SearchHistories" Context="historyContext">
                                        @{
                                            string history = Regex.Replace(historyContext.Name, "<.*?>", String.Empty);

                                        }
                                        <BreadcrumbItem>
                                            <BreadcrumbLink onclick="@(() => LoadSearchHistory(historyContext.Order))">@history</BreadcrumbLink>
                                        </BreadcrumbItem>
                                    </Repeater>
                                </Breadcrumb>
                                <button class="_more _lessmore" @onclick="@Toggle">show less</button>
                            </Div>
                        </Breadcrumb>
                    }
                </Div>

                <DataGridforSearch @ref="DataGridforSearchchild"
                                   FileType="SearchPage"
                                   SendDataForFileDownloadEvent="@GetDataFromDataGrid"
                                   SendCountInforomSpToParentEvent="@GetDataCount">
                    @* SendDataFromSpToParentEvent="@GetDataFromSpFromChild"*@
                    @*  <DataGridforSearch @ref="DataGridforSearchchild"
                    FileType="SearchPage"
                    SendDataToParentEvent="@GetDataFromChild"
                    SendCountInfoToParentEvent="@GetDataCount"
                    SendDataForFileDownloadEvent="@GetDataFromDataGrid"
                    SendDataFromSpToParentEvent="@GetDataFromSpFromChild">*@
                </DataGridforSearch>
                <FileDownload FileType="SearchPage" @ref="FileDownloadChild" SearchHistories="@SearchHistories" ShowCSVPanel="@ShowCSVPanel">
                </FileDownload>
                <Typemap />
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>
                <Div Class="HedFilters _adfilter" id="GeneralFilter">
                    <AdminEditbut Key="@SearchPageConfigurationKey.GeneralFilters" />
                    <Heading Flex="Flex.JustifyContent.Between" onclick="@(()=>ToggleGeneralFilterMode())">
                        <span class="_goto">
                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.GeneralFilters))
                        </span>
                        @if (!isGeneralFilter)
                        {
                            <Icon Class="fas fa-arrow-right" />
                        }
                    </Heading>
                </Div>
                
                @if (!isGeneralFilter)
                {
                    <Div Class="HedFilters _adfilter mt-1" id="AdvancedFilter">
                        <AdminEditbut Key="@SearchPageConfigurationKey.AdvancedFilters" />
                        <Heading Flex="Flex.JustifyContent.Between">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.AdvancedFilters))
                            </span>
                        </Heading>
                    </Div>
                }
                <Loading_progress loaderVisibility="@searchLoaderVisibility" />
                <Div Class="filters-Search" id="accordion2Example">
                    <Div class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.DataType))
                                <AdminEditbut Key="@SearchPageConfigurationKey.DataType" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"><Icon Clicked="@OnClearDataTypesClicked" Name="IconName.Redo" Class="_editicon" id="GeneralDataReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsethree" aria-expanded="true" aria-controls="collapsethree"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapsethree" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion2Example">
                            <Div Class="accordion-body">
                                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                    <span>Data (items)</span>
                                    <span>Countries</span>
                                </Div>
                                <AntDesign.Menu Class="_mapforoverlay _datatypes">
                                    @foreach (var item in PolicyLookups)
                                    {
                                        <Check Class="check-light-blue" TValue="bool" Checked="item.IsChecked"
                                               CheckedChanged="(e) => ToggleDataTypeSelection(item.Name,e)">
                                            <Span Flex="Flex.JustifyContent.Start">
                                                <Span Class="_nowrap">@item.Name &nbsp;</Span>
                                                <Span Flex="Flex.JustifyContent.Between" Class="w-100"><Span>(@GetDataTypeCount(item.Symbol))</Span><Span>@GetDataTypeCountryCount(item.Symbol)</Span></Span>
                                            </Span>
                                        </Check>
                                    }
                                </AntDesign.Menu>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                @*general filter *@
                <Div Class="filters-Search" id="accordionExample">
                    <Div class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Regions))
                                <AdminEditbut Key="@SearchPageConfigurationKey.Regions" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearRegionsClicked" Name="IconName.Redo" Class="_editicon" id="GeneralRegionsReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                            <Div Class="accordion-body">

                                <AntDesign.Select TItemValue="string"
                                                  DataSource="@AllRegions"
                                                  Placeholder="Select a WHO region"
                                                  TItem="CountryWithRegion"
                                                  ValueName="RegionCode"
                                                  LabelName="RegionCode"
                                                  OnSelectedItemChanged="@OnRegionChanged"
                                                  @bind-Value="searchRequest.SelectedRegionCode"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 13px; margin-top:8px;">
                                    <ItemTemplate Context="regionContext">
                                        <span>
                                            @GetRegionName(regionContext.RegionCode) @(!string.IsNullOrWhiteSpace(regionContext.RegionDescription) ? "- " + regionContext.RegionDescription : "")
                                        </span>
                                    </ItemTemplate>
                                </AntDesign.Select>

                                <AntDesign.Select TItem="string"
                                                  Placeholder="Select an income group"
                                                  TItemValue="string"
                                                  DataSource="@AllIncomGroups"
                                                  @bind-Value="@searchRequest.SelectedIncomeCode"
                                                  OnSelectedItemChanged="OnIncomeGroupChanged"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 8px;">
                                    <ItemTemplate Context="inconeGroupContext">
                                        <span>
                                            @GetIncomeGroup(inconeGroupContext)
                                        </span>
                                    </ItemTemplate>
                                </AntDesign.Select>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                @* @if (CountryVisible)
                {*@
                <Div Class="filters-Search" id="accordion4Example">
                    <Div class="accordion">

                        <Div class="_alltopic-header">
                            <span>@((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Countries)) <AdminEditbut Key="@SearchPageConfigurationKey.Countries" /></span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearCountryClicked" Name="IconName.Redo" Class="_editicon" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsefive" aria-expanded="true" aria-controls="collapsefive"> </button></span>
                            </span>
                        </Div>

                        <Div id="collapsefive" class="accordion-collapse collapse show" aria-labelledby="headingfive" data-bs-parent="#accordion4Example">
                            <Div Class="accordion-body">
                                <AntDesign.Select DataSource="@fullCountryDetail"
                                                  Mode="multiple"
                                                  TItemValue="string"
                                                  TItem="CountryWithRegion"
                                                  LabelName="@nameof(CountryWithRegion.CountryWithDescription)"
                                                  ValueName="@nameof(CountryWithRegion.CountryName)"
                                                  OnSelectedItemsChanged="OnSelectedCountries"
                                                  Placeholder="Select one or more countries"
                                                  AllowClear
                                                  Values="searchRequest.SelectedCountries"
                                                  EnableSearch>
                                </AntDesign.Select>

                            </Div>
                        </Div>
                    </Div>
                </Div>
                @*}*@
                <Div Class="filters-Search" id="accordion1Example" disabled="@IsTopicDDDisabled">
                    <Div Class="accordion">
                        <Div class="_alltopic-header">
                            <span>
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.PublishedYear))
                                <AdminEditbut Key="@SearchPageConfigurationKey.PublishedYear" />
                            </span>
                            <span class="flex-center">
                                <Tooltip Text="Reset"> <Icon Clicked="@OnClearPublishedYearClicked" Name="IconName.Redo" Class="_editicon" id="GeneralPublichedReset" /></Tooltip>
                                <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo"> </button></span>
                            </span>
                        </Div>
                        <Div id="collapsetwo" Class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion1Example">
                            <Div class="accordion-body" disabled="true">
                                <Div Flex="Flex.JustifyContent.Between">
                                    <AntDesign.DatePicker Class="w-48" TValue="DateTime?" Picker="year" Placeholder="@Startplace" Value="@searchRequest.StartDate" ValueChanged="e=>ChangeStartYear(e)" DisabledDate="@(date => date >= searchRequest.EndDate)" />
                                    <AntDesign.DatePicker Class="w-48" TValue="DateTime?" Picker="year" Placeholder="@Endplace" Value="@searchRequest.EndDate" ValueChanged="e=>ChangeEndYear(e)" DisabledDate="@(date => date <= searchRequest.StartDate)" />
                                </Div>

                            </Div>
                        </Div>
                    </Div>
                </Div>

                @*end ther*@

                @if (!isGeneralFilter)
                {
                    <Div Class="filters-Search" id="accordion4Example" disabled="@IsTopicDDDisabled">
                        <Div Class="accordion">
                            <Div class="_alltopic-header">
                                <span>
                                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Keywords))
                                    <AdminEditbut Key="@SearchPageConfigurationKey.Keywords" />
                                </span>
                                <span class="flex-center">
                                    <Tooltip Text="Reset"><Icon Clicked="@OnClearSearchTextClicked" Name="IconName.Redo" Class="_editicon" id="AdvancedKeywordsReset" /></Tooltip>
                                    <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapseKeyword" aria-expanded="true" aria-controls="collapseKeyword"> </button></span>
                                </span>
                            </Div>
                            <Div id="collapseKeyword" Class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion4Example">
                                <Div class="accordion-body" disabled="true">
                                    <Div Flex="Flex.JustifyContent.Between">
                                        <Blazorise.TextEdit Placeholder="Search by Keywords..." @bind-Text="@searchRequest.SearchText" @onkeydown="@OnSearchTextEnter" />
                                    </Div>
                                </Div>
                            </Div>
                        </Div>
                    </Div>

                }
                @if (!isGeneralFilter)
                {
                    @* <Div Class="filters-Search" id="accordion7Example">
                        <Div Class="accordion">
                            <Div class="_alltopic-header">
                                <span>
                                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Country))
                                    <AdminEditbut Key="@SearchPageConfigurationKey.Country" />
                                </span>
                                <span class="flex-center">

                                    <Tooltip Text="Reset">
                                        <Icon Clicked="@OnClearCountryGroupRegion" Name="IconName.Redo" Class="_editicon" id="AdvancedCountryReset" />
                                    </Tooltip>
                                    <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#accordion7" aria-expanded="true" aria-controls="accordion7"> </button></span>
                                </span>
                            </Div>

                            <Div id="accordion7" Class="accordion-collapse collapse show" aria-labelledby="accordion7Example" data-bs-parent="#accordion7Example">
                                <Div class="accordion-body _Regions-pad" disabled="true">
                                    <Div Class="filters-Search" id="Regions1list">
                                        <AntDesign.Tree ShowIcon
                                                        @ref="refCoutryGroupTree"
                                                        MatchedClass="site-tree-search-value"
                                                        DataSource="CountryGroupsTree"
                                                        KeyExpression="x => x.DataItem.Iso3Code"
                                                        TItem="TermTreeNode"
                                                        OnCheck="x => CountryGroupCheckboxClicked(x)"
                                                        Checkable
                                                        TitleExpression="x =>
                                                                {
                                                                return x.DataItem.Name;
                                                                }"
                                                        ChildrenExpression="x => x.DataItem.Children">
                                            <TitleTemplate Context="countryGroupContext">
                                                <Div Class="_gsearchitem">
                                                    <Span>
                                                        @($"{countryGroupContext.DataItem.Name} ({(GetCountryGroupCountryCount(countryGroupContext.DataItem.Iso3Code, countryGroupContext.ParentNode))})")
                                                    </Span>
                                                </Div>
                                            </TitleTemplate>
                                        </AntDesign.Tree>
                                    </Div> *@

                                    @*<Div Class="filters-Search" id="Regions3list" disabled="@IsTopicDDDisabled">
                                <Div Class="accordion">*@
                                    @* <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                <Span Class="_all_Filter-p"></Span><Span>WHO Region </Span>
                                </Span>
                                <Span Flex="Flex.JustifyContent.Between">
                                <Span></Span>
                                <Span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Regions3" aria-expanded="true" aria-controls="Regions3"> </button></Span>
                                </Span>
                                </Div>*@

                                    @*  <Div id="Regions3" Class="accordion-collapse collapse show" aria-labelledby="Regions3" data-bs-parent="#Regions3list">
                                <Div class="accordion-body _Regions-pad" disabled="true">
                                <AntDesign.Tree ShowIcon
                                @ref="refCoutryRegionTree"
                                MatchedClass="site-tree-search-value"
                                DataSource="CountryRegionGroupTree"
                                KeyExpression="x => x.DataItem.Iso3Code"
                                TItem="TermTreeNode"
                                Checkable
                                TitleExpression="x =>
                                {
                                return x.DataItem.Name;
                                }"
                                OnCheck="x => CountryRegionCheckboxClicked(x)"
                                ChildrenExpression="x => x.DataItem.Children">
                                <TitleTemplate Context="countryRegionContext">
                                <Div Class="_gsearchitem">
                                @if(countryRegionContext.ParentNode == null)
                                {

                                <Span>
                                @($"{GetRegionName(countryRegionContext.DataItem.RegionCode)} ({(GetCountryRegionCountryCount(countryRegionContext.DataItem.Iso3Code, countryRegionContext.ParentNode))})")
                                </Span>
                                }
                                else
                                {
                                <Span>
                                @($"{countryRegionContext.DataItem.Name} ({(GetCountryRegionCountryCount(countryRegionContext.DataItem.Iso3Code, countryRegionContext.ParentNode))})")
                                </Span>
                                }
                                </Div>
                                </TitleTemplate>
                                </AntDesign.Tree>
                                </Div>
                                </Div>
                                *@
                                @* </Div>
                            </Div>
                        </Div>
                    </Div> *@
                    @* </Div>
                </Div>*@
<Div Class="filters-Search" id="accordion6Example" disabled="@IsTopicDDDisabled">
                        <Div Class="accordion">
                            <Div class="_alltopic-header">
                                <span>
                                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.NutritionTopics))
                                    <AdminEditbut Key="@SearchPageConfigurationKey.NutritionTopics" />
                                </span>
                                <span class="flex-center">
                                    <Tooltip Text="Reset"><Icon Clicked="@OnClearNutritionTopics" Name="IconName.Redo" Class="_editicon" id="AdvancedNutritionReset" /></Tooltip>
                                    <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTopicSearch" aria-expanded="true" aria-controls="collapsetwo"> </button></span>
                                </span>
                            </Div>

                            <Div id="collapseTopicSearch" Class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion6Example">
                                <Div class="accordion-body" disabled="true">
                                    @if (searchRequest.IsPolicyDataTypeSelected && PolicyTopicTreeNodes.Any())
                                    {
                                        <Div>
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span style="font-weight: 600;">Policy</span>
                                                <Span Style="margin-left: 3px;">
                                                    <Tooltip Text="Format: Nutrition name (data count) country count">
                                                        <Icon Name="IconName.QuestionCircle" />
                                                    </Tooltip>
                                                </Span>
                                            </Div>
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                            </Div>
                                            <AntDesign.Tree ShowIcon
                                                            @ref="policyTopicTree"
                                                            MatchedClass="site-tree-search-value"
                                                            DataSource="PolicyTopicTreeNodes"
                                                            KeyExpression="x => x.DataItem.Key"
                                                            TItem="GTreeNode"
                                                            Checkable
                                                            TitleExpression="x => x.DataItem.Title"
                                                            ChildrenExpression="x => x.DataItem.Children"
                                                            OnCheck="x => PolicyTopicTreeCheckboxClicked(x)">
                                                <TitleTemplate Context="policyTopicContext">
                                                    <Div Class="_gsearchitem">
                                                        <Span>
                                                            @policyTopicContext.DataItem.Title
                                                            @if (!policyTopicContext.DataItem.IsLoading)
                                                            {
                                                                <Span>
                                                                    @($" ({GetPolicyTopicCount(policyTopicContext.DataItem.TopicId, policyTopicContext.ParentNode)})")
                                                                </Span>
                                                            }
                                                        </Span>
                                                        @if (policyTopicContext.DataItem.IsLoading)
                                                        {
                                                            <Span> 
                                                                <AntDesign.Icon Type="loading" Theme="outline" />
                                                            </Span>
                                                        }
                                                        else
                                                        {
                                                            <Span> @GetPolicyTopicCountryCount(policyTopicContext.DataItem.TopicId, policyTopicContext.ParentNode)</Span>
                                                        }
                                                    </Div>
                                                </TitleTemplate>
                                            </AntDesign.Tree>

                                        </Div>
                                    }
                                    @* ActionTopic Tree- On Check Removed,On Expanded removed*@
                                    @if (searchRequest.IsPragrammesAndActionsDataTypeSelected && ActionTopicTreeNodes.Any())
                                    {
                                        <Div Style="margin-top: 15px;">
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span style="font-weight: 600;">Action</span>
                                                <Span Style="margin-left: 3px;">
                                                    <Tooltip Text="Format: Nutrition name (data count) country count">
                                                        <Icon Name="IconName.QuestionCircle" />
                                                    </Tooltip>
                                                </Span>
                                            </Div>
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                            </Div>
                                            <AntDesign.Tree ShowIcon
                                                            @ref="actionTopicTree"
                                                            MatchedClass="site-tree-search-value"
                                                            DataSource="ActionTopicTreeNodes"
                                                            KeyExpression="x => x.DataItem.Key"
                                                            OnCheck="x => ActionTopicTreeCheckboxClicked(x)"
                                                            TItem="GTreeNode"
                                                            TitleExpression="x =>
                                                            {
                                                            return x.DataItem.Title;
                                                            }"
                                                            ChildrenExpression="x => x.DataItem.Children"
                                                            Checkable
                                                            DefaultCheckedKeys="searchRequest.SelectedActionTopcisString">
                                                <TitleTemplate Context="actionTopicContext">
                                                    <Div Class="_gsearchitem">
                                                        <Span>
                                                            @actionTopicContext.DataItem.Title
                                                            <Span>
                                                                @($" ({(GetActionTopicCount(actionTopicContext.DataItem.TopicId, actionTopicContext.ParentNode))})")
                                                            </Span>
                                                        </Span>

                                                        <Span> @GetActionTopicCountryCount(actionTopicContext.DataItem.TopicId, actionTopicContext.ParentNode)</Span>
                                                    </Div>
                                                </TitleTemplate>
                                            </AntDesign.Tree>
                                        </Div>
                                    }
                                    @if (searchRequest.IsMechanicalDataTypeSelected && MechanismTopicTreeNodes.Any())
                                    {
                                        <Div Style="margin-top: 15px;">
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span style="font-weight: 600;">Mechanism</span>
                                                <Span Style="margin-left: 3px;">
                                                    <Tooltip Text="Format: Nutrition name (data count) country count">
                                                        <Icon Name="IconName.QuestionCircle" />
                                                    </Tooltip>
                                                </Span>
                                            </Div>
                                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                            </Div>
                                            <AntDesign.Tree ShowIcon
                                                            @ref="mechanismTopicTree"
                                                            MatchedClass="site-tree-search-value"
                                                            DataSource="MechanismTopicTreeNodes"
                                                            KeyExpression="x => x.DataItem.Key"
                                                            TItem="GTreeNode"
                                                            Checkable
                                                            TitleExpression="x =>
                                                            {
                                                            return x.DataItem.Title;
                                                            }"
                                                            ChildrenExpression="x => x.DataItem.Children"
                                                            OnCheck="x => MechanismTopicTreeCheckboxClicked(x)"
                                                            DefaultCheckedKeys="searchRequest.SelectedMechanismTopcisString">
                                                <TitleTemplate Context="mechanismTopicContext">
                                                    <Div Class="_gsearchitem">
                                                        <Span>
                                                            @mechanismTopicContext.DataItem.Title
                                                            @if (!mechanismTopicContext.DataItem.IsLoading)
                                                            {
                                                                <Span>
                                                                    @($" ({(GetMechanismTopicCount(mechanismTopicContext.DataItem.TopicId, mechanismTopicContext.ParentNode))})")
                                                                </Span>
                                                            }
                                                        </Span>

                                                        @if (mechanismTopicContext.DataItem.IsLoading)
                                                        {
                                                            <Span> <AntDesign.Icon Type="loading" Theme="outline" /></Span>
                                                        }
                                                        else
                                                        {
                                                            <Span> @GetMechanismTopicCountryCount(mechanismTopicContext.DataItem.TopicId, mechanismTopicContext.ParentNode)</Span>
                                                        }
                                                    </Div>
                                                </TitleTemplate>
                                            </AntDesign.Tree>
                                        </Div>
                                    }


                                </Div>
                            </Div>
                        </Div>
                    </Div>
                    <Div Class="filters-Search" id="accordion8Example" disabled="@IsTopicDDDisabled">
                        <Div Class="accordion">
                            <Div class="_alltopic-header">
                                <span>
                                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.Partners))
                                    <AdminEditbut Key="@SearchPageConfigurationKey.Partners" />
                                </span>
                                <span class="flex-center">
                                    <Tooltip Text="Reset">
                                        <Icon Clicked="@OnClearPartner" Name="IconName.Redo" Class="_editicon" id="AdvancedPartnersReset" />
                                    </Tooltip>
                                    <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#accordion8" aria-expanded="true" aria-controls="accordion8"> </button></span>
                                </span>
                            </Div>

                            <Div id="accordion8" Class="accordion-collapse collapse show" aria-labelledby="accordion8Example" data-bs-parent="#accordion8Example">
                                <Div class="accordion-body" disabled="true">
                                    <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                            </Div>
                                    <AntDesign.Tree ShowIcon
                                                    @ref="refPartnerTree"
                                                    MatchedClass="site-tree-search-value"
                                                    DataSource="PartnerTree"
                                                    KeyExpression="x => x.DataItem.Id.ToString()"
                                                    TItem="TermTreeNode"
                                                    Checkable
                                                    OnCheck="x => PartnerTreeCheckboxClicked(x)"
                                                    ChildrenExpression="x => x.DataItem.Children">
                                        <TitleTemplate Context="partnerContext">
                                            <Div Class="_gsearchitem">
                                                <Span>
                                                    @($"{partnerContext.DataItem.Name} ({(GetPartnerCount(partnerContext.DataItem.Id, partnerContext.ParentNode))})")
                                                </Span>

                                                <Span> @GetPartnerCountryCount(partnerContext.DataItem.Id, partnerContext.ParentNode)</Span>
                                                        
                                            </Div>
                                        </TitleTemplate>
                                    </AntDesign.Tree>
                                </Div>
                            </Div>
                        </Div>
                    </Div>

                    if (searchRequest.IsPolicyDataTypeSelected)
                    {
                        <Accordion Class="filters-Search _advancesearch">
                            <Collapse Visible="@CollPolicyData" Clicked="@PolicyTypeClicked">
                            <CollapseHeader Class="accordion">
                                <Div class="_alltopic-header">
                                    <span>
                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.PolicyFilters))
                                            <AdminEditbut Key="@SearchPageConfigurationKey.PolicyFilters" />
                                    </span>
                                    <span class="flex-center">
                                            <Tooltip Text="Reset"><Icon Clicked="@OnClearPolicyType" Name="IconName.Redo" Class="_editicon" id="AdvancedPolicyReset" /></Tooltip>
                                            <span><Button class="viewpublis bg_transparent" Clicked="@(()=>CollPolicyData = !CollPolicyData)"></Button></span>
                                    </span>
                                </Div>
                            </CollapseHeader>
                            <CollapseBody>
                            <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                            </Div>
                                    <AntDesign.Tree ShowIcon
                                                     @ref="refPolicyTypeTree"
                                                     MatchedClass="site-tree-search-value"
                                                     DataSource="PolicyTypes"
                                                     KeyExpression="x => x.DataItem.Id.ToString()"
                                                     TItem="PolicyTypeViewModel"
                                                        Checkable
                                                     OnCheck="x => PolicyTypeTreeCheckboxClicked(x)"
                                                     TitleExpression="x => x.DataItem.Name">
                                         <TitleTemplate Context="policyTypeContext">
                                             <Div Class="_gsearchitem">
                                                 <Span>
                                                     @($"{policyTypeContext.DataItem.Name} ({(GetPolicyTypeCount(policyTypeContext.DataItem.Id))})")
                                                 </Span>
                                                 <Span> @GetPolicyTypeCountryCount(policyTypeContext.DataItem.Id)</Span>
                                                    
                                             </Div>
                                         </TitleTemplate>
                                     </AntDesign.Tree>
                             </CollapseBody>
                         </Collapse>
                         </Accordion>
                    }
                    if (searchRequest.IsPragrammesAndActionsDataTypeSelected)
                    {
                        <Div Class="filters-Search" id="accordion10Example" disabled="@IsTopicDDDisabled">
                            <Div Class="accordion">
                                <Div class="_alltopic-header">
                                    <span>
                                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.ActionFilters))
                                        <AdminEditbut Key="@SearchPageConfigurationKey.ActionFilters" />
                                    </span>
                                    <span class="flex-center">
                                        <Tooltip Text="Reset">
                                            <Icon Clicked="@OnClearActionFilters" Name="IconName.Redo" Class="_editicon" />
                                        </Tooltip>
                                        <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#accordion10" aria-expanded="true" aria-controls="accordion10"> </button></span>
                                    </span>
                                </Div>

                                <Div id="accordion10" Class="accordion-collapse collapse show" aria-labelledby="accordion10Example" data-bs-parent="#accordion10Example">
                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                        <Div Class="filters-Search" id="Action1list" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.ProgramTypes))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.ProgramTypes" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Action1" aria-expanded="true" aria-controls="Action1"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Action1" Class="accordion-collapse collapse show" aria-labelledby="Action1list" data-bs-parent="#Action1list">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refProgramTypeTree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="ProgramTypes"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="ProgramType"
                                                                        Checkable
                                                                        OnCheck="x => ProgramTypeTreeCheckboxClicked(x)">
                                                            <TitleTemplate Context="programTypeContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{programTypeContext.DataItem.Name} ({(GetProgramTypeCount(programTypeContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetProgramTypeCountryCount(programTypeContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>
                                            </Div>
                                        </Div>

                                        <Div Class="filters-Search" id="Action2list" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span>
                                                        <Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.FundingSource))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.FundingSource" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#Action2" aria-expanded="false" aria-controls="Action2"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Action2" Class="accordion-collapse collapse" aria-labelledby="Action2" data-bs-parent="#Action2list">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refFundingSource"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="PartnerCategories"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="PartnerCategory"
                                                                        Checkable
                                                                        OnCheck="x => FundingSourceTreeCheckboxClicked(x)"
                                                                        TitleExpression="x => x.DataItem.Name">
                                                            <TitleTemplate Context="fundingSourceContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{fundingSourceContext.DataItem.Name} ({(GetFundingSourceContextCount(fundingSourceContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetFundingSourceCountryCount(fundingSourceContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>


                                            </Div>
                                        </Div>
                                        <Div Class="filters-Search" id="Action3list" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.TargetGroup))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.TargetGroup" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#Action3" aria-expanded="false" aria-controls="Action3"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Action3" Class="accordion-collapse collapse" aria-labelledby="Action3" data-bs-parent="#Action3list">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refTargetGroupTree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="TargetGroups"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="TargetGroup"
                                                                        Checkable
                                                                        OnCheck="x => TargetGroupTreeCheckboxClicked(x)"
                                                                        TitleExpression="x => x.DataItem.Name">
                                                            <TitleTemplate Context="targetGroupContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{targetGroupContext.DataItem.Name} ({(GetTargetGroupCount(targetGroupContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetTargetGroupCountryCount(targetGroupContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>


                                            </Div>
                                        </Div>
                                        <Div Class="filters-Search" id="Action4list" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.DeliveryChannel))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.DeliveryChannel" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#Action4" aria-expanded="false" aria-controls="Action4"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Action4" Class="accordion-collapse collapse" aria-labelledby="Action4" data-bs-parent="#Action4list">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refDeliveryTree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="Deliveries"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="Delivery"
                                                                        Checkable
                                                                        OnCheck="x => DeliveryChnlTreeCheckboxClicked(x)"
                                                                        TitleExpression="x => x.DataItem.Name">
                                                            <TitleTemplate Context="deliveryChnlContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{deliveryChnlContext.DataItem.Name} ({(GetDeliveryChnlContextCount(deliveryChnlContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetDeliveryChnlCountryCount(deliveryChnlContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>


                                            </Div>
                                        </Div>
                                        <Div Class="filters-Search" id="Action5list" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.ProblemAndSolution))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.ProblemAndSolution" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#Action5" aria-expanded="false" aria-controls="Action5"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Action5" Class="accordion-collapse collapse" aria-labelledby="Action5" data-bs-parent="#Action5list">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refProblemTypeTree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="ProblemTypes"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="ProblemType"
                                                                        Checkable
                                                                        OnCheck="x => ProblemTypeTreeCheckboxClicked(x)"
                                                                        TitleExpression="x => x.DataItem.Name">
                                                            <TitleTemplate Context="problemTypeContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{problemTypeContext.DataItem.Name} ({(GetProblemTypeCount(problemTypeContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetProblemTypeCountryCount(problemTypeContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>


                                            </Div>
                                        </Div>

                                    </Div>
                                </Div>
                            </Div>
                        </Div>
                    }
                    if (searchRequest.IsMechanicalDataTypeSelected)
                    {
                        <Div Class="filters-Search" id="Mechanism1accord" disabled="@IsTopicDDDisabled">
                            <Div Class="accordion">
                                <Div class="_alltopic-header">
                                    <span>
                                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.MechanismFilters))
                                        <AdminEditbut Key="@SearchPageConfigurationKey.MechanismFilters" />

                                    </span>
                                    <span class="flex-center">

                                        <Tooltip Text="Reset"><Icon Clicked="@OnClearMechanismFilter" Name="IconName.Redo" Class="_editicon" /></Tooltip>
                                        <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Mechanism1" aria-expanded="true" aria-controls="Mechanism1"> </button></span>
                                    </span>
                                </Div>

                                <Div id="Mechanism1" Class="accordion-collapse collapse show" aria-labelledby="Mechanism1accord" data-bs-parent="#Mechanism1accord">
                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                        <Div Class="filters-Search" id="Mechanism2accord" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.MechanismTypes))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.MechanismTypes" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span></Span>
                                                        <Span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Mechanism2" aria-expanded="true" aria-controls="Mechanism2"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Mechanism2" Class="accordion-collapse collapse show" aria-labelledby="Mechanism2accord" data-bs-parent="#Mechanism2accord">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refMechanismTypeTree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="MechanismTypes"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="MechanismType"
                                                                        Checkable
                                                                        OnCheck="x => MechanismTypeTreeCheckboxClicked(x)"
                                                                        TitleExpression="x => x.DataItem.Name">
                                                            <TitleTemplate Context="mechanismTypeContext">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{mechanismTypeContext.DataItem.Name} ({(GetMechanismTypeCount(mechanismTypeContext.DataItem.Id))})")
                                                                    </Span>
                                                                    <Span> @GetMechanismTypeCountryCount(mechanismTypeContext.DataItem.Id)</Span>
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>


                                            </Div>
                                        </Div>
                                    </Div>
                                </Div>
                            </Div>
                        </Div>
                    }

                    if (searchRequest.IsCommitmentsDataTypeSelected)
                    {
                        <Div Class="filters-Search" id="Commitments1accord" disabled="@IsTopicDDDisabled">
                            <Div Class="accordion">
                                <Div class="_alltopic-header">
                                    <span>
                                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.CommitmentFilters))
                                        <AdminEditbut Key="@SearchPageConfigurationKey.CommitmentFilters" />
                                    </span>
                                    <span class="flex-center">

                                        <Tooltip Text="Reset"><Icon Clicked="@OnClearCommitmentFilter" Name="IconName.Redo" Class="_editicon" /></Tooltip>
                                        <span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Commitments1" aria-expanded="true" aria-controls="Commitments1"> </button></span>
                                    </span>
                                </Div>

                                <Div id="Commitments1" Class="accordion-collapse collapse show" aria-labelledby="Commitments1accord" data-bs-parent="#Commitments1accord">
                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                        <Div Class="filters-Search" id="Commitments2accord" disabled="@IsTopicDDDisabled">
                                            <Div Class="accordion">
                                                <Div Class="_alltopic-1" Flex="Flex.JustifyContent.Between">
                                                    <Span Flex="Flex.JustifyContent.Start.AlignItems.Center">
                                                        <Span Class="_all_Filter-p"></Span><Span>
                                                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.ICN2Recommend))
                                                            <AdminEditbut Key="@SearchPageConfigurationKey.ICN2Recommend" />
                                                        </Span>
                                                    </Span>
                                                    <Span Flex="Flex.JustifyContent.Between">
                                                        <Span><button class="viewpublis" type="button" data-bs-toggle="collapse" data-bs-target="#Commitments2" aria-expanded="true" aria-controls="Commitments2"> </button></Span>
                                                    </Span>
                                                </Div>
                                                <Div id="Commitments2" Class="accordion-collapse collapse show" aria-labelledby="Commitments2accord" data-bs-parent="#Commitments2accord">
                                                    <Div class="accordion-body _Regions-pad" disabled="true">
                                                        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                            <span>Data (items)</span>
                                                            <span>Countries </span>
                                                        </Div>
                                                        <AntDesign.Tree ShowIcon
                                                                        @ref="refICN2Tree"
                                                                        MatchedClass="site-tree-search-value"
                                                                        DataSource="ICN2Tree"
                                                                        KeyExpression="x => x.DataItem.Id.ToString()"
                                                                        TItem="TermTreeNode"
                                                                        Checkable
                                                                        OnCheck="x => ICN2TreeCheckboxClicked(x)"
                                                                        ChildrenExpression="x => x.DataItem.Children">
                                                            <TitleTemplate Context="icn2Context">
                                                                <Div Class="_gsearchitem">
                                                                    <Span>
                                                                        @($"{icn2Context.DataItem.Name} ({(GetICN2Count(icn2Context.DataItem.Id, icn2Context.ParentNode))})")
                                                                    </Span>

                                                                    <Span> @GetICN2CountryCount(icn2Context.DataItem.Id, icn2Context.ParentNode)</Span>
                                                                            
                                                                </Div>
                                                            </TitleTemplate>
                                                        </AntDesign.Tree>
                                                    </Div>
                                                </Div>
                                            </Div>
                                        </Div>
                                    </Div>
                                </Div>
                            </Div>
                        </Div>
                    }
                    
                    <Accordion Class="filters-Search _advancesearch">
                        <Collapse Visible="@CollLangFilters">
                            <CollapseHeader Class="accordion">
                                <Div class="_alltopic-header">
                                    <span>
                                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(SearchPageConfigurationKey.LanguageFilters))
                                        <AdminEditbut Key="@SearchPageConfigurationKey.LanguageFilters" />
                                    </span>
                                    <span class="flex-center">
                                        <Tooltip Text="Reset"><Icon Clicked="@OnClearLanguageFilters" Name="IconName.Redo" Class="_editicon" id="AdvancedLanguageReset" /></Tooltip>
                                        <span><Button class="viewpublis bg_transparent" Clicked="@(()=>CollLangFilters = !CollLangFilters)"></Button></span>
                                    </span>
                                </Div>
                            </CollapseHeader>
                            <CollapseBody>
                                <Div Flex="Flex.JustifyContent.Between" Class="poli-flex pl-0 pr-0 _filterdata">
                                                <span>Data (items)</span>
                                                <span>Countries </span>
                                </Div>
                                <AntDesign.Tree ShowIcon
                                                 @ref="refLanguageTree"
                                                 MatchedClass="site-tree-search-value"
                                                 DataSource="Languages"
                                                 KeyExpression="x => x.DataItem.Id.ToString()"
                                                 TItem="Language"
                                                    Checkable
                                                 OnCheck="x => LanguageCheckboxClicked(x)"
                                                 TitleExpression="x => x.DataItem.Name">
                                     <TitleTemplate Context="languageContext">
                                         <Div Class="_gsearchitem">
                                             <Span>
                                                 @($"{languageContext.DataItem.Name} ({(GetLanguageCount(languageContext.DataItem.Id))})")
                                             </Span>
                                             <Span> @GetLanguageCountryCount(languageContext.DataItem.Id)</Span>
                                         </Div>
                                     </TitleTemplate>
                                 </AntDesign.Tree>
                            </CollapseBody>
                        </Collapse>
                    </Accordion>


                }

                
            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Container>