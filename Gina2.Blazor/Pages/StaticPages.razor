﻿@page "/admin/static"
@using ChartJs.Blazor.Common
@using ChartJs.Blazor.PieChart
@using ChartJs.Blazor.Util
@using AntDesign.Select
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Core.Enums
@using Gina2.DbModels
@using Gina2.DbModels.HomeDrafts
@using Gina2.Core.Methods;
<PageTitle>GIFNA Static Pages</PageTitle>
@inherits PageConfirgurationComponent
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">
        @{
            RenderFragment footer = @<AntDesign.Template>
            </AntDesign.Template>;
        }
        <Modal @bind-Visible="@SliderModal" Class="modals-lg Staticdraggable" ShowBackdrop=false>
            <ModalContent Centered Class="forms">
                <ModalHeader Class="ant-header">
                    <ModalTitle>
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(StaticPagePopupConfigurationKey.Heading))
                        <AdminEditbut Key="@StaticPagePopupConfigurationKey.Heading" />
                    </ModalTitle>
                    <CloseButton />
                </ModalHeader>
                <Div Class="modal-body _modalscroll">
                    <ModalBody>
                        <Validations ValidateOnLoad="false">
                            <Field>
                                <FieldLabel>Title <Span>*</Span></FieldLabel>
                                <Fields>
                                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                        <_quillEditor @ref="@quillEditorTitle" value="@currentEditingPage.Title"></_quillEditor>
                                    </Field>
                                    @if (TitleError)
                                    {
                                        <Span Class="text-danger">Title can not be null or empty</Span>
                                    }
                                </Fields>
                            </Field>

                            <Field>
                                <FieldLabel>Description</FieldLabel>
                                <Fields>
                                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                        <_quillEditor @ref="@quillEditorDescription" value="@currentEditingPage.Description"></_quillEditor>
                                    </Field>

                                </Fields>
                            </Field>
                            <Field>
                                <FieldLabel>Body content <Span>*</Span></FieldLabel>
                                <Fields>
                                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                        <_quillEditor @ref="@quillEditorContent" value="@currentEditingPage.Content"></_quillEditor>
                                    </Field>
                                    @if (ContentError)
                                    {
                                        <Span Class="text-danger">Content can not be null or empty</Span>
                                    }
                                </Fields>
                            </Field>

                            <Field>
                                <FieldLabel>Redirection URL<Span>*</Span></FieldLabel>
                                <TextEdit @bind-Text="@currentEditingPage.URL" Placeholder="Please provide the redirection link" />
                                @if (!string.IsNullOrEmpty(currentEditingPage.URL))
                                {
                                    <FieldHelp>URL will look like</FieldHelp>
                                    <FieldHelp>@($"{Configuration["App:SelfUrl"]}pages/{currentEditingPage.URL}")</FieldHelp>
                                }
                                @if (URLError)
                                {
                                    <Span Class="text-danger">URL can not be null or empty</Span>
                                }
                            </Field>
                        </Validations>
                    </ModalBody>
                </Div>
                <ModalFooter>
                    <Button Class="but-yellow" @onclick="async (e)=>await CreateOrUpdateDraftPage()">Save as draft</Button>
                    <Button Class="but-by-yellow" @onclick="async (e)=>await CreateOrUpdatePage()">Save and publish</Button>

                </ModalFooter>

            </ModalContent>
        </Modal>
     
        <Div Class="_live-preview">
            <AntDesign.Modal Title="@title"
                             Visible="@_visible"
                             DestroyOnClose=true
                             Closable=true
                             OnCancel="@HandleCancel" Footer="@footer" Width="1200">
                @if (_visible)
                {

                    <StaticPageView dynamicUrl="@previewDraftId" staticPageType="@StaticTableType" />
                }
            </AntDesign.Modal>
        </Div>
        <Loader IsLoading="@IsLoading" />
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
                <Container Class="pt-5 pb-5">
                    <Heading Class="h-title">Static pages</Heading>
                </Container>
            </Card>
        </Container>
        <Container Fluid Class="bg-trdot pt-3">
            <Container Class="Dashboard">
                <Row Class="mt-4 mb-4">
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_mydrafts _HomeCurrent">
                        <Tabs SelectedTab="@selectedTab" SelectedTabChanged="@OnSelectedTabChanged">
                            <Items>
                                <Div>
                                    <Tab Name="Drafts">Drafts</Tab>
                                    <Tab Name="Published">Published contents</Tab>
                                </Div>
                                <Div Class="adddraft d-flex">
                                    <TextEdit @bind-Text="@searchQuery" Placeholder="Enter title here..." style="padding: 19px;"></TextEdit>
                                    <Button Clicked="async (e)=>await Search()" Class="but-yellow pl-1 pr-1 ml-1 mr-1"><Icon class="fas fa-search pr-0" /></Button>
                                    <Button Clicked="async (e)=>await ShowModal(0)" Class="btn but-yellow pl-1 pr-1 d-flex justify-content-center align-items-center"><Icon Class="far fa-plus" /> Add</Button>
                                </Div>
                            </Items>
                            <Content>
                                <TabPanel Name="Drafts">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth _actions"
                                                      TItem="@StaticPageDraft"
                                                      Data="@draftPages"
                                                      PageSize="5"
                                                      TotalItems="@SearchModel.TotalFilteredCount"
                                                      CurrentPage="SearchModel.PageNo"
                                                            ShowPageSizes
                                                            ShowPager
                                                            Responsive
                                                      SortMode="DataGridSortMode.Single">
                                                <EmptyTemplate>
                                                    <Div>No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Caption="Title" Sortable="true" Field="@nameof(StaticPageDraft.Title)" Width="40%">
                                                        <DisplayTemplate>
                                                            @((MarkupString)context.Title)
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                    <DataGridColumn Field="@nameof(StaticPage.CreatedBy)" Caption="Created by" Width="22%" />

                                                    <DataGridColumn Field="@nameof(StaticPage.DateCreated)" Caption="Created date" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            @DateTimeOffsetHelper.GetDateTimeFormat(currentRow.DateCreated)
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button Clicked="@((e)=>ShowDraftModal(currentRow.Id))">
                                                                    <Tooltip Text="Edit">
                                                                        <Icon Name="IconName.Pen" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                            <Button>
                                                                <Tooltip Text="Preview">
                                                                    <Icon Clicked="@(e=>ShowPreview(currentRow.URL,"draft"))" Name="IconName.Eye">
                                                                    </Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button>
                                                                    <Tooltip Text="Delete">
                                                                        <Icon Clicked="(e)=>OpenDeleteDraftStaticPage(currentRow.Id)" Name="IconName.Delete" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                                <TotalItemsTemplate Context="row">
                                                    <Badge TextColor="TextColor.Dark">
                                                        @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                                                    </Badge>
                                                </TotalItemsTemplate>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                                <TabPanel Name="Published">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth"
                                                      TItem="@StaticPage"
                                                      Data="@publishedPages"
                                                      PageSize="5"
                                                      TotalItems="@SearchModel.TotalFilteredCount"
                                                      CurrentPage="SearchModel.PageNo"
                                                            ShowPageSizes
                                                            ShowPager
                                                            Responsive
                                                      SortMode="DataGridSortMode.Single">
                                                <EmptyTemplate>
                                                    <Div>No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Caption="Title" Sortable="true" Field="@nameof(StaticPage.Title)" Width="32%">
                                                        <DisplayTemplate>
                                                            @((MarkupString)context.Title)
                                                        </DisplayTemplate>
                                                    </DataGridColumn>

                                                     <DataGridColumn Field="@nameof(StaticPage.URL)" Caption="URL" Width="25%">
                                                        <DisplayTemplate Context="currentRow">
                                                             <a href=@($"/pages/{currentRow.URL}") target="_blank">@currentRow.URL</a>
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                    <DataGridColumn Field="@nameof(StaticPage.CreatedBy)" Caption="Created by" Width="12%" />

                                                    <DataGridColumn Field="@nameof(StaticPage.DateCreated)" Caption="Created date" Width="12%">
                                                        <DisplayTemplate Context="currentRow">
                                                            @DateTimeOffsetHelper.GetDateTimeFormat(currentRow.DateCreated)
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="18%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Button Clicked="async (e)=>await ShowModal(currentRow.Id)">
                                                                <Tooltip Text="Edit">
                                                                    <Icon Name="IconName.Pen" />
                                                                </Tooltip>
                                                            </Button>
                                                            <Button>
                                                                <Tooltip Text="Preview">
                                                                    <Icon Clicked="@((e)=>ShowPreview(currentRow.URL,"published"))" Name="IconName.Eye"></Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (!string.IsNullOrEmpty(currentRow.URL))
                                                            {
                                                                <Button>
                                                                    <Tooltip Text="Copy URL">
                                                                        <i class="fas far fa-copy" onclick="@(()=>CopyTextToClipboard(currentRow.URL))"></i>
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button>
                                                                    <Tooltip Text="Delete">
                                                                        <Icon Clicked="(e)=>OpenDeletePublishedStaticPage(currentRow.Id)" Name="IconName.Delete" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                                <TotalItemsTemplate Context="row">
                                                    <Badge TextColor="TextColor.Dark">
                                                        @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                                                    </Badge>
                                                </TotalItemsTemplate>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                            </Content>
                        </Tabs>
                    </Column>

                </Row>
            </Container>
        </Container>

        <Modal @bind-Visible="@deleteDraftModalVisible" Class="modals-lg _modalcenter">
            <ModalContent Centered Class="forms">
                <ModalHeader>
                    <ModalTitle>Are you sure want to Delete this draft static page?</ModalTitle>
                </ModalHeader>
                <ModalFooter>
                    <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteDraftPage(draftToBeDeleted);} )">Delete</Button>
                    <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteDraftModalVisible = false)">Cancel</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

        <Modal @bind-Visible="@deleteStaticPageModalVisible" Class="modals-lg _modalcenter">
            <ModalContent Centered Class="forms">
                <ModalHeader>
                    <ModalTitle>Are you sure want to Delete this published static page?</ModalTitle>
                </ModalHeader>
                <ModalFooter>
                    <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeletePage(publishedToBeDeleted);} )">Delete</Button>
                    <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteStaticPageModalVisible = false)">Cancel</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

    </Authorized>
</AuthorizeView>