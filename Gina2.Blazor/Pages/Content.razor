﻿@page "/content/{SearchText}"
@using Gina2.SPs;


<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/emailcomposes.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3">
                        Content
                        <AdminEditbut Key="@BulkImportPageConfigurationKey.Title" />
                    </Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="HomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Content</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="pt-7 m-pt-2 pl-2 pr-2 adminuser">

    @if (SubscriberContentResult == null || SubscriberContentResult.Count == 0)
    {
        <Div Flex="Flex.JustifyContent.Center">No results found.</Div>
    }
    else
    {
        <Div Class="DataGrids">
            <DataGrid PageSizes="new[] { 50,100,250,500,1000 }"
            @ref="@SearchDataGrid"
                      Class="_checkgrid"
                      FixedHeaderDataGridMaxHeight="500px"
                      FixedHeaderDataGridHeight="450px"
                      TItem="@CombinedSearchResult"
                      Data="@SubscriberContentResult"
                    ShowPageSizes
                    ShowPager
                    Responsive
                    PageSize="50"
                    SortMode="DataGridSortMode.Single">
                @*SelectionMode="DataGridSelectionMode.Multiple"*@
                <EmptyTemplate>
                    <Div>No data found for the search criteria.</Div>
                </EmptyTemplate>

                <DataGridColumns>
                    <DataGridColumn Field="@nameof(CombinedSearchResult.RegionCode)" Caption="WHO region" Sortable="true" Width="16%" />
                    <DataGridColumn Field="@nameof(CombinedSearchResult.CountryList)" Caption="Country" Sortable="true" Width="15%" />
                    <DataGridColumn Caption="Title" Sortable="true" Field="@nameof(CombinedSearchResult.CombinedTitle)" Width="40%">
                        <DisplayTemplate>
                            <NavLink href="@context?.DetailsUrl">@context?.CombinedTitle</NavLink>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn Field="@nameof(CombinedSearchResult.Type)" Caption="Type" Width="12%" Sortable="true" />
                    <DataGridColumn Field="@nameof(CombinedSearchResult.StartYearString)" Caption="Start year" Sortable="true" Width="14%" />
                </DataGridColumns>
                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                <TotalItemsTemplate>
                    <Badge TextColor="TextColor.Dark">
                        @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                    </Badge>
                </TotalItemsTemplate>
            </DataGrid>
        </Div>
    }
</Container>
