﻿@page "/countries/{CountryCode}/policies"
<Loader IsLoading="@IsLoading" />
<CascadingValue Value="@Title" Name="Title">
    <CascadingValue Value="@TabTitle" Name="TabTitle">
        <CascadingValue Value="@TypeofTitle" Name="TypeofTitle">
            <CascadingValue Value="@TableTitle" Name="TableTitle">
                <CascadingValue Value="@AdminTitle" Name="AdminTitle">
                    <CascadingValue Value="@DetailUrlContext" Name="DetailUrlContext">
                        <CascadingValue Value="@policies" Name="Data">
                            <CascadingValue Value="@policyTypes" Name="Categories">
                                <CascadingValue Value="@CountryCode" Name="CountryCode">
                                    <CascadingValue Value="@CsvKeys" Name="CsvKeys">
                                        <CascadingValue Value="@CsvData" Name="CsvData">
                                            <CascadingValue Value="@FileName" Name="FileName">
                                                <CountryDetails SelectedTab="policies">
                                                    <PublishedList SelectedListTab="flattenItemTab" />
                                                </CountryDetails>
                                            </CascadingValue>
                                        </CascadingValue>
                                    </CascadingValue>
                                </CascadingValue>
                            </CascadingValue>
                        </CascadingValue>
                    </CascadingValue>
                </CascadingValue>
            </CascadingValue>
        </CascadingValue>
    </CascadingValue>
</CascadingValue>