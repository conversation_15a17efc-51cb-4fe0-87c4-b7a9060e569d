using Blazorise;
using Gina2.Blazor.Shared;
using Gina2.Core.Enums;
using Gina2.Core.Methods;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Text.RegularExpressions;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class CreateScorecard
    {
        [Parameter]
        public int Id { get; set; }

        [Inject]
        public IConfiguration Configuration { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }

        [Inject]
        INotificationService NotificationService { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        Validations ValidationCheck;
        DbModels.Scorecard Scorecard { get; set; } = new ();
        public List<DbModels.Scorecard> ScorecardList { get; set; } = new ();
        private bool isLoading { get; set; } = false;
        private string description { get; set; } = string.Empty;
        private string bottomDiscrition { get; set; } = string.Empty;
        private string populationInfo { get; set; } = string.Empty;
        private _quillEditor quillEditorDescriptionRef;
        private _quillEditor quillEditorBottomDescriptionRef;
        protected override async Task OnInitializedAsync()
        {
            base.OnInitializedAsync();

            if (Id != 0)
            {
                try
                {
                    Scorecard = await ScorecardService.GetAsync(Id);
                    Scorecard.CountryUrl = $"summary/{Scorecard.URL}/data";
                    if (Scorecard == null)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }

                    description = Scorecard.Description;
                    bottomDiscrition = Scorecard.BottomDescription;
                    populationInfo = Scorecard.PopulationCountInfo;
                }
                catch (Exception ex)
                {

                    throw ex;
                }
                
               
                
            }
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JSRuntime.InvokeVoidAsync("resetRecaptcha");
                ScorecardList = await ScorecardService.GetAllAsync();
                if (Id != 0)
                {
                    ScorecardList.Remove(ScorecardList.FirstOrDefault(s => s.Id == Id));
                }
                    isLoading = false;
                    await InvokeAsync(StateHasChanged);
                
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        private void ChangeDescription(string value)
        {
            Scorecard.Description = value;
        }

        private void ChangeBottomDescription(string value)
        {
            Scorecard.BottomDescription = value;
        }

        private void ChangePopulationInfo(string value)
        {
            Scorecard.PopulationCountInfo = value;
        }

        private async Task SaveAsync()
        {
            if (ScorecardList.Any(s => s.Title.Equals(Scorecard.Title)) && Id == 0)
            {
                await OpenErrorToaster("This Scorecard name already exist");
                return;
            }
            if (await ValidationCheck.ValidateAll())
            {
                isLoading = true;
                await InvokeAsync(StateHasChanged);

                if (!string.IsNullOrEmpty(Scorecard.URL) && RegexHelper.IsRegexMatch(Scorecard.URL, @"^[a-zA-Z0-9 ]*$") && !Scorecard.URL.Any(w => Char.IsWhiteSpace(w)) &&  !ScorecardHelper.ReservedURL.Contains(Scorecard.URL.ToLower())
                    && !string.IsNullOrEmpty(Scorecard.CountryUrl) && !ScorecardHelper.ReservedURL.Contains(Scorecard.CountryUrl.ToLower()))
                {
                    Scorecard.Description = await quillEditorDescriptionRef.GetHTML();
                    Scorecard.BottomDescription = await quillEditorBottomDescriptionRef.GetHTML();
                    
                        await ScorecardService.SaveAsync(Scorecard);
                        //await NotificationService.Success("Saved successfully.");
                        var message = Id == 0 ? "Saved" : "Updated";
                        await OpenSuccessToaster($"{message} successfully");
                        NavigationManager.NavigateTo("admin/scorecards");
                    
                }
                else
                {
                    await NotificationService.Error("URL or country url can not have special characters or spaces.");
                }

                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }

        }

        void UrlChange(string value)
        {
            Scorecard.URL = value;
            Scorecard.CountryUrl = $"summary/{value}/data";
        }

        private void OnTitleChanged(string value)
        {
            Scorecard.Title = value;
        }

        private void ValidateText(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }

    }
}
