﻿@page "/about-us/edit"
<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem href="about-us/edit">Edit</DropdownItem>
                <DropdownItem href="about-us/moderate">Moderate</DropdownItem>
                <DropdownItem href="about-us/translate">Translate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3">Edit - About Us</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="about-us/edit">Edit - About Us</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>               
            </Div>
        </Container>
    </Card>
</Container>

<Container Fluid Class="newdraft" Padding="Padding.Is0">
    <Container Padding="Padding.Is0" Class="pt-6 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3">Update the information for About Us</Heading>
        <Divider Class="divi-blue" />
    </Container>
     <Container Class="form-newd">
     <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Title<Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Enter policy title here..."></TextEdit>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Language</FieldLabel>
                <Select TValue="int">
                    <Repeater Items="@LanguageData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            </Fields>
            <Fields>
            <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Body</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                </Div>
            </Div>
            <RextEditors />
        </Field>
        </Fields>
     </Container>

     <Container Class="form-newd mt-4">
         <Heading Class="blo-head" Size="HeadingSize.Is3">Content Blocks</Heading>
         <Divider Class="diviv-blocks"/>
     <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Content block left</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                </Div>
            </Div>
            <RextEditors />
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Content block left</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                </Div>
            </Div>
            <RextEditors />
            </Field>
            </Fields>
     </Container>

     <Container Class="form-newd mt-4">
         <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1 pb-0">
                   <Heading Class="blo-head" Size="HeadingSize.Is3">Sidebar blocks</Heading>
                </Div>
                <Div Class="item2 pb-0">
                    <Button Class="but-box-blues"><Icon Name="IconName.PlusSquare" /> New Block</Button>
                    </Div>
          </Div>
         <Divider Class="diviv-blocks"/>
         <Field>
                <FieldLabel>Block title</FieldLabel>
                <TextEdit Placeholder="WHO resources"></TextEdit>
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Block content</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                </Div>
            </Div>
            <RextEditors />
            </Field>
     </Container>

      <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>File upload</FieldLabel>
            <FileEdit Changed="@OnChanged" Placeholder="Select or drag and drop multiple files" Multiple />
           <FieldHelp>Files must be less than 25 MB   |    Allowed file types: .txt .csv</FieldHelp>
        </Field>
        <Field>
            <FieldLabel>Translation settings</FieldLabel>
        <Check TValue="bool">If you made a significant change, which means translations should be updated, you can flag all translations of this post as outdated. This will not change any other property of those posts, like whether they are published or not.</Check>
         </Field>
     </Container>

     <Container Class="mt-4 pb-6">
        <Button Class="but-blues" Clicked="@(()=>snackbar.Show())">Save</Button>
        <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
            <SnackbarBody>
                New content: Your draft will be placed in moderation.
            </SnackbarBody>
        </Snackbar>
    </Container>
</Container>
@code {

}
