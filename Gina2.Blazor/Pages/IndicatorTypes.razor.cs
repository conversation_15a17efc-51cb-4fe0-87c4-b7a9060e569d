﻿using AutoMapper;
using DocumentFormat.OpenXml.Wordprocessing;
using Domain.Vocabulary;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.DbModels;
using Gina2.Services.Language;
using Gina2.Services.MapIndicatorConfigurations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class IndicatorTypes : PageConfirgurationComponent
    {
        [Inject]
        private IIndicatorTypeService IndicatorTypeService { get; set; }

        [Inject]
        private IMapper _mapper { get; set; }
        
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        private IEnumerable<IndicatorTypeModel> IndicatorType { get; set; } = Enumerable.Empty<IndicatorTypeModel>();
        //private IndicatorType AddorUpdateIndicatorType { get; set; } = new IndicatorType();
        private IndicatorTypeModel AddorUpdateIndicatorType { get; set; } = new IndicatorTypeModel();
        private bool IsSavingloading { get; set; } = false;
        private bool ThemesVisible;
        bool deleteModalVisible = false;
        int indicatorToBeDeleted=0;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                await GetMapIndicatorConfiguration();
                await Task.Run(() => IsLoading = false);
                StateHasChanged();
            }
        }
        private async Task GetMapIndicatorConfiguration()
        {
            var indicator = await IndicatorTypeService.GetIndicatorTypesAsync();
            IndicatorType = _mapper.Map<List<IndicatorTypeModel>>(indicator);
        }
        public async Task DeleteById(int indicatorId)
        {
            await Task.Run(() => IsLoading = true);
            var isDeleted = await IndicatorTypeService.DeleteMapIndicatorAsync(indicatorId);
            deleteModalVisible = false;
            if (isDeleted)
            {
                _ = OpenSuccessToaster("Map indicator data deleted successfully");
                await GetMapIndicatorConfiguration();
            }
            else
            {
                _ = OpenErrorToaster("Map indicator data not deleted successfully");
            }
            await Task.Run(() => IsLoading = false);
        }
        private void ShowAddForm()
        {
            ThemesVisible = true;
            StateHasChanged();
        }
        private void HideModal()
        {
            ThemesVisible = false;
            AddorUpdateIndicatorType = new();
            StateHasChanged();
        }
        private async Task UpdateById(IndicatorTypeModel indicatorType)
        {

            await Task.Run(() => IsLoading = true);
            ShowAddForm();
            AddorUpdateIndicatorType = _mapper.Map<IndicatorTypeModel>(indicatorType);

            //AddorUpdateIndicatorType = new DbModels.IndicatorType()
            //{
            //    Id = indicatorType.Id,
            //    IndicatorName = indicatorType.IndicatorName,
            //    IndicatorCode = indicatorType.IndicatorCode,
            //    FilterCriteria = indicatorType.FilterCriteria,
            //};
            await Task.Run(() => IsLoading = false);
            StateHasChanged();
        }
        private async Task BtnSaveClicked()
        {
            IsSavingloading = true;
            await AddorUpdateRecord();
            AddorUpdateIndicatorType = new();
            IsSavingloading = false;
        }
        private async Task AddorUpdateRecord()
        {
           // var isSameEntryExists = await MapIndicatorConfigurationService.IsSameEntryExists(MapIndicatorConfiguration);
            //if (isSameEntryExists)
            //{
            //    _ = OpenInfoToaster("Same entry already exists please try with diffrent values");
            //    return;
            //}
            var indicatorAddOrUpdate = _mapper.Map<IndicatorType>(AddorUpdateIndicatorType);
            var isupdated = await IndicatorTypeService.SaveMapIndicatorAsync(indicatorAddOrUpdate);
            HideModal();
            if (isupdated)
            {
                _ = OpenSuccessToaster("Map indicator data saved successfully");
                await GetMapIndicatorConfiguration();
            }
            else
            {
                _ = OpenErrorToaster("Map indicator data save failed");
            }
            
        }
        private void ShowDeleteModal(int indicatorId)
        {
            deleteModalVisible = true;
            indicatorToBeDeleted = indicatorId;
        }
    }
}
