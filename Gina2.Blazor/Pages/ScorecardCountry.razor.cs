﻿using DocumentFormat.OpenXml.Spreadsheet;
using Domain.CsvDetails;
using Gina2.Blazor.Models.Scorecard;
using Gina2.Core.Methods;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ClosedXML.Excel;
using Gina2.Blazor.Helpers.PageConfigrationData;
using AutoMapper;
using Domain.ScoreCard;
using Blazorise.DataGrid;
using System.Dynamic;
using Gina2.DbModels;
using Newtonsoft.Json;
using Hangfire.Storage;
using Newtonsoft.Json.Linq;
using Blazorise;
using System.Data;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using System.Drawing.Printing;
using Microsoft.Graph.IdentityGovernance.AccessReviews.Definitions.FilterByCurrentUserWithOn;

namespace Gina2.Blazor.Pages
{
    public partial class ScorecardCountry : PageConfirgurationComponent
    {
        [Parameter]
        public string ScorecardCountryURL { get; set; }

        [Inject]
        public IMapper _mapper { get; set; }
        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        private IScorecardTopicService scorecardTopicService { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }
        [Inject]
        private IJSRuntime _JSRuntime { get; set; }
        public Domain.ScoreCard.Scorecard Scorecard { get; set; }
        private int ScorecardID { get; set; }
        public List<ScorecardScore> Scores { get; set; }

        public List<Domain.ScoreCard.ScorecardCountry> scorecardCountries = new();

        public List<string> Header { get; set; } = new List<string>();

        private List<Dictionary<string, object>> expandoObjects = new List<Dictionary<string, object>>();
        private List<Dictionary<string, object>> FullList = new List<Dictionary<string, object>>();
        private List<string> columnNames = new List<string>();
        private bool isLoading = false;
        private bool isSortingOrPageChanging = false;
        string baseURL = string.Empty;
        private string countryClass = string.Empty;
        private string CountryStyle = string.Empty;
        private ScoreCountryTable scoreCountryTable = new ScoreCountryTable();
        private int Direction { get; set; } = 1;
        private string SelectedColumn { get; set; }
        private bool ExportCsvLoading { get; set; }


        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                isLoading = true;
                baseURL = NavigationManager.BaseUri;
                await InvokeAsync(StateHasChanged);
                var score = await ScorecardService.GetScorecardByURL(ScorecardCountryURL);
                if (score == null)
                {
                    NavigationManager.NavigateTo("not-found", true, true);
                    return;
                }
                ScorecardID = score.Id;


                scoreCountryTable = await ScorecardService.GetScoreCountryToTableContent(ScorecardID);
                if (scoreCountryTable != null)
                {
                    columnNames = JsonConvert.DeserializeObject<List<string>>(scoreCountryTable.Header);
                    CountryStyle = $"width:{columnNames.Count * 250}px";
                    countryClass = columnNames.Count > 6 ? "DataGrids scorecard-scroller" : "DataGrids";
                    //SelectedColumn = columnNames.FirstOrDefault();
                    //expandoObjects = JsonConvert.DeserializeObject<List<ScoreCardDynamic>>(scoreCountryTable.Content);
                    expandoObjects = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(scoreCountryTable.Content).ToList();
                    EndPage = (int)Math.Ceiling((double)expandoObjects.Count / PageItems);
                    paginationData();

                }
                (Scorecard, scorecardCountries) = await ScorecardService.GetScoreCountryAsync(ScorecardID);
                isLoading = false;
                _ = base.OnAfterRenderAsync(firstRender);
                await InvokeAsync(StateHasChanged);
            }
            if (isSortingOrPageChanging)
            {
                StopLoading();
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        async Task StopLoading()
        {
            isLoading = false;
            isSortingOrPageChanging = false;
            await InvokeAsync(StateHasChanged);
        }

        public async Task ExportCSV()
        {
            ExportCsvLoading = true;
            var scorecardExcelDetails = await scorecardTopicService.GetScorecardExcelDeatil(ScorecardID);
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("gifna_country_scorecard");
                var _scoreCountry = JsonConvert.DeserializeObject<DataTable>(scoreCountryTable.Content);

                List<string> header = columnNames != null ? columnNames : new List<string>();
                int index = 0;

                foreach (var column in header)
                {
                    index++;
                    worksheet.Row(1).Row(index, index).Value = header[index - 1];
                }
                worksheet.Cell(2, 1).InsertData(_scoreCountry.AsEnumerable());
                var workbookBytes = new byte[0];
                using (var ms = new MemoryStream())
                {
                    workbook.SaveAs(ms);
                    workbookBytes = ms.ToArray();
                    ExportCsvLoading = false;
                    string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
                    await _JSRuntime.InvokeVoidAsync("ExcelDownload", $"GIFNA-{Scorecard.Title}-country-detail-{currentDate}.xlsx", Convert.ToBase64String(workbookBytes, 0, workbookBytes.Length));
                }
            }


        }
        void paginationData()
        {
            if (!string.IsNullOrEmpty(SelectedColumn))
            { 
            expandoObjects = expandoObjects.OrderBy(d => d[SelectedColumn].ToString()).ToList();
            }
            FullList = expandoObjects.Skip((Convert.ToInt16(currentPage) - 1) * PageItems)
           .Take(PageItems)
           .ToList();
            TotalPageCount = (int)Math.Ceiling(expandoObjects.Count / (double)PageItems);
            
        }
        void SortingScoreCountryTable(string columnName)
        {
            SelectedColumn = columnName;
            if (Direction == 0)
            {
                expandoObjects = expandoObjects.OrderBy(d => d[SelectedColumn].ToString()).ToList();
                Direction = 1;
            }
            else
            {
                Direction = 0;
                expandoObjects = expandoObjects.OrderByDescending(d => d[SelectedColumn].ToString()).ToList();
            }
            FullList = expandoObjects.Skip((Convert.ToInt16(currentPage) - 1) * PageItems)
           .Take(PageItems).ToList();
            StateHasChanged();
        }

        async Task PageChangeScoreCountryTable(DataGridPageChangedEventArgs score)
        {
            isSortingOrPageChanging = true;
            isLoading = true;

        }

        private async Task PageSizeChangeScoreCountryTable(int pageNo)
        {
            isSortingOrPageChanging = true;
            isLoading = true;
        }


        



    private const string PREVIOUS = "previous";
        private const string NEXT = "next";
        private string currentPage { get; set; } = "1";
        private int PageItems { get; set; } = 50;
        private int EndPage { get; set; }
        private int TotalPageCount { get; set; }
        private string NextButtonDisabled { get; set; } = "";
        private string PreviousButtonDisable { get; set; } = "disabled";

        Task OnSelectedValueChanged(int value)
        {
            PageItems = value;
            currentPage = "1";
            paginationData();
            StateHasChanged();
            return Task.CompletedTask;
        }

        List<int> GetPageNumbers()
        {
            int totalItems = expandoObjects.Count; // Total number of items
            int itemsPerPage = PageItems; // Items per page
            int CurrentPage = Convert.ToInt16(currentPage); // The current page

            int totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);
            int pageRange = 5; // Desired range of displayed pages

            int startPage = CurrentPage - (pageRange - 1) / 2;
            int endPage = startPage + pageRange - 1;

            if (startPage < 1)
            {
                startPage = 1;
                endPage = Math.Min(totalPages, pageRange);
            }
            else if (endPage > totalPages)
            {
                endPage = totalPages;
                startPage = Math.Max(1, totalPages - pageRange + 1);
            }

            // Ensure the current page is within the adjusted range
            if (CurrentPage < startPage)
            {
                currentPage = startPage.ToString();
            }
            else if (CurrentPage > endPage)
            {
                currentPage = endPage.ToString();
            }

            return Enumerable.Range(startPage, endPage - startPage + 1).TakeLast(5).ToList();
        }

        private bool IsActive(string page)
        {
            return currentPage == page;
        }

        private bool IsPageNavigationDisabled(string navigation)
        {
            IsPreviousNextDisabled();
            if (navigation.Equals(PREVIOUS))
            {
                return currentPage.Equals("1");
            }
            else if (navigation.Equals(NEXT))
            {
                return currentPage.Equals(EndPage.ToString());
            }
            StateHasChanged();

            return false;
        }

        private void Previous()
        {
            var currentPageAsInt = int.Parse(currentPage);
            if (currentPageAsInt > 1)
            {
                currentPage = (currentPageAsInt - 1).ToString();
                paginationData();
            }
            IsPreviousNextDisabled();
            StateHasChanged();

        }

        private void Next()
        {
            var currentPageAsInt = int.Parse(currentPage);
            if (currentPageAsInt < EndPage)
            {
                currentPage = (currentPageAsInt + 1).ToString();
                paginationData();
            }
            IsPreviousNextDisabled();
            StateHasChanged();

        }
        private void IsPreviousNextDisabled()
        {
            PreviousButtonDisable = (currentPage == "1") ? "disabled" : "";
            NextButtonDisabled = (currentPage == TotalPageCount.ToString()) ? "disabled" : "";
        }

        private void SetActive(string page)
        {
            currentPage = page;
            paginationData();
            IsPreviousNextDisabled();
            StateHasChanged();
        }
    }
}