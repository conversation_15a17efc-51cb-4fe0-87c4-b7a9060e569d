﻿using AntDesign;
using Domain.Search;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core.Constant;
using Gina2.DbModels;
using Gina2.Services.Language;
using Gina2.Services.MapIndicatorConfigurations;
using Gina2.Services.Models;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Net;
using System.Text;
using Gina2.SqlRepository.Models;
using Gina2.Services.Search;
using Gina2.Services.Policy;
using Blazorise;
using System.Reflection;
using Org.BouncyCastle.Asn1.Pkcs;

namespace Gina2.Blazor.Pages
{
    public partial class Map : PageConfirgurationComponent
    {
        string Currentdate = DateTime.Now.ToString("d-M-yyyy");
        [Inject]
        private ISearchService SearchService { get; set; }
        [Inject]
        private IPolicyService PolicyService { get; set; }
        [Inject]
        private ILogger<Map> logger { get; set; }
        [Parameter]
        public string SearchText { get; set; }
        [Inject]
        private IIndicatorTypeService IndicatorTypeService { get; set; }
        [Inject]
        private IMapIndicatorConfigurationService MapIndicatorConfigurationService { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private ITopicService TopicService { get; set; }
        public List<PolicyLookup> PolicyLookups { get; set; } = new List<PolicyLookup>();
        public List<IndicatorType> SelectDeselectLookups { get; set; } = new List<IndicatorType>();
        private IEnumerable<MapIndicatorConfiguration> MapIndicatorConfigurations { get; set; } = Enumerable.Empty<MapIndicatorConfiguration>();
        public List<MapIndicatorDetail> MapIndicatorDetails { get; set; } = new();
        [Inject]
        private IMapIndictorService MapIndictorService { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        public string SelectedTheme { get; set; }
        private const string MapDivId = "viewDiv";
        private bool SearchBtnDisable { get; set; } = false;
        public List<MapLegend> Legends = new();
        private IEnumerable<Domain.Topics.Topic> Topics = Enumerable.Empty<Domain.Topics.Topic>();
        private string SelectDeselectIndicator { get; set; }
        public string TotalRecordMessage { get; set; }
        private GlobalSearchRequest searchRequest = new() { DefaultPage = "map" };
        public bool showCSVPanel = true;
        private bool ExportCsvLoading { get; set; }
        private List<MapCountrywithColor> MapData { get; set; } = new List<MapCountrywithColor>();
        DataGridforSearch child { get; set; }
        private Visibility loaderVisibility = Visibility.Invisible;
        FileDownload fileDownloadChild { get; set; }
        private int _tagCount = 1;
        bool loadSearchData;
        private IEnumerable<string> SelectedDatatype = new List<string> { "Policies" };
        private IEnumerable<int> SelectedFilterBYTheme = new List<int>();
        public bool AllowTopicFilter { get; set; } = false;
        public int StartYear { get; set; }
        public int EndYear { get; set; }
        private Timer _debounceTimer;
        private SliderMark[] SliderValue { get; set; }
        private (double, double) SliderDefaultValue { get; set; }
        private SearchResponseXmlModel SearchResponseFromSp { get; set; } = new();
        private DataGridforSearch DataGridforSearchchild { get; set; }
        private IJSObjectReference? LoadArcgisModule;
        private IJSObjectReference? LoadMapModule;
        public async Task GetDataCount(Domain.Search.SearchResultCounts value)
        {
            int totalCountry = 0;
            if (value.TotalRecordCount > 0)
            {
                totalCountry = value.TotalCountryCount;
            }
            //TotalRecordMessage = $"Search Result - {value.TotalRecordCount}, Countries - {totalCountry}";
            TotalRecordMessage = $"Results: {value.TotalRecordCount} data items in {totalCountry} countries";
            fileDownloadChild.RefreshCount(value, searchRequest);
        }
        private async Task OnChange((double, double) value)
        {
            searchRequest.StartYear = (int)value.Item1;
            searchRequest.EndYear = (int)value.Item2;
            SliderDefaultValue = value;
            _debounceTimer?.Dispose(); // Cancel any existing timer
            _debounceTimer = new Timer(async _ =>
            {
                _ = child.ToggleLoader(true);
                _ = InvokeAsync(child.RefreshDataGrid);
                _ = InvokeAsync(OnSearchClicked);
                _ = PopulateMapwithIndicators();
                _debounceTimer.Dispose(); // Dispose the timer when it fires
            }, null, 1000, Timeout.Infinite);
            await InvokeAsync(StateHasChanged);
        }
        public async Task SetSliderSelectedValue()
        {

            SliderDefaultValue = (0, 0);
            await Task.Delay(500);
            SliderDefaultValue = (searchRequest.StartYear.Value, searchRequest.EndYear.Value);
            await InvokeAsync(StateHasChanged);
        }
        public int GetSteperValue(int totalvalue)
        {
            int step = 1;
            if (totalvalue <= 30)
            {
                step = 1;
            }
            else if (totalvalue > 30 && totalvalue <= 60)
            {
                step = 2;
            }
            else if (totalvalue > 60 && totalvalue <= 90)
            {
                step = 3;
            }
            else if (totalvalue > 90 && totalvalue <= 120)
            {
                step = 4;
            }
            else if (totalvalue > 90 && totalvalue <= 120)
            {
                step = 5;
            }
            else
            {
                step = 10;
            }
            return step;
        }
        async Task SetRangeSliderValueFromConfigurationORSearchPage()
        {
            bool isFilterUrl = NavigationManager.Uri.Contains("redirect_filter_data");

            if (!isFilterUrl)
            {
                searchRequest.StartYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.SelectedStartYear) ?? DateTime.Now.Year - 32;
                searchRequest.EndYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.SelectedEndYear) ?? DateTime.Now.Year;
                searchRequest.MinPublishedYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeStartYear) ?? 2010;
                searchRequest.MaxPublishedYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeEndYear) ?? 2020;
            }
            else
            {
                Tuple<int?, int?> tuple = null;
                if (!searchRequest.StartYear.HasValue || !searchRequest.EndYear.HasValue)
                {
                    tuple = await PolicyService.GetAvailableStartYearAndEndYearForDatatype();
                }
                if (!searchRequest.StartYear.HasValue)
                {
                    searchRequest.StartYear = tuple.Item1;
                }
                if (!searchRequest.EndYear.HasValue)
                {
                    searchRequest.EndYear = tuple.Item2;
                }
                if (searchRequest.StartYear <= searchRequest.MinPublishedYear)
                {
                    searchRequest.MinPublishedYear = searchRequest.StartYear.Value - 3;
                }
                if (searchRequest.EndYear >= searchRequest.MaxPublishedYear)
                {
                    searchRequest.MaxPublishedYear = searchRequest.EndYear.Value + 3;
                }
            }

        }
        public async Task SetSliderValue()
        {
            // if url contain take that data 
            StartYear = searchRequest.MinPublishedYear;
            EndYear = searchRequest.MaxPublishedYear;
            var totalValue = GetSteperValue(EndYear - StartYear);
            var sliderValue = new List<SliderMark>();
            SliderDefaultValue = (0, 0);
            await Task.Delay(500);
            for (int i = StartYear; i <= EndYear; i += totalValue)
            {
                sliderValue.Add(new SliderMark(i, i.ToString()));
            }

            SliderValue = sliderValue.ToArray();
            await InvokeAsync(StateHasChanged);
        }
        private int? GetPageConfigrationValuebyKey(string key)
        {
            string value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(key))?.Value;
            if (!string.IsNullOrEmpty(value))
            {
                return Convert.ToInt32(value);
            }
            return null;
        }
        protected override async Task OnParametersSetAsync()
        {
            searchRequest.StartYear = 2010;
            searchRequest.EndYear = 2020;
            var filterConfig = await ProtectedSessionStore.GetAsync<string>("config");

            bool isFilterUrl = NavigationManager.Uri.Contains("redirect_filter_data");
            if (isFilterUrl)
            {
                searchRequest = await Deserialize(filterConfig.Value);
            }
            else
            {

            }
            SetSelectedDatatype();
            await SetRangeSliderValueFromConfigurationORSearchPage();
            RefreshMe();
        }
        public new void RefreshMe()
        {
            bool isFilterUrl = NavigationManager.Uri.Contains("redirect_filter_data");

            Task.Run(async () =>
            {
                base.RefreshMe();
                if (!isFilterUrl)
                {
                    searchRequest.StartYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.SelectedStartYear) ?? DateTime.Now.Year - 32;
                    searchRequest.EndYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.SelectedEndYear) ?? DateTime.Now.Year;
                    searchRequest.MinPublishedYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeStartYear) ?? 2010;
                    searchRequest.MaxPublishedYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeEndYear) ?? 2020;
                }
                
                _ = SearchGlobally();
                _ = child.ToggleLoader(true);
                child.SearchRequest = searchRequest;
                _ = child.RefreshDataGrid();
                await SetSliderValue();
                _ = SetSliderSelectedValue();
            });

        }
        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            pageConfigrationCache.RefreshRequested += RefreshMe;
            LoadMapModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/map.js");
            LoadArcgisModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/LoadArcGIS.js");
            if (LoadArcgisModule is not null)
            {
                await LoadArcgisModule.InvokeVoidAsync("loadArcGIS");
            }
            StateHasChanged();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                bool isFilterUrl = NavigationManager.Uri.Contains("redirect_filter_data");
                if (!isFilterUrl)
                {
                    searchRequest.IsPolicyDataTypeSelected = true;
                }
                else
                {
                    child.SearchRequest = searchRequest;
                    child.RefreshDataGrid();

                }
                GetLegends();
                _ = SetSliderSelectedValue();
                _ = PopulateMapFitler();
                _ = GetFilterByTheme();
                _ = OnSearchClicked();
                loadSearchData = false;
                PolicyLookups = PolicyLookup.GetPolicyFilter();

                StateHasChanged();
            }
        }
        private async Task PopulateMapFitler()
        {
            SelectDeselectLookups = await IndicatorTypeService.GetIndicatorLookUp();
            StateHasChanged();
        }
        private async Task GetDelectDeSelectIndicatorLookup()
        {
            SelectDeselectLookups = await IndicatorTypeService.GetIndicatorLookUp();
            StateHasChanged();
        }
        private void SetSelectedDatatype()
        {
            logger.LogInformation($"{nameof(SetSelectedDatatype)} calling ,  {nameof(PolicyLookups)} has value {PolicyLookups.Any()} ," +
                $"{nameof(searchRequest.IsPolicyDataTypeSelected)} has selected {searchRequest.IsPolicyDataTypeSelected} ");
            var data = new List<string>();
            if (searchRequest.IsPolicyDataTypeSelected)
            {
                data.Add("Policies");
            }
            if (searchRequest.IsPragrammesAndActionsDataTypeSelected)
            {
                data.Add("Programmes and actions");
            }
            if (searchRequest.IsMechanicalDataTypeSelected)
            {
                data.Add("Mechanisms");
            }
            if (searchRequest.IsCommitmentsDataTypeSelected)
            {
                data.Add("SMART commitments");
            }

            SelectedDatatype = data;
            SelectedFilterBYTheme = searchRequest.SelectedTopcis;
            StateHasChanged();
        }
        private async Task SelectedByThemeChange(IEnumerable<string> value)
        {
            IsAccordianLoading = true;
            child.ToggleLoader(true);
            if (value == null || !value.Any())
            {
                SelectedTheme = string.Empty;
            }
            else
            {
                var convertedValues = value.Select(int.Parse).ToList();
                var name = Topics.Where(e => convertedValues.Contains(e.Id)).Select(e => e.Name);
                SelectedTheme = string.Join(", ", name);
                await InvokeAsync(StateHasChanged);
            }
            if (loadSearchData)
            {
                await OnSearchClicked();
            }
            child.SearchRequest = searchRequest;
            child.RefreshDataGrid();
        }
        IndicatorType SelectedIndicator = new IndicatorType();
        private async Task SelectedDeselectedChange(IndicatorType indicator)
        {
            IsAccordianLoading = true;
            SelectedIndicator = indicator;
            _ = PopulateMapwithIndicators();
            IsAccordianLoading = false;
            StateHasChanged();
        }
        private async Task PopulateMapwithIndicators()
        {

            if (SelectedIndicator == null)
            {
                SelectDeselectIndicator = string.Empty;
                searchRequest.IndicatorId = 0;
                MapIndicatorConfigurations = Enumerable.Empty<MapIndicatorConfiguration>();
                MapIndicatorDetails.Clear();
            }
            else
            {
                SelectDeselectIndicator = SelectedIndicator.IndicatorName;
                searchRequest.IndicatorId = SelectedIndicator.Id;
                MapIndicatorConfigurations = await MapIndicatorConfigurationService.GetAllByIndicatorIdAsync(searchRequest.IndicatorId);
                MapIndicatorDetails = await MapIndictorService.GetIndicatorCountryDetailForMap(MapIndicatorConfigurations, searchRequest.StartYear.Value, searchRequest.EndYear.Value);
            }
            _ = PopulateMap();

        }
        private async Task<GlobalSearchRequest> Deserialize(string token)
        {
            string inputStr = Encoding.UTF8.GetString(Convert.FromBase64String(token));
            string decoderString = WebUtility.UrlDecode(inputStr);
            return JsonConvert.DeserializeObject<GlobalSearchRequest>(decoderString);
        }
        public async Task ShowLoader(bool value)
        {
            await Task.Run(() =>
            {
                IsAccordianLoading = value;
            });
        }
        private void GetLegends()
        {
            Legends = MapLegend.GetLegendsData();
        }
        private async Task GetFilterByTheme()
        {
            Topics = await TopicService.GetTopicsByThemes();
            await InvokeAsync(StateHasChanged);
        }
        private async Task OnSearchClicked()
        {
            await ShowLoader(true);
            StateHasChanged();
            searchRequest.IsPragrammesAndActionsDataTypeSelected = SelectedDatatype.Any(e => e == "Programmes and actions");
            searchRequest.IsPolicyDataTypeSelected = SelectedDatatype.Any(e => e == "Policies");
            searchRequest.IsMechanicalDataTypeSelected = SelectedDatatype.Any(e => e == "Mechanisms");
            searchRequest.IsCommitmentsDataTypeSelected = SelectedDatatype.Any(e => e == "SMART commitments");
            searchRequest.IsPolicyTopicSelected = searchRequest.IsMechanicalTopicSelected =
            searchRequest.IsPragrammesAndActionsTopicSelected = SelectedFilterBYTheme?.Any() ?? false;
            searchRequest.SelectedTopcis = SelectedFilterBYTheme?.ToList();
            child.SearchRequest = searchRequest;
            // Todo: please add in same class
            _ = SearchGlobally(true);
            await ShowLoader(false);
            loadSearchData = true;
            StateHasChanged();
        }
        private async Task initializeMap(List<MapCountrywithColor> mapCountrywithColors)
        {
            dynamic mapData = new { MapDivId = "viewDiv", countryList = mapCountrywithColors };
            _ = JSRuntime.InvokeVoidAsync("initializeMap", new { mapData });
            IsAccordianLoading = false;
        }
        public async Task PopulateMap()
        {
            //if (!searchRequest.IsPolicyDataTypeSelected && !searchRequest.IsCommitmentsDataTypeSelected
            //    && !searchRequest.IsPragrammesAndActionsDataTypeSelected && !searchRequest.IsMechanicalDataTypeSelected)
            //{
            //    MapData = new List<MapCountrywithColor>();
            //}
            dynamic mapData = new { MapDivId = "viewDiv", countryList = SetMapIndicatorData(MapIndicatorDetails) };
            if (LoadMapModule is not null)
            {
                await LoadMapModule.InvokeVoidAsync("initializeMap", new { mapData });
            }
            InvokeAsync(StateHasChanged);
        }
        private List<MapCountrywithColor> SetMapIndicatorData(List<MapIndicatorDetail> mapIndicatorDetails)
        {
            if (mapIndicatorDetails != null && MapData.Any() && mapIndicatorDetails.Any())
            {
                var datatypeData = MapData;
                MapData = mapIndicatorDetails.Select(o1 => new MapCountrywithColor
                {
                    IndicatorName = mapIndicatorDetails.FirstOrDefault(e => e.CountryCode == o1.CountryCode)?.IndicatorName,
                    CountryCode = mapIndicatorDetails.FirstOrDefault(e => e.CountryCode == o1.CountryCode)?.CountryCode,
                    Country = mapIndicatorDetails.FirstOrDefault(e => e.CountryCode == o1.CountryCode)?.CountryName,
                    IndicatorColor = mapIndicatorDetails.FirstOrDefault(e => e.CountryCode == o1.CountryCode)?.ColorCode,
                }).ToList();

                foreach (var item in datatypeData)
                {
                    if (MapData.Any(e => e.CountryCode == item.CountryCode))
                    {
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).Type = item.Type;
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).TotalPolicyByCountry = item.TotalPolicyByCountry;
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).TotalSmartComitmentsByCountry = item.TotalSmartComitmentsByCountry;
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).TotalMechanismsByCountry = item.TotalMechanismsByCountry;
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).TotalActionByCountry = item.TotalActionByCountry;
                        MapData.FirstOrDefault(e => e.CountryCode == item.CountryCode).Color = item.Color;
                    }
                    else
                    {
                        item.IndicatorName = string.Empty;
                        item.IndicatorColor = string.Empty;
                        MapData.Add(item);
                    }
                }
            }
            else if (mapIndicatorDetails != null && MapData.Any())
            {
                MapData = MapData.Select(o1 => new MapCountrywithColor
                {
                    Type = o1.Type,
                    CountryCode = o1.CountryCode,
                    Country = o1.Country,
                    Color = o1.Color,
                    TotalPolicyByCountry = o1.TotalPolicyByCountry,
                    TotalSmartComitmentsByCountry = o1.TotalSmartComitmentsByCountry,
                    TotalMechanismsByCountry = o1.TotalMechanismsByCountry,
                    TotalActionByCountry = o1.TotalActionByCountry,
                }).ToList();
            }
            else if (mapIndicatorDetails != null && mapIndicatorDetails.Any())
            {
                MapData = mapIndicatorDetails.Select(o1 => new MapCountrywithColor
                {
                    IndicatorColor = o1.ColorCode,
                    IndicatorName = o1.IndicatorName,
                    CountryCode = o1.CountryCode,
                    Country = o1.CountryName,
                }).ToList();
            }
            if (mapIndicatorDetails == null && MapData.Any())
            {
                foreach (var item in MapData)
                {
                    item.IndicatorColor = string.Empty;
                    item.IndicatorName = string.Empty;
                }
            }
            return MapData;
        }
        public async Task SearchGlobally(bool initialCall = false)
        {
            IsAccordianLoading = true;
            var searchResponseFromSp = await SearchService.GetCombinedSearchResultCounts(searchRequest);
            _ = GetDataCount(searchResponseFromSp.SearchResultCounts);
            MapData = searchResponseFromSp.SearchResultCounts.MapData;
            _ = PopulateMap();
            //_ = initializeMap(MapData);
            IsAccordianLoading = false;
            StateHasChanged();
        }
        public async Task OnSelectedItemsChanged(IEnumerable<string> values)
        {
            IsAccordianLoading = true;
            child.ToggleLoader(true);
            if (values == null || !values.Any())
            {
                searchRequest.IsPragrammesAndActionsDataTypeSelected = searchRequest.IsPolicyDataTypeSelected = searchRequest.IsMechanicalDataTypeSelected = searchRequest.IsCommitmentsDataTypeSelected = false;
                child.SearchRequest = searchRequest;
                //_ = PapolateMap(MapIndicatorDetails);

                SearchBtnDisable = true;
                searchRequest.SelectedTopcis = new List<int>();
                //SelectedFilterBYTheme = new List<int>();
            }
            else
            {
                SearchBtnDisable = false;
                AllowTopicFilter = !values.Any(e => e == "Policies" || e == "Mechanisms" || e == "Programmes and actions");
                //if (!values.Any(e => e == "Policies" || e == "Mechanisms" || e == "Programmes and actions"))
                //{
                //    SelectedFilterBYTheme = new List<int>();
                //}
            }
            if (loadSearchData)
            {
                await OnSearchClicked();
            }
            child.SearchRequest = searchRequest;
            _ = child.RefreshDataGrid();

            StateHasChanged();
        }
        private async Task OnExportMapClicked()
        {
            await Task.Run(() =>
            {
                ExportCsvLoading = true;
            });

            await LoadMapModule.InvokeVoidAsync("DownloadMap", null);
            await Task.Run(() =>
            {
                ExportCsvLoading = false;
            });
        }

        public async Task ToggleLoader(Visibility value)
        {
            loaderVisibility = value;
            await InvokeAsync(StateHasChanged);
        }
    }
}