﻿@page "/countries/{CountryCode}/commitments/{CommitmentCode:int}"
@page "/countries/{CountryCode}/commitments/{CommitmentCode:int}/{VersionId:int}"
@page "/commitments/{CommitmentCode:int}"
@page "/commitments/{CommitmentCode:int}/{VersionId:int}"

@if (!isAuthenticated)
{
    <UnAuthorizedView />
}
else
{
<CascadingValue Value="@CountryCode" Name="CountryCode">
    <CascadingValue Value="@CommitmentCode" Name="CommitmentCode">
        <CascadingValue Value="@VersionId" Name="VersionId">
        <CountryDetails SelectedTab="commitments" ContentName="Commitment">
            <CommitmentDetailTab/>
        </CountryDetails>
    </CascadingValue>
</CascadingValue>
</CascadingValue>
}

