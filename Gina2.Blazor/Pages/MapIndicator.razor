﻿@page "/admin/mapindicator"
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Gina2.Blazor.Models.AdminModel;
@using Gina2.DbModels;
@inherits PageConfirgurationComponent;
@using Gina2.Core.Methods;
<Loader IsLoading="@IsLoading" />
<PageTitle>GIFNA @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Modal @bind-Visible="@ThemesVisible" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms adminmobel">
        <ModalHeader Class="ant-header">
            <ModalTitle>Add map indicator </ModalTitle>
            <NavLink class="close" onclick="@(()=> HideModal())"><img src="/img/close.png" /></NavLink>
        </ModalHeader>
        <ModalBody>
            <AntDesign.Form Class="_mapindicator" Layout="@AntDesign.FormLayout.Vertical" Loading="@IsSavingloading" Model="@MapIndicatorConfiguration"
                            LabelColSpan="8"
                            WrapperColSpan="16"
                            OnFinish="BtnSaveClicked">
                <AntDesign.Row>

                    <AntDesign.Col Span="24">
                     <AntDesign.FormItem WrapperColSpan="24">
                            <LabelTemplate>
                                <label class="pr-1">
                                @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorOrderLabel)
                                </label>
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorOrderTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorOrderGroup" />
                                
                            </LabelTemplate>
                            <ChildContent>
                                <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorOrderPlaceHolder)"
                                                 @bind-Value="@context.IndicatorOrder" />
                            </ChildContent>
                        </AntDesign.FormItem>
                    </AntDesign.Col>
                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                                <label>
                                    @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorTypeLabel)<span class="pr-1">*</span> 
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorTypeTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorTypeGroup" />
                                </label>
                                <AntDesign.Select DataSource="@IndicatorLookups"
                                                  TItem="@IndicatorType"
                                                  TItemValue="int"
                                                  Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorTypePlaceHolder)"
                                                  TItem="string"
                                                  ValueName="@nameof(IndicatorType.Id)"
                                                  LabelName="@nameof(IndicatorType.IndicatorName)"
                                                  @bind-Value="@MapIndicatorConfiguration.IndicatorId"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 8px;" />
                        </AntDesign.FormItem>
                    </AntDesign.Col>

                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <LabelTemplate>
                                <label class="pr-1">
                                    @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorStartValueLabel)
                                </label>
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorStartValueTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorStartValueGroup" />
                                
                            </LabelTemplate>
                            <ChildContent>
                                <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorStartValuePlaceHolder)"
                                                 @bind-Value="@context.StartValue" />
                            </ChildContent>
                        </AntDesign.FormItem>

                    </AntDesign.Col>

                    <AntDesign.Col Span="10">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <label style="margin-bottom:5px;">
                                @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorSymbolLabel)<span class="pr-1">*</span>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorSymbolTooltip)">
                                    <Button Class="but-info _tooltip">
                                        <Icon Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorSymbolGroup" />
                            </label>
                            <AntDesign.Select DataSource="@SymbolLookUp"
                                              TItemValue="string"
                                              Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorSymbolPlaceHolder)"
                                              TItem="string"
                                              @bind-Value="@MapIndicatorConfiguration.Symbol"
                                              EnableSearch
                                              AllowClear
                                              Style="width: 100%; margin-bottom: 8px;" />

                        </AntDesign.FormItem>
                    </AntDesign.Col>

                    <AntDesign.Col Span="14">
                        <AntDesign.FormItem WrapperColOffset="1" WrapperColSpan="23">
                            <label style="margin-bottom:5px;">
                                @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorEndValueLabel)<span class="pr-1">*</span>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorEndValueTooltip)">
                                    <Button Class="but-info _tooltip">
                                        <Icon Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorEndValueGroup" />
                            </label>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorEndValuePlaceHolder)"
                                             @bind-Value="@context.EndValue" />

                        </AntDesign.FormItem>

                    </AntDesign.Col>

                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem WrapperColSpan="24">
                            <LabelTemplate>
                                <label class="pr-1">
                                    @PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorColorLabel)
                                </label>
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.IndicatorColorTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@MapIndicatorConfigurationKey.IndicatorColorGroup" />
                                
                            </LabelTemplate>
                            <ChildContent>
                                <ColorEdit Color="@context.ColorCode" ColorChanged="OnColorChange"></ColorEdit>
                            </ChildContent>
                           
                       

                        </AntDesign.FormItem>
                    </AntDesign.Col>
                </AntDesign.Row>
                <AntDesign.Button Class="but-blues apply" HtmlType="submit"> Save</AntDesign.Button>
            </AntDesign.Form>
        </ModalBody>
    </ModalContent>
</Modal>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Maskgroup.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3" data-cy="IndicatorTitle">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.Title))
                        <AdminEditbut Key="@MapIndicatorConfigurationKey.Title" />
                    </Heading>
                     <Paragraph Class="color-w" data-cy="IndicatorPageDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.PageDescription))
                        <AdminEditbut Key="@MapIndicatorConfigurationKey.PageDescription" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="HomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                         <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="Indicator" To="#">Indicator</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="IndicatorValue" To="#">Indicator value</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="pt-4 pl-2 pr-2 adminuser">
    <Layout Class="search-box pb-3 mob-layout">
        <Layout Class="left-layout DataGrids">
            <LayoutContent>
                <Div Class="form-bg _antdesign pl-0 pr-0">

                    <Fields>
                        <Field ColumnSize="ColumnSize.Is8.OnTablet.Is12.OnMobile.Is11.OnDesktop.Is11.OnWidescreen.Is11.OnFullHD" Class="_antdesign">
                            <Heading Size="HeadingSize.Is2" Class="Headingh3" data-cy="AddMapIndicatorTitle">
                                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapIndicatorConfigurationKey.Heading))
                                <Tooltip data-cy="AddMapIndicatorToolTip" Text="Add map indicator">
                                    <Button data-cy="AddMapIndicatorBtn" Class="but-info _tooltip">
                                        <Icon data-cy="AddMapIndicatorIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@MapIndicatorConfigurationKey.Heading" />
                            </Heading>
                        </Field>

                        <Field Flex="Flex.JustifyContent.End.AlignItems.End" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is1.OnDesktop.Is1.OnWidescreen.Is1.OnFullHD">
                            <Button data-cy="AddBtn" Class="btn but-yellow w-100" Clicked="@ShowAddForm"><Icon Class="far fa-plus" /> Add</Button>
                        </Field>
                    </Fields>


                </Div>

                <DataGrid FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@MapIndicatorConfiguration"
                          Data="@MapIndicatorConfigurations"
                          PageSize="50"
                          PageSizes="new[] { 50,100,250,500,1000 }"
                          Responsive
                          ShowPageSizes
                          ShowPager
                          Sortable=true
                          SortMode="DataGridSortMode.Single">
                    <EmptyTemplate>
                        <Div data-cy="NoDataFound">No data found for the search criteria.</Div>
                    </EmptyTemplate>
                    <DataGridColumns>
                        <DataGridColumn Width="10%" Caption="Order" Field="@nameof(MapIndicatorConfiguration.IndicatorOrder)" Sortable="true">
                            <DisplayTemplate>
                                <Text data-cy="IndicatorName">
                                    @context?.IndicatorOrder
                                </Text>
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Width="40%" Caption="Indicator" Field="@nameof(MapIndicatorConfiguration.IndicatorId)" Sortable="true">
                            <DisplayTemplate>
                                <Text data-cy="IndicatorName">
                                    @context?.IndicatorType?.IndicatorName 
                                </Text>
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Width="20%" Caption="Value" Field="@nameof(MapIndicatorConfiguration.StartValue)" Sortable="false">
                            <DisplayTemplate>
                                <Text data-cy="IndicatorEndValue">
                                    @context.StartValue @context.Symbol @context.EndValue%
                                </Text>
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Caption="Color" Width="15%" Field="@nameof(MapIndicatorConfiguration.ColorCode)" Sortable="true">
                            <DisplayTemplate>
                                <Div Class="_colorcode">
                                <Div data-cy="Color" Class="_colors-w" Style="@string.Format($"background-color:{context.ColorCode}")">
                                </Div>
                                <span class="pl-1">@context.ColorCode</span>
                                </Div>
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Caption="Action" Sortable="false" Width="10%">
                            <DisplayTemplate>
                                <Icon data-cy="PenIcon" Class="_colors-w" Clicked="(e)=>UpdateById(context)" Name="IconName.Pen" />
                                <Icon data-cy="DeleteIcon" Class="_colors-w" Clicked="(e)=>ShowDeleteModal(context.Id)" Name="IconName.Delete" />
                            </DisplayTemplate>
                        </DataGridColumn>
                    </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate>
                        <Badge TextColor="TextColor.Dark">
                            @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
            </LayoutContent>
        </Layout>
    </Layout>

</Container>
<Modal @bind-Visible="@deleteModalVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Are you sure want to delete this data?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button data-cy="DeleteBtn" Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteById(indicatorToBeDeleted);} )">Delete</Button>
            <Button data-cy="CancelBtn" Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteModalVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>