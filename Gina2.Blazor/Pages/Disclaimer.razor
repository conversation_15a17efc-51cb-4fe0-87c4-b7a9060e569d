﻿@page "/disclaimer"
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
<PageTitle>GIFNA disclaimer</PageTitle>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">Disclaimer</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="disclaimer">Disclaimer</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="text-used gina-50">
     @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DisclaimerPageConfigurationKey.DisclaimerBodyContent))
    <AdminEditbut Key="@DisclaimerPageConfigurationKey.DisclaimerBodyContent" />

</Container>