﻿@page "/vocabularies/{Vocabulary}/terms/{Id}/references"

@using Gina2.Blazor.Models;

<Loader IsLoading="@isLoadingTerms" />

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="pt-3 pb-3 pl-1  pr-1">
                     <Heading Class="h3 h-title" Size="HeadingSize.Is3">@termReferences.Name</Heading>

                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="pl-2 pr-2 adminuser pt-4 pb-4">
    <Layout Class="search-box mob-layout">
        <Layout Class="left-layout DataGrids">
            <LayoutContent>
                <DataGrid FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@TopicReference"
                          Data="@TopicReferencesData"
                          PageSize="@PageSize"
                          TotalItems="@TopicReferences.Count"
                          ReadData="@OnReadData"
                          PageSizes="new[] { 50,100,250,500,1000 }"
                          ShowPageSizes
                          ShowPager
                          Sortable="true"
                          SortChanged="@SortingTermRefernce"
                          CurrentPage="@CurrentPage"
                          SortMode="DataGridSortMode.Single">
                    <EmptyTemplate>
                        <Div>No data found for the search criteria.</Div>
                    </EmptyTemplate>
                    <DataGridColumns>

                        <DataGridColumn Field="@nameof(TopicReference.RegionCodes)" Caption="Region" Sortable="true" Width="10%" />

                        <DataGridColumn Field="@nameof(TopicReference.Countries)" Caption="Country" Sortable="true" Width="30%" />

                        <DataGridColumn Field="@nameof(TopicReference.Title)" Caption="Title" Sortable="true" Width="40%">
                            <DisplayTemplate>
                                <NavLink href="@($"/countries/{context.CountryCode}/{context.Link}/{context.Id}")">@context.Title</NavLink>
                            </DisplayTemplate>
                        </DataGridColumn>

                        <DataGridColumn Field="@nameof(TopicReference.Type)" Caption="Type" Sortable="true" Width="8%" />
                        <DataGridColumn Field="@nameof(TopicReference.StartYear)" Caption="Startyear" Sortable="true" Width="11%">
                            <DisplayTemplate>
                                @{
                                    string year = "-";

                                    @if (context.StartYear != 0)
                                    {
                                        year = @context.StartYear.ToString();
                                    }
                                }
                                @year
                            </DisplayTemplate>
                        </DataGridColumn>
                    </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate>
                        <Badge TextColor="TextColor.Dark">
                            @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
            </LayoutContent>
        </Layout>
    </Layout>


</Container>

