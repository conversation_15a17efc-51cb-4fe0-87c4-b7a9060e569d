using AntDesign;
using Blazorise;
using Blazorise.DataGrid;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.JSInterop;
using System.Security.Claims;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class Roles : PageConfirgurationComponent
    {
        [Inject]
        public RoleManager<IdentityRole> _roleManager { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        public IEnumerable<RoleViewModel> RoleList { get; set; } = new List<RoleViewModel>();
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JSRuntime.InvokeVoidAsync("resetRecaptcha");
                await GetRoleList();
                await Task.Run(() => IsLoading = false);
                StateHasChanged();
            } 
        }

        private async Task GetRoleList()
        {
            RoleList = await _roleManager.Roles.Select(e => new RoleViewModel
                    {
                        Name = e.Name
                    }).OrderBy(e => e.Name).ToListAsync();
        }
    }
}