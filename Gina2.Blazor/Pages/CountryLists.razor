﻿@page "/countries/{SelectedDatatype}"
@page "/countries"

@using Core.Enums
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA  @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(CountryPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@IsLoading" />
@*<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Color="Color.Primary" Split data-cy="DropDownToggelMenu"/>
            <DropdownMenu>
                <DropdownItem href="admin/contents/country-detail/moderate" data-cy="DropDownModerate">Moderate</DropdownItem>
                <DropdownItem href="admin/contents/country-detail/translate" data-cy="DropDownTranslate">Translate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>*@
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Maskgroup.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3" data-cy="CountryHeaderTitle">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(CountryPageConfigurationKey.Title))
                        <AdminEditbut Key="@CountryPageConfigurationKey.Title" />
                    </Heading>
                    <Paragraph class="color-w subtitleediticon" data-cy="CountryDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(CountryPageConfigurationKey.Description))
                        <AdminEditbut Key="@CountryPageConfigurationKey.Description" />
                    </Paragraph> 
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Fluid Class="bg-trdot pt-4 m-pt-2">
    <Container>
        <Div Flex="Flex.JustifyContent.Between" Class="poli-flex mob-f-column">
            <Div Class="item1">

                <AntDesign.Select TItem="string"
                            TItemValue="string" 
                                  DataSource="@PolicyLookups"
                                  Mode="single"
                                  Placeholder="Select type(s)"
                                 @bind-Value="@SelectedDatatype"
                                  OnSelectedItemChanged="@((e)=>OnSelectedItemsChanged(e))"
                                  TItem="string"
                                  Style="min-width: 30%; margin-bottom: 0px;">
                </AntDesign.Select>

            </Div>

            <Div Class="item2 buttonscountry">
                <Buttons>
                    <Button data-cy="CountryListBtn" id="_countrylist" Class="_countrylist _countryactive" Color="Color.Secondary" Clicked="@(() => ToggleCountryViewOption(CountriesViewOption.CountryList))">By country</Button>
                    <Button data-cy="RegionListBtn" id="_regionlist" Class="_countrylist" Color="Color.Secondary" Clicked="@(() => ToggleCountryViewOption(CountriesViewOption.RegionList))">By WHO region</Button>
                </Buttons>
            </Div>
        </Div>

        <Divider Class="divi-blue" style="margin-top:0" />
    </Container>

    @if (ViewType == @CountriesViewOption.CountryList)
    {
        <CountriesComponent 
            CountryItems="@CountryItems" 
            SelectedDataUrl="@SelectedDataUrl" 
            searchRequest="@searchRequest"
                            OnCountryStatus="@OnCountryStatus"
            SelectedDatatype="@SelectedDatatype"
            FileDownloadChild="@FileDownloadChild"/>
    }
    @if (ViewType == @CountriesViewOption.RegionList)
    {
        <CascadingValue Value="@SelectedDataUrl" Name="Url">
            <RegionsComponent Countries="@CountryItems" Regions="@Regions"
                          searchRequest="@searchRequest"
                          SelectedDatatype="@SelectedDatatype"
                          FileDownloadChild="@FileDownloadChild" />
        </CascadingValue>
    }
    <Div Style="margin-bottom:75px">
        <FileDownload FileType="CountryPage"
                      SearchRequest="@searchRequest"
        @ref="FileDownloadChild"
                      ShowCSVPanel="@ShowCSVPanel">
        </FileDownload>
    </Div>
</Container>

