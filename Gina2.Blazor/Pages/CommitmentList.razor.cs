﻿using Gina2.Blazor.Models;
using Gina2.DbModels;
using Gina2.Services.Commitments;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json.Linq;

namespace Gina2.Blazor.Pages
{
    public partial class CommitmentList
    {
        [Inject]
        private ICommitmentService _CommitmentService { get; set; }
        [Parameter]
        public string CountryCode { get; set; }
        public readonly string Title = "Commitments";
        public readonly string TabTitle = "commitments";
        public readonly string TypeofTitle = "Type of Commitments";
        public readonly string TableTitle = "SMART commitment";
        public readonly string AdminTitle = "SMART commitment";
        public readonly string DetailUrlContext = "commitments";
        public string CsvKeys { get; set; }
        public List<string> CsvData { get; set; } = new();
        public string FileName = "gina2_smartcommitments";

        private List<PublishedListItem> commitments = new();
        public List<SmartCommitment> smartCommitment = new();
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                var CommitmentNames = await _CommitmentService.GetCommitmentsByCountryAsync(CountryCode);
                smartCommitment = await _CommitmentService.GetSmartCommitmentsAsync();
                
                foreach (var item in CommitmentNames)
                {
                    var smartWithCommitment = smartCommitment.Where(x => x.CommitmentId == item.Id).ToList();
                    foreach (var smartitem in smartWithCommitment)
                    {
                        commitments.Add(new PublishedListItem()
                        {
                            Key = smartitem.Id,
                            Title = $"{item.Title} - Commitment {smartitem.CommitmentNumber} - {smartitem.Title}",
                            StartYear = item.StartYear.ToString() ?? "-",
                            EndYear = "-",
                        });
                    }
                }

                await InvokeAsync(StateHasChanged);
            }
        }
    }
}
