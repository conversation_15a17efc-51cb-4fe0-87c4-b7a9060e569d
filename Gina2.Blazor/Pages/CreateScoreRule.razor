﻿@page "/admin/scorerule/create"

<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">

        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-5 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item1 pl-1  pr-1">
                            <Heading Size="HeadingSize.Is3">Create Score Rule</Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="#">Content</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="admin/scorerule/create">Create Score Rule</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>

        <Container Class="pt-5 m-pt-2 pl-2 pr-2 adminuser">
            <Container Class="form-newd">
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel>Score Rule Name <Span>*</Span></FieldLabel>
                        <TextEdit Placeholder="Sodium Country Score Card"></TextEdit>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel>Score</FieldLabel>
                        <TextEdit></TextEdit>
                    </Field>
                </Fields>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                        <FieldLabel>Criteria</FieldLabel>
                        <RadzenHtmlEditor class="_editor"><RadzenHtmlEditorUndo /><RadzenHtmlEditorRedo /><RadzenHtmlEditorSeparator /><RadzenHtmlEditorBold /><RadzenHtmlEditorItalic /><RadzenHtmlEditorUnderline /><RadzenHtmlEditorStrikeThrough /><RadzenHtmlEditorSeparator /></RadzenHtmlEditor>

                    </Field>
                </Fields>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel>Color code</FieldLabel>
                        <Field Class="m-0 p-0 _colorpicker"><ColorsPicker ColorChanged="@OnLastTitleColorChanged" /></Field>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Flex="Flex.AlignContent.Start.AlignItems.Start">
                        <Check TValue="bool" Class="mr-3">Enable additional settings</Check>
                        <Check TValue="bool">Legend formatting enabled</Check>
                    </Field>
                </Fields>

            </Container>
            <Container Class="mt-4 pb-6 p-0">
                <Div Class="stickybottom">
                    <Button Class="but-yellow">Save</Button>
                </Div>
            </Container>
        </Container>

    </Authorized>
</AuthorizeView>

@code {
    public class Country
    {
        public string Name { get; set; }
        public string Id { get; set; }
    }
    private IEnumerable<Country> CountryResults = new List<Country>()
        {
            new Country() { Id="1", Name="Algeria", },
            new Country() { Id="2", Name="Botswana", },
            new Country() { Id="3", Name="Angola", },
            new Country() { Id="4", Name="Burkina Faso", },
            new Country() { Id="5", Name="Burundi", },
            new Country() { Id="6", Name="Eswatini", }
        };
    private void OnCountry(IEnumerable<Country> Country)
    {
        // ActionsResults = countries.Select(c => c.Iso3Code).ToList();
    }
    public class Scorecard
    {
        public string Name { get; set; }
        public string Id { get; set; }
    }
    private IEnumerable<Scorecard> ScorecardResults = new List<Scorecard>()
        {
            new Scorecard() { Id="1", Name="Algeria", },
            new Scorecard() { Id="2", Name="Botswana", },
            new Scorecard() { Id="3", Name="Angola", }
        };
    private void OnScorecard(IEnumerable<Scorecard> Scorecard)
    {
        // ActionsResults = countries.Select(c => c.Iso3Code).ToList();
    }

    private void OnLastTitleColorChanged(string color)
    {
        //SliderData.LastColor = color;
    }
}

