﻿@page "/admin/taxonomies/{Vocabulary}"

@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Plk.Blazor.DragDrop;
@using Domain.Vocabulary;
@using Domain.Terms;
@using Gina2.Core;
@using Gina2.Core.Models;
@using Microsoft.AspNetCore.Components.Web.Virtualization;
@using Gina2.Core.Enums;
@inherits PageConfirgurationComponent;

<SnackbarStack @ref="SnackbarStack" />


<PageTitle>GIFNA @TermTitle</PageTitle>

<Modal @ref="modalRef" Class="modals-lg modalw-7 antdraggable" ShowBackdrop=false>
    <ModalContent Centered Class="forms _custscroll">
        <ModalHeader Class="ant-header">
            <ModalTitle>@(isAddingTermMode ? "Add a" : "Edit the") term</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            <Validations ValidateOnLoad="false">
                <Repeater Items="@AddTerms">
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is5.OnTablet.Is12.OnMobile.Is5.OnDesktop.Is5.OnWidescreen.Is5.OnFullHD">
                            <FieldLabel>Name <Span>*</Span></FieldLabel>
                            <Validation Validator="@ValidateText">
                                <TextEdit Disabled="@(Vocabulary == Gina2.Core.Constants.VocabularyTables.Country)" Style="height:38px;" Placeholder="Enter Name..." @bind-Text="@context.Name">
                                    <Feedback>
                                        <ValidationError>Enter valid name</ValidationError>
                                    </Feedback>
                                </TextEdit>
                            </Validation>

                        </Field>

                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel>Description</FieldLabel>
                            <Validation Validator="@ValidateText">
                                <MemoEdit Rows="1" @bind-Text="@context.Description">
                                    <Feedback>
                                        <ValidationError>Enter valid description</ValidationError>
                                    </Feedback>
                                </MemoEdit>
                            </Validation>
                        </Field>

                        @if (!IsEditChild)
                        {
                            <Field ColumnSize="ColumnSize.Is1.OnTablet.Is12.OnMobile.Is1.OnDesktop.Is1.OnWidescreen.Is1.OnFullHD">
                                <FieldLabel> </FieldLabel>
                                <Tooltip Text="Remove" Class="text-center _Remove">
                                    <Icon Class="fa-solid fa-trash" Clicked="@(() => RemoveMultipleTerm(context))" />
                                </Tooltip>
                            </Field>
                        }

                    </Fields>
                </Repeater>

                @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Partners || Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2Category)
                {
                    <Field>
                        <FieldLabel>Select Category<Span>*</Span></FieldLabel>
                        <Select TValue="int?" @bind-SelectedValue="@TermDetail.ParentId">
                            <SelectItem Value="0">Select Category</SelectItem>
                            <Repeater Items="@Terms">
                                <SelectItem Value="context.Id">@context.Name</SelectItem>
                            </Repeater>
                        </Select>
                    </Field>
                }
                @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic || Vocabulary == Gina2.Core.Constants.VocabularyTables.Sdg)
                {
                    <AntDesign.Tree ShowIcon
                                MatchedClass="site-tree-search-value"
                                DataSource="Terms"
                                OnExpandChanged="OnExpandTopicChild"
                                TItem="Term"
                                TitleExpression="x => x.DataItem.Name"
                                ChildrenExpression="x => x.DataItem.Child">
                        <TitleTemplate Context="TermData">
                            <Div Class="_taxonomies" Style="@(DeleteSingleData ? "display: none;" : "")" Flex="Flex.JustifyContent.Between" draggable="true">
                                <Div Class="drag-1">
                                    <Radio CheckedChanged="@(value => OnChangeChild(TermData, value))" TValue="int" Group="colors" Value="@TermData.DataItem.Id">@TermData.DataItem.Name</Radio>
                                </Div>

                            </Div>
                        </TitleTemplate>
                    </AntDesign.Tree>

                }
            </Validations>
        </ModalBody>
        <ModalFooter Class="_align-start">
            @if (!IsEditChild)
            {
                <AntDesign.Button Class="_but-light-Orange pl-2 pr-2 mt-0 mb-0"
                              OnClick="@AddMultipleTerm">
                    Add
                </AntDesign.Button>
            }
            <AntDesign.Button Loading="@isSavingTerm"
                              Disabled="@(AddTerms.Any(t => t.Name == string.Empty))"
                              Class="_but-light-blue pl-2 pr-2 mt-0"
                              OnClick="@ValidateName">
                Save
            </AntDesign.Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @ref="editModalRef" Class="modals-lg antdraggable" ShowBackdrop=false>
    <ModalContent Centered Class="forms _custscroll">
        <ModalHeader Class="ant-header">
            <ModalTitle>Add Child For @TermDetail.Name</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            <Validations ValidateOnLoad="false">
                <Repeater Items="@AddTerms">
                    <Fields>

                        @if (Vocabulary != Gina2.Core.Constants.VocabularyTables.CountryGroup)
                        {
                            <Field ColumnSize="ColumnSize.Is5.OnTablet.Is12.OnMobile.Is5.OnDesktop.Is5.OnWidescreen.Is5.OnFullHD">
                                <FieldLabel>Name <Span>*</Span></FieldLabel>
                                <Validation Validator="@ValidateText">
                                    <TextEdit Style="height:38px;" Placeholder="Enter Name..." @bind-text="@context.Name">
                                        <Feedback>
                                            <ValidationError>Enter valid title</ValidationError>
                                        </Feedback>
                                    </TextEdit>
                                </Validation>

                                <FieldLabel hidden="@context.ValidateError" Style="color:red">This name already exists under this datatype, please provide a different name.</FieldLabel>
                            </Field>

                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel>Description</FieldLabel>
                                <Validation Validator="@ValidateText">
                                    <MemoEdit Rows="1" @bind-Text="@context.Description">
                                        <Feedback>
                                            <ValidationError>Enter valid Description</ValidationError>
                                        </Feedback>
                                    </MemoEdit>
                                </Validation>
                            </Field>
                        }


                        @if (!IsEditChild && Vocabulary != Gina2.Core.Constants.VocabularyTables.CountryGroup)
                        {
                            <Field ColumnSize="ColumnSize.Is1.OnTablet.Is12.OnMobile.Is1.OnDesktop.Is1.OnWidescreen.Is1.OnFullHD">
                                <FieldLabel> </FieldLabel>
                                <Tooltip Text="Remove" Class="text-center _Remove">
                                    <Icon Class="fa-solid fa-trash" Clicked="@(() => RemoveMultipleTerm(context))" />
                                </Tooltip>
                            </Field>
                        }

                        @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Country)
                        {
                            <Field ColumnSize="ColumnSize.Is5.OnTablet.Is12.OnMobile.Is5.OnDesktop.Is5.OnWidescreen.Is5.OnFullHD">
                                <FieldLabel>Select Status<Span>*</Span></FieldLabel>
                                <Select TValue="string" Disabled="IsEditChild" @bind-SelectedValue="@context.Status">
                                    <SelectItem Value="0">Select Status</SelectItem>
                                    <Repeater Items="@CountryStatus" Context="dataItem">
                                        <SelectItem Value="dataItem">@dataItem</SelectItem>
                                    </Repeater>
                                </Select>
                            </Field>
                        }
                        @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.CountryGroup)
                        {
                            <FieldLabel>Select country(ies)</FieldLabel>
                            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="mt-0">

                                <AntDesign.Select DataSource="@AllCountries"
                                              Mode="multiple"
                                              TItemValue="string"
                                              Placeholder="Select country(ies)"
                                              TItem="Gina2.DbModels.Country"
                                              LabelName="@nameof(Gina2.DbModels.Country.Name)"
                                              ValueName="@nameof(Gina2.DbModels.Country.Iso3Code)"
                                              @bind-Values="@SelectedCountries"
                                              AllowClear
                                              EnableSearch
                                              Style="width: 100%;">
                                </AntDesign.Select>
                            </Field>
                        }

                    </Fields>
                </Repeater>

                <FieldLabel hidden="@IsChildValidate" Style="color:red">Please Remove the duplicate name</FieldLabel>
            </Validations>



        </ModalBody>
        <ModalFooter Class="_align-start">
            @if (!IsEditChild && Vocabulary != Gina2.Core.Constants.VocabularyTables.CountryGroup)
            {
                <AntDesign.Button Class="_but-light-Orange pl-2 pr-2 mt-0"
                              OnClick="@AddChildMultipleTerm">
                    Add
                </AntDesign.Button>
            }
            <AntDesign.Button Loading="@isAddUpdateChild"
                              Disabled="@(AddTerms.Any(t => t.Name == string.Empty && Vocabulary != Gina2.Core.Constants.VocabularyTables.CountryGroup))"
                              Class="_but-light-blue pl-2 pr-2 mt-0"
                              OnClick="@AddNewChildTerm">
                Save
            </AntDesign.Button>
            @*@(async () => await UpOrDown(TermData,TermData.DataItem, -1, Terms))"*@
        </ModalFooter>
    </ModalContent>
</Modal>


<Modal @bind-Visible="@SearchVisible" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms ">
        <ModalHeader Class="ant-header">
            <ModalTitle>@(IsCheck ? "What would you like to select?" : "All topics will be deselected. Do you want to proceed??")</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@SelectFamily">@(IsCheck ? "Family" : "Cancel")</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@SelectParent">@(IsCheck ? "Only parent" : "Proceed")</Button>
        </ModalFooter>
    </ModalContent>
</Modal>




<Modal @bind-Visible="@IsValidateName" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
                <ModalTitle>The term @(ValidateTermName.Any() ? string.Join(",", ValidateTermName.ToArray()) : "") exists already, do you still want to proceed?</ModalTitle>
            @{
                string str = String.Join(",", AddTerms.Select(a => a.Name));
            }
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@SaveOrUpdateNewTerms">Yes</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => IsValidateName = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>


<Modal @bind-Visible="@DeleteVisible" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle>Are you sure want to Delete @DeleteTopic.DataItem.Name ?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Disabled="@DeleteDisable" Class="_but-delete pl-2 pr-2" Clicked="@DeleteTopics">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>



<Loader IsLoading="@isLoadingTerms" />
@{
    RenderFragment footer = @<AntDesign.Template>
    </AntDesign.Template>;
}



<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>

    <Authorized Context="authorizedContext">
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-7 pb-7">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item1 pt-3 pb-3 pl-1  pr-1">
                            <Heading Size="HeadingSize.Is3">Terms for '@TermTitle'</Heading>
                            @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
                            {
                                <Paragraph Class="color-w">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(TaxonomyTopicConfigurationKey.PageDescription))
                                    <AdminEditbut Key="@TaxonomyTopicConfigurationKey.PageDescription" />
                                </Paragraph>
                            }

                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="admin/taxonomies">Taxonomies</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#">@TermTitle</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Class="pt-4 m-pt-2 mb-3 mt-3 pl-2 pr-2 adminuser form-newd">
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <Row Class="mt-2 mb-2">
                        <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="pl-0">
                        <Div Flex="Flex.JustifyContent.Start.AlignItems.Center">
                            <Div Class="pl-0"><Button Class="but-yellow pl1 pr-1 mr-1" Clicked="@(() => AddNewTerm(Vocabulary))">Add terms</Button></Div>
                            @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
                            {
                                <Div Class="pr-0 _add_terms">
                                    <Autocomplete TItem="Term"
                                              TValue="Term"
                                              Data="@SearchingTerms"
                                              TextField="@(( item ) => item.Name)"
                                              ValueField="@(( item ) => item)"
                                              SelectedValueChanged="@(item => SelectedChangeTopic(item))"
                                              Placeholder="Search"
                                              SearchChanged="@OnSearchTopic"
                                              Filter="AutocompleteFilter.StartsWith"
                                              FreeTyping
                                              CustomFilter="@(( item, searchValue ) => item.Name.IndexOf( searchValue, 0, StringComparison.CurrentCultureIgnoreCase ) >= 0 )">
                                        <NotFoundContent> Sorry... @context was not found! </NotFoundContent>
                                        <ItemContent>
                                            <Tooltip Text="@context.Item.ParentName">
                                                @context.Item.Name
                                            </Tooltip>
                                        </ItemContent>
                                    </Autocomplete>
                                </Div>
                                <Div Class="pl-0">
                                    <Button Class="but-yellow pl-2 pr-2 ml-1" Clicked="@GetSearchingTopicData">Search</Button>
                                </Div>

                                
                            }
                        </Div>
                        <Div Class="_Tree-Collapse mt-4">
                            <AntDesign.Collapse>
                                <AntDesign.Panel Disabled="@EnableDoubleTree" Header="Mass term" Key="1" OnActiveChange="@MassCollapse">
                                    <MemoEdit Rows="4" @bind-Text="@MassTerm" />
                                    <FieldLabel Style="color:red">
                                        @MassErrorMessage
                                    </FieldLabel>

                            <Div>
                                        <AntDesign.Button Class="_but-light-Orange pl-2 pr-2 mt-2 mb-0" OnClick="@AddMassTerm">Add mass term</AntDesign.Button>

                            </Div>

    </AntDesign.Panel>
                            </AntDesign.Collapse>
                        </Div>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        @if (showTopicsToSearchTree)
                        {
                            <Row>
                                <AntDesign.Alert ShowIcon="true" Message="Please select topics to show them in Search filters." Type="@AntDesign.AlertType.Info" />
                            </Row>
                        }
                    </Column>
                </Row>

            </Column>
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <Row Class="mt-2 mb-2">
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="pl-0 pr-3">
                        <Div Class="_taxon content _customTree" id="_searchElement">
                            <AntDesign.Tree ShowIcon
                                            MatchedClass="site-tree-search-value"
                                            DataSource="Terms"
                                            OnExpandChanged="OnExpandTopicChild"
                                            TItem="Term"
                                            Checkable="@IsCheckable"
                                            OnCheck="x => OnChangeTreeChild(x, Left)"
                                            CheckStrictly
                                            KeyExpression="x => x.DataItem.Id.ToString()"
                                            TitleExpression="x => x.DataItem.Name"
                                            ChildrenExpression="x => x.DataItem.Child">
                                <TitleTemplate Context="TermData">
                                    <Div Class="_taxonomies" Style="@(DeleteSingleData ? "display: none;" : "")" Flex="Flex.JustifyContent.Between" draggable="true">
                                        @{
                                            Vocabulary = Gina2.Core.Constants.VocabularyTables.IncomeGroup == Vocabulary && !TermData.DataItem.IsGrandParent ? Gina2.Core.Constants.VocabularyTables.Country: Vocabulary;
                                            string id = Gina2.Core.Constants.VocabularyTables.Country == TermData.DataItem.URL
                                            || Gina2.Core.Constants.VocabularyTables.Region == TermData.DataItem.URL
                                            || Gina2.Core.Constants.VocabularyTables.IncomeGroup == TermData.DataItem.URL ?
                                            TermData.ParentNode == null ? TermData.DataItem.Code : TermData.DataItem.Iso3Code :
                                            Gina2.Core.Constants.VocabularyTables.Area == TermData.DataItem.URL ? TermData.DataItem.Name : TermData.DataItem.Id.ToString();
                                        }
                                        @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic && MassTermSelected)
                                        {
                                            <AntDesign.Checkbox Class="_checkgtree" OnChange="@(value =>  SelectChild(TermData.DataItem, value))"> </AntDesign.Checkbox>
                                        }
                                        <Div Class="drag-1">
                                           
                                            <Tooltip Text="@TermData.DataItem.Description">
                                                <NavLink href="@($"/vocabularies/{TermData.DataItem.URL}/terms/{id}/references")" target="_blank">
                                                    <Div id="@(!string.IsNullOrEmpty(SearchTopic) && SearchTopic == TermData.DataItem.Name ? $"_searchscroll{TermData.DataItem.Id}" : "")"
                                                         Style="@(!string.IsNullOrEmpty(SearchTopic) && SearchTopic == TermData.DataItem.Name ? "color:orange" : "")">
                                                        @TermData.DataItem.Name
                                                    </Div>
                                                </NavLink>
                                            </Tooltip>
                                        </Div>
                                        <Div Class="drag-2 _dropbutton">
                                            <Tooltip Text="Up">
                                                <Icon Name="IconName.ArrowUp" Class="fa-solid fa-up" Clicked="@(async () => await UpOrDown(TermData,TermData.DataItem, -1, Terms))" />
                                            </Tooltip>
                                            <Tooltip Text="Down">
                                                <Icon Name="IconName.ArrowDown" Class="fa-solid fa-down" Clicked="@(async () => await UpOrDown(TermData, TermData.DataItem, 1, Terms))" />
                                            </Tooltip>
                                            @if ((Vocabulary == Gina2.Core.Constants.VocabularyTables.CountryGroup && !TermData.DataItem.ParentId.HasValue) || Vocabulary != Gina2.Core.Constants.VocabularyTables.CountryGroup)
                                            {
                                                <Tooltip Text="Edit the Term">
                                                    <Icon Class="fa-solid fa-pen" Clicked="@( () =>  EditTermPopupAsync(TermData, TermData.DataItem))" />
                                                </Tooltip>
                                            }

                                            <Tooltip Text="Delete the Term">
                                                <Icon Class="@(Vocabulary != Gina2.Core.Constants.VocabularyTables.Country
&& Vocabulary != Gina2.Core.Constants.VocabularyTables.Region && TermData.DataItem.DeleteDisable ? "fa-solid fa-trash" :"fa-solid fa-trash _disab-icon" )" Clicked="@(() => ShowDeleteModal(TermData, "LeftTree"))" />
                                            </Tooltip>
                                            @if (!ShowPartnerOricn2Child)
                                            {
                                                if (TermData.DataItem.ParentId == null || Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
                                                {
                                                    <Tooltip Text="Add the Child">
                                                        <Icon Class="fa-solid fa-plus" Clicked="@( () =>  OpenModalAddNewChildTerm(TermData.DataItem, "LeftTree"))" />
                                                    </Tooltip>
                                                }
                                            }
                                            @if (TermData.DataItem.ParentId != null && Vocabulary == Gina2.Core.Constants.VocabularyTables.Country)
                                            {
                                                <Tooltip Text="Allow in country list">
                                                    <Check TValue="bool" Class="_checkpadd" Checked="@TermData.DataItem.IsActive" CheckedChanged="@(async (value)=>await SetCountryVisibility(value,TermData.DataItem))">
                                                    </Check>
                                                </Tooltip>
                                            }

                                        </Div>
                                    </Div>
                                </TitleTemplate>
                            </AntDesign.Tree>
                        </Div>

                        <Div Class="_leftrightTree">
                            @if (EnableDoubleTree)
                            {
                                <AntDesign.Button OnClick="@(() => OnLeftToRightChild(RightTree, LeftTree, "RightToLeft"))" Class="_but-light-blue _noborder"><Icon Name="IconName.AngleLeft" /></AntDesign.Button>
                                <AntDesign.Button OnClick="@(() => OnLeftToRightChild(LeftTree, RightTree, "LeftToRight"))" Class="_but-light-blue _noborder"><Icon Name="IconName.AngleRight" /></AntDesign.Button>
                            }
                        </Div>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">

                        @if (EnableDoubleTree)
                        {
                            <Div Class="_taxon content _customTree">
                                <AntDesign.Tree ShowIcon
                                            MatchedClass="site-tree-search-value"
                                            DataSource="Terms"
                                            OnExpandChanged="OnExpandTopicChild"
                                            TItem="Term"
                                            Checkable
                                            OnCheck="x => OnChangeTreeChild(x, Right)"
                                            CheckStrictly
                                            KeyExpression="x => x.DataItem.Id.ToString()"
                                            TitleExpression="x => x.DataItem.Name"
                                            ChildrenExpression="x => x.DataItem.Child">
                                    <TitleTemplate Context="TermData">
                                        <Div Class="_taxonomies" Style="@(DeleteSingleData ? "display: none;" : "")" Flex="Flex.JustifyContent.Between" draggable="true">
                                            <Div Class="drag-1">
                                                <Tooltip Text="@TermData.DataItem.Description">
                                                    <NavLink href="@($"/vocabularies/{TermData.DataItem.URL}/terms/{TermData.DataItem.Id.ToString()}/references")" target="_blank">@TermData.DataItem.Name</NavLink>
                                                </Tooltip>
                                            </Div>
                                            <Div Class="drag-2 _dropbutton">
                                                <Tooltip Text="Up">
                                                    <Icon Name="IconName.ArrowUp" Class="fa-solid fa-up" Clicked="@(async () => await UpOrDown(TermData,TermData.DataItem, -1, SelectTreeTerms))" />
                                                </Tooltip>
                                                <Tooltip Text="Down">
                                                    <Icon Name="IconName.ArrowDown" Class="fa-solid fa-down" Clicked="@(async () => await UpOrDown(TermData,TermData.DataItem, 1, SelectTreeTerms))" />
                                                </Tooltip>
                                                <Tooltip Text="Edit the Term">
                                                    <Icon Class="@(Vocabulary != Gina2.Core.Constants.VocabularyTables.Country
&& Vocabulary != Gina2.Core.Constants.VocabularyTables.Region ? "fa-solid fa-pen" :"fa-solid fa-pen _disab-icon" )" Clicked="@( () =>  EditTermPopupAsync(TermData, TermData.DataItem))" />
                                                </Tooltip>
                                                <Tooltip Text="Delete the Term">
                                                    <Icon Class="@(Vocabulary != Gina2.Core.Constants.VocabularyTables.Country
&& Vocabulary != Gina2.Core.Constants.VocabularyTables.Region && TermData.DataItem.DeleteDisable ? "fa-solid fa-trash" :"fa-solid fa-trash _disab-icon" )" Clicked="@(() => ShowDeleteModal(TermData, "RightTree"))" />
                                                </Tooltip>
                                                @if (!ShowPartnerOricn2Child)
                                                {
                                                    <Tooltip Text="Add the Child">
                                                        <Icon Class="@(Vocabulary != Gina2.Core.Constants.VocabularyTables.Country
&& Vocabulary != Gina2.Core.Constants.VocabularyTables.Region ? "fa-solid fa-plus" :"fa-solid fa-plus _disab-icon" )" Clicked="@( () =>  OpenModalAddNewChildTerm(TermData.DataItem, "LeftTree"))" />
                                                    </Tooltip>
                                                }

                                            </Div>
                                        </Div>
                                    </TitleTemplate>
                                </AntDesign.Tree>
                            </Div>
                        }
                        @if (showTopicsToSearchTree)
                        {
                            <Div Class="_taxon content _customTree _SearchTree">
                                <AntDesign.Tree ShowIcon
                                            @ref="topicTree"
                                            MatchedClass="site-tree-search-value"
                                            DataSource="@SearchTreeTerms"
                                            TItem="Term"
                                            OnExpandChanged="OnExpandTopicChild"
                                            TitleExpression="x => x.DataItem.Name"
                                            ChildrenExpression="x => x.DataItem.Child"
                                            KeyExpression="x => x.DataItem.Key">
                                    <TitleTemplate Context="TermData">
                                        <Div Class="_taxonomies _topicssearch" Style="@(DeleteSingleData ? "display: none;" : "")" Flex="Flex.JustifyContent.Between" draggable="true">
                                            <Div Class="drag-1">
                                                <Check TValue="bool" Checked="@TermData.DataItem.IsCheck" CheckedChanged="@(value => OpenModalIsSelectAllParent(value, TermData))" />

                                                <Tooltip Text="@TermData.DataItem.Description">
                                                    @TermData.DataItem.Name
                                                </Tooltip>
                                            </Div>
                                        </Div>
                                    </TitleTemplate>
                                </AntDesign.Tree>
                            </Div>
                        }

                    </Column>
                </Row>
            </Column>





            @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
            {
                <AntDesign.Button Disabled="@MassTermSelected" OnClick="@EnableDoubleTreeView" Class="_but-light-blue pl-2 pr-2 _noborder">@*@(EnableDoubleTree ? "Disable" : "Enable") *@Double Tree</AntDesign.Button>
            }

            @if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
            {
                <AntDesign.Button Disabled="@MassTermSelected" OnClick="@ToggleTopicsInSearch" Class="_but-light-blue pl-2 pr-2 _noborder"> Topics in Search</AntDesign.Button>
            }

            <AntDesign.Button Disabled="@(!ExportCsvDisable)" Loading="@ExportCsvLoading" OnClick="@ExportCsvTerm" Class="_but-light-blue pl-2 pr-2 _noborder">Export CSV</AntDesign.Button>
            <Div hidden="@(!EnableDoubleTree)" Class="_topic-keepparent"><Check TValue="bool" @bind-Checked="@KeepParent">Keep Parent</Check></Div>
        </Container>
    </Authorized>
</AuthorizeView>



