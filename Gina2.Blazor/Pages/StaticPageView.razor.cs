﻿using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Core.Lookups;
using Gina2.DbModels;
using Gina2.Services.StaticPageService;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Pages
{

    public partial class StaticPageView
    {
        [Parameter]
        public string dynamicUrl { get; set; }
        [Parameter]
        public string staticPageType { get; set; } = "published";
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        private StaticPage staticPage;
        [Inject]
        private IStaticPageService _staticPageService { get; set; }

        protected override async Task OnInitializedAsync()
        {
            var response = await _staticPageService.GetStaticPageByUrl(dynamicUrl, staticPageType);
            if (response.IsSuccess)
            {
                staticPage = response.Result;
                if (staticPage == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                }
            }
            else
            {
                NavigationManager.NavigateTo("NotFound");
            }
        }
    }
}
