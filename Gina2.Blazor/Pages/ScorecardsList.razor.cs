using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.DbModels;
using Gina2.Services.MapIndicatorConfigurations;
using Gina2.Services.ScoreCarad;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Threading.Tasks;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class ScorecardsList : PageConfirgurationComponent
    {
        [Inject]
        public NavigationManager _NavigationManager { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        public ITopicService TopicService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        public ApplicationUserSearchRequestModel SearchModel { get; set; } = new ApplicationUserSearchRequestModel();
        public List<DbModels.Scorecard> ScorecardList { get; set; }
        public DbModels.Scorecard Scorecard { get; set; }
        private bool deleteModalVisible;
        private int ScorecardId = 0;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                _ = GetAllScorecards();
            }
        }
        private async Task GetAllScorecards()
        {
            ScorecardList = await ScorecardService.GetAllAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task UpdateScoreCard(DbModels.Scorecard score)
        {
            score.LastUpdated = DateTime.UtcNow;
            await ScorecardService.SaveAsync(score);
            
        }

        private void ShowDeleteModal(DbModels.Scorecard scorecard)
        {
            deleteModalVisible = true;
            Scorecard = scorecard;
        }

        public async Task DeleteById()
        {
            ScorecardList.Remove(Scorecard);
            await ScorecardService.DeleteAsync(Scorecard.Id);
            _ = OpenSuccessToaster("Scorecard deleted successfully");
            deleteModalVisible = false;
        }

        public void NavigateToMap(string link)
        {
            _NavigationManager.NavigateTo($"summary/{link}", true);
        }

        private async Task HandleCountryTableClick(Scorecard row)
        {
            _NavigationManager.NavigateTo($"summary/{row.URL}/data");
        }
    }
}