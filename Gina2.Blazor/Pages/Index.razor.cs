﻿using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Core;
using Gina2.DbModels.HomeDrafts;
using Gina2.Services.HomeDraftServices;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Pages
{
    public partial class Index  : PageConfirgurationComponent
    {
        [Inject]
        private IWebHostEnvironment _environment { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        private IHomeDraftService HomeDraftService { get; set; }

        [Inject]
        private IConfiguration Configuration { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IMemoryCache MemoryCache { get; set; }


        private ObservableCollection<HomeSliderDto> SlidersList { get; set; } = new();

        private ObservableCollection<HomeFeatureDto> FeaturesList { get; set; } = new();

        private ObservableCollection<HomeNutritionDto> NutrientList { get; set; } = new();

        private bool isLoadingNutrients = true;
        private bool isFeaturesLoading = true;
        private bool getImagesFromBlob = true;

        [Parameter]
        public int? PreviewDraftId { get; set; }
        [Parameter]
        public string RerenderTokenId { get; set; }
        bool showSliderSpinner = true;
        public CloudBlobContainer CloudBlobContainer
        {
            get
            {
                string blobStorageConnectionString = new AppSettingsHelper(Configuration).GetBlobStorageConnectionString();
                if (string.IsNullOrWhiteSpace(blobStorageConnectionString))
                {
                    return null;
                }

                CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
                CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
                return cloudBlobClient.GetContainerReference(Constants.BlobStorage.ContainerName);
            }
        }

        public int publishedDraftId = 0;

        HomeDraft homeDraft = new();
        private string latestAddition = string.Empty;

        protected override async Task OnParametersSetAsync()
        {
            getImagesFromBlob = bool.Parse(Configuration["GetImagesFromBlob"] ?? "true");
            showSliderSpinner = true;
            await base.OnParametersSetAsync();
            var publisehdDraft = await HomeDraftService.GetPublishedDraft();
            publishedDraftId = publisehdDraft.Id;
            if (PreviewDraftId.HasValue)
            {
                homeDraft = await HomeDraftService.GetById(PreviewDraftId.Value);
            }
            else
            {
                homeDraft = publisehdDraft;
            }
            FeaturesList.Clear();
            SlidersList.Clear();
            NutrientList.Clear();

            if (homeDraft.LatestAdditions?.Count > 0)
            {
                latestAddition = homeDraft.LatestAdditions.OrderByDescending(la => la.DateCreated).FirstOrDefault()?.Title;
            }

            Task t1 = LoadSlidersAsync();
            Task t2 = LoadFeaturesAsync();
            Task t3 = LoadNutritionAsync();
            await Task.WhenAll(t1, t2, t3);
            await InvokeAsync(StateHasChanged);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            var EnableRecaptch =Convert.ToBoolean(Configuration["ReCaptcha:EnableRecaptcha"] ?? "false");
            if (EnableRecaptch && firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
            }
            await base.OnAfterRenderAsync(firstRender);
        }
        private async Task LoadSlidersAsync()
        {
            var sliderCache = MemoryCache.Get<IEnumerable<HomeSliderDto>>(Constants.Cachekeys.Slider);

            if (sliderCache?.Count() > 0 && !PreviewDraftId.HasValue)
            {
                SlidersList = new(sliderCache);
                await InvokeAsync(StateHasChanged);
                await LoadSliderImagesAsync();
                return;
            }

            List<HomeSliderDraft> sliders = homeDraft.HomeSliderDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            foreach (var item in sliders)
            {
                HomeSliderDto slider = new()
                {
                    Id = item.Id,
                    SliderTitle = item.SliderTitle,

                    RedirectionUrl = item.RedirectionUrl,
                    FirstTitle = item.FirstTitle,
                    LastTitle = item.LastTitle,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId
                };

                SlidersList.Add(slider);
            }
            StateHasChanged();
            List<Task> tasks = new();
            foreach (var item in SlidersList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }

            await Task.WhenAll(tasks);
            if (PreviewDraftId == null)
            {
                MemoryCache.Set(Constants.Cachekeys.Slider, SlidersList);
            }

            await LoadSliderImagesAsync();

        }

        private async Task LoadSliderImagesAsync()
        {
            var slidersWithData = SlidersList.Where(n => n.ImageData != null && n.ImageData.Length > 0).ToList();

            if (slidersWithData.Count == 0)
            {
                showSliderSpinner = false;
                await InvokeAsync(StateHasChanged);
                var path = Path.Combine(_environment.WebRootPath, "img", "Slider3.png");
                using MemoryStream stream = new(File.ReadAllBytes(path));
                var dotnetImageStream = new DotNetStreamReference(stream);
                await JsRuntime.InvokeVoidAsync("setSliderImage", SlidersList?.FirstOrDefault()?.ElementId ?? "default-slider-div", dotnetImageStream);
            }

            foreach (var item in slidersWithData)
            {
                using MemoryStream stream = new(item.ImageData);
                var dotnetImageStream = new DotNetStreamReference(stream);
                showSliderSpinner = false;
                await InvokeAsync(StateHasChanged);
                await JsRuntime.InvokeVoidAsync("setSliderImage", item.ElementId, dotnetImageStream);
            }
        }

        private async Task SetBlobBytesAsync(HomeSliderDto item)
        {
            if (getImagesFromBlob)
            {
                try
                {
                    item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
                }
                catch (Exception ex)
                {
                    // No need to handle here.
                }
            }
        }

        private async Task<byte[]> GetBlobBytesAsync(string backgroundImage)
        {
            CloudBlockBlob cloudBlockBlob = CloudBlobContainer.GetBlockBlobReference(backgroundImage);
            await cloudBlockBlob.FetchAttributesAsync();
            byte[] arr = new byte[cloudBlockBlob.Properties.Length];
            await cloudBlockBlob.DownloadToByteArrayAsync(arr, 0);
            return arr;
        }

        private async Task LoadFeaturesAsync()
        {
            var scorecardCache = MemoryCache.Get<IEnumerable<HomeFeatureDto>>(Constants.Cachekeys.Scorecard);

            if (scorecardCache?.Count() > 0 && !PreviewDraftId.HasValue)
            {
                FeaturesList = new(scorecardCache.Take(3));
                isFeaturesLoading = false;
                await InvokeAsync(StateHasChanged);
                await LoadFeatureImagesAsync();
                return;
            }

            List<ScorecardDraft> scorecards = homeDraft.ScoreCardDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            var latestScorecards = scorecards.Take(3);

            foreach (ScorecardDraft item in latestScorecards)
            {
                HomeFeatureDto feature = new()
                {
                    Id = item.Id,
                    Title = item.Title,
                    Description = item.Description,
                    RedirectionUrl = item.RedirectionUrl,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId
                };

                FeaturesList.Add(feature);
            }

            isFeaturesLoading = false;
            await InvokeAsync(StateHasChanged);

            List<Task> tasks = new();
            foreach (var item in FeaturesList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }

            await Task.WhenAll(tasks);
            if (PreviewDraftId == null)
            {
                MemoryCache.Set(Constants.Cachekeys.Scorecard, FeaturesList);
            }
            await LoadFeatureImagesAsync();
        }

        private async Task SetBlobBytesAsync(HomeFeatureDto item)
        {
            if (getImagesFromBlob)
            {
                try
                {
                    item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
                }
                catch (Exception ex)
                {
                    // No need to handle here.
                }
            }
        }

        private async Task LoadFeatureImagesAsync()
        {
            int i = 0;
            foreach (var item in FeaturesList.ToList())
            {
                i++;
                if (item.ImageData?.Length > 0)
                {
                    using MemoryStream stream = new(item.ImageData);
                    var dotnetImageStream = new DotNetStreamReference(stream);
                    await JsRuntime.InvokeVoidAsync("setFeatureImages", item.ElementId, dotnetImageStream);
                }
                else
                {
                    var path = Path.Combine(_environment.WebRootPath, "img", $"story-card{i}.png");
                    using MemoryStream streamLocalImage = new(File.ReadAllBytes(path));
                    var dotnetImageStream = new DotNetStreamReference(streamLocalImage);
                    await JsRuntime.InvokeVoidAsync("setFeatureImages", item.ElementId, dotnetImageStream);
                }
            }
        }

        private async Task LoadNutritionAsync()
        {
            var nutrientCache = MemoryCache.Get<IEnumerable<HomeNutritionDto>>(Constants.Cachekeys.Nutrient);

            if (nutrientCache?.Count() > 0 && PreviewDraftId == null)
            {
                NutrientList = new(nutrientCache.Take(15));
                await InvokeAsync(StateHasChanged);
                isLoadingNutrients = false;
                await LoadNutrientImagesAsync();
                return;
            }

            List<HomeNutritionDraft> nutritiions = homeDraft.HomeNutritionDrafts.OrderByDescending(x => x.DragAndDropKey).ToList();

            var latestNutrients = nutritiions.Take(15);

            foreach (HomeNutritionDraft item in latestNutrients)
            {
                HomeNutritionDto nutrition = new()
                {
                    Id = item.Id,
                    SliderTitle = item.SliderTitle,
                    Description = item.Description,
                    RedirectionUrl = item.RedirectionUrl,
                    DragAndDropKey = item.DragAndDropKey,
                    BackgroundImage = item.BackgroundImage,
                    HomeDraftId = item.HomeDraftId,
                };

                NutrientList.Add(nutrition);
            }

            isLoadingNutrients = false;
            await InvokeAsync(StateHasChanged);

            List<Task> tasks = new();
            foreach (var item in NutrientList)
            {
                tasks.Add(SetBlobBytesAsync(item));
            }

            await Task.WhenAll(tasks);
            if (PreviewDraftId == null)
            {
                MemoryCache.Set(Constants.Cachekeys.Nutrient, NutrientList);
            }
            await LoadNutrientImagesAsync();
        }

        private async Task SetBlobBytesAsync(HomeNutritionDto item)
        {
            if (getImagesFromBlob)
            {
                try
                {
                    item.ImageData = await GetBlobBytesAsync(item.BackgroundImage);
                }
                catch (Exception ex)
                {
                    // No need to handle here.
                }
            }
        }

        private async Task LoadNutrientImagesAsync()
        {
            int i = 0;
            foreach (var item in NutrientList.ToList())
            {
                i++;
                if (item.ImageData?.Length > 0)
                {
                    using MemoryStream stream = new(item.ImageData);
                    var dotnetImageStream = new DotNetStreamReference(stream);
                    await JsRuntime.InvokeVoidAsync("setNutrientImages", item.ElementId, dotnetImageStream);
                }
                else
                {
                    string imageNamePostfix = i % 2 == 0 ? "2" : "1";
                    var path = Path.Combine(_environment.WebRootPath, "img", $"links-imag{imageNamePostfix}.png");
                    using MemoryStream streamLocalImage = new(File.ReadAllBytes(path));
                    var dotnetImageStream = new DotNetStreamReference(streamLocalImage);
                    await JsRuntime.InvokeVoidAsync("setNutrientImages", item.ElementId, dotnetImageStream);

                }
            }
            await InvokeAsync(StateHasChanged);
        }

        private void NavigateToSliderLink()
        {
            NavigationManager.NavigateTo(SlidersList.FirstOrDefault()?.RedirectionUrl, true);
        }
        
        private async Task NavigateToSliderLinkOutside()
        {
            string redirectUrl = SlidersList.FirstOrDefault()?.RedirectionUrl;
            if(redirectUrl != null)
            {
                if(redirectUrl.StartsWith(NavigationManager.BaseUri))
                {
                    await JsRuntime.InvokeVoidAsync("liveedit", $"{SlidersList.FirstOrDefault()?.RedirectionUrl}",false);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("liveedit", $"{SlidersList.FirstOrDefault()?.RedirectionUrl}", true);
                }
            }
            //confirmModalVisible = false;
            // await JsRuntime.InvokeAsync<string>("open", $"{SlidersList.FirstOrDefault()?.RedirectionUrl}", "_blank");
           
        }

        // Developer function, not referenced in the code but can be used to clear the memory cache of the sliders, scorecards and nutrients
        public void ClearAllCaches()
        {
            var cacheKeys = new List<string> { Constants.Cachekeys.Slider, Constants.Cachekeys.Scorecard, Constants.Cachekeys.Nutrient };
            foreach (var key in cacheKeys)
            {
                MemoryCache.Remove(key);
            }
        }
    }
}