﻿@page "/commitment/highlights"
@using Gina2.DbModels;
<AuthorizeView Context="authorizedContext">
    <Authorized>
        <Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Div class="container pt-5 pb-5">
            <CardBody class="container pt-5 mo-p-0">
                <CardTitle Size="2" Class="color-yel">Commitment Highlights</CardTitle>

                <Div Class="global-search">
                        <Div>
                            <Dropdown Class="Dropdown-Topic">
                            <DropdownToggle>@DefaultCountry <Icon Name="IconName.ChevronRight" /></DropdownToggle>
                            <DropdownMenu>
                                @*<DropdownItem Clicked="@(() => OnChangeCountry("All"))">All</DropdownItem>*@
                            <Repeater Items="@Countries">
                                <DropdownItem Clicked="(() => OnChangeCountry(context.Name))">@context.Name</DropdownItem>
                                </Repeater>
                            </DropdownMenu>
                            </Dropdown>
                        </Div>
                        <Div>
                            <Dropdown Class="Dropdown-Topic">
                            <DropdownToggle>@DefaultYear <Icon Name="IconName.ChevronRight" /></DropdownToggle>
                            <DropdownMenu>
                                @*<DropdownItem Clicked="@(() => OnChangeYear("Any"))">Any</DropdownItem>*@
                            <Repeater Items="@Years">
                                <DropdownItem Clicked="(() => OnChangeYear(context.ToString()))">@context</DropdownItem>
                                </Repeater>
                            </DropdownMenu>
                            </Dropdown>
                        </Div>
                        <Div Class="Highlights-Filter">
                            <Dropdown Class="Dropdown-Topic">
                            <DropdownToggle>@DefaultIcn2Category <Icon Name="IconName.ChevronRight" /></DropdownToggle>
                            <DropdownMenu>
                                @*<DropdownItem Clicked="@(() => OnChangeIcn2Category(0, "Any"))">Any</DropdownItem>*@

                           <Repeater Items="@Icn2Catogries">
                                <DropdownItem Clicked="(() => OnChangeIcn2Category(context.Id, context.Name))">@context.Name</DropdownItem>
                                </Repeater>
                            </DropdownMenu>
                            </Dropdown>
                        
                   
                      </Div>
                </Div>
                @if (FilterIcn2 != null && FilterIcn2.Count() > 0)
                    {
                <Div Class="global-search" Flex="Flex.JustifyContent.Center.AlignItems.Center">
                 
                        <Div>
                            <Dropdown Class="Dropdown-Topic">
                            <DropdownToggle>@DefaultIcn2 <Icon Name="IconName.ChevronRight" /></DropdownToggle>
                            <DropdownMenu>
                                <DropdownItem Clicked="@(() => OnChangeIcn2(0, "Any"))">Any</DropdownItem>

                           <Repeater Items="@FilterIcn2">
                                <DropdownItem Clicked="(() => OnChangeIcn2(context.Id, context.Name))">@context.Name</DropdownItem>
                                </Repeater>
                            </DropdownMenu>
                            </Dropdown>
                        </Div>
                         </Div>
                      }
                
                <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="pt-4">
                    <Button Class="but-yellow mr-1 mob-t-hide" Clicked="ApplyFilter">Apply</Button>
                    <Button Class="but-by-yellow mob-t-hide text-by-yellow" Clicked="Reset">Reset</Button>
                </Div>
            </CardBody>

        </Div>
    </Card>
</Container>

<Container Fluid Class="bg-trdot pt-4 m-pt-2 search-box">
    <Container Class="DataGrids">
       <DataGrid
                          FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@CommitmentHighlightDetails"
                          Data="@FilterHightlightList"
                          TotalItems="@SearchModel.TotalFilteredCount"
                          PageSize="SearchModel.PageSize"
                          CurrentPage="SearchModel.PageNo"
                          ShowPageSizes
                          ShowPager
                          Responsive
                          SortMode="DataGridSortMode.Single">
                            <EmptyTemplate>
                                <Div>No data found for the search criteria.</Div>
                            </EmptyTemplate>
                            <DataGridColumns>
                                @*<DataGridColumn Field="@nameof(CommitmentHighlightDetails.Country)" Caption="Country" Sortable="false" Width="200px" />*@
                                <DataGridColumn Caption="Country" Sortable="true" Field="@nameof(CommitmentHighlightDetails.Country)" Width="100px">
                                    <DisplayTemplate>
                                        <Link To="@($"/countries/{context.CountryCode}/commitments")">@context.Country</Link>
                                    </DisplayTemplate>
                                </DataGridColumn>
                                <DataGridColumn Field="@nameof(CommitmentHighlightDetails.Year)" Caption="Year" Width="100px" Sortable="true"/>
                               <DataGridColumn Caption="Commitments" Sortable="true" Field="@nameof(CommitmentHighlightDetails.Commitments)" Width="200px">
                                    <DisplayTemplate>
                                        <Link To="@($"/commitments/{context.SmartCommitmentId}")">@context.Commitments</Link>
                                    </DisplayTemplate>
                                </DataGridColumn>
                                <DataGridColumn Caption="Links to the ICN2 Framework for Action" Sortable="true" Field="@nameof(CommitmentHighlightDetails.Icn2)">
                                    <DisplayTemplate>
                                        <Repeater Items = "context.Icn2" Context="icn2">
                                            @icn2.Icn2.Name
                                        </Repeater>
                                        <NavLink href="">@context.Country</NavLink>
                                    </DisplayTemplate>
                                </DataGridColumn>
                                <DataGridColumn Caption="Links to the Sustainable Development Goals (SDGs)" Sortable="true" Field="@nameof(CommitmentHighlightDetails.Icn2)" Width="300px">
                                    <DisplayTemplate>
                                        <Repeater Items = "context.sdg" Context="sdg">
                                            @sdg.Sdg.Name
                                        </Repeater>
                                    </DisplayTemplate>
                                </DataGridColumn>
                            </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate Context="row">
                        <Badge TextColor="TextColor.Dark">
                            @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                        </DataGrid>
    </Container>
</Container>
    </Authorized>
    <NotAuthorized>
    <UnAuthorizedView />
    </NotAuthorized>
</AuthorizeView>








