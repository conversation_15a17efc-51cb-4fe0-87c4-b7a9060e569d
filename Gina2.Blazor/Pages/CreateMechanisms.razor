﻿@page "/admin/mechanisms/create"
@using Gina2.Core.Models
@using Gina2.DbModels
<PageTitle>GIFNA Enter mechanisms</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                 <Container Class="ginasearch pt-5 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item0 pl-1">
                            <Heading Size="HeadingSize.Is3" data-cy="EnterMechanisms">Enter mechanisms</Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="/" data-cy="HomeLink">Home</BreadcrumbLink>
                                </BreadcrumbItem>
                                 <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#" data-cy="BreadcrumbLinkContent">Content</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#" data-cy="MechanismLink">Mechanism</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Padding="Padding.Is0">
        <MechanismCreateEditTab />
        </Container>
    </Authorized>
</AuthorizeView>
