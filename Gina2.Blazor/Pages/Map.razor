﻿@page "/map"
@page "/map/redirect_filter_data"
@using Gina2.Blazor.Helpers;
@using Domain.Search;
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.DbModels;
@using Gina2.Blazor.Models;
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
@inherits PageConfirgurationComponent
@inject IAppState AppState;
@inject ProtectedSessionStorage ProtectedSessionStore;
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Heading).ConvertHtmlToPlainText())</PageTitle>
@* <Loader IsLoading="@IsLoading" /> *@
<canvas id=mycanvas style="display:none;" height:450px; width:100%;"> </canvas>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Heading))
                        <AdminEditbut Key="@MapPageConfigurationKey.Heading" />
                    </Heading>
                    <CardText Class="color-w subtitleediticon">
                        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.SubHeader))
                        <AdminEditbut Key="@MapPageConfigurationKey.SubHeader" />
                    </CardText>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

 <div id="viewDiv1" style="background-color: #e8e9ec"> 
    <Container Fluid Padding="Padding.Is0" Class="position-relative">
         <MapLoader IsAccordianLoading="@IsAccordianLoading" />
            <div id="@MapDivId" style="padding:0;margin:auto;height:750px; width:1030px;">
            <div class="bookmark-container">
                <div class="esriBookmarks" id="bookmarks">
                    <div class="esriBookmarkList">
                        <div class="esriBookmarkTable">
                            @foreach (var item in Legends)
                            {
                                <div class="esriBookmarkItem">
                                    <div class="_mapboxLabel">
                                        <Span Class="_mapboxcolor" Style=@string.Format("background-image: url(../img/{0});" ,item.Color)></Span>
                                        <Span>@item.Value</Span>
                                    </div>
                                </div>
                            }
                            @foreach (var item in MapIndicatorConfigurations)
                            {
                                <div class="esriBookmarkItem">
                                    <div class="_mapboxLabel">
                                        <Span Class="_mapboxcolor" Style=@string.Format("background-color:{0};" ,item.ColorCode)></Span>
                                        <Span> @item.StartValue @item.Symbol @item.EndValue%</Span>
                                    </div>
                                </div>
                            }

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </Container>
    <Container Fluid Class="bg-trdot p-0 m-pt-2 mb-4 map-box g-map">
        <Div Class="Advanceds-Filter">
            <Heading Class="adv-fill d-flex justify-content-between" data-cy="FilterHeading">
                <span>
                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Filter))
                    <AdminEditbut Key="@MapPageConfigurationKey.Filter" />
                </span>
                <span id="Currentdates" style="display:none;">Date: @Currentdate</span>
            </Heading>

            <Div Class="Filters">
                <Div Class="YersFilter">
                    <Heading Class="year pb-1" data-cy="RangeTitle">
                        <AdminEditbut Key="@MapPageConfigurationKey.RangeTitle" />
                        @{
                            var value = string.Join(" -", SliderDefaultValue.ToString().Split(",").ToList());
                        }
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.RangeTitle)) @value
                        <AdminEditbut Key="@MapPageConfigurationKey.RangeTitleGroup" />
                    </Heading>
                    <AntDesign.Slider TValue="(double, double)"
                                      Value="@SliderDefaultValue"
                                      Min="@StartYear"
                                      Max="@EndYear"
                                      DefaultValue="@SliderDefaultValue"
                                      OnChange="@(async (e)=>await OnChange(e))"
                                      Marks=@SliderValue />

                </Div>
            </Div>
            <Div Class="Advanced-Filter">

                <Div Class="Filter-item _antdesign">
                    <Div Class="Filtersad">
                        <Div Class="fil-item pl-2">
                            <Label data-cy="DataLabel">
                                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Datatype))
                                @*<Tooltip data-cy="PolicyTitleEnglishLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Datatype)">
                                <Button data-cy="PolicyTitleEnglishLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                                </Tooltip>*@
                                <AdminEditbut Key="@MapPageConfigurationKey.Datatype" />
                            </Label>
                            <AntDesign.Select TItemValue="string"
                                              Mode="multiple"
                                              Placeholder="Select type(s)"
                                              TItem="string"
                                              @bind-Values="SelectedDatatype"
                                              OnSelectedItemsChanged="@((e)=>OnSelectedItemsChanged(e))"
                                              EnableSearch
                                              AllowClears
                                              MaxTagCount="@_tagCount"
                                              Style="width: 100%; margin-bottom: 0px;">
                                <SelectOptions>
                                    @foreach (var item in PolicyLookups)
                                        {
                                    <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Name Label=@item.Name />
                                        }
                                </SelectOptions>
                            </AntDesign.Select>
                        </Div>
                        <Div Class="fil-item">
                            <Div>
                                <Label data-cy="FilterThemeLabel">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Areadata))
                                    @*<Tooltip data-cy="PolicyTitleEnglishLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Areadata)">
                                    <Button data-cy="PolicyTitleEnglishLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                                    </Tooltip>*@
                                    <AdminEditbut Key="@MapPageConfigurationKey.Areadata" />
                                </Label>
                                <AntDesign.Select TItemValue="int"
                                                  Mode="multiple"
                                                  Placeholder="Filter by theme"
                                                  TItem="string"
                                                  @bind-Values="SelectedFilterBYTheme"
                                                  OnSelectedItemsChanged="@((e)=>SelectedByThemeChange(e))"
                                                  Disabled="AllowTopicFilter"
                                                  EnableSearch
                                                  AllowClear
                                                  MaxTagCount="@_tagCount"
                                                  Style="width: 100%; margin-bottom:0px;">
                                    <SelectOptions>
                                        @foreach (var item in Topics)
                                            {
                                        <AntDesign.SelectOption TItemValue="int" TItem="string" Value=@item.Id Label=@item.Name />
                                            }
                                    </SelectOptions>
                                </AntDesign.Select>
                            </Div>
                        </Div>

                        <Div Class="fil-item _antpad">
                            <Label data-cy="Indicatorlabel">
                                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Indicator))
                                @*<Tooltip data-cy="PolicyTitleEnglishLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Indicator)">
                                <Button data-cy="PolicyTitleEnglishLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                                </Tooltip>*@
                                <AdminEditbut Key="@MapPageConfigurationKey.Indicator" />
                            </Label>
                            <AntDesign.Select DataSource="@SelectDeselectLookups"
                                              TItem="@IndicatorType"
                                              TItemValue="int"
                                              Placeholder="Select/deselect indicator"
                                              ValueName="@nameof(IndicatorType.Id)"
                                              LabelName="@nameof(IndicatorType.IndicatorName)"
                                              OnSelectedItemChanged="@(async (item)=> await SelectedDeselectedChange(item))"
                                                        EnableSearch
                                                        AllowClear
                                              MaxTagCount="@_tagCount"
                                              Style="width: 100%; margin-bottom: 0px;">
                            </AntDesign.Select>
                        </Div>
                    </Div>
                </Div>
                <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="export-filter">
                    @*<Button Class="but-Filter-map" Disabled="@SearchBtnDisable" Clicked="@OnSearchClicked">Filter</Button>*@
                    <Button Loading="@ExportCsvLoading" Class="but-export-map" Clicked="@OnExportMapClicked" data-cy="ExportMapBtn">Export map</Button>
                </Div>
            </Div>
        </Div>
    </Container>
</div>
<Container Class="position-relative">
     <Paragraph Class="map-p _mapdescription" data-cy="MapPageDescription">
        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Description))
        <AdminEditbut Key="@MapPageConfigurationKey.Description" />
    </Paragraph>
    <Div Flex="Flex.JustifyContent.Between" Class="poli-flex mob-f-column">
        <Div Class="item1">
            <Heading Size="HeadingSize.Is3" Class="titleh3" data-cy="SearchResultTitle">@TotalRecordMessage</Heading>
        </Div>
    </Div>
    <FileDownload FileType="MapPage"
                  SearchRequest="@searchRequest"
                  @ref="fileDownloadChild"
                  ShowCSVPanel="@showCSVPanel">
    </FileDownload>


    @if (!string.IsNullOrEmpty(SelectDeselectIndicator) || !string.IsNullOrEmpty(SelectedTheme))
                {
    <Divider Class="hr-light" />
                }
    @if (!string.IsNullOrEmpty(SelectedTheme))
                {
    <Paragraph Class="map-p1 pt-1 d-inlines">
        <b>@((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Areadata)): </b>  @SelectedTheme
    </Paragraph>
                }
    @if (!string.IsNullOrEmpty(SelectDeselectIndicator))
                {
        <Paragraph Class="map-p1 d-inlines">
        <b>  @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.Indicator)): </b>  @SelectDeselectIndicator
    </Paragraph>
    <Paragraph Class="map-p1 _note">
        <b> Note: </b>
        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.IndicatorNotes))
        <AdminEditbut Key="@MapPageConfigurationKey.IndicatorNotes" />
    </Paragraph>
                }
    <Div Class="search-box pb-4">
        <DataGridforSearch SendCountInforomSpToParentEvent="@GetDataCount"
                           @ref="child"
                           FileType="MapPage">
        </DataGridforSearch>
        
        <Typemap />
    </Div>
</Container>
