using AutoMapper;
using Blazorise.DataGrid;
using Blazorise.Snackbar;
using Gina2.Core.Enums;
using Gina2.DbModels;
using Gina2.Services.Commitments;
using Gina2.Services.Country;
using Gina2.Services.Mechanism;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Gina2.Services.ScoreCarad;
using Gina2.Services.ScorecardTask;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using OneOf.Types;
using static Gina2.Core.Enums.ScorecardHelper;
using NuGet.Protocol;
using Gina2.Blazor.Helpers;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Linq;
using Newtonsoft.Json.Linq;
using Microsoft.Graph.Models;
using Gina2.Core.Methods;
using static Microsoft.Graph.CoreConstants;
using Radzen.Blazor.Rendering;

namespace Gina2.Blazor.Pages.ExcelPlugin
{
    [Authorize(Roles = "Admin")]
    public partial class AddInPage : IDisposable
    {
        [Inject]
        public ICountryService countryService { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Inject]
        private IProgrammeService ProgramService { get; set; }
        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Inject]
        private ICommitmentService CommitmentService { get; set; }

        [Inject]
        private IScorecardCountryService ScorecardCountryService { get; set; }
        [Inject]
        private IScorecardTaskService ScorecardTaskService { get; set; }

        [Inject]
        private ITopicService TopicService { get; set; }
        [Inject]
        private IScorecardTopicService scorecardTopicService { get; set; }
        [Inject]
        private IScoreCardService scoreCardService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IWebHostEnvironment _hostingEnvironment { get; set; }
        [Inject]
        private IMapper Mapper { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        bool todolist = true;
        private DotNetObjectReference<AddInPage> _dotNetReference;
        private List<ScorecardTask> scorecardTasks { get; set; }
        private SnackbarStack snackbarStack { get; set; }
        private bool HideOrShowPull { get; set; }
        private bool HideOrShowPush { get; set; }
        private string Type { get; set; }
        private string TaskName { get; set; }
        private bool AddTaskVisible;
        private bool IsLoading = false;
        private bool SelectAll = false;
        private string SelectScoreId { get; set; }
        public List<DbModels.Scorecard> ScorecardList { get; set; }
        public List<DbModels.Country> countries { get; set; } = new List<DbModels.Country>();
        public List<Models.AddinScore> AddinScores { get; set; } = new();
        public string ScoreSheetName { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsLoading = true;
                ScorecardList = await ScorecardService.GetAllAsync();
                countries = await countryService.GetAllCountriesWithRegion();
                //addinScores = _mapper.Map<List<Models.AddinScore>>(ScorecardList);
                foreach (var item in ScorecardList)
                {
                    AddinScores.Add(new()
                    {
                        Id = item.Id,
                        Title = item.Title,
                        IsScore = false
                    });
                }
                var mapColors = ScorecardHelper.MapColorCode;
                var colorCode = mapColors.ToArray();
                scorecardTasks = await ScorecardTaskService.GetAllAsync();
                _dotNetReference = DotNetObjectReference.Create(this);
                var sheetname = await JsRuntime.InvokeAsync<string>("SetDotNetReference", _dotNetReference);
                await JsRuntime.InvokeVoidAsync("SetColor", string.Join(",", colorCode));
                await HandleInputAndOuput(sheetname);
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            _ = base.OnAfterRenderAsync(firstRender);
        }

        bool ScoreContain(List<string> score, List<string> gifnaCsv)
        {
            var notfounterror = score.Except(gifnaCsv);
            if (notfounterror.Any())
            {
                snackbarStack.PushAsync("One of the score column has wrong value.", SnackbarColor.Danger);
                return true;
            }
            return false;
        }
        static List<string> GetPropertyList(string json, string propertyName)
        {
            List<string> result = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(json)
                .Select(obj => obj.ContainsKey(propertyName) ? obj[propertyName] : null)
                .Where(value => value != null)
                .Distinct().ToList();

            return result;
        }
        public async Task PoliciesScoreCardOutput(string type)
        {
            if (string.IsNullOrEmpty(SelectScoreId))
            {
                await snackbarStack.PushAsync("Select score card", SnackbarColor.Danger);
                return;
            }

            IsLoading = true;
            bool success = false;

            // Use try-finally to ensure resources are cleaned up properly
            try
            {
                // Fetch score order and outputs in batches or streams to reduce memory usage
                var scoreOrder = await JsRuntime.InvokeAsync<string[]>("GetScoreOrder");
                var allscores = JsonConvert.DeserializeObject<List<string>>(scoreOrder[0]);
                var NoCountriesScore = JsonConvert.DeserializeObject<List<string>>(scoreOrder[1]);
                var headers = await JsRuntime.InvokeAsync<List<string>>("TableHeaders");
                var output = await JsRuntime.InvokeAsync<List<string>>("CountryScorecardOutput");
                var mapOutput = await JsRuntime.InvokeAsync<string>("ScoreMapData");
                var populateCount = await JsRuntime.InvokeAsync<List<string>>("GetPopulateCount");
                var mapScoreColor = await JsRuntime.InvokeAsync<string>("GetMapColor");
                var tableToDisplay = await JsRuntime.InvokeAsync<List<string>>("TableToDisplay");
                // Process data streams in smaller batches to reduce memory pressure
                var outputList = ProcessLargeJsonStream<ScorecardCountyMappingItem>(mapOutput);
                var mapColors = ProcessLargeJsonStream<MapColors>(mapScoreColor);
                var mapOutputScore = outputList.Select(x => x.Score).ToList();

                // Validate scores without loading the entire dataset into memory at once
                if (ScoreContain(mapOutputScore, mapColors.Select(c => c.Score).ToList()))
                {
                    IsLoading = false;
                    return;
                }

                // Update outputList with mapped color codes and fill patterns
                var updatedOutputList = from scorecardCountry in outputList
                                        join mapColor in mapColors on scorecardCountry.Score equals mapColor.Score
                                        select new ScorecardCountyMappingItem
                                        {
                                            ScorecardName = SelectScoreId,
                                            ScorecardId = AddinScores.FirstOrDefault(s => s.Title.Equals(SelectScoreId))?.Id ?? 0,
                                            Score = scorecardCountry.Score,
                                            CountryIso3 = scorecardCountry.CountryIso3,
                                            CountryName = scorecardCountry.CountryName,
                                            Criteria = scorecardCountry.Criteria,
                                            WhoRegion = scorecardCountry.WhoRegion,
                                            Link = scorecardCountry.Link,
                                            ColorCode = mapColor.ColorCode,
                                            MapFillPattern = (int)mapColor.MapFillPatterns,
                                            EntityType = scorecardCountry.EntityType,
                                            EntityTitle = scorecardCountry.EntityTitle,
                                            ScoreOrder = allscores.IndexOf(scorecardCountry.Score)
                                        };
                var link = NavigationManager.BaseUri;
                bool IsOtherDomain = updatedOutputList.Any(s => !s.Link.Contains(link));
                bool isOtherOutput = output.Contains(link);
                if (IsOtherDomain || isOtherOutput)
                {
                    await snackbarStack.PushAsync($"Please check link column in {(IsOtherDomain ? "GIFNA main table CSV" : "GIFNA CSV table")}.\n It should not contain other domains.", SnackbarColor.Danger);
                    IsLoading = false;
                    return;
                }

                // Process the updated output in batches to avoid memory overload
                var outputListBatch = updatedOutputList.Batch(100); // Batch processing
                int index = 0;
                foreach (var batch in outputListBatch)
                {
                    index++;
                    success = await ScorecardCountryService.SaveScoreCardOutput(
                        mapScoreColor, SelectScoreId, new List<string>(), output, tableToDisplay, batch.ToList(), populateCount, string.Join(",", headers.ToList()), index
                    );

                    if (!success)
                    {
                        await snackbarStack.PushAsync("Output save failed.", SnackbarColor.Danger);
                        IsLoading = false;
                        return;
                    }
                }

                await snackbarStack.PushAsync("Saved successfully.", SnackbarColor.Success);
            }
            catch (Exception ex)
            {
                await snackbarStack.PushAsync(ex.Message, SnackbarColor.Danger);
            }
            finally
            {
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        // Method to handle large JSON streams using `JsonTextReader` for lower memory footprint
        private IEnumerable<T> ProcessLargeJsonStream<T>(string json)
        {
            using var stringReader = new StringReader(json);
            using var jsonReader = new JsonTextReader(stringReader);
            var serializer = new JsonSerializer();

            while (jsonReader.Read())
            {
                if (jsonReader.TokenType == JsonToken.StartObject)
                {
                    yield return serializer.Deserialize<T>(jsonReader);
                }
            }
        }




        public async Task PullInputdata()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectScoreId))
                {
                    await snackbarStack.PushAsync("Select score card", SnackbarColor.Danger);
                }
                else if (Type == "Policy")
                {
                    await PopulatePoliciesForScoreCard();
                }
                else if (Type == "Action")
                {
                    await PopulateActionsForScoreCard();
                }
                else if (Type == "Mechanism")
                {
                    await PopulateMechanismForScoreCard();
                }
                else if (Type == "Commitment")
                {
                    await PopulateCommitmentForScoreCard();
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
            }

            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }

        public string exception { get; set; }

        public async Task PopulatePoliciesForScoreCard()
        {
            IsLoading = true;
            var scorecard = await scoreCardService.GetAsyncByname(SelectScoreId);

            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(scorecard.Id, Type);
            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).DistinctBy(s => s).ToList();
            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).DistinctBy(x => x).ToList();

            var dt = await PolicyService.CreatePoliciesDataTable(scorecardTopics, ScorecardTopicsNames);

            var dataInputPolicy = JsonConverterUsingStream.DataTableToJson(dt);

            var result = await JsRuntime.InvokeAsync<bool>("PoliciesForScorecard", ScorecardTopicsNames, dataInputPolicy, scorecardTopics);

            if (result)
            {
                await snackbarStack.PushAsync("Pulled data successfully.", SnackbarColor.Success);
            }
            else
            {
                await snackbarStack.PushAsync("Pulled data failed.", SnackbarColor.Danger);
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }


        public async Task PopulateActionsForScoreCard()
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);
            var scorecard = await scoreCardService.GetAsyncByname(SelectScoreId);

            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(scorecard.Id, Type);
            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic.Name).Distinct().ToList();
            List<int> scorecardTopicIds = scorecardTopicsList.Select(s => s.Topic.Id).Distinct().ToList();

            var dt = await ProgramService.CreateActionDataTable(scorecardTopicIds, ScorecardTopicsNames);

            var dataInputAction = JsonConverterUsingStream.DataTableToJson(dt);//JsonConvert.SerializeObject(dt);

            var result = await JsRuntime.InvokeAsync<bool>("ActionsForScorecard", ScorecardTopicsNames, dataInputAction, scorecardTopicIds);
            if (result)
            {
                await snackbarStack.PushAsync("Pulled data successfully.", SnackbarColor.Success);
            }
            else
            {
                await snackbarStack.PushAsync("Pulled data failed.", SnackbarColor.Danger);
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);

        }

        public async Task PopulateMechanismForScoreCard()
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);
            var scorecard = await scoreCardService.GetAsyncByname(SelectScoreId);

            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(scorecard.Id, ScorecardEntityTypes.Mechanism.ToString());
            List<string> ScorecardTopicsNames = scorecardTopicsList.Select(s => s.Topic?.Name).Distinct().ToList();
            List<int> scorecardTopics = scorecardTopicsList.Select(s => s.Topic.Id).ToList();

            var dt = await MechanismService.CreateMechanismDataTable(scorecardTopics, ScorecardTopicsNames);

            var dataInputMechanism = JsonConverterUsingStream.DataTableToJson(dt);

            var result = await JsRuntime.InvokeAsync<bool>("MechanismsForScorecard", ScorecardTopicsNames, dataInputMechanism, scorecardTopics);
            if (result)
            {
                await snackbarStack.PushAsync("Pulled data successfully.", SnackbarColor.Success);
            }
            else
            {
                await snackbarStack.PushAsync("Pulled data failed.", SnackbarColor.Danger);
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }

        public async Task PopulateCommitmentForScoreCard()
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);
            var scorecard = await scoreCardService.GetAsyncByname(SelectScoreId);

            var scorecardTopicsList = await scorecardTopicService.ScorecardTopics(scorecard.Id, ScorecardEntityTypes.Mechanism.ToString());
            List<string> ScorecardTopicsNames = new();
            List<int> scorecardTopics = new();

            var dt = await CommitmentService.CreateCommitmentDataTable(scorecardTopics, ScorecardTopicsNames);

            var dataInputCommitment = JsonConverterUsingStream.DataTableToJson(dt);
            var result = await JsRuntime.InvokeAsync<bool>("CommitmentsForScorecard", ScorecardTopicsNames, dataInputCommitment);
            if (result)
            {
                await snackbarStack.PushAsync("Pulled data successfully.", SnackbarColor.Success);
            }
            else
            {
                await snackbarStack.PushAsync("Pulled data failed.", SnackbarColor.Danger);
            }
            IsLoading = false;
            await InvokeAsync(StateHasChanged);

        }


        [JSInvokable("WorkSheetName")]
        public async Task HandleInputAndOuput(string sheetName)
        {
            Type = String.Empty;
            HideOrShowPull = false;
            HideOrShowPush = false;
            if (sheetName == "Data input 1")
            {
                Type = "Policy";
            }
            else
            if (sheetName == "Data input 2")
            {
                Type = "Action";

            }
            else
            if (sheetName == "Data input 3")
            {
                Type = "Mechanism";

            }
            else
            if (sheetName == "Data input 4")
            {
                Type = "Commitment";

            }
            if (sheetName != "Data input 1" && sheetName != "Data input 2" && sheetName != "Data input 3" && sheetName != "Data input 4")
            {
                HideOrShowPull = true;
            }
            if (sheetName != "GIFNA main table CSV"
                && sheetName != "Table to display"
                && sheetName != "GIFNA CSV table"
                && sheetName != "GIFNA population count"
                && sheetName != "GIFNA map display")
            {
                HideOrShowPush = true;
            }
            StateHasChanged();
        }

        [JSInvokable("ShowErrorToast")]
        public async Task ShowErrorToast(string text)
        {
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
            await snackbarStack.PushAsync(text, SnackbarColor.Danger);
            return;
        }


        public async Task GetTopics()
        {
            var scorecardTopics = ScorecardHelper.PolicyTopicIdList;
            List<string> ScorecardTopicsNames = await TopicService.GetTopicsByIdList(scorecardTopics);
            var output = await JsRuntime.InvokeAsync<string>("GetSelectableCell", string.Join(",", ScorecardTopicsNames.ToArray()));
        }

        public async Task CreateTask()
        {
            scorecardTasks = await ScorecardTaskService.CreateAsync(TaskName);
            AddTaskVisible = false;
        }

        private void ShowModal()
        {
            AddTaskVisible = true;
        }

        public async Task CompletedTask()
        {
            scorecardTasks = await ScorecardTaskService.CreateAsync(TaskName);
        }

        private static void OnRowStyling(ScorecardTask task, DataGridRowStyling styling)
        {
            if (task.IsChecked)
                styling.Style = "background-color: #b0e1bd;";
        }

        void OnCheck(bool value, int id)
        {
            var selectTask = scorecardTasks.Where(t => t.Id == id).First();
            selectTask.IsChecked = value;
        }

        private async Task SaveCompletedTask()
        {
            scorecardTasks = await ScorecardTaskService.UpdateRangeAsync(scorecardTasks);
        }

        private async Task SelectAllScore(bool value)
        {
            foreach (var item in AddinScores)
            {
                item.IsScore = value;
            }
            SelectAll = value;
        }

        public void Dispose()
        {
            _dotNetReference?.Dispose();
        }
    }
}