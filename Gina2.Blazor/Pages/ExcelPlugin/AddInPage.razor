﻿@page "/admin/plug-in/home"

@using Gina2.DbModels;
<style>
    .logotext {
        padding-left: 6px;
        font-size: 8px;
    }

    .admin-bar {
        height: 44vh !important;
    min-height: 38vh !important;
    margin-top: 70px;
    }
    @@media only screen and (max-width: 1920px) {
        .admin-bar {
            height: 64vh !important;
            min-height: 38vh !important;
            margin-top: 70px;
        }
}
</style>
<Loader IsLoading="@IsLoading" />
<SnackbarStack @ref="snackbarStack" />

<Modal @bind-Visible="@AddTaskVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Create task</ModalTitle>
        </ModalHeader>
        <ModalBody>
            <Field>
                    <TextEdit @bind-Text="@TaskName" Placeholder="Enter the task"  />
                    @*<FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(TaskName))">Plese Enter Task Name</FieldLabel>*@
                </Field>
        </ModalBody>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@CreateTask">Save</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => AddTaskVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>



 <Container Class="_exclebody" Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-3 pb-2">
            @*@if (!string.IsNullOrEmpty(Type))
            { 
            <Heading Size="HeadingSize.Is3">Pull @Type Data</Heading>
            }*@
            
            @*<Heading Size="HeadingSize.Is3" Class="_title-card">Sugars Country Score Card</Heading>*@
            <Select TValue="string" @bind-SelectedValue="@SelectScoreId">
                    <SelectItem Value="string.Empty">Select Score card</SelectItem>
                <Repeater Items="@AddinScores">
                    <SelectItem Value="context.Title">@context.Title</SelectItem>
                </Repeater>
            </Select>
            <Div Class="mb-2 mt-2 text-center">
                <Button Disabled="@HideOrShowPull" Class="but-plug" Clicked="@PullInputdata">Pull</Button>
            </Div>
           
        </Container>
    </Card>
</Container>

<Container Class="text-center pt-2 pb-2 addinpages">
   <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="pt-2">
        <Button Disabled="@HideOrShowPush" Class="but-plug" Clicked="@(() => PoliciesScoreCardOutput("single"))">Push</Button>
   </Div>
   
    <Div> @exception</Div>
</Container>





 
