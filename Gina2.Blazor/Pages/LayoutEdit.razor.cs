﻿using AutoMapper;
using Blazorise;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core.Interface;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.Services.LayoutCustomization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using SixLabors.ImageSharp.PixelFormats;
using System.Text.RegularExpressions;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Pages
{
    public partial class LayoutEdit : PageConfirgurationComponent
    {
        [Inject]
        private ILogger<LayoutEdit> Logger { get; set; }

        [Inject]
        private ILayoutCustomizationService LayoutCustomizationService { get; set; }

        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IMapper Mapper { get; set; }

        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }

        [Inject]
        private NavigationManager navigationManager { get; set; }

        [Parameter]
        public int draftId { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [CascadingParameter]
        public SiteCustomizationDto layoutSetting { get; set; }

        public string Headerprev { get; set; }
        private const string RequiredText = "Required";
        private const string EmojiErrorText = "The text contains Emoji(s).";
        
        private long ImageSizeLimit { get; set; } = 100000;
        private string ImageErrorHeader { get; set; }
        bool _visible = false;
        private string ImageErrorFooter { get; set; }
        private FileEdit fileEditHeader { get; set; }
        private FileEdit fileEditFooter { get; set; }
        private bool isUploadingHeader = false;
        private bool isUploadingFooter = false;

        private bool isLayoutSaving = false;

        private double headerUplodedPercentage = 0;
        private double footerUplodedPercentage = 0;
        private bool modifyPublishedDraft = false;
        string currentUserId = string.Empty;
        bool IsLoading = false;
        string progressBarStrokeColor = "#1890ff";


        private bool canSaveSlider = true;


        LayoutCustomization draftToEdit = new LayoutCustomization();

        Stream fileStreamHeader { get; set; }
        Stream fileStreamFooter { get; set; }

        private _quillEditor quillDraftTitleRef;
        private _quillEditor quillFooterTextRef;
        private _quillEditor quillOrganizationLinkRef;
        private _quillEditor quillContactLinkRef;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                currentUserId = CurrentUserService.UserId;
                var currentPublisheVersion = await LayoutCustomizationService.GetPublishedDraft();

                draftToEdit = await LayoutCustomizationService.GetById(draftId);
                if (draftToEdit == null)
                {
                    navigationManager.NavigateTo("NotFound");
                    return;
                }
                if (currentPublisheVersion.Id == draftId)
                {
                    modifyPublishedDraft = true;
                }

                StateHasChanged();
            }

            _ = base.OnAfterRenderAsync(firstRender);
        }


        private async Task AddHeaderImage(FileChangedEventArgs args)
        {
            isUploadingHeader = true;

            try
            {
                var files = args.Files;
                if (files.FirstOrDefault() != null)
                {
                    var fileExtension = Path.GetExtension(files.FirstOrDefault().Name)?.ToLower();

                    if (fileExtension == ".jpg" || fileExtension == ".png")
                    {
                        foreach (var file in files)
                        {
                            if (file.Size < ImageSizeLimit)
                            {
                                await using (fileStreamHeader = file.OpenReadStream())
                                {
                                    await using MemoryStream fs = new();
                                    await fileStreamHeader.CopyToAsync(fs);

                                    fs.Position = 0; // Reset stream position for ImageSharp
                                    using (var image = SixLabors.ImageSharp.Image.Load<Rgba32>(fs))
                                    {
                                        // Validate image dimensions
                                        if (image.Width == 136 && image.Height == 42)
                                        {
                                            string filename = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                                            draftToEdit.HeaderImage = $"{Guid.NewGuid():N}-{filename}";
                                            ImageErrorHeader = string.Empty;
                                        }
                                        else
                                        {
                                            draftToEdit.HeaderImage = string.Empty;
                                            ImageErrorHeader = "Header image dimension should be 136x42px";
                                        }
                                    }
                                }
                            }
                            else
                            {
                                ImageErrorHeader = "Header image size is too large. Maximum size is 100 KB.";
                            }
                        }
                    }
                    else
                    {
                        ImageErrorHeader = "Only JPG or PNG files are allowed.";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }

            await InvokeAsync(StateHasChanged);
            isUploadingHeader = false;
        }

        private async Task AddFooterImage(FileChangedEventArgs args)
        {
            isUploadingFooter = true;

            try
            {
                var files = args.Files;
                if (files.FirstOrDefault() != null)
                {
                    var fileExtension = Path.GetExtension(files.FirstOrDefault().Name)?.ToLower();

                    if (fileExtension == ".jpg" || fileExtension == ".png")
                    {
                        foreach (var file in files)
                        {
                            if (file.Size < ImageSizeLimit)
                            {
                                await using (fileStreamFooter = file.OpenReadStream())
                                {
                                    await using MemoryStream fs = new();
                                    await fileStreamFooter.CopyToAsync(fs);

                                    fs.Position = 0; // Reset stream position for ImageSharp
                                    using (var image = SixLabors.ImageSharp.Image.Load<Rgba32>(fs))
                                    {
                                        // Validate image dimensions
                                        if (image.Width == 373 && image.Height == 86)
                                        {
                                            string filename = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                                            draftToEdit.FooterImage = $"{Guid.NewGuid():N}-{filename}";
                                            ImageErrorFooter = string.Empty;
                                        }
                                        else
                                        {
                                            draftToEdit.FooterImage = string.Empty;
                                            ImageErrorFooter = "Footer image dimension should be 373x86px";
                                        }
                                    }
                                }
                            }
                            else
                            {
                                ImageErrorFooter = "Footer image size is too large. Maximum size is 100 KB.";
                            }
                        }
                    }
                    else
                    {
                        ImageErrorFooter = "Only JPG or PNG files are allowed.";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }

            await InvokeAsync(StateHasChanged);
            isUploadingFooter = false;
        }


        private async Task SaveImageToAzureStorage(string fileName, Stream filestreamFile)
        {
            if (fileName != null && filestreamFile != null)
            {
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), Core.Constants.localFileUploadPath, fileName);
                try
                {
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await filestreamFile.CopyToAsync(fileStream);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError("Error ::", ex);
                    throw;
                }
            }
        }

        private async Task SaveAndUpdateLayout()
        {
            draftToEdit.Title = await quillDraftTitleRef.GetHTML();
            draftToEdit.FooterText = await quillFooterTextRef.GetHTML();
            draftToEdit.OrganizationLink = await quillOrganizationLinkRef.GetHTML();
            draftToEdit.ContactLink = await quillContactLinkRef.GetHTML();
            await Task.Run(() => isLayoutSaving = true);
            await InvokeAsync(() => StateHasChanged());
            if (string.IsNullOrEmpty(draftToEdit.Title))
            {
                isLayoutSaving = false;
                await InvokeAsync(StateHasChanged);
                _ = OpenValidationToaster("Title cannot be empty");
                return;
            }
            if (EmojiConstant.EmojiPattern.Contains(draftToEdit.Title))
            {
                await Task.Run(() => isLayoutSaving = false);
                await OpenValidationToaster("Title cannot contain special character");
                await InvokeAsync(StateHasChanged);
                return;
            }
            if (!string.IsNullOrEmpty(draftToEdit.HeaderImage)
                && string.IsNullOrEmpty(ImageErrorHeader) && string.IsNullOrEmpty(ImageErrorFooter))
            {
                bool hasUploadedHeader = fileStreamHeader != null;
                bool hasUploadedFooter = fileStreamFooter != null;

                if (hasUploadedHeader)
                {
                    using (fileStreamHeader)
                    {
                        await SaveImageToAzureStorage(draftToEdit.HeaderImage, fileStreamHeader);
                    }
                }
                if (hasUploadedFooter)
                {
                    using (fileStreamFooter)
                    {
                        await SaveImageToAzureStorage(draftToEdit.FooterImage, fileStreamFooter);
                    }
                }

                await LayoutCustomizationService.Update(draftToEdit, currentUserId);
                await OpenSuccessToaster("Layout updated successfully");


                if (!string.IsNullOrEmpty(draftToEdit.HeaderImage) && !string.IsNullOrEmpty(draftToEdit.FooterImage) && modifyPublishedDraft)
                {
                    MemoryCache.Set(Core.Constants.Cachekeys.CustomLayout, Mapper.Map<LayoutCustomization, SiteCustomizationDto>(draftToEdit));
                }
                if (draftToEdit.IsCurrentPublished)
                {
                    navigationManager.NavigateTo("", true);
                }
            }
            else
            {
                ImageErrorHeader = string.IsNullOrEmpty(draftToEdit.HeaderImage) ? "Image is Required" : string.Empty;
            }

            await Task.Run(() => isLayoutSaving = false);
           
            await InvokeAsync(StateHasChanged);
        }

        private void OnHeaderUploadProgress(FileProgressedEventArgs e)
        {
            headerUplodedPercentage = e.Percentage;
        }

        private void OnFooterUploadProgress(FileProgressedEventArgs e)
        {
            footerUplodedPercentage = e.Percentage;
        }
        private async Task PublishDraft(int draftId)
        {
            IsLoading = true;
            var draft = await LayoutCustomizationService.GetById(draftId);
            bool success = await LayoutCustomizationService.PublishDraft(draft, currentUserId);

            if (success)
            {
                IsLoading = false;
                MemoryCache.Set(Core.Constants.Cachekeys.CustomLayout, Mapper.Map<LayoutCustomization, SiteCustomizationDto>(draft));

                await OpenSuccessToaster("Draft published successfully");
                navigationManager.NavigateTo("", true);
            }
            else
            {
                IsLoading = false;
                await OpenErrorToaster("Draft publish failed");
            }

        }
        private void HandleCancel(MouseEventArgs e)
        {
            Headerprev = "position:fixed;";
            _visible = false;
        }
        private async Task ShowPreview(int draftId)
        {
            Headerprev = "position:relative;";
            _visible = true;
            var siteLayoutSetting = await LayoutCustomizationService.GetById(draftId);
            await JsRuntime.InvokeAsync<object>("SetLayoutPreview", siteLayoutSetting);
            await InvokeAsync(StateHasChanged);

        }

        private void OnTitleChange(string value)
        {
            draftToEdit.Title = value;
        }


        private void OnFooterTextChange(string value)
        {
            draftToEdit.FooterText = value;
        }

        private void OnOrgLinkChange(string value)
        {
            draftToEdit.OrganizationLink = value;
        }
        private void OnContactChange(string value)
        {
            draftToEdit.ContactLink = value;
        }
    }
}