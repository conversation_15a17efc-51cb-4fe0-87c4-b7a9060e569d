﻿using Blazorise;
using Blazorise.Snackbar;
using System.Collections.ObjectModel;
namespace Gina2.Blazor.Pages
{
    public partial class AboutEdit
    {

        Task OnChanged(FileChangedEventArgs e)
        {
            return Task.CompletedTask;
        }
        Snackbar snackbar;

        ObservableCollection<string> LanguageData { get; set; } = new ObservableCollection<string>() {
            "Select Language",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };
    }
}
