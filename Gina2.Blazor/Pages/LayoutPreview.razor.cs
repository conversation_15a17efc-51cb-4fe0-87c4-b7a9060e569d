﻿using Gina2.DbModels;
using Gina2.Services.LayoutCustomization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Pages
{
    public partial class LayoutPreview
    {
        [Parameter]
        public int draftId { get; set; }

        [Inject]
        private ILayoutCustomizationService LayoutCustomizationService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        private LayoutCustomization siteLayoutSetting = new LayoutCustomization();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                siteLayoutSetting = await LayoutCustomizationService.GetById(draftId);
                await JsRuntime.InvokeAsync<object>("SetLayoutPreview", siteLayoutSetting);
                StateHasChanged();
            }

            _ = base.OnAfterRenderAsync(firstRender);
        }
    }
}
