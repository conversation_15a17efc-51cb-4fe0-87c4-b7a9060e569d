using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Gina2.Core.Methods;
using Gina2.Services.Country;
using Gina2.Services.Commitments;
using Gina2.Services.ICN2;
using Gina2.Blazor.Models.AdminModel;

namespace Gina2.Blazor.Pages
{
    public partial class CommitmentsHighlights
    {
        [Inject]
        private ICommitmentService CommitmentService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IIcn2Service Icn2Service { get; set; }
        private IEnumerable<Country> Countries { get; set; } = new List<Country>();
        private IEnumerable<Icn2> Icn2 { get; set; } = new List<Icn2>();
        private IEnumerable<Icn2> FilterIcn2 { get; set; } = new List<Icn2>();

        private List<int> Years = new List<int> { 2019, 2020, 2021, 2022, 2023, 2024, 2025 };

        private List<SmartCommitment> smartCommitment = new();
        private List<Commitment> commitment1 = new();
        private List<CommitmentCountryMapItem> commitment = new();
        private List<Icn2Category> Icn2Catogries = new();
        public ApplicationUserSearchRequestModel SearchModel { get; set; } = new ApplicationUserSearchRequestModel();
        public class CommitmentHighlightDetails
        {
            public string Country { get; set; }
            public string CountryCode { get; set; }
            public int? Year { get; set; }
            public int? Icn2id { get; set; }
            public string Commitments { get; set; }
            public int? CommitmentId { get; set; }
            public int? SmartCommitmentId { get; set; }
            public List<SmartCommitmentIcn2MappingItem> Icn2 { get; set; }
            public List<SmartCommitmentSdgMappingItem> sdg { get; set; }
        }

        private List<CommitmentHighlightDetails> HightlightList = new();
        private List<CommitmentHighlightDetails> FilterHightlightList = new();
        private List<CommitmentHighlightDetails> ApplyingFilterList = new();
        private int TotalCount { get; set; }

        private string DefaultCountry = "Select Country";
        private string DefaultYear = "Year";
        private string DefaultIcn2Category = "Links to the ICN2 Framework for Action";
        private string DefaultIcn2 = "Any";
        private int DefaultIcn2Id = 0;

        private int Year { get; set; }
        private string CountryCode { get; set; }
        private int Icn2Id { get; set; }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
               _= GetCategory();
                _ = GetIC2N();
                _ = GetCountry();
                Task.WaitAll();
                await GetHighlightList();

                StateHasChanged();
            }

            _ = base.OnAfterRenderAsync(firstRender);
        }
        private async Task GetCategory()
        {
            Icn2Catogries = await Icn2Service?.GetIc2nCategoriesAsync();
        }
        private async Task GetIC2N()
        {
            Icn2 = await Icn2Service?.GetIc2nsAsync();

        }
        private async Task GetCountry()
        {
            Countries = await CountryService?.GetCountriesAsync();

        }
        private async Task GetHighlightList()
        {
            commitment = await CommitmentService.GetCommitmentsByCountryAsync();
            foreach (var item in commitment)
            {
                foreach (var smart in item.Commitment.SmartCommitments)
                {
                    CommitmentHighlightDetails newDetailshighlight = new CommitmentHighlightDetails();
                    newDetailshighlight.Country = item.Country?.Name;
                    newDetailshighlight.CountryCode = item.CountryCode;
                    newDetailshighlight.Year = item.Commitment.StartYear;
                    newDetailshighlight.CommitmentId = item.CommitmentId;
                    newDetailshighlight.SmartCommitmentId = smart.Id;
                    newDetailshighlight.Commitments = $"{item.Commitment?.Title} - {smart.Title}";
                    newDetailshighlight.Icn2 = smart.CommitmentICN2s.ToList();
                    newDetailshighlight.sdg = smart.SmartCommitmentSdgMappingItems.ToList();
                    HightlightList.Add(newDetailshighlight);
                }
            }

            FilterHightlightList = HightlightList;
        }

        private void OnChangeCountry(string countryCode)
        {
            DefaultCountry = countryCode;

        }

        private void OnChangeYear(string countryCode)
        {
            DefaultYear = countryCode;
        }

        private void OnChangeIcn2Category(int icn2CategoryId, string icn2CategoryName)
        {
            DefaultIcn2 = "Any";
            DefaultIcn2Category = icn2CategoryName;
            FilterIcn2 = Icn2.Where(x => x.CategoryId == icn2CategoryId).ToList();

        }

        private void OnChangeIcn2(int icn2Id, string icn2Name)
        {
            DefaultIcn2 = icn2Name;
            DefaultIcn2Id = icn2Id;

        }

        private void ApplyFilter()
        {
            FilterHightlightList = HightlightList
                .WhereIf(DefaultCountry != null && DefaultCountry != "Select Country" && DefaultCountry != "All", x => x.Country == DefaultCountry)
                .WhereIf(DefaultYear != null && DefaultYear != "Year" && DefaultYear != "Any", x => x.Year.ToString() == DefaultYear)
                .WhereIf(DefaultIcn2 != null && DefaultIcn2 != "Links to the ICN2 Framework for Action" && DefaultIcn2 != "Any", x => x.Icn2.Any(x => x.Icn2Id == DefaultIcn2Id)).ToList();

        }

        private async Task Reset()
        {
            DefaultCountry = "Select Country";
            DefaultYear = "Year";
            DefaultIcn2Category = "Links to the ICN2 Framework for Action";
            DefaultIcn2 = "Any";
            DefaultIcn2Id = 0;
            FilterIcn2 = new List<Icn2>();
            await GetHighlightList();
        }



    }
}