﻿using Gina2.Blazor.Helpers.PageConfigrationData;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Pages
{
    public partial class AboutUs : PageConfirgurationComponent
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        private bool isLoading = true;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                pageConfigrationCache.RefreshRequested += RefreshMe;
                await GetConfigration();
                isLoading = false;
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                StateHasChanged();
            }

            _ = base.OnAfterRenderAsync(firstRender);
        }
    }
}
