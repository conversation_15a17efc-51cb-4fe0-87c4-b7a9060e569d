﻿@page "/summaries"
@using Gina2.DbModels
@using Gina2.Blazor.Helpers.PageConfigrationData
@using System.Globalization;
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(PublishedScorecardConfigKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-4">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3" data-cy="ScoreCardTitle">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(PublishedScorecardConfigKey.Title))
                        <AdminEditbut Key="@PublishedScorecardConfigKey.Title" />
                    </Heading>
                    <Paragraph data-cy="ScoreCardparagraph" class="color-w subtitleediticon">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(PublishedScorecardConfigKey.HeaderText))
                        <AdminEditbut Key="@PublishedScorecardConfigKey.HeaderText" />
                    </Paragraph>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="gina-30 search-box">
    <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel" data-cy="ScoreCardFooterText" >
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(PublishedScorecardConfigKey.FooterText))
                <AdminEditbut Key="@PublishedScorecardConfigKey.FooterText" />
            </LayoutContent>
        </Layout>
</Container>