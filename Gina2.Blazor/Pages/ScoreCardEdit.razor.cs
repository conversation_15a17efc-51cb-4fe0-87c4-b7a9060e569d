﻿using Blazorise;
using Blazorise.Snackbar;
using Gina2.Core.Enums;
using Gina2.Core.Methods;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;

namespace Gina2.Blazor.Pages
{
    public partial class ScoreCardEdit
    {
        [Parameter]
        public int Id { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }

        [Inject]
        INotificationService NotificationService { get; set; }


        Validations ValidationCheck;


        DbModels.Scorecard Scorecard = new DbModels.Scorecard();
        Snackbar snackbar;

        private bool isLoading = true;
        private string description = string.Empty;
        private string bottomDiscrition = string.Empty;
        private string populationInfo = string.Empty;


        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                Scorecard = await ScorecardService.GetAsync(Id);

                if (Scorecard == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }

                description = Scorecard.Description;
                bottomDiscrition = Scorecard.BottomDescription;
                populationInfo = Scorecard.PopulationCountInfo;
                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        Task OnChanged(FileChangedEventArgs e)
        {
            return Task.CompletedTask;
        }

        private async Task ChangeDescription(string value)
        {
            Scorecard.Description = value;
            await InvokeAsync(StateHasChanged);
        }

        private async Task ChangeBottomDescription(string value)
        {
            Scorecard.BottomDescription = value;
            await InvokeAsync(StateHasChanged);
        }

        private async Task ChangePopulationInfo(string value)
        {
            Scorecard.PopulationCountInfo = value;
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveAsync()
        {
            if (await ValidationCheck.ValidateAll())
            {
                isLoading = true;
                await InvokeAsync(StateHasChanged);

                if (!string.IsNullOrEmpty(Scorecard.URL))
                {
                    if (RegexHelper.IsRegexMatch(Scorecard.URL, @"^[a-zA-Z0-9 ]*$") && !Scorecard.URL.Any(w => Char.IsWhiteSpace(w)) && !ScorecardHelper.ReservedURL.Contains(Scorecard.URL.ToLower()))
                    {
                        bool isURLTaken = await ScorecardService.CheckURLTaken(Scorecard);
                        if (!isURLTaken)
                        {
                            await ScorecardService.SaveAsync(Scorecard);
                            await NotificationService.Success("Saved successfully.");
                        }
                    }
                    else
                    {
                        await NotificationService.Error("URL can not have special characters or spaces or  reserved URLs like Search, Map etc.");
                    }
                }
                else
                {
                    await ScorecardService.SaveAsync(Scorecard);
                    await NotificationService.Success("Saved successfully.");
                }

                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }

        }

        private async Task OnTitleChanged(string value)
        {
            Scorecard.Title = value;
        }

    }
}
