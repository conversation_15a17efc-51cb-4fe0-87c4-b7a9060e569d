﻿@page "/countries/{CountryCode}/programmes-and-actions/{ProgrammeAndActionCode:int}"
@page "/countries/{CountryCode}/programmes-and-actions/{ProgrammeAndActionCode:int}/{VersionId:int}"
@page "/programmes-and-actions/{ProgrammeAndActionCode:int}"
@page "/programmes-and-actions/{ProgrammeAndActionCode:int}/{VersionId:int}"

@if (!isAuthenticated)
{
    <UnAuthorizedView />
}
else
{
<CascadingValue Value="@CountryCode" Name="CountryCode">
    <CascadingValue Value="@ProgrammeAndActionCode" Name="ProgrammeAndActionCode">
        <CascadingValue Value="@VersionId" Name="VersionId">
        <CountryDetails SelectedTab="programmes-and-actions" ContentName="Programme and action">
            <ProgramAndActionDetailTab/>
        </CountryDetails>
    </CascadingValue>
</CascadingValue>
</CascadingValue>
}

