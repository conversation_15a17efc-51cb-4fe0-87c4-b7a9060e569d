﻿using Gina2.Blazor.Helpers.Authenticate;
using Gina2.Core.Interface;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.MySqlRepository.Repositories;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Pages
{
    public partial class CommitmentDetails
    {
        [Inject]
        private IAuthenticateHelper AuthenticateHelper { get; set; }

        [Inject]
        private ICurrentUserService currentUserService { get; set; }

        [Inject]
        private ICommitmentRepository policyRepository { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        [Parameter]
        public int CommitmentCode { get; set; }

        [Parameter]
        public int VersionId { get; set; }

        private bool isAuthenticated;
        private string role;
        private string LoginUser;
        private string PublishedUser;
        private bool IsPublished = true;

        protected override async Task OnInitializedAsync()
        {
            isAuthenticated = true;
            (role, LoginUser) = await AuthenticateHelper.IsAuthenticated();
            var actionResult = await AuthenticateHelper.IsPublished<SmartCommitmentRevision>(p => p.Id == CommitmentCode);

            int commitmentId = actionResult.Count > 0 ? actionResult.FirstOrDefault().CommitmentId : 0;
            var commitmentLogs = await AuthenticateHelper.IsPublished<CommitmentLog>(p => p.CommitmentId == commitmentId);
            if (commitmentLogs is null || commitmentLogs.Count == 0)
            {
                isAuthenticated = false;
                return;
            }

            if (CanAccessForPublishedLog(commitmentLogs))
                return;

            if (await CanAccessForLoginUserOrRole(commitmentLogs))
                return;

            isAuthenticated = false;
        }

        private bool CanAccessForPublishedLog(List<CommitmentLog> commitmentLogs)
        {
            CommitmentLog publishedLog = VersionId > 0
                ? commitmentLogs.FirstOrDefault(v => v.CommitmentVId == VersionId)
                : commitmentLogs.FirstOrDefault(v => v.IsPublished);

            IsPublished = publishedLog?.IsPublished ?? false;
            PublishedUser = publishedLog?.UserName;

            if (IsPublished)
                return true;

            return false;
        }

        private async Task<bool> CanAccessForLoginUserOrRole(List<CommitmentLog> commitmentLogs)
        {
            var revisionLog = commitmentLogs.FirstOrDefault(v => v.CommitmentVId == VersionId);

            if (revisionLog?.UserName == LoginUser || revisionLog?.DelegatedUserName == LoginUser)
            {
                return true;
            }

            if (string.Equals(role, "admin", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            if (role.Equals("approver", StringComparison.OrdinalIgnoreCase) && revisionLog?.UserName != LoginUser)
            {
                var commitmentCountries = await policyRepository.GetCommitmentCountriesByPolicyIdAndRevisionId(CommitmentCode, VersionId);
                var approverCountries = currentUserService.ApproverCountryCode;

                if (approverCountries.Any(country => approverCountries.Contains(country)))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
