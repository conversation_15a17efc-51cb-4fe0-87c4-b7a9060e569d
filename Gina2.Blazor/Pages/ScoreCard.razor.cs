﻿using Blazorise;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.Scorecard;
using Gina2.Core.Enums;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.Services.Country;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace Gina2.Blazor.Pages
{
    public partial class ScoreCard
    {
        [Parameter]
        public int? Id { get; set; }

        [Parameter]
        public string ScorecardURL { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        public AuthenticationStateProvider AuthenticationStateProvider { get; set; }

        [Inject]
        public ICountryService countryService { get; set; }

        [Inject]
        public IScorecardCountryService ScorecardCountryService { get; set; }
        [Inject]
        private ILogger<ScoreCard> _logger { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }

        [Inject]
        private IJSRuntime _JSRuntime { get; set; }

        public DbModels.Scorecard Scorecard { get; set; }

        public List<ScorecardScore> Scores { get; set; }

        public List<ScorecardCountyMappingItem> ScorecardCountyMappingItems = new();
        public DbModels.TableToDisplay tableToDisplay = new DbModels.TableToDisplay();
        public List<string> Headers = new List<string>();
        public List<string> ScoreCountryHeaders = new List<string>();
        public List<Dictionary<string, string>> Content = new List<Dictionary<string, string>>();
        public IEnumerable<Country> countries = new List<Country>();
        public TableScore tableScore = new TableScore();
        private bool isLoading = true;
        private ScoreCountryTable scoreCountryTable = new ScoreCountryTable();
        private List<ScoreCardDynamic> expandoObjects = new List<ScoreCardDynamic>();
        private List<ScoreCardDynamic> scoreCountryPopup = new List<ScoreCardDynamic>();
        private List<MapColors> scoreMapDatas = new List<MapColors>();
        private string ScoreCountryName { get; set; }
        private string ScoreName { get; set; }
        private string editLink => $"scorecards/{Id}/edit";
        double width = 0;
        private string scoreClassTable = string.Empty;
        private string scorestyleTable = string.Empty;
        private bool IAuthorizePublish { get; set; }
        private AuthenticationState authenticationState;
        private async Task Editmenu(string name)
        {
            await _JSRuntime.InvokeVoidAsync("toggleEdits", name);
            StateHasChanged();
        }

        protected override async Task OnInitializedAsync()
        {
            authenticationState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await _JSRuntime.InvokeVoidAsync("loadArcGIS");

                if (!Id.HasValue)
                {
                    Scorecard = await ScorecardService.GetScorecardByURL(ScorecardURL);
                    if (Scorecard == null)
                    {
                        NavigationManager.NavigateTo("not-found", true, true);
                        return;
                    }
                    IAuthorizePublish = Scorecard.IsPublished;
                    if (Scorecard.ScoreDetail != null)
                    {
                        var scoreMapDetail = JsonConvert.DeserializeObject<List<MapColors>>(Scorecard.ScoreDetail);
                        scoreMapDatas = scoreMapDetail.Where(s => !string.IsNullOrEmpty(s.ColorCode) && s.MapFillPatterns != 0).ToList();
                    }
                    tableToDisplay = await ScorecardService.GetTableToDisplayContent(Scorecard.Id);
                    countries = await countryService.GetSimpleCountryList();
                    scoreCountryTable = await ScorecardService.GetScoreCountryToTableContent(Scorecard.Id);
                    if (scoreCountryTable != null)
                    {
                        expandoObjects = JsonConvert.DeserializeObject<List<ScoreCardDynamic>>(scoreCountryTable.Content);
                        ScoreCountryHeaders = JsonConvert.DeserializeObject<List<string>>(scoreCountryTable.Header);
                    }
                    //Headers = tableToDisplay.Header;
                    if (tableToDisplay != null)
                    {
                        Headers = JsonConvert.DeserializeObject<List<string>>(tableToDisplay.Header);
                        scoreClassTable = Headers.Count > 6 ? "scorecard-scroller" : "";
                        if (Headers.Count == 3)
                        {
                            scorestyleTable = "width:auto;";
                        }
                        else if (Headers.Count == 4)
                        {
                            scorestyleTable = "width:100%;";
                        }
                        else
                        {
                            scorestyleTable = $"width:{Headers.Count * 280}px";
                        }

                        Content = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(tableToDisplay.Content);
                    }
                    foreach (var item2 in Content)
                    {
                        if (!string.IsNullOrEmpty(item2[Headers[1]]))
                        {
                            item2.Add("Code", SetUpCountryName(item2[Headers[1]]));
                        }
                    }
                }
                else
                {
                    Scorecard = await ScorecardService.GetAsync(Id.Value);
                }
                if (Scorecard != null && Scorecard.PopulationPercentage.HasValue)
                {
                    width = Scorecard?.PopulationPercentage.Value ?? 0;
                }
                _ = BuildScores();
                isLoading = false;
                await InvokeAsync(StateHasChanged);
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        private string SetUpCountryName(string scorecardCountryList)
        {
            var listCountry = scorecardCountryList.Split(",").ToList();
            string scoreCountryOutput = string.Join("|", scorecardCountryList.Split(",").ToList());
            foreach (var item in listCountry)
            {
                string iso3 = item.Trim().ToLower();
                string name = countries.FirstOrDefault(c => c.Iso3Code.Trim().ToLower().Equals(iso3))?.Name;
                if (name != null)
                {
                    scoreCountryOutput = scoreCountryOutput.Replace(item, name);
                }
            }
            return scoreCountryOutput;
        }

        private async Task BuildScores()
        {
            if (Scorecard.ScorecardCountyMappingItems.Any())
            {
                List<ScorecardScore> scores = new();
                foreach (IGrouping<string, DbModels.ScorecardCountyMappingItem> group in Scorecard.ScorecardCountyMappingItems.GroupBy(x => x.Score))
                {
                    string item = group.Key;
                    string description = string.Empty;
                    string title = item;

                    if (item.Contains(":"))
                    {
                        var splits = item.Split(":");

                        if (splits.Length >= 2)
                        {
                            description = splits[1];
                            title = splits[0];

                            if (title.Contains('_'))
                            {
                                title = title.Replace("_", "");
                            }
                        }
                    }

                    int? pattern = group.FirstOrDefault()?.MapFillPattern;
                    string style = pattern == 2 ? "stripes" : pattern == 3 ? "dots" : "";
                    scores.Add(new ScorecardScore
                    {
                        Score = group.Key,
                        Title = title,
                        Pattern = pattern,
                        StyleClass = $"_score_icons {style}",
                        ScoreOrder = group.FirstOrDefault()?.ScoreOrder,
                        Description = description,
                        ColorCode = group.First().ColorCode,
                        ScorecardCountryMapId = group.First().Id,
                        countryIsoCode = group.DistinctBy(x => x.CountryIso3).Select(x => new CountryIso() { Countries = x.CountryName, Iso3Code = x.CountryIso3 }).ToList(),
                        CountryColors = group.GroupBy(s => s.CountryIso3).Select(d => new CountryColor
                        {
                            ISOCode = d.Key,
                            ColorCode = d.First().ColorCode,
                            Country = d.First().CountryName,
                            StripePattern = d.First().MapFillPattern.Value
                        }).Distinct().ToList(),

                        CountryWisePolicies = group.GroupBy(w => new { w.CountryIso3, w.CountryName })
                        .Select(s => new CountryWiseEntity
                        {
                            CountryName = s.Key.CountryName,
                            CountryCode = s.Key.CountryIso3,
                            MapEntities = s.Select(s => new MapEntity
                            {
                                EntityId = s.EntityId,
                                EntityTitle = s.EntityTitle,
                                EntityType = s.EntityType,
                            }).ToList(),
                        }).ToList()
                    });
                }

                Scores = scores.OrderBy(s => s.ScoreOrder).ToList();
                try
                {
                    await _JSRuntime.InvokeVoidAsync("scorecardMap.initMap", Scores);
                    await InvokeAsync(StateHasChanged);
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error ::", ex);
                }

            }
        }

        public async Task ExportCSV()
        {
            var data = Scorecard.ScorecardCountyMappingItems.Select(x => new {
                Score = x.Score,
                CountryName = x.CountryName,
                EntityTitle = x.EntityTitle,
                ScorecardName = x.ScorecardName,
                EntityType = x.EntityType,
                Criteria = x.Criteria,
                CountryIso3 = x.CountryIso3,
                WhoRegion = x.WhoRegion,
                Link = x.Link
            }).ToList();

            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data);
                await _JSRuntime.InvokeVoidAsync("saveAsFile", $"gina2_country_scorecard.csv", fileData);
            }
            StateHasChanged();
        }
        private Modal modalRef;

        private async Task ShowScorecardCountry(string score, string isoCode)
        {
            ScoreCountryName = string.Empty;
            ScoreName = string.Empty;

            ScorecardCountyMappingItems = Scorecard.ScorecardCountyMappingItems.Where(e => e.CountryIso3 == isoCode && e.Score == score).ToList() ?? new List<ScorecardCountyMappingItem>();


            if (ScorecardCountyMappingItems.Any())
            {
                ScorecardCountyMappingItem findFirstCountry = ScorecardCountyMappingItems.FirstOrDefault();
                ScoreCountryName = findFirstCountry.CountryName;
                ScoreName = findFirstCountry.Score;
            }
            await InvokeAsync(modalRef.Show);
            await _JSRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");
        }

        private Task HideModal()
        {
            return modalRef.Hide();
        }

        private async Task exportMap()
        {
            await _JSRuntime.InvokeVoidAsync("exportScorecardMap", null);
        }
    }
}