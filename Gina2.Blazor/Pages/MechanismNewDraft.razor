﻿@page "/mechanismnewdraft"
@using Domain.Enum

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/view-polic.png);">
        <Div class="container pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex pt-3 mobile-col">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">New Draft Mechanism</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="#">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem>
                            <BreadcrumbLink To="policies">Mechanism</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">New Mechanism</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
                <Div Class="item2">
                    <Button Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem href="mechanismviewpublished">View published</DropdownItem>
                            <DropdownItem href="mechanismsmoderate">Moderate</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </Div>
            </Div>

        </Div>
    </Card>
</Container>
<Container Fluid Class="newdraft" Padding="Padding.Is0">
    <Container Padding="Padding.Is0" Class="pt-6 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new policy</Heading>
        <Divider Class="divi-blue" />
    </Container>

    <Container Class="form-newd">
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Title<Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Enter policy title here..."></TextEdit>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Type<Span>*</Span></FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@TypeOfPolicyData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            </Fields>
            <Fields>
            <Field>
                <FieldLabel>Country(ies)</FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@LanguageData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
        </Fields>
 
        <Fields>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Start Date</FieldLabel>
                <GinaDate />
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>End Date</FieldLabel>
                <GinaDate />
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Published Date</FieldLabel>
                <GinaDate />
            </Field>
        </Fields>
        
            <Field>
                <FieldLabel>Lead government agency in mechanism</FieldLabel>
                <TextEdit Placeholder="Lead government agency in mechanism"></TextEdit>
            </Field>
       
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Government</Tab>
                <Tab Name="Tab2">Bilateral and d...</Tab>
                <Tab Name="Tab3">UN agencies</Tab>
                <Tab Name="Tab4">International N...</Tab>
                <Tab Name="Tab5">Intergovernm...</Tab>
                <Tab Name="Tab6">National NG...</Tab>
                <Tab Name="Tab7">Research / A...</Tab>
                <Tab Name="Tab8">Private Sector</Tab>
                <Tab Name="Tab9">Other</Tab>
            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the government sector(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab2">
                    Content for profile.
                </TabPanel>
                <TabPanel Name="Tab3">
                    Content for messages.
                </TabPanel>
                <TabPanel Name="Tab4">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab5">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab6">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab7">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab8">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab9">
                    Content for settings.
                </TabPanel>
            </Content>
        </Tabs>
    </Container>

    <Container Class="form-newd mt-4">
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Coordination</Tab>
                <Tab Name="Tab2">Monitoring</Tab>

            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Coordination</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                </TabPanel>
                <TabPanel Name="Tab2">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Monitoring</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                </TabPanel>
         <Field Class="pt-2">
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Mandate</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Full HTML</SelectItem>
                        <SelectItem Value="2">Plain text</SelectItem>
                        <SelectItem Value="3">PHP code</SelectItem>
                    </Select>
                    <Tooltip Text="Disable rich-text More information about text formats">
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
</Tooltip>
                </Div>
            </Div>
            <RextEditors />
        </Field>
            </Content>
        </Tabs>
    </Container>
    <Container Class="mt-4 w-98 mo-m-0">
        <Row>
            <Column Class="form-newd mr-1 mo-mr-0 w-100">
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Topics</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Button Class="but-gray">Clear</Button>
                    </Div>
                </Div>
                <Divider Padding="Padding.Is0" Class="m-0" />
                <GinaTreeView />
            </Column>
            <Column Class="form-newd gina-new ml-1 w-100">
                <FieldLabel Class="gina-bold">Link to policy(ies)</FieldLabel>
                <Divider />
                <Addons Class="pb-3">
                    <Addon AddonType="AddonType.Start">
                        <Button Color="Color.Light">
                            <Icon Name="IconName.Search" />
                        </Button>
                    </Addon>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit Placeholder="Some text value..." />
                    </Addon>
                </Addons>
                <Repeater Items="@ListOfActionRecords">
                    <Check TValue="bool">@context</Check>
                </Repeater>

                <FieldLabel Class="gina-bold">Link to action(s)</FieldLabel>
                <Divider />
                <Addons Class="pb-3">
                    <Addon AddonType="AddonType.Start">
                        <Button Color="Color.Light">
                            <Icon Name="IconName.Search" />
                        </Button>
                    </Addon>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit Placeholder="Some text value..." />
                    </Addon>
                </Addons>
                <Repeater Items="@ListOfActionRecords">
                    <Check TValue="bool">@context</Check>
                </Repeater>
            </Column>
        </Row>
    </Container>

    <Container Class="form-newd mt-4">
        <Field >
            <FieldLabel>Lessons learnt</FieldLabel>
            <TextEdit Placeholder="Enter policy title here..."></TextEdit>
        </Field>
        <Field>
            <FieldLabel>URL link</FieldLabel>
            <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
            <TextEdit Placeholder="Enter policy title here..."></TextEdit>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Notes</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>

            <RextEditors />
        </Field>
        
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Menu settings <FieldHelp>Not in menu</FieldHelp></Tab>
                <Tab Name="Mec2">Comment <FieldHelp>settings</FieldHelp></Tab>
                <Tab Name="Mec3">URL path settings <FieldHelp>No alias</FieldHelp></Tab>
                <Tab Name="Mec4">Printer, email and <FieldHelp>PDF versions</FieldHelp></Tab>
                <Tab Name="Mec5">Authoring information <FieldHelp>By <EMAIL></FieldHelp></Tab>
                <Tab Name="Mec6">Publishing options <FieldHelp>Draft (Current)</FieldHelp></Tab>
            </Items>
            <Content>
                <TabPanel Name="Tab1" Class="pt-2">
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemMech">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                </TabPanel>
                <TabPanel Name="Mec2" Class="pt-2">
                        <Radio TValue="string" Group="Open" Value="@("Open")">Open</Radio>
                        <FieldHelp>Users with the "Post comments" permission can post comments.</FieldHelp>
                        <Radio TValue="string" Group="Closed" Value="@("Closed")">Closed</Radio>
                        <FieldHelp>Users cannot post comments.</FieldHelp>
                </TabPanel>
                 <TabPanel Name="Mec3">
                        <Field>
            <FieldLabel>URL alias</FieldLabel>
            <TextEdit Placeholder="Enter policy title here..."></TextEdit>
            <FieldHelp>Optionally specify an alternative URL by which this content can be accessed. For example, type "about" when writing an about page. Use a relative path and don't add a trailing slash or the URL alias won't work.</FieldHelp>
        </Field>
                </TabPanel>
                <TabPanel Name="Mec4" Class="pt-2">
                   <FieldLabel>Printer-friendly version</FieldLabel>
                   <Check TValue="bool">Show link</Check>
                   <Check TValue="bool">Show link in individual comments</Check>
                   <Check TValue="bool">Show Printer-friendly URLs list</Check>
                   <FieldLabel>PDF version</FieldLabel>
                   <Check TValue="bool">Show link</Check>
                   <Check TValue="bool">Show link in individual comments</Check>
                   <Check TValue="bool">Show Printer-friendly URLs list</Check>
                   <Field>
                       <FieldLabel>Paper size</FieldLabel>
                       <FieldHelp>Choose the paper size of the generated PDF.</FieldHelp>
            <Select TValue="int">
                        <SelectItem Value="0">Unchanged</SelectItem>
                        <SelectItem Value="1">A0</SelectItem>
                        <SelectItem Value="2">A1</SelectItem>
                        <SelectItem Value="3">A2</SelectItem>
                    </Select>
            
                   </Field>
                </TabPanel>
                <TabPanel Name="Mec5" Class="pt-2">
                   <Field>
                       <FieldLabel>Authored by</FieldLabel>
                       <FieldHelp>Leave blank for Anonymous.</FieldHelp>
            <TextEdit Placeholder="Enter here..."></TextEdit>
            
                   </Field>
                   <Field>
                       <FieldLabel>Authored on</FieldLabel>
                       <FieldHelp>
Format: 2022-05-26 13:31:45 +0200. The date format is YYYY-MM-DD and +0200 is the time zone offset from UTC. Leave blank to use the time of form submission.</FieldHelp>
            <TextEdit Placeholder="Enter here..."></TextEdit>
            
                   </Field>
                </TabPanel>
                <TabPanel Name="Mec6" Class="pt-2">
                   <Check TValue="bool">Promoted to front page</Check>
                   <Check TValue="bool">Sticky at top of lists</Check>
                   <Field>
                       <FieldLabel>Moderation notes <Span>*</Span></FieldLabel>
                       <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivations.</FieldHelp>
            <TextEdit Placeholder="Enter here..."></TextEdit>
            
                   </Field>
                   <Field>
                       <FieldLabel>Submission status</FieldLabel>
                       <FieldHelp>Submission status</FieldHelp>
            <Select TValue="int">
                        <SelectItem Value="0">Draft (Current)</SelectItem>
                        <SelectItem Value="1">Delegated</SelectItem>
                        <SelectItem Value="2">Request for correction</SelectItem>
                        <SelectItem Value="3">Needs Review</SelectItem>
                        <SelectItem Value="4">Published</SelectItem>
                    </Select>
                   </Field>
                </TabPanel>
            </Content>
        </Tabs>
    </Container>
    <Container Class="mt-4 pb-6">
        <Button Class="but-blues" Clicked="@(()=>snackbar.Show())">Save</Button>
        <br />
        <Heading Class="alert-warn mt-3">New content: Your draft will be placed in moderation.</Heading>
        <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
            <SnackbarBody>
                New content: Your draft will be placed in moderation.
            </SnackbarBody>
        </Snackbar>
    </Container>
</Container>
@code {

}
