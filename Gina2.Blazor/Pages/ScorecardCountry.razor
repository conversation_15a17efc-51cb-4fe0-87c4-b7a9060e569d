﻿@page "/summary/{ScorecardCountryURL}/data"
@using Gina2.Blazor.Helpers
@using Gina2.Blazor.Models.Scorecard
@using Gina2.DbModels
@using System.Dynamic
@using Gina2.Core.Methods;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Ra<PERSON>zen;
@using static Gina2.Core.Enums.ScorecardHelper;
@inherits PageConfirgurationComponent
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardCountryPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@isLoading" />

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-4 pb-4">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pt-3 pb-3 pl-2">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardCountryPageConfigurationKey.Title))
                        <AdminEditbut Key="@ScorecardCountryPageConfigurationKey.Title" />
                    </Heading>
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardCountryPageConfigurationKey.Description))
                        <AdminEditbut Key="@ScorecardCountryPageConfigurationKey.Description" />
                    </Heading>
                    @*<Breadcrumb Class="bread-crumb">
                    <BreadcrumbItem>
                    <BreadcrumbLink To="/">Home</BreadcrumbLink>
                    </BreadcrumbItem>
                    </Breadcrumb>*@
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="pt-5 m-pt-2 pl-2 pr-2 adminuser">
    <Div Flex="Flex.JustifyContent.Between" Class="poli-flex">
        <Div Class="item1">
            <Heading Size="HeadingSize.Is1" Class="Headingh3">@Scorecard?.Title</Heading>
        </Div>
        <Div Class="item2 _but_hig">
            <Button Loading="@ExportCsvLoading" Class="but-yellow" Clicked="ExportCSV"><Icon Class="fas arrow-bottom" /> Export</Button>
        </Div>
    </Div>

    <Layout Class="search-box pt-3 pb-3 mob-layout">
        <Layout Class="left-layout DataGrids over-line">
            <LayoutContent>
                <Div Class="table-responsive-xxl _ScorecardCountry" style="padding:5px;">
                    <Table Bordered>
                        <TableHeader>
                            <TableRow>
                                @foreach (var propertyName in columnNames)
                                {
                                    @if (!propertyName.Equals("Link"))
                                    {
                                        string tableWidth = "auto";
                                    @if (propertyName.Equals("Type") || propertyName.Equals("WHO Region") ||
                                   propertyName.Equals("ISO 3"))
                                    {
                                        tableWidth = "100px";

                                    }
                                    //<TableHeaderCell Style="@($"width:{tableWidth}; cursor:pointer;")" Clicked="@(() => SortingScoreCountryTable(propertyName))">
                                    <TableHeaderCell Style="@($"cursor:pointer;")" Clicked="@(() => SortingScoreCountryTable(propertyName))">
                                        @propertyName
                                        @if (Direction == 0 && SelectedColumn == propertyName)
                                        {
                                            <i class="fas fa-sort-down"></i>
                                        }
                                        else if (Direction == 1 && SelectedColumn == propertyName)
                                        {
                                            <i class="fas fa-sort-up"></i>
                                        }
                                    </TableHeaderCell>
                                    }
                                }
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            @foreach (var propertyName in FullList)
                            {
                                <TableRow>
                                    @foreach (var item in columnNames)
                                    {
                                        @if (!item.Equals("Link"))
                                        {
                                            @if (item.Equals("Title"))
                                            {
                                                <TableRowHeader>
                                                <NavLink Class="_linkhover" href=@propertyName["Link"].ToString().Replace("\"","")>@propertyName[item]</NavLink>
                                                </TableRowHeader>
                                            }
                                            else if (item.Equals("Score"))
                                            {
                                                <TableRowHeader>@((MarkupString)@propertyName["Score"].ToString())</TableRowHeader>
                                            }
                                            else
                                            {
                                                <TableRowHeader>@((MarkupString)propertyName[item].ToString())</TableRowHeader>
                                            }
                                        }
                                    }
                                </TableRow>
                            }
                        </TableBody>
                    </Table>

                </Div>
                <Div Flex="Flex.JustifyContent.Between">
                    @if (expandoObjects.Any())
                    {
                        <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">

                            <Div>
                                @((PageItems * (Convert.ToInt16(currentPage) - 1) + 1)) -
                                @(Math.Min(((Convert.ToInt16(currentPage) - 1) * PageItems) + Convert.ToInt16(PageItems), @expandoObjects.Count()))
                                of @expandoObjects.Count() data items
                            </Div>
                        </Column>
                        <Column Flex="Flex.JustifyContent.End" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <div class="col-auto d-flex flex-row">
                                <ul class="pagination">
                                    <li class="page-item @PreviousButtonDisable" @onclick="@(()=>SetActive("1"))"><a class="page-link">First</a></li>
                                    <li class="page-item @PreviousButtonDisable"><a class="page-link" Disabled="@IsPageNavigationDisabled(PREVIOUS)" @onclick="Previous">Prev</a></li>
                                    @foreach (var pageNumber in GetPageNumbers())
                                    {
                                        var pageNumberAsString = pageNumber.ToString();
                                      <PaginationItem @key="pageNumberAsString" Active="@IsActive(pageNumberAsString)">
                                            <PaginationLink Page="@pageNumberAsString" Clicked="SetActive">
                                                @pageNumberAsString
                                            </PaginationLink>
                                        </PaginationItem>
                                    }
                                    
                                    <li class="page-item d-inline-block d-sm-none">
                                        <select id="0HMUS5FQP0113" class="form-select" _bl_c89377a2-72c8-4991-9a28-76211c2e2b53="">
                                            <option value="1" selected="">1</option></select>
                                    </li>
                                    <li class="page-item @NextButtonDisabled"><a class="page-link" Disabled="@IsPageNavigationDisabled(NEXT)" @onclick="Next">Next</a></li>
                                    <li class="page-item @NextButtonDisabled" @onclick="@(()=>SetActive(EndPage.ToString()))"><a class="page-link" Disabled="@IsPageNavigationDisabled(NEXT)">Last</a></li>
                                    <li class="page-item mb-0 ms-3">
                                        <Select TValue="int" SelectedValueChanged="@OnSelectedValueChanged">
                                            <SelectItem Value="50">50</SelectItem>
                                            <SelectItem Value="100">100</SelectItem>
                                            <SelectItem Value="250">250</SelectItem>
                                            <SelectItem Value="500">500</SelectItem>
                                        </Select>
                                    </li>
                                    <div class="form-group my-auto mb-0 ms-2 d-none d-md-inline-block"></div>
                                </ul>
                            </div>
                        </Column>
                    }
                </Div>
            </LayoutContent>
        </Layout>
    </Layout>
</Container>



@code
{

}