using AntDesign;
using AutoMapper;
using Blazorise;
using Blazorise.Snackbar;
using Domain.Terms;
using Domain.Vocabulary;
using Force.DeepCloner;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Core.Constant;
using Gina2.Core.Interface;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.Services.Commitments;
using Gina2.Services.Country;
using Gina2.Services.ICN2;
using Gina2.Services.PartnerCategorys;
using Gina2.Services.Topic;
using Gina2.Services.Vocabulary;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Text.RegularExpressions;
using static Gina2.Core.Constants;


namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class DoubleTree : PageConfirgurationComponent
    {
        [Inject]
        public IVocabularyService VocabularyService { get; set; }

        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }

        [Inject]
        public IIcn2Service Icn2Service { get; set; }
        [Inject]
        public IPartnerCategoryService PartnerCategoryService { get; set; }
        [Inject]
        public ICommitmentService CommitmentService { get; set; }

        [Inject]
        private ILogger<DoubleTree> Logger { get; set; }

        [Inject]
        public ITopicService TopicService { get; set; }
        [Inject]
        private IMapper Mapper { get; set; }
        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Parameter]
        public string Vocabulary { get; set; }

        private Blazorise.Modal modalRef;

        private Blazorise.Modal editModalRef;
        public IEnumerable<TopicParent> AllParentTopics { get; set; } = Enumerable.Empty<TopicParent>();
        public IEnumerable<Icn2> AllICN2s { get; set; } = Enumerable.Empty<Icn2>();
        public IEnumerable<Country> AllCountries { get; set; } = Enumerable.Empty<Country>();
        public IEnumerable<CountryGroupMapping> AllCountryGroupMapping { get; set; } = Enumerable.Empty<CountryGroupMapping>();
        public IEnumerable<Partner> AllPartners { get; set; } = Enumerable.Empty<Partner>();
        public IEnumerable<Sdg> AllSDGs { get; set; } = Enumerable.Empty<Sdg>();
        public IEnumerable<CountryRegionMapItem> AllCountryRegionMap { get; set; } = Enumerable.Empty<CountryRegionMapItem>();
        public IEnumerable<CountryIncomeGroupMapItem> AllCountryIncomeGroupMapItem { get; set; } = Enumerable.Empty<CountryIncomeGroupMapItem>();
      

        public List<GTreeNode> TopicList { get; set; } = new List<GTreeNode>();
        private List<int> DoubleTreeRender { get; set; } = new List<int>() { 1 };
        private Term TermDetail { get; set; } = new();
        private Term OriginalTermDetail { get; set; } = new();
        private VocabularyModel selectedVocabulary = new();
        private bool isLoadingTerms = true;
        private bool isSavingTerm = false;
        private bool showAddEditTermModal;
        private bool isAddingTermMode = false;
        private bool EnableDoubleTree = false;
        private string IsSingleOrMultipleDoubleTree = String.Empty;
        private bool showAddEditChildModal = false;
        private bool isAddUpdateChild = false;
        private bool ShowTopicChild = false;
        private bool ShowPartnerOricn2Child = false;
        private bool HideAddButton = false;
        private bool HideChildAddButton = false;
        private bool DeleteVisible = false;
        private bool ChildVisible = false;
        private bool ExportCsvLoading { get; set; }
        private bool ExportCsvDisable { get; set; }
        private TreeNode<Term> DeleteTopic { get; set; } = new TreeNode<Term>() { DataItem = new Term() };
        private bool KeepParent { get; set; }
        private int TreeChildId { get; set; }
        private List<DbModels.Region> Regions { get; set; } = new();
        private SnackbarStack SnackbarStack { get; set; } = new();
        public List<string> CsvData { get; set; } = new();
        public IEnumerable<string> SelectedCountries { get; set; } = Enumerable.Empty<string>();
        public string CsvKeys { get; set; }
        private bool DeleteSingleData { get; set; }
        public List<Term> Terms { get; set; } = new();
        public List<Term> AddTerms { get; set; } = new();
        public List<Term> ShowInSearchTerms { get; set; } = new();
        public List<Term> SearchingTerms { get; set; } = new();
        public List<Term> SelectTreeTerms { get; set; } = new();
        public List<Term> SearchTreeTerms { get; set; } = new();
        public List<Topic> SelectTopicTerms { get; set; } = new();
        public List<Term> TopicTerms { get; set; } = new();
        private List<Sdg> SdgData = new List<Sdg>();
        //private List<Icn2Category> icn2Categories = new List<Icn2Category>();
        //private List<PartnerCategory> partnerCategories = new List<PartnerCategory>();
        private string SelectNewOrExistChild = "new";
        private List<string> CountryStatus = new List<string>();
        private int leftPadding = 25;
        private string TermTitle = String.Empty;
        private string SelectLeftOrRight { get; set; }
        private bool IsMultipleOrSingle { get; set; }
        public IEnumerable<string> items = Enumerable.Range(0, 333000).Select(i => i.ToString()).ToArray();
        private bool ShowTreeCheckBox => Vocabulary.Equals(Gina2.Core.Constants.VocabularyTables.Topic);
        private bool showTopicsToSearchTree = false;
        private bool IsScorllSearch { get; set; }
        //private PageConfirgurationComponent pageConfirgurationComponent = new PageConfirgurationComponent();
        private bool IsAllCheck { get; set; } = true;
        private bool IsCheckable { get; set; }
        private string[] Expandkeys = new string[] { };
        private string[] CheckedKeys { get; set; }
        private List<string> CheckedKeyList { get; set; } = new();
        private Term SelectTerm { get; set; }
        private List<Term> RemovableTerm = new List<Term>();
        private string IsLeftOrRight { get; set; }
        private bool IsValidateName { get; set; }
        private List<string> ValidateTermName { get; set; } = new();
        private Term EditChild = new Term();
        private Term ChildDetail = new Term();
        private bool IsEditCountry { get; set; }
        private bool IsEditChild { get; set; }
        private string SearchTopic = string.Empty;
        private string SearchBindTopic = string.Empty;
        private int SearchBindTopicId = 0;
        private string SearchClass = string.Empty;
        private Term BindSearchTerm = new();
        private List<Term> LeftTree { get; set; } = new List<Term>();
        private List<Term> RightTree { get; set; } = new List<Term>();
        private int SelectedTerm { get; set; }
        private string LeftOrRight { get; set; }
        private string Left = "LeftTree";
        private string Right = "RightTree";
        private bool IsCheck { get; set; }
        private bool IsSelectedCheck { get; set; }
        private Tree<Term> topicTree = new();
        private int SetIsIndeterminate { get; set; }
        private bool Indeterminate { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (IsScorllSearch)
            {
                await JsRuntime.InvokeVoidAsync("ScrollToView", $"_searchscroll{SearchBindTopicId}");
                IsScorllSearch = false;
            }
            if (firstRender)
            {
                isLoadingTerms = true;

                AllParentTopics = await TopicService.GetAllParentTopicsAsync();
                //await JsRuntime.InvokeAsync<object>("HideShowDoubleTree", EnableDoubleTree);
                ShowPartnerOricn2Child = Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic
                    //|| Vocabulary == Gina2.Core.Constants.VocabularyTables.Country
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.CountryGroup
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.Sdg
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.IncomeGroup
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.Partners
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2Category ? false : true;

                selectedVocabulary = await VocabularyService.GetByTableNameAsync(Vocabulary);
                await LoadCountryStatus();
                TermTitle = selectedVocabulary.Name;
                HideChildAddButton = HideAddButton = Vocabulary == Gina2.Core.Constants.VocabularyTables.Partners
                    || Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2 ? false : true;
                if (Vocabulary.Equals(Gina2.Core.Constants.VocabularyTables.Country))
                {
                    Regions = await CountryService.GetRegionsAsync();

                }
                await ListTermsAsync();
                //CheckedKeyList = await VocabularyService.GetSelectedTopicSearchasync();
                //CheckedKeys = CheckedKeyList.Select(c => c.ToString()).ToArray();
                isLoadingTerms = false;
                _ = Task.Run(async () => await InvokeAsync(StateHasChanged));
            }
        }

        private async Task LoadCountryStatus()
        {
            CountryStatus = await CountryService.GetCountryStatus();
        }

        private async Task ListTermsAsync()
        {
            isLoadingTerms = true;
            Terms.Clear();
            await GetAllTerms(Vocabulary);
            Terms = await VocabularyService.GetTermsAsync(Vocabulary);
            ExportCsvDisable = Terms.Any();
            SelectTreeTerms = new(Terms);
            foreach (var item in Terms)
            {
                item.IsGrandParent = true;
                var doubleTree = Terms.IndexOf(item);
                var childTopic = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                item.Child = childTopic;
                item.IsTopicParent = childTopic.Count <= 0;
                SelectTreeTerms[doubleTree].IsTopicParent = childTopic.Count <= 0;
                SelectTreeTerms[doubleTree].Child = childTopic;
                SelectTreeTerms[doubleTree].IsGrandParent = true;
            }
            ShowInSearchTerms = new(Terms);

            isLoadingTerms = false;
        }


        private async Task ValidateName()
        {
            var name = Terms.Select(t => t.Name).ToList();
            if(AddTerms.Count != AddTerms.Select(w=>w.Name).Distinct().Count())
            {
                _ = OpenValidationToaster("Please remove duplicate values");
                return;
            }
            foreach (var item in AddTerms)
            {
                item.ParentId = TermDetail.ParentId;
            }
            ValidateTermName = AddTerms.Where(term => name.Contains(term.Name)).Select(x => x.Name).ToList();
            if (!ValidateTermName.Any())
            {
                _ = SaveOrUpdateNewTerms();
            }
            else
            {
                //showAddEditTermModal = false;
                //IsValidateName = true;
                if (EditChild.Id != 0)
                {
                    SaveOrUpdateNewTerms();
                }
                else
                {
                    showAddEditTermModal = false;
                    IsValidateName = true;
                }
            }
        }
        private async Task SaveOrUpdateNewTerms()
        {
            string vacabulary = Vocabulary;
            string originalKey = string.Empty;

            if (!TermDetail.IsEditCountry && Vocabulary == Gina2.Core.Constants.VocabularyTables.Country)
            {
                originalKey = OriginalTermDetail.Code;
                vacabulary = Core.Constants.VocabularyTables.Region;
                // check for regions
                bool isCode = Terms.Any(c => !c.Code.Equals(OriginalTermDetail.Code) && c.Code == TermDetail.Code);
                if (isCode)
                {
                    _ = OpenErrorToaster("Country code is already present.");
                    return;
                }
            }
            isSavingTerm = true;

            foreach (var item in AddTerms)
            {
                item.IsGrandParent = !GlobalAdd;
                var term = await VocabularyService.AddEditTermsAsync(vacabulary, item, originalKey);
            }
            await ListTermsAsync();
            showAddEditTermModal = false;
            EditChild = new();
            AddTerms = new();
            AddMultipleTerm();
            TermDetail = new();
            OriginalTermDetail = new();
            isSavingTerm = false;
            IsValidateName = false;
            MassTerm = string.Empty;
            StateHasChanged();
            _ = OpenSuccessToaster("The term is saved.");
        }

        public async Task OnNameChanged()
        {
            var child = GetTermsByIdAsync(Vocabulary, OriginalTermDetail.Id, ChildDetail.Code);
            foreach (var item in AddTerms)
            {
                item.ValidateError = !child.Any(c => c.Name == item.Name);

            }

            if (AddTerms.Any(a => a.ValidateError == false))
            {
                return;
            }

        }
        private List<Term> listOfSelectedChilds = new List<Term>();

        public async Task SaveUpdateOrderTerms()
        {
            if (!string.IsNullOrEmpty(IsLeftOrRight))
            {
                isLoadingTerms = true;
            }
            if (IsLeftOrRight == "LeftToRight")
            {
                Terms = SelectTreeTerms;
            }
            if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
            {
                listOfSelectedChilds = Terms.DeepClone();
                foreach (var item in AllParentId)
                {
                    listOfSelectedChilds = listOfSelectedChilds.FirstOrDefault(t => t.Id.Equals(item))?.Child;
                }

                listOfSelectedChilds.ForEach(l => l.DragAndDropKey = listOfSelectedChilds.IndexOf(l));
            }
            else {
                foreach (var item in Terms)
                {
                    item.DragAndDropKey = Terms.IndexOf(item);
                    if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2Category &&
                        item.Child != null && item.Child.Count > 0)
                    {
                        foreach (var icn in item.Child)
                        {
                            icn.DragAndDropKey = item.Child.IndexOf(icn);
                            icn.ParentId = item.Id;
                        }
                    }

                    if (Vocabulary == Gina2.Core.Constants.VocabularyTables.PartnerCategory &&
                        item.Child != null && item.Child.Count > 0)
                    {
                        foreach (var partner in item.Child)
                        {
                            partner.DragAndDropKey = item.Child.IndexOf(partner);
                            partner.ParentId = item.Id;
                        }
                    }
                    if (Vocabulary == Gina2.Core.Constants.VocabularyTables.CountryGroup &&
                       item.Child != null && item.Child.Count > 0)
                    {
                        foreach (var partner in item.Child)
                        {
                            partner.DragAndDropKey = item.Child.IndexOf(partner);
                            partner.ParentId = item.Id;
                        }
                    }

                    if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Sdg
                        && item.Child != null && item.Child.Count > 0)
                    {
                        foreach (var child in item.Child)
                        {
                            child.DragAndDropKey = item.Child.IndexOf(child);
                            if (child.Child != null && child.Child.Count > 0)
                            {
                                await GetBackTopicView(child.Child, child.Id);
                            }
                        }
                    }
                }
                listOfSelectedChilds = Terms.DeepClone();
            }
            await VocabularyService.SaveDragAndDropTermsAsync(Vocabulary, listOfSelectedChilds, IsSingleOrMultipleDoubleTree, RemovableTerm);
            if (!string.IsNullOrEmpty(IsLeftOrRight))
            {
                isLoadingTerms = false;
                _ = OpenSuccessToaster("The term is saved.");
            }
            IsLeftOrRight = string.Empty;
            RemovableTerm = new();
            RightTree = new();
            LeftTree = new();
            KeepParent = false;
        }

        private async Task GetBackTopicView(List<Term> term, int parentId)
        {
            foreach (var child in term)
            {
                child.DragAndDropKey = term.IndexOf(child);
                if (child.Child != null && child.Child.Count > 0)
                {
                    await GetBackTopicView(child.Child, child.Id);
                }
            }
        }

        private async Task AddNewTerm(String tableName, int parentId = 0, string parentname = "")
        {
            GlobalAdd = false;
            IsEditChild = false;
            AddTerms = new();
            SelectTreeTerms = new();
            TermDetail = new();
            OriginalTermDetail = new();
            TermDetail.ParentId = 0;
            SelectTreeTerms = new(Terms);
            TermDetail.DragAndDropKey = Terms.Count;
            AddMultipleTerm();
            //showAddEditTermModal = true;
            isAddingTermMode = true;
            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");
            StateHasChanged();
        }

        private void AddMultipleTerm()
        {
            AddTerms.Add(new()
            {
                Name = "",
                Description = "",
                ParentId = 0,
                IsGrandParent = true
            });
        }

        private void AddChildMultipleTerm()
        {
            IsChildValidate = true;
            AddTerms.Add(new()
            {
                Name = "",
                Description = "",
                ParentId = OriginalTermDetail.Id
            });
        }

        private void RemoveMultipleTerm(Term term)
        {
            IsChildValidate = true;
            AddTerms.Remove(term);
        }

        private string IsLeftOrRightTree { get; set; }
        public async Task OpenModalAddNewChildTerm(Term child, string LeftOrRight)
        {
            IsChildValidate = true;
            IsLeftOrRightTree = LeftOrRight;
            IsEditChild = false;
            AddTerms = new();
            ParentChild = new();
            ChildDetail = new();
            AllCountries = await CountryService.GetActiveCountriesNotAvailableInCurrent(child.Id);
            SelectedCountries = Enumerable.Empty<string>();
            SelectNewOrExistChild = "new";
            TermDetail = child.DeepClone();
            OriginalTermDetail = child;
            ChildDetail.ParentId = child.Id;
            ChildDetail.DragAndDropKey = child.Child.Count;
            AddChildMultipleTerm();
            showAddEditChildModal = true;
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");

        }


        private bool IsChildValidate { get; set; } = true;
        private async Task AddNewChildTerm()
        {
            var term = new Term();
            if (SelectNewOrExistChild == "exist")
            {
                bool isParent = await VocabularyService.IsTopicParent((int)ChildDetail.ParentId, int.Parse(ChildDetail.ExistingChildIdString));
                if (isParent)
                {
                    _ = OpenErrorToaster("You are selected duplicate child.");
                    isAddUpdateChild = false;
                    return;
                }
                term = await VocabularyService.AddExistingTopicChild(Vocabulary, ChildDetail);
            }
            else
            {
                var child = GetTermsByIdAsync(Vocabulary, OriginalTermDetail.Id, OriginalTermDetail.Code);
                var isSame = AddTerms.Any(c => c.Name == EditChild.Name);
                var isValidate = AddTerms.DistinctBy(a => a.Name).ToList();
                if (!isSame)
                {
                    var isName = AddTerms.Select(t => t.Name).ToList();
                    var IsExistChild = ParentChild.FirstOrDefault(term => isName.Contains(term.Name));
                    if (IsExistChild != null)
                    {
                        IsChildValidate = false;
                        return;
                    }
                    if (isValidate.Count != AddTerms.Count)
                    {
                        IsChildValidate = false;
                        return;
                    }
                    foreach (var item in AddTerms)
                    {

                        item.ValidateError =!child.Any(c => c.Name == item.Name);

                    }

                    if (AddTerms.Any(a => a.ValidateError == false))
                    {
                        IsChildValidate = true;
                        return;
                    };
                }


                isAddUpdateChild = true;
                if (Vocabulary == Gina2.Core.Constants.VocabularyTables.CountryGroup)
                {
                    if (!SelectedCountries.Any())
                    {
                        _ = OpenValidationToaster("Please select any country");
                        isAddUpdateChild = false;
                        return;
                    }

                    var countryGroupMappingTerms = new List<Term>();
                    foreach (var item in SelectedCountries)
                    {
                        countryGroupMappingTerms.Add(new Term
                        {
                            Iso3Code = item,
                            ParentId = AddTerms.First().ParentId,
                            Name = AllCountries.First(w => w.Iso3Code == item).Name
                        });
                    }
                    var mappingList = await VocabularyService.AddCountryGroups(Vocabulary, countryGroupMappingTerms, SelectedCountries);
                    OriginalTermDetail.Child.AddRange(mappingList);
                   
                }
                else
                {
                    foreach (var item in AddTerms)
                    {
                        item.DragAndDropKey = OriginalTermDetail.Child.Count;
                        term = await VocabularyService.AddEditTermsAsync(Vocabulary, item);
                        OriginalTermDetail.Child.Add(term);
                    }
                }

                EditChild.Name = term.Name;
                EditChild.Description = term.Description;
                OriginalTermDetail.IsTopicParent = false;
            }
            await GetAllTerms(Vocabulary);
            //OriginalTermDetail.Child = GetTermsByIdAsync(Vocabulary, OriginalTermDetail.Id, OriginalTermDetail.Code);
            foreach (var item in OriginalTermDetail.Child)
            {
                item.IsTopicParent = false;
            }
            if (ChildDetail.Id != 0)
            {
                TermDetail.Name = term.Name;
            }

            SelectNewOrExistChild = "new";
            showAddEditChildModal = false;
            TermDetail = new();
            EditChild = new();
            ParentChild = new();
            OriginalTermDetail = new();
            ChildDetail = new();
            isAddUpdateChild = false;
            IsEditChild = false;
            _ = OpenSuccessToaster("The Child is saved.");
            StateHasChanged();
        }

        private List<Term> ParentChild = new List<Term>();
        private async Task EditTermPopupAsync(AntDesign.TreeNode<Term> parent, Term term)
        {
            AddTerms = new();
            ParentChild = new();
            IsChildValidate = true;
            IsEditChild = true;
            EditChild = term;
            TermDetail.Id = term.Id;
            TermDetail.Name = term.Name;
            TermDetail.Description = term.Description;
            TermDetail.DragAndDropKey = term.DragAndDropKey;
            TermDetail.Iso3Code = term.Iso3Code;
            TermDetail.Status = term.Status;
            TermDetail.Code = term.Code;
            TermDetail.ParentId = term.ParentId;
            TermDetail.IsEditCountry = !string.IsNullOrEmpty(TermDetail.Iso3Code);
            AddTerms.Add(TermDetail);
            if (parent.ParentNode != null)
            {
                ParentChild = parent.ParentNode.DataItem.Child;
                //ParentChild.Remove(term);
               
            }
            else
            {
                ParentChild = new List<Term>();
               
            }
            isAddingTermMode = false;
            OriginalTermDetail = TermDetail.DeepClone();
            await editModalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "editantdraggable", "edit-ant-header");
        }

        public void EditChildTermPopupAsync(Term term)
        {
            IsEditChild = false;
            AddTerms = new();
            var result = new Term();
            result.Id = term.Id;
            result.ParentId = term.ParentId;
            result.ParentName = term.ParentName;
            result.Name = term.Name;
            result.Description = term.Description;
            result.Status = term.Status;
            result.Iso3Code = term.Iso3Code;
            AddTerms.Add(term);
            ChildDetail = Mapper.Map<Term>(result);
            TermDetail = term;
            OriginalTermDetail = term.DeepClone();
            showAddEditChildModal = true;
            isAddingTermMode = false;
        }

        private bool GlobalAdd { get; set; }
        private void OnChangeChild(TreeNode<Term> term, bool value)
        {
            GlobalAdd = value;
            TermDetail.ParentId = term.DataItem.Id;
        }

        private void EnableDoubleTreeView()
        {
            IsCheckable = !IsCheckable;
            EnableDoubleTree = !EnableDoubleTree;
            showTopicsToSearchTree = false;
        }

        private async Task ToggleTopicsInSearch()
        {
            isLoadingTerms = true;
            SearchTreeTerms = Terms.Where(w => w.Id == SearchTopicConfigurationKey.SearchTopicId).ToList().DeepClone();
            EnableDoubleTree = false;
            IsCheckable = false;
            showTopicsToSearchTree = !showTopicsToSearchTree;
            isLoadingTerms = false;
        }

        private bool isSearchTopic { get; set; }
        private string SelectAppendKey { get; set; }
        private async Task GetAllSelectTopics(List<Term> termList, Term term)
        {
            foreach (var item in termList)
            {
                item.AllParentId.AddRange(term.AllParentId);
                item.AllParentId.Add(term.Id);
                item.Key = $"{item.Id.ToString()}{term.Key}";
                if (item.IsSearchCheck && !item.IsAllChildCheck)
                {
                    item.Child = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                    await GetAllSelectTopics(item.Child, item);
                }
                //await GetAllSelectTopics(item.Child, item);
                if (item.IsSearchCheck && item.IsAllChildCheck && isSearchTopic)
                {
                    CheckedKeyList.Add(item.Key);
                }
            }
        }
        public async Task<List<Term>> OpenParent(Term term)
        {
            if (EnableDoubleTree)
            {

                if (!term.IsPlusIcon)
                {
                    term.IsPlusIcon = true;
                    term.Child = GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
                    foreach (var item in term.Child)
                    {
                        item.AllParentId.Add(term.Id);
                        item.AllParentId.AddRange(term.AllParentId);
                        var childTopic = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                        item.IsCheck = term.IsSelectedCheck;
                        item.IsTopicParent = childTopic.Count <= 0;
                    }
                    return term.Child;
                }
                else
                {
                    term.IsPlusIcon = false;
                    return term.Child = new List<Term>();
                }
            }

            else
            {
                if (!term.IsPlusIcon)
                {
                    term.IsPlusIcon = true;
                    term.Child = GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
                    foreach (var item in term.Child)
                    {
                        item.AllParentId.Add(term.Id);
                        item.AllParentId.AddRange(term.AllParentId);
                        var childTopic = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                        item.IsTopicParent = childTopic.Count <= 0;
                    }
                    return term.Child;
                }
                else
                {
                    term.IsPlusIcon = false;
                    return term.Child = new List<Term>();
                }
            }

        }


        private async Task OnSearchTopic(string name)
        {
            if (!string.IsNullOrEmpty(name))
            {
                SearchingTerms = await VocabularyService.OnSearchByTopicName(name, Vocabulary);
                //SearchingTerms = SearchingTerms.DistinctBy(s => s.ParentId).ToList();
            }
        }
        private void SelectedChangeTopic(Term term)
        {
            BindSearchTerm = term;
            SearchBindTopic = term == null ? string.Empty : term.Name;
            SearchBindTopicId = term == null ? 0 : term.Id;
        }
        private async Task GetSearchingTopicData()
        {
            isLoadingTerms = true;
            _ = Task.Run(async () => await InvokeAsync(StateHasChanged));
            if (string.IsNullOrEmpty(SearchBindTopic))
            {
                _ = OpenErrorToaster("Enter input data");
                isLoadingTerms = false;
                return;
            }
            SearchTopic = SearchBindTopic;
            var isTerm = Terms.Any(t => t.Name.Equals(SearchTopic));
            if (isTerm)
            {
                isLoadingTerms = false;
                return;
            }
            if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic)
            {
                await ListTermsAsync();
                var searchData = await VocabularyService.OnTreeSearchByTopicName(BindSearchTerm);
                var a = Terms.Where(t => t.Id == searchData.FirstOrDefault()).ToList();
                await OnExpandTopicSearch(a);
            }
            //Expandkeys = SearchingTerms.Select(s => s.ParentId.ToString()).ToArray();
            if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Partners
                || Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2Category)
            {
                await ListTermsAsync();
                var searchData = await VocabularyService.OnSearchByName(Vocabulary, SearchTopic);
                foreach (var item in searchData)
                {
                    var term = Terms.Where(t => t.Id == item.Id).First();
                    term.IsPlusIcon = true;
                    term.Child = item.Child;
                }
            }
            IsScorllSearch = true;

            isLoadingTerms = false;
        }

        private async Task AddChildInTerm(Term term, List<Term> TreeList)
        {
            var childTopic = GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
            foreach (var item in childTopic)
            {
                TreeList.Add(item);
                await AddChildInTerm(item, TreeList);
            }
        }
        private async Task OnChangeTreeChild(TreeEventArgs<Term> term, string leftOrRight)
        {
            //isLoadingTerms = true;
            if (leftOrRight == "LeftTree")
            {
                if (term.Node.Checked)
                {
                    LeftTree.Add(term.Node.DataItem);
                }
                else
                {
                    LeftTree.Remove(term.Node.DataItem);
                }
            }

            if (leftOrRight == "RightTree")
            {
                if (term.Node.Checked)
                {
                    RightTree.Add(term.Node.DataItem);
                }
                else
                {
                    RightTree.Remove(term.Node.DataItem);
                }
            }

        }


        private async Task OnSearchTreeChild(bool check, Term term, string leftOrRight, Term parent, bool IsAllOrParent)
        {
            if (IsAllOrParent)
            {
                await SetTermChildrenSelection(term, check, IsAllOrParent);
            }
            await VocabularyService.SetTermChildrenSelection(term, check, IsAllOrParent);
            await GetAllTerms(Vocabulary);
            isLoadingTerms = false;
            _ = OpenSuccessToaster($"Updated the term: '{term.Name}'");
        }


        private async Task SetTermChildrenSelection(Term term, bool check, bool IsAllOrParent)
        {
            foreach (var item in term.Child)
            {
                item.IsCheck = check;
                item.IsIndeterminate = false;

                if (item.Child?.Any() == true)
                {
                    _ = SetTermChildrenSelection(item, check, IsAllOrParent);
                }
            }

        }

        private async Task OnLeftToRightChild(List<Term> term1, List<Term> term2, string treePlace)
        {
            IsLeftOrRight = treePlace;
            if (term1 != null && term1.Count > 0 && term2 != null && term2.Count > 0)
            {
                foreach (var item in term2)
                {

                    foreach (var item1 in term1)
                    {
                        bool isExistParent = term1.Any(t => t.Id == item.Id);
                        bool isAllGrandExistParent = item.AllParentId.Any(i => i == item1.Id);
                        if (isExistParent || isAllGrandExistParent)
                        {
                            _ = OpenErrorToaster("This selection is not allowed");
                            await ListTermsAsync();
                            RightTree = new();
                            LeftTree = new();
                            return;
                        }
                        bool isParent = await VocabularyService.IsTopicParent(item.Id, item1.Id);
                        bool IsName = item.Child.Any(x => x.Name == item1.Name);
                        if (isParent || IsName)
                        {
                            _ = OpenErrorToaster("This selection is not allowed");
                            //await ListTermsAsync();
                            //RightTree = new();
                            //LeftTree = new();
                            return;
                        }
                    }

                }
                isLoadingTerms = true;
                foreach (var item in term2)
                {
                    item.Child.InsertRange(0, term1);
                    item.IsCheck = false;
                    if (!KeepParent)
                    {
                        RemovableTerm.AddRange(term1);
                    }
                }
                //await SaveUpdateOrderTerms();
                await VocabularyService.SaveDoubleTreeTopicAsync(term1, term2, "", RemovableTerm);
                RightTree = new();
                LeftTree = new();
                RemovableTerm = new();
                KeepParent = false;
                _ = OpenSuccessToaster("The term is saved.");
                //isLoadingTerms = false;
                await ListTermsAsync();
            }
            else
            {
                _ = OpenErrorToaster("Please select Left or Right Data");

            }

        }

        private async Task ExportCsvTerm()
        {
            ExportCsvLoading = true;
            CsvKeys = string.Empty;
            CsvData = new();
            List<Models.ExportCsvTerm> ExportCsv = new List<Models.ExportCsvTerm>();
            if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Topic
                || Vocabulary == Gina2.Core.Constants.VocabularyTables.Partners
                || Vocabulary == Gina2.Core.Constants.VocabularyTables.Icn2Category)
            {
                ExportCsv = Mapper.Map<List<Models.ExportCsvTerm>>(await VocabularyService.ExportCsvTerm(Vocabulary));
            }
            else
            {
                ExportCsv = Mapper.Map<List<Models.ExportCsvTerm>>(Terms);
            }
            var dictionary = JObject.FromObject(ExportCsv[0]).ToObject<Dictionary<string, string>>();
            if (Vocabulary != Gina2.Core.Constants.VocabularyTables.Country)
            {
                dictionary.Remove("Iso3Code");

            }
            else
            {
                dictionary.Remove("ParentId");
                dictionary.Remove("Id");
            }
            CsvKeys = string.Join(",", dictionary.Select(a => $"{a.Key}"));
            if (Vocabulary == Gina2.Core.Constants.VocabularyTables.Country)
            {
                var country = ExportCsv.Select(x => new
                {
                    x.Name,
                    x.Iso3Code
                }).ToList();
                foreach (var item in country)
                {
                    //var itemExport = _mapper.Map<Models.ExportCsvTerm>(item);
                    var dictionaryvalues = JObject.FromObject(item).ToObject<Dictionary<string, string>>();
                    string CsvValue = string.Join(",", dictionaryvalues.Select(a => $"\"{a.Value}\""));
                    CsvData.Add(CsvValue);
                }
            }
            else
            {
                foreach (var item in ExportCsv)
                {
                    var itemExport = Mapper.Map<Models.ExportCsvTerm>(item);
                    var dictionaryvalues = JObject.FromObject(itemExport).ToObject<Dictionary<string, string>>();
                    string CsvValue = string.Join(",", dictionaryvalues.Select(a => $"\"{a.Value}\""));
                    CsvData.Add(CsvValue);
                }
            }


            byte[] bytes = null;
            using (var ms = new MemoryStream())
            {
                using (TextWriter tw = new StreamWriter(ms))
                {
                    tw.Write($"{CsvKeys}\n");
                    foreach (var item in CsvData)
                    {
                        tw.Write($"{item}\n");
                    }

                    tw.Flush();
                    bytes = ms.ToArray();
                }

            }
            var base64 = new DotNetStreamReference(stream: new MemoryStream(bytes));
            ExportCsvLoading = false;
            await JsRuntime.InvokeVoidAsync("saveAsFile", $"{Vocabulary}.csv", base64);
        }

        private List<int> AllParentId = new List<int>();
        private async Task UpOrDown(TreeNode<Term> node, Term term, int movementKey, List<Term> terms)
        {
            var termList = node.GetParentChildDataItems();
            int oldIndex = termList.IndexOf(term);
            int newIndex = oldIndex + movementKey;
            AllParentId = term.AllParentId;
            if (newIndex >= 0 && newIndex < termList.Count)
            {
                termList.Remove(term);
                termList.Insert(newIndex, term);
                if (term.IsGrandParent)
                {
                    Terms = termList.ToList();
                }
                else
                {
                    node.ParentNode.DataItem.Child = termList.ToList();
                }
                await SaveUpdateOrderTerms();
            }
        }

        private bool DeleteDisable { get; set; } = false;
        private async void ShowDeleteModal(TreeNode<Term> term, string isLeftOrRight)
        {
            if (term.DataItem.DeleteDisable)
            {
                term.DataItem.DeleteDisable = false;
            //isLoadingTerms = true;
            if (SearchTopicConfigurationKey.GrandParentId.Any(x => x == term.DataItem.Id))
            {
                    term.DataItem.DeleteDisable = true;
                _ = OpenToaster("This topic is system required and cannot be deleted", "", AntDesign.NotificationType.Warning);
            }
            else
            {
                var topics = AllParentTopics.Where(x => x.TopicId == term.DataItem.Id).ToList();
                var allparents = AllParentTopics.ToList();
                IsLeftOrRightTree = isLeftOrRight;
                DeleteTopic = term;
                if (topics.Count == 1 || Vocabulary != Gina2.Core.Constants.VocabularyTables.Topic)
                {
                    allparents.Remove(allparents.FirstOrDefault(t => t.TopicId.ToString() == term.Id && t.ParentId.ToString() == term.ParentNode.Id));
                    DeleteVisible = true;
                }
                else
                {
                    await DeleteTopics();
                }
                    //isLoadingTerms = false;
                    term.DataItem.DeleteDisable = true;
            }
            }
            

        }
        private async Task DeleteTopics()
        {
            if (!DeleteDisable)
            {
                DeleteDisable = true;
                if (DeleteTopic.DataItem.IsGrandParent)
                {
                    Terms.Remove(DeleteTopic.DataItem);
                    SelectTreeTerms.Remove(DeleteTopic.DataItem);
                }
                else
                {
                    DeleteTopic.ParentNode.DataItem.Child.Remove(DeleteTopic.DataItem);
                }
                DeleteVisible = false;
                await VocabularyService.DeleteTopics(DeleteTopic.DataItem, Vocabulary);
                await GetAllTerms(Vocabulary);
                DeleteDisable = false;
                _ = OpenSuccessToaster($"{TermTitle} record is successfully deleted");
            }
        }

        private async Task OnExpandTopicChild(TreeEventArgs<Term> Term)
        {


            foreach (var item in Term.Node.DataItem.Child)
            {
                if (item.Child.Count == 0)
                {
                    item.AllParentId.AddRange(Term.Node.DataItem.AllParentId);
                    item.AllParentId.Add(Term.Node.DataItem.Id);
                    item.Child = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                    //Term.Node.AddChildNode(item);
                }
            }
        }

        private async Task OnExpandTopicSearch(List<Term> Term)
        {
            var IsTerm = Term.Any(i => i.Id == BindSearchTerm.Id);
            if (IsTerm)
            {
                foreach (var item in Term)
                {
                    item.Child = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                }
                List<string> bind = new List<string>();
                bind.Add(BindSearchTerm.ParentId.ToString());
                Expandkeys = bind.ToArray();
                return;
            }
            foreach (var item in Term)
            {
                item.Child = GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                await OnExpandTopicSearch(item.Child);
            }
        }

        private bool SearchVisible { get; set; }
        private TreeNode<Term> SelectableNode { get; set; }


        private async Task OpenModalIsSelectAllParent(bool value, TreeNode<Term> term)
        {
            term.DataItem.IsCheck = value;
            IsCheck = value;
            SetIsIndeterminate = 0;
            Indeterminate = false;
            term.DataItem.AllParentId.Remove(term.DataItem.Id);
            SelectableNode = term;

            await OnSearchTreeChild(IsCheck, term.DataItem, "", new Term(), false);
            SelectableNode = new();
        }

        private async Task SelectFamily()
        {
            if (SelectableNode.DataItem != null)
            {
                SelectableNode.DataItem.IsIndeterminate = false;
            }
            if (IsCheck && SelectableNode.DataItem != null)
            {
                SelectableNode.DataItem.IsCheck = IsCheck;
                //SetParentIndeterMinate(SelectableNode);
                SelectTerm = SelectableNode.DataItem;
                await OnSearchTreeChild(true, SelectTerm, "", new Term(), true);
            }
            else
            {
                //SetParentIndeterMinate(SelectableNode);
                await OnSearchTreeChild(true, SelectTerm, "", new Term(), true);
            }
            SearchVisible = false;
        }


        private void SetParentIndeterMinate(TreeNode<Term> node)
        {
            if (node.ParentNode != null)
            {
                if (!Indeterminate)
                {
                    Indeterminate = node.ParentNode.DataItem.Child.Any(x => x.IsIndeterminate == true) || !node.ParentNode.DataItem.Child.All(x => x.IsCheck == IsCheck);
                    node.ParentNode.DataItem.IsIndeterminate = Indeterminate;
                }
                node.ParentNode.DataItem.IsIndeterminate = Indeterminate;
                node.ParentNode.DataItem.IsCheck = node.ParentNode.DataItem.Child.All(a => a.IsCheck == true);
                //SetIsIndeterminate++;
                SetParentIndeterMinate(node.ParentNode);
            }
        }

        private async Task SelectParent()
        {
            if (IsCheck && SelectableNode.DataItem != null)
            {
                SelectableNode.DataItem.AllParentId.Remove(SelectableNode.DataItem.Id);

                SelectableNode.DataItem.IsIndeterminate = false;
                SelectTerm = SelectableNode.DataItem;
                //SetParentIndeterMinate(SelectableNode);
                await OnSearchTreeChild(IsCheck, SelectTerm, "", new Term(), false);
            }
            SearchVisible = false;
        }
        private async Task GetAllTerms(string vocabulary)
        {
            switch (Vocabulary)
            {
                case VocabularyTables.Icn2Category:
                    AllICN2s = await Icn2Service.GetIc2nsAsync();
                    break;
                case VocabularyTables.Country:
                    AllCountryRegionMap = await CountryService.GetAllCountryRegionMap();
                    break;
                case VocabularyTables.IncomeGroup:
                    AllCountryIncomeGroupMapItem = await CountryService.GetAllCountryIncomeGroupMapItem();
                    break;
                case VocabularyTables.PartnerCategory:
                    AllPartners = await PartnerCategoryService.GetAllPartners();
                    break;
                case VocabularyTables.Sdg:
                    AllSDGs = await CommitmentService.GetAllSdgAsync();
                    break;
                case VocabularyTables.Topic:
                    AllParentTopics = await TopicService.GetAllParentTopicsAsync();
                    break;
                case VocabularyTables.CountryGroup:
                    {
                        AllCountryGroupMapping = await CountryService.GetAllCountryGroupMap();
                        //foreach (var item in AllCountryGroupMapping)
                        //{
                        //    item.Name = item.Country.Name;//need to refactor
                        //}
                    }
                    break;
                default:
                    break;
            };
        }

        private async Task SetCountryVisibility(bool value, Term term)
        {
            term.IsActive = value;
            var affectedRow = await CountryService.ToggleCountryActive(term.Iso3Code, value);

            if (affectedRow > 0)
            {
                _ = OpenSuccessToaster("Saved successfully");
            }
            else
            {
                term.IsActive = !value;
                _ = OpenErrorToaster("Save failed. Please try again.");
            }
        }

        public List<Term> GetTermsByIdAsync(string tableName, int id, string code)
        {

            try
            {
                return tableName switch
                {
                    VocabularyTables.Icn2Category => VocabularyService.ConvertToTerms(AllICN2s.Where(w => w.CategoryId == id).ToList()),
                    VocabularyTables.Country => VocabularyService.ConvertToTerms(AllCountryRegionMap.Where(w => w.RegionCode == code).Select(w => w.Country).ToList()),
                    VocabularyTables.PartnerCategory => VocabularyService.ConvertToTerms(AllPartners.Where(w => w.PartnerCategoryId == id).ToList()),
                    VocabularyTables.Sdg => VocabularyService.ConvertToTerms(AllSDGs.Where(w => w.ParentId == id).ToList()),
                    VocabularyTables.IncomeGroup => VocabularyService.ConvertToTerms(AllCountryIncomeGroupMapItem.Where(w => w.IncomeGroupCode == code).Select(w => w.Country).ToList()),
                    VocabularyTables.Topic => VocabularyService.ConvertToTerms(AllParentTopics.Where(w => w.ParentId == id).OrderBy(o => o.OrderKey).ToList()),
                    VocabularyTables.CountryGroup => VocabularyService.ConvertToTerms(AllCountryGroupMapping.Where(w => w.CountryGroupId == id).ToList()),
                    _ => VocabularyService.ConvertToTerms(AllICN2s.Where(w => w.CategoryId == id).ToList()),
                };
            }
            catch (Exception ex)
            {
                Logger.LogError("Error ::", ex);
                return new List<Term>();
            }

        }

        private void ValidateText(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }

        private string MassTerm { get; set; }
        private List<Term> MassChildTerm { get; set; } = new();
        private bool MassTermSelected { get; set; }
        private bool MassTermValidate { get; set; } = true;
        private string MassErrorMessage { get; set; }
        void OnChange(string value)
        {
            Console.WriteLine(value);
            MassTerm = value;
        }

        private async Task AddMassTerm()
        {
            List<string> terms = new List<string>();
            if (!string.IsNullOrEmpty(MassTerm))
            { 
            terms = MassTerm.Split("\n").Remove("").ToList();
            }
            if (terms.Any())
            {
                TermDetail.ParentId = 0;

                if (MassChildTerm.Any())
                {
                    var listOfChildName = MassChildTerm.Select(x => x.Child.Select(c => c.Name).ToList());
                    var childNames = listOfChildName.Any() ? string.Join(",", listOfChildName.FirstOrDefault().ToArray()) : "";
                    var isName = terms.Any(x => childNames.Contains(x));
                    if (isName)
                    {
                        MassTermValidate = false;
                        MassErrorMessage = "One of the name already exists under this selected datatype, please provide a different name.";
                        return;
                    }
                    foreach (var item in MassChildTerm)
                    {
                        AddTerms = new();
                        foreach (string term in terms)
                        {
                            AddTerms.Add(new Term()
                            {
                                Name = term,
                                Description = "",
                                ParentId = item.Id
                            });
                        }
                        await AddNewChildTerm();
                    }
                    MassTerm = string.Empty;
                }
                else
                {
                    AddTerms = new();
                    foreach (string term in terms)
                    {
                        AddTerms.Add(new Term()
                        {
                            Name = term,
                            Description = "",
                            ParentId = 0,
                            IsGrandParent = true
                        });
                    }
                    await ValidateName();
                }

                await ListTermsAsync();
                MassChildTerm = new();
                MassErrorMessage = string.Empty;
                AddTerms = new();
                MassTermValidate = true;
                OriginalTermDetail = new();
            }
            else {
                MassTerm = string.Empty;
                MassTermValidate = false;
                MassErrorMessage = "Please enter mass term";
            }
            
        }
        void SelectChild(Term term, bool value)
        {
            if (value)
            {
                MassChildTerm.Add(term);
            }
            else { 
            MassChildTerm.Remove(term);
            }
        }

        void MassCollapse(bool value)
        {
            MassTermSelected = value;
        }

        private async Task ShowModal()
        {

            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");
            StateHasChanged();
        }
    }
}