﻿using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Pages
{
    public partial class PublishedScorecards : PageConfirgurationComponent
    {
        [Inject]
        public NavigationManager _NavigationManager { get; set; }

        [Inject]
        public IScoreCardService ScorecardService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        public List<DbModels.Scorecard> ScorecardList { get; set; }
        public DbModels.Scorecard Scorecard { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                ScorecardList = (await ScorecardService.GetAllAsync()).Where(w=>w.IsPublished).ToList();

                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                _ = base.OnAfterRenderAsync(firstRender);
                await InvokeAsync(StateHasChanged);
            }
        }
    }
}
