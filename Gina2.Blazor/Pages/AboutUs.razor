﻿@page "/about-us"
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<Loader IsLoading="@isLoading" />

@*<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem href="about-us/edit">Edit</DropdownItem>
                <DropdownItem href="about-us/moderate">Moderate</DropdownItem>
                <DropdownItem href="about-us/translate">Translate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>*@
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item0">
                    <Heading Size="HeadingSize.Is3" data-cy="AboutUsHeaderTitle">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.Title))
                        <AdminEditbut Key="@AboutUsPageConfigurationKey.Title" />
                    </Heading>
                    <Paragraph class="color-w" data-cy="AboutUsDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.Description))
                        <AdminEditbut Key="@AboutUsPageConfigurationKey.Description" />
                    </Paragraph>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container>
    <Layout Sider Class="search-box aboutus pt-6 pb-5 mob-layout">
        <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.Body))
                <AdminEditbut Key="@AboutUsPageConfigurationKey.Body"/>
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider  right-layout pl-1">
            <LayoutSiderContent>
                <Div Class="accordion-Search" id="accordionExample">
                     <Div class="accordion _Donors">
                         <Div class="accordionbut">
                             <Div Flex="Flex.JustifyContent.Between" class="w-100" data-cy="PartnerandDataResourceBtn">
                                 <Div>
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.WHOResourceTitle).ConvertHtmlToPlainText())
                                     <AdminEditbut Key="@AboutUsPageConfigurationKey.WHOResourceTitle" />
                                 </Div>
                                 <i class="plus _cursor" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne"></i>
                             </Div>
                         </Div>
                        <Div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne">
                            <Div class="accordion-body p-0">
                                 <Div Flex="Flex.JustifyContent.Start" Class="downl-flex _edittop _Donorss">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.WHOResourceBody))
                                    <AdminEditbut Key="@AboutUsPageConfigurationKey.WHOResourceBody" />
                                </Div>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                <Div Class="accordion-Search mt-2" id="accordionExample">
                     <Div Class="accordion _Donors">
                         <Div class="accordionbut">
                             <Div Flex="Flex.JustifyContent.Between" class="w-100" data-cy="PartnerandDataResourceBtn">
                                 <Div>
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.PartnerandDataResourceTitle))
                                     <AdminEditbut Key="@AboutUsPageConfigurationKey.PartnerandDataResourceTitle" />
                                 </Div>
                                 <i class="plus _cursor" data-bs-toggle="collapse" data-bs-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo"></i>
                             </Div>
                         </Div>
                        <Div id="collapsetwo" Class="accordion-collapse collapse show" aria-labelledby="headingOne">
                            <Div class="accordion-body p-0">
                                 <Div Flex="Flex.JustifyContent.Start" Class="downl-flex _edittop _Donorss">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.PartnerandDataResourceBody))
                                    <AdminEditbut Key="@AboutUsPageConfigurationKey.PartnerandDataResourceBody" />
                                </Div>
                            </Div>
                        </Div>
                    </Div>
                </Div>
                 <Div Class="accordion-Search mt-2" id="accordionExample">
                     <Div Class="accordion _Donors">
                         <Div class="accordionbut">
                             <Div Flex="Flex.JustifyContent.Between" class="w-100"><Div>@((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.DonorsTitle).ConvertHtmlToPlainText())
                                     <AdminEditbut Key="@AboutUsPageConfigurationKey.DonorsTitle" />
                                 </Div>
                             <i class="plus _cursor" data-bs-toggle="collapse" data-bs-target="#collapsethree" aria-expanded="true" aria-controls="collapsethree"></i>
                             </Div>
                         </Div>
                         <Div id="collapsethree" Class="accordion-collapse collapse show" aria-labelledby="headingOne">
                             <Div class="accordion-body p-0">
                                 <Div Flex="Flex.JustifyContent.Start" Class="downl-flex _edittop _Donorss">
                                     @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(AboutUsPageConfigurationKey.DonorsBody))
                                     <AdminEditbut Key="@AboutUsPageConfigurationKey.DonorsBody" />
                                 </Div>
                             </Div>
                         </Div>
                     </Div>
                 </Div>
            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Container>
