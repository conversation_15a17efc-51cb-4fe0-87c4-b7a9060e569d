﻿@page "/admin/bulkimportexport"
@using Domain.ImportCSV;
@using Gina2.Blazor.Models.AdminModel
@using Gina2.DbModels
@using static Gina2.Core.Constants
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@IsLoading" />
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.Title))
                        <AdminEditbut Key="@BulkImportPageConfigurationKey.Title" />
                    </Heading>
                    <Paragraph Class="color-w subtitleediticon" data-cy="IndicatorPageDescription">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.SubTitle))
                        <AdminEditbut Key="@BulkImportPageConfigurationKey.SubTitle" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="HomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">@((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.Title))</BreadcrumbLink>
                            </BreadcrumbItem>
                        </Breadcrumb>
                    </Div>
                </Div>
            </Container>
        </Card>
    </Container>

    <Container Class="pt-7 m-pt-2 pl-2 pr-2 adminuser">
        @*<Row>
    <Column ColumnSize="ColumnSize.Is10.OnTablet.Is2.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
    <Heading Size="HeadingSize.Is2" Class="Headingh3">
    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.HeadingPage))
    <AdminEditbut Key="@UserManagementPageConfigurationKey.HeadingPage" />
    </Heading>
    </Column>
    </Row>
    <Divider Class="divi-gray" />*@
        <Fields Class="form-news">
        @if (!string.IsNullOrEmpty(isHeaderMatch))
        {
            <Row>
        <Column ColumnSize="ColumnSize.Is12.OnTablet.Is2.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
               
                    <h2>Error: Column Mismatch</h2>
                    <p>The following columns in the uploaded file do not match the expected format. Please correct the column headers.</p>
                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" class="_Columnmis">
                        <ul class="_ColumnMismatch">
                            @foreach (var column in isHeaderMatch.Split(',').ToList())
                            {
                                <li class="text-danger">@column</li>
                            }
                        </ul>
                    </Field>
                
        </Column>
        </Row>
                }
        <Field ColumnSize="ColumnSize.Is5.OnTablet.Is12.OnMobile.Is5.OnDesktop.Is5.OnWidescreen.Is5.OnFullHD" Class="_antdesign">
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.DataTypeLabel)  <Span>*</Span>
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.DataTypeTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@BulkImportPageConfigurationKey.DataTypeGroup" />
            </FieldLabel>
            <AntDesign.Select TItem="string"
                              TItemValue="string"
                              DataSource="@DataTypeLookups"
            @bind-Value="@SelectedDataType"
                              Placeholder="@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.DataTypePlaceHolder)"
                              DefaultActiveFirstOption="false"
                                    EnableSearch
                              OnSelectedItemChanged="OnSelectedItemChangedHandler">
            </AntDesign.Select>
        </Field>
        <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
            <FieldLabel data-cy="FileUploadLabel">
                @PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.FileUploadLabel) <Span>*</Span>
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(BulkImportPageConfigurationKey.FileUploadTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>

                <AdminEditbut Key="@BulkImportPageConfigurationKey.FileUploadGroup" />
            </FieldLabel>
            <FileEdit data-cy="SelectOrDrahPlaceHolder" @ref="@fileEdit" AutoReset="true" Filter=".csv" Disabled="@DisableFile"
            Changed="@OnChanged" Placeholder="Select or drag and drop multiple files" />
            <Span>@fileName</Span>
            @*@if (isUploadingSlider)
            {
            <Progress Percent="@sliderUplodedPercentage" />
            }*@

            <FieldHelp data-cy="AllowFileTypeText">Files must be less than 25 MB     |     Allowed file types: .csv</FieldHelp>
            @* <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel>
            <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageError) ? true : false)">@SliderData.BackgroundImage</FieldLabel>*@
        </Field>
            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
            <FieldLabel style="width: 100%; height: 12px;"></FieldLabel>
                <Div Flex="Flex.JustifyContent.End.AlignItems.Center">
            <Button Class="but-import apply w-100 mt-1 mr-1" Disabled="@IsDatatypeandFileSelected" Clicked="@ImportClicked">Import</Button>
                <Button Class="but-import apply w-100 mt-1" Disabled="@DisableFile" Clicked="@ExportClicked">Export template</Button>
                </Div>
        </Field>
    </Fields>

   @*@if(ProcessFile){*@
    <Divider Class="divi-gray" />
    <Layout Class="search-box pt-3 pb-3 mob-layout">
        <Layout Class="left-layout DataGrids _captext">
            <LayoutContent>
                <DataGrid PageSizes="new[] { 50,100,250,500,1000 }"
                          FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@UploadStatus"
                          Data="@uploadStatuses"
                          TotalItems="@uploadStatuses.Count"
                          ShowPageSizes
                          PageSize="50"
                          CurrentPage="1"
                          ShowPager
                          Responsive
                          SortMode="DataGridSortMode.Single"
                          Class="_DataGrids_action">
                    <EmptyTemplate>
                        <Div>No data found.</Div>
                    </EmptyTemplate>
                    <DataGridColumns>
                        <DataGridColumn Field="@nameof(UploadStatus.Id)" Caption="ID" Sortable="true" Width="10%"></DataGridColumn>
                        @*<DataGridColumn Field="@nameof(UploadStatus.Version)" Caption="Version" Sortable="true" Width="15%" />*@
                        <DataGridColumn Field="@nameof(UploadStatus.Title)" Caption="Title" Sortable="true" Width="40%">
                            <DisplayTemplate>
                                <NavLink target="blank" href="@($"/{context.Url}/{context.Id}")">@context.Title</NavLink>
                            </DisplayTemplate>
                        </DataGridColumn>
                            <DataGridColumn Field="@nameof(UploadStatus.Type)" Caption="Type" Sortable="true" Width="10%" />
                            <DataGridColumn Field="@nameof(UploadStatus.Status)"  Caption="Status" Sortable="true" Width="20%">
                            <DisplayTemplate>
                                @{
                                    string color = context.Color;
                                }
                                <span style="background-color: @color; color: white; padding: 3px">@context.Status</span>
                            </DisplayTemplate>
                        </DataGridColumn>
                            <DataGridColumn Field="@nameof(UploadStatus.Error)" Caption="Error" Sortable="true" Width="40%" />
                    </DataGridColumns>
                    <TotalItemsTemplate>
                            <Badge TextColor="TextColor.Dark">
                                @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                            </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
            </LayoutContent>
        </Layout>
    </Layout>
    @*}*@
</Container>