﻿@page "/admin/policies/create"
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using AntDesign.Select
<PageTitle>GIFNA Enter policy</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                 <Container Class="ginasearch pt-5 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item0 pl-1">
                            <Heading Size="HeadingSize.Is3" data-cy="EnterPolicyHeading">Enter policy</Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="/" data-cy="BreadcrumbLinkHome">Home</BreadcrumbLink>
                                </BreadcrumbItem>
                                 <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#" data-cy="BreadcrumbLinkContent">Content</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#" data-cy="BreadcrumbLinkPolicy">Policy</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Padding="Padding.Is0">
        <PolicyCreateEditTab />
        </Container>
    </Authorized>
</AuthorizeView>
