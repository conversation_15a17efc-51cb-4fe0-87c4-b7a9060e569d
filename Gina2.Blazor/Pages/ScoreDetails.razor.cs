using Microsoft.AspNetCore.Components;
using Gina2.Services.ScoreCarad;

namespace Gina2.Blazor.Pages
{
    public partial class ScoreDetails
    {
        [Parameter]
        public string IsoCode { get; set; }
        [Parameter]
        public int ScorecardCountryMapId { get; set; }

        [Inject]
        public IScorecardCountryService scorecardCountryService { get; set; }

        public List<DbModels.ScorecardCountyMappingItem> ScorecardCountry { get; set; } = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                var scorecardMapById = await scorecardCountryService.GetById(ScorecardCountryMapId);
                ScorecardCountry = await scorecardCountryService.GetScorecardMapByCountry(scorecardMapById.Score, IsoCode);
                await InvokeAsync(StateHasChanged);
            }
        }
    }
}