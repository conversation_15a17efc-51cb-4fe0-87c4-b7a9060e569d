﻿@page "/admin/composeemail"
@using System.Text.Json;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Gina2.Core.Enums;
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@IsLoading" />
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/emailcomposes.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.Title))
                        <AdminEditbut Key="@ComposeEmailPageConfigurationKey.Title" />
                    </Heading>
                    <Paragraph Class="color-w subtitleediticon" Size="HeadingSize.Is3">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.SubTitle))
                        <AdminEditbut Key="@ComposeEmailPageConfigurationKey.SubTitle" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="HomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="#">Compose email</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="pt-7 m-pt-2 pl-2 pr-2 adminuser">

    <AntDesign.Row>
        <AntDesign.Col Span="16" Xs="24" Sm="24" Md="16" Lg="16" Xl="16" Class="_antSelecto" Style="padding-right:10px;">
            <label class="pr-1">
                @PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.EmailTypeLabel)
                <span class="_mandatory">*</span>
            </label>
            <Tooltip data-cy="UserProfileRegionsOfInterestTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.EmailTypeTooltip)">
                <Button data-cy="UserProfileRegionsOfInterestBtn" Class="but-info _tooltip">
                    <Icon data-cy="UserProfileRegionsOfInterestIcon" Name="IconName.QuestionCircle" />
                </Button>
            </Tooltip>
            <AdminEditbut Key="@ComposeEmailPageConfigurationKey.EmailTypeGroup" />

            <AntDesign.EnumSelect TEnum="SubscriptionType" OnSelectedItemChanged="HandleChange" />
            @if (IsSubscriptionTypeError)
            {
                <Span Style="color:red;">Email type is requried</Span>
            }

        </AntDesign.Col>

        <AntDesign.Col Span="8" Xs="24" Sm="24" Md="8" Lg="8" Xl="8">
            <label class="pr-1">
                @PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.DateRangeLabel)
                <span class="_mandatory">*</span>
            </label>
            <Tooltip data-cy="UserProfileRegionsOfInterestTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ComposeEmailPageConfigurationKey.DateRangeTooltip)">
                <Button data-cy="UserProfileRegionsOfInterestBtn" Class="but-info _tooltip">
                    <Icon data-cy="UserProfileRegionsOfInterestIcon" Name="IconName.QuestionCircle" />
                </Button>
            </Tooltip>
            <AdminEditbut Key="@ComposeEmailPageConfigurationKey.DateRangeGroup" />
            <AntDesign.RangePicker TValue="DateTime?[]" Format="dd/MM/yyyy" @bind-Value="@SubscriptionRequestModel.SelectedDateRange" Style="width: 100%; margin-bottom: 8px;" />
            @if (IsSelectedDateRangeError)
            {
                <Span Style="color:red;">Selected date range is requried</Span>
            }
        </AntDesign.Col>
    </AntDesign.Row>
    <AntDesign.Row>
        <AntDesign.Col Span="16" Xs="24" Sm="24" Md="16" Lg="16" Xl="16" Class="_antSelecto" Style="padding-right:10px;">
            <Check Class="check-light-blue" TValue="bool"Checked="@SubscriptionRequestModel.IncludeRevisiedContent"
            CheckedChanged="(e) => ToggleDataTypeSelection(e)">
                <Span Flex="Flex.JustifyContent.Start">
                    <Span Class="_nowrap">Should include revised content &nbsp;</Span>
                </Span>
            </Check>
        </AntDesign.Col>
    </AntDesign.Row>
    <Row>
        <Div Class="stickybottom pl-1">
            <Button data-cy="UpdateBtn" Class="but-blues apply p-1 pl-3 pr-3" Clicked="@OnfitlerBtnClicked">Compose email</Button>
        </Div>
    </Row>
</Container>

@if (EnableEmailSection)
{
    <Container Class="pt-2 m-pt-2 pl-2 pr-2 adminuser ">
        <Div class="_Compose_messasge1">
            <Row>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_composeM">
                    <label class="pr_Compose  w-100"><b>Compose new messasge</b></label>
                </Field>
            </Row>
        </Div>
        <Div class="_Compose_messasge">
            <Row>
                <Field> <label class="pr_Compose w-100">To <span style="color:red;">*</span></label></Field>
            </Row><Row>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_composeM">
                    <input type="text" placeholder="To" Style="width: 100%; margin-bottom: 8px;" disabled="@(SubscriptionRequestModel.SubscriptionType!=SubscriptionType.Other)" @bind-value="@Emails" />
                    @if (IsEmailsError)
                    {
                        <Span Style="color:red;">Email is requried</Span>
                    }
                </Field>
            </Row><Row>
                <Field> <label class="pr_Compose  w-100">Subject <span style="color:red;">*</span></label></Field>
            </Row>
            <Row>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_composeM">
                    <input type="text" @bind-value="@EmailSubject" placeholder="Subject" Style="width: 100%; margin-bottom: 8px;" />
                    @if (IsEmailSubjectError)
                    {
                        <Span Style="color:red;">Subject is requried</Span>
                    }
                </Field>
            </Row><Row>
                <Field>
                    <label class="pr_Compose  w-100">Body</label>
                </Field>
            </Row><Row>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_composeM">
                    <_quillEditor value="@inputValue" @ref="quillEditorRef"></_quillEditor>
                </Field>
            </Row>
            <Row>
                <Div Class="stickybottom pl-1">
                    <AntDesign.Button Class="but-yellow" HtmlType="submit" OnClick="@PreviewBtnClick">
                        Preivew
                    </AntDesign.Button>
                </Div>
            </Row>
        </Div>
    </Container>
}


<Modal @bind-Visible="@ThemesVisible" Class="modals-lg">
    <ModalContent Centered Class="forms adminmobel">
        <ModalHeader Class="pb-0">
            <ModalTitle>Preview Email Template to @SubscriptionUserEmails.ElementAtOrDefault(ElementToShow)?.Email</ModalTitle>
            <NavLink class="close" onclick="@(()=> HideModal())"><img src="/img/close.png" /></NavLink>
        </ModalHeader>
        <hr />
        <ModalBody Class="pl-2 pr-2">
            @((MarkupString)SubscriptionUserEmails.ElementAtOrDefault(ElementToShow)?.Body)
        </ModalBody>

        <ModalFooter Class="d-flex justify-content-between">
            <Div>
                <AntDesign.Button Class="but-blues apply" HtmlType="submit" OnClick="@SendToSampleBtnClicked"> Send sample email</AntDesign.Button>
                <AntDesign.Button Class="but-blues apply" HtmlType="submit" OnClick="@SendToBtnClicked"> Send</AntDesign.Button>
                <AntDesign.Button Class="but-blues apply" HtmlType="submit" OnClick="@SendToAllBtnClicked"> Send to all</AntDesign.Button>
                <AntDesign.Button Class="cancel" OnClick="@HideModal">Cancel</AntDesign.Button>
            </Div>
            <Div>
                @if (ElementToShow != 0)
                {
                    <AntDesign.Button Class="but-yellow apply" OnClick="@PreviewBtnClicked">Previous</AntDesign.Button>
                }
                @if (ElementToShow != SubscriptionUserEmails.Count - 1)
                {
                    <AntDesign.Button Class="but-yellow apply" OnClick="@NextBtnClicked">Next</AntDesign.Button>
                }
            </Div>
        </ModalFooter>
    </ModalContent>
</Modal>
