﻿@page "/countries/{CountryCode}/policies/{PolicyCode:int}"
@page "/countries/{CountryCode}/policies/{PolicyCode:int}/{VersionId:int}"
@page "/policies/{PolicyCode:int}"
@page "/policies/{PolicyCode:int}/{VersionId:int}"

@if (!isAuthenticated)
{
    <UnAuthorizedView/>
}
else
{
    <CascadingValue Value="@CountryCode" Name="CountryCode">
        <CascadingValue Value="@PolicyCode" Name="PolicyCode">
            <CascadingValue Value="@VersionId" Name="VersionId">  
                <CountryDetails SelectedTab="policies" ContentName="Policy">
                    <PolicyDetailTab />
                </CountryDetails>
            </CascadingValue>
        </CascadingValue>
    </CascadingValue>
}


