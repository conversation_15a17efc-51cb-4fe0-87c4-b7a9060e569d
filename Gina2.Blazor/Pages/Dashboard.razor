﻿@page "/admin/dashboard"
@using ChartJs.Blazor.Common
@using ChartJs.Blazor.PieChart
@using ChartJs.Blazor.Util
@using AntDesign.Select
@using Gina2.Blazor.Areas.Identity.Data;
@using Gina2.Blazor.Models.Dashboard
@using Gina2.Blazor.Models;
@using Gina2.Core.Enums
@using Gina2.Core.Extensions
@using Gina2.Core.Lookups
@using Gina2.DbModels.Views;
@using Gina2.DbModels;
@using Gina2.Services.Models
@using static Gina2.Core.Constants
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.Title).ConvertHtmlToPlainText())</PageTitle>
@* <Loader IsLoading="@IsLoading" /> *@
<SnackbarStack @ref="SnackbarStack" />
<Snackbar @ref="snackbar" Location="SnackbarLocation.Default" Color="SnackbarColor.Warning">
    <SnackbarBody>
        Your request has been requested (reason: Lorem ipsum text)
    </SnackbarBody>
</Snackbar>

<Modal @bind-Visible="@DeleteVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Are you sure want to Delete? This title </ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="but-yellow pl-2 pr-2">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@HistoryVisible" Class="modals-lg Dashdraggable" ShowBackdrop=false>
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <Div Class="w-100" Flex="Flex.JustifyContent.Between.AlignItems.Baseline">
                <ModalTitle>History</ModalTitle>
            </Div>
            <CloseButton />
        </ModalHeader>
        <ModalBody Class="_historyScroll">
            <Row>
                <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                    <Div Class="table-responsive">
                        <Table Class="table-nth">
                            <TableHeader ThemeContrast="ThemeContrast.Dark">
                                <TableRow>

                                    <TableHeaderCell>Id</TableHeaderCell>
                                    <TableHeaderCell>Revision</TableHeaderCell>
                                    <TableHeaderCell>Title</TableHeaderCell>
                                    <TableHeaderCell>Date</TableHeaderCell>
                                    <TableHeaderCell Class="_actionscenter">Action</TableHeaderCell>
                                    <TableHeaderCell>Moderation Action</TableHeaderCell>

                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <Repeater Items="@ModerationLog">
                                    @{
                                        if (RevisionId != context.EntityRevisionId)
                                        {
                                            RevisionId = context.EntityRevisionId;
                                        }
                                        else
                                        {
                                            return;
                                        }
                                    }
                                    <TableRow>
                                        <TableRowCell>@context.EntityId</TableRowCell>
                                        <TableRowCell>@context.EntityRevisionId</TableRowCell>
                                        <TableRowCell>
                                            <Heading Size="HeadingSize.Is3">@context.CombinedTitle</Heading>
                                            <Paragraph>Revised by <NavLink href="#">@context.UserName</NavLink></Paragraph>
                                        </TableRowCell>
                                        <TableRowCell>@DateTimeOffsetHelper.GetDateTimeFormat(@context.RevisedDate) </TableRowCell>
                                        <TableRowCell Class="_actionscenter">
                                            @if (context.IsPublished)
                                            {
                                                @*  <Tooltip Text="Revert">
                                                    <Icon Name="IconName.Redo" Clicked="e=>OpenConfirm(RevertContent, context.EntityId,context.EntityRevisionId,context.ContentType)" />
                                                </Tooltip> *@
                                            }
                                            <Tooltip Text="Preview">
                                                @{
                                                    var dashboardDataItem = new DashboardDataItem
                                                            {
                                                                CountryCode = context.CountryCode,
                                                                EntityId = context.EntityId,
                                                                EntityRevisionId = context.EntityRevisionId,
                                                                ContentType = context.ContentType
                                                            };
                                                }
                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(dashboardDataItem)"></Icon>

                                            </Tooltip>
                                            <Tooltip Text="Edit draft">
                                                @{
                                                    var dashboardDataItem = new DashboardDataItem
                                                            {
                                                                CountryCode = context.CountryCode,
                                                                EntityId = context.EntityId,
                                                                EntityRevisionId = context.EntityRevisionId,
                                                                ContentType = context.ContentType
                                                            };
                                                }
                                                <Icon Name="IconName.Edit" Clicked="e=> NavigateToDraftEdit(dashboardDataItem)"></Icon>

                                            </Tooltip>


                                        </TableRowCell>
                                        <TableRowCell>
                                            @{
                                                ModerateActions = ModerationLog.Where(m => m.EntityRevisionId == context.EntityRevisionId).ToList();
                                            }
                                            <Repeater Items="@ModerateActions" Context="user">
                                                @if (user.IsPublished)
                                                {
                                                    <Heading Size="HeadingSize.Is3">This is the published revision.</Heading>
                                                    <Paragraph class="alink"><Label Class="text-primary" @onclick="e=> OpenConfirm(UnpublishContent ,user.EntityId,user.EntityRevisionId,user.ContentType, user.Id)">Unpublish</Label></Paragraph>
                                                }
                                                else
                                                {
                                                    <Paragraph Class="date">From @user.UserVisibleFromState.ToString() --> @user.UserVisibleToState.ToString() on @DateTimeOffsetHelper.GetDateTimeFormat(user.RevisedDate) </Paragraph>
                                                    <Paragraph>by <NavLink href="#">@user.UserName</NavLink></Paragraph>
                                                }
                                            </Repeater>
                                            @{
                                                RevisionId = 0;
                                            }
                                        </TableRowCell>
                                    </TableRow>
                                </Repeater>
                            </TableBody>
                        </Table>
                    </Div>
                </Column>
            </Row>
        </ModalBody>
        <ModalFooter>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => HistoryVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Container Class="pt-5 pb-5">
            <Heading Class="h-title _admin-head">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.Title))
                <AdminEditbut Key="@DashboardPageConfiguration.Title" />
            </Heading>

            <Paragraph Class="color-w _admin-pad">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.Description.ConvertHtmlToPlainText()))
                <AdminEditbut Key="@DashboardPageConfiguration.Description" />
            </Paragraph>
            <Breadcrumb Class="bread-crumb">
                <BreadcrumbItem>
                    <BreadcrumbLink To="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem Active>
                    <BreadcrumbLink To="#">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
            </Breadcrumb>
        </Container>
    </Card>
</Container>
<Container Fluid Class="bg-trdot pt-3">
    <Container Class="Dashboard">
        <Row>
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                <Card Class="card-box1">
                    <Div Class="icon-card"><Icon Name="IconName.Edit" /></Div>
                    <Div Class="card-tital">My drafts</Div>
                    <Div Class="number">@MyCounters.MyDrafts</Div>
                </Card>
            </Column>
            <Column ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                <Card Class="card-box2">
                    <Div Class="icon-card"><Icon Name="IconName.Hourglass" /></Div>
                    <Div Class="card-tital">
                        @{
                            var othersDrafts = "Other’s drafts needs review";
                            var myDrafts = "My drafts needs review";
                            if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                            {
                                @($"{(othersDrafts)}")
                            }
                            else
                            {
                                @($"{(myDrafts)}")
                            }
                        }
                    </Div>
                    <Div Class="number">
                        @{
                            if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                            {
                                @($"{(MyCounters.OthersDraftsNeedsReview)}")
                            }
                            else
                            {
                                @($"{(MyCounters.MyDraftsNeedsReview)}")
                            }
                        }
                    </Div>
                </Card>
            </Column>

            @if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
            {
                <Column ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                    <Card Class="card-box3">
                        <Div Class="icon-card"><Icon Name="IconName.FileAlt" /></Div>
                        <Div Class="card-tital">
                            @{
                                var othersDrafts = "Others draft sent to me for correction";
                                var myDrafts = "My drafts sent for correction";
                                if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                                {
                                    @($"{(othersDrafts)}")
                                }
                                else
                                {
                                    @($"{(myDrafts)}")
                                }
                            }
                        </Div>
                        <Div Class="number">
                            @{
                                if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                                {
                                    @($"{(MyCounters.OthersDraftsSentForCorrection)}")
                                }
                                else
                                {
                                    @($"{(MyCounters.MyDraftsSentForCorrection)}")
                                }
                            }
                        </Div>
                    </Card>
                </Column>
            }


            <Column ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                <Card Class="card-box4">
                    <Div Class="icon-card"><Icon Name="IconName.FileAlt" /></Div>
                    <Div Class="card-tital">My published contents</Div>
                    <Div Class="number">@MyCounters.MyPublishedDrafts</Div>
                </Card>
            </Column>
        </Row>
        <Row Class="mt-4 mb-4">
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <Div Class="_mydrafts">
                    <Div Class="flex-header">
                        <Heading Class="_dashh3">
                            @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.ContentHeading))
                            <AdminEditbut Key="@DashboardPageConfiguration.ContentHeading" />
                        </Heading>
                    </Div>
                    <Div Class="accordion MyAccordion pb-2" id="accordionExample">
                        @*My drafts*@
                        <Div Class="accordion-item">
                            <h2 Class="accordion-header" id="heading1">
                                <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.ContentSectionTable1" /> </Div>
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" onclick="@(()=>ToggleAccordion(DashboardTab.MyDrafts))" data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.ContentSectionTable1))
                                </button>
                            </h2>
                            <Div id="collapse1" Class="accordion-collapse collapse show" aria-labelledby="heading1" data-bs-parent="#accordionExample">
                                <Div Class="accordion-body">
                                    <Fields Class="_downpad d-flex pt-2 pb-2">
                                        <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <TextEdit @bind-Text="@(myDraftInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                        </Field>
                                        <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <AntDesign.SimpleSelect @bind-Value="myDraftInputRequest.ContentType" Placeholder="Type" OnSelectedItemChanged="HandleChange" AllowClear>
                                                <SelectOptions>
                                                    <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                        <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                    </Repeater>
                                                </SelectOptions>
                                            </AntDesign.SimpleSelect>
                                            <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.MyDrafts,true))"><Icon class="fas fa-search pr-0" /></Button>
                                        </Field>
                                    </Fields>
                                    <DataGrid Class="table-nth _actions"
                                              TItem="@DashboardDataItem"
                                              Data="@MyDraftsResults"
                                              PageSize="myDraftInputRequest.PageSize"
                                              ShowPageSizes
                                              ShowPager
                                              Responsive
                                              ReadData="@((args)=>OnReadData(args,DashboardTab.MyDrafts))"
                                              TotalItems="@MyDraftsCount"
                                              CurrentPage="myDraftInputRequest.PageNo"
                                              SortChanged="@((args) => OnSortChanged( args, DashboardTab.MyDrafts))"
                                              SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                            @if (!IsAccordianLoading)
                                            {
                                                <Div>No data found.</Div>
                                            }
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                            {
                                                <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                    <DisplayTemplate Context="currentRow">
                                                        <Tooltip Text="View">
                                                            <Icon Clicked="(e)=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                        </Tooltip>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            }
                                            <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                <DisplayTemplate>
                                                    <div class="Countryhim">
                                                        @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                    </div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                            <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                            <DataGridColumn Caption="Actions" Sortable="false" Width="200px" HeaderCellClass="_actionscenter">
                                                <DisplayTemplate>
                                                    <Div Class="_f-r-center">
                                                        <Span Class="_state">
                                                            Set moderation state:
                                                            <p>
                                                                @{
                                                                    var moderateStatue = (context as DashboardDataItem)?.ToState;
                                                                    @($"{(moderateStatue)}")
                                                                }
                                                            </p>
                                                        </Span>
                                                        <Span hidden=@HidePublishButton Class="_publish">
                                                            @{
                                                                var revisionId = Convert.ToInt32((context as DashboardDataItem).Id.ToString().Replace('#', '0'));
                                                            }
                                                            @*<Button Class='but-blues mr-1' Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)">Edit</Button>*@
                                                            <Button>
                                                                <Tooltip Text="Preview">
                                                                    <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                                </Tooltip>
                                                                <Tooltip Text="Edit">
                                                                    <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                                </Tooltip>

                                                            </Button>
                                                            @* @{
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                            <Button Class='but-blues' Clicked="e=> CreatePolicyDetails(WorkflowStatusToState.Published,revisionId)">Publish</Button>
                                                            }
                                                            }*@
                                                        </Span>
                                                    </Div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                        </DataGridColumns>
                                        <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                        <TotalItemsTemplate Context="currentRow">
                                            <Badge TextColor="TextColor.Dark">
                                                @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                            </Badge>
                                        </TotalItemsTemplate>
                                    </DataGrid>
                                </Div>
                            </Div>
                        </Div>

                        @*My drafts needs review*@
                        <Div Class="accordion-item">
                            <h2 Class="accordion-header" id="heading1">
                                <Div Class="_adminEditbut">
                                    <AdminEditbut Key="@DashboardPageConfiguration.Mydraftsneedsreview" />
                                </Div>
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" onclick="@(()=>ToggleAccordion(DashboardTab.MyDraftsNeedsReview))" data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse1">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.Mydraftsneedsreview))
                                </button>
                            </h2>
                            @*My drafts needs review*@
                            <Div id="collapse2" Class="accordion-collapse collapse" aria-labelledby="heading1" data-bs-parent="#accordionExample">
                                <Div Class="accordion-body">
                                    <Fields Class="_downpad d-flex pt-2 pb-2">
                                        <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <TextEdit @bind-Text="@(myDraftInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                        </Field>
                                        <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <AntDesign.SimpleSelect @bind-Value="myDraftInputRequest.ContentType" Placeholder="Type" OnSelectedItemChanged="HandleChange" AllowClear>
                                                <SelectOptions>
                                                    <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                        <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                    </Repeater>
                                                </SelectOptions>
                                            </AntDesign.SimpleSelect>
                                            <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.MyDraftsNeedsReview,true))"><Icon class="fas fa-search pr-0" /></Button>
                                        </Field>
                                    </Fields>
                                    <DataGrid Class="table-nth _actions"
                                              TItem="@DashboardDataItem"
                                              Data="@MyDraftsNeedsReviewResults"
                                              PageSize="myDraftInputRequest.PageSize"
                                              ShowPageSizes
                                              ShowPager
                                              Responsive
                                              ReadData="@((args)=>OnReadData(args,DashboardTab.MyDraftsNeedsReview))"
                                              TotalItems="@MyDraftsInReviewCount"
                                              CurrentPage="myDraftInputRequest.PageNo"
                                              SortChanged="@((args) => OnSortChanged( args, DashboardTab.MyDraftsNeedsReview))"
                                              SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                            @if (!IsAccordianLoading)
                                            {
                                                <Div>No data found.</Div>
                                            }
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                            {
                                                <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                    <DisplayTemplate Context="currentRow">
                                                        <Tooltip Text="View">
                                                            <Icon Clicked="(e)=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                        </Tooltip>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            }
                                            <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                <DisplayTemplate>
                                                    <div class="Countryhim">
                                                        @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                    </div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                            <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                            <DataGridColumn Caption="Actions" Sortable="false" Width="200px" HeaderCellClass="_actionscenter">
                                                <DisplayTemplate>
                                                    <Div Class="_f-r-center">
                                                        <Span Class="_state">
                                                            Set moderation state:
                                                            <p>
                                                                @{
                                                                    var moderateStatue = (context as DashboardDataItem)?.ToState;
                                                                    @($"{(moderateStatue)}")
                                                                }
                                                            </p>
                                                        </Span>
                                                        <Span hidden=@HidePublishButton Class="_publish">
                                                            @{
                                                                var revisionId = Convert.ToInt32((context as DashboardDataItem).Id.ToString().Replace('#', '0'));
                                                            }
                                                            <Button>
                                                                <Tooltip Text="Preview">
                                                                    <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                                </Tooltip>
                                                                @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                                {
                                                                    <Tooltip Text="Edit">
                                                                        <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                                    </Tooltip>
                                                                }
                                                            </Button>
                                                        </Span>
                                                    </Div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                        </DataGridColumns>
                                        <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                        <TotalItemsTemplate Context="currentRow">
                                            <Badge TextColor="TextColor.Dark">
                                                @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                            </Badge>
                                        </TotalItemsTemplate>
                                    </DataGrid>
                                </Div>
                            </Div>
                        </Div>

                        @if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                        {
                            @* My/Other drafts which have sent back for correction *@
                            <Div Class="accordion-item">
                                <h2 Class="accordion-header" id="heading2">
                                    <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.SentForCorrectionByme" /></Div>
                                    <button class="accordion-button collapsed" onclick="@(()=>ToggleAccordion(DashboardTab.SentForCorrectionByMe))" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse2">
                                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.SentForCorrectionByme))


                                    </button>
                                </h2>
                                <Div id="collapse3" Class="accordion-collapse collapse" aria-labelledby="heading2" data-bs-parent="#accordionExample">
                                    <Div Class="accordion-body">
                                        <Fields Class="_downpad d-flex pt-2 pb-2">
                                            <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <TextEdit @bind-Text="@(sentForCorrectionByMeInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                            </Field>
                                            <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <AntDesign.SimpleSelect @bind-Value="sentForCorrectionByMeInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                    <SelectOptions>
                                                        <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                            <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                        </Repeater>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                                <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.SentForCorrectionByMe,true))"><Icon class="fas fa-search pr-0" /></Button>
                                            </Field>
                                        </Fields>
                                        <DataGrid Class="table-nth"
                                                  TItem="@DashboardDataItem"
                                                  Data="@DraftsSentForCorrectionByMeResults"
                                                  PageSize="sentForCorrectionByMeInputRequest.PageSize"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  ReadData="@((args)=>OnReadData(args,DashboardTab.SentForCorrectionByMe))"
                                                  TotalItems="@DraftsSentForCorrectionByMeCount"
                                                  CurrentPage="sentForCorrectionByMeInputRequest.PageNo"
                                                  SortChanged="@((args) => OnSortChanged( args, DashboardTab.SentForCorrectionByMe))"
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                                @if (!IsAccordianLoading)
                                                {
                                                    <Div>No data found.</Div>
                                                }
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                @if (CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                {
                                                    <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Tooltip Text="View">
                                                                <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                            </Tooltip>
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                }
                                                <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                    <DisplayTemplate>
                                                        <div class="Countryhim">
                                                            @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                        </div>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                                <DataGridColumn Caption="Actions" Width="14%" HeaderCellClass="_actionscenter">
                                                    <DisplayTemplate>
                                                        <Button>
                                                            <Tooltip Text="Preview">
                                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                            @if (CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                            {
                                                                <Tooltip Text="Edit">
                                                                    @{
                                                                        var policyId_MypublishedDraft = (context as DashboardDataItem)?.EntityId;
                                                                    }
                                                                    <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context)" />
                                                                </Tooltip>
                                                            }
                                                        </Button>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            </DataGridColumns>
                                            <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                            <TotalItemsTemplate Context="currentRow">
                                                <Badge TextColor="TextColor.Dark">
                                                    @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                                </Badge>
                                            </TotalItemsTemplate>
                                        </DataGrid>
                                    </Div>
                                </Div>
                            </Div>
                        }
                        @*My Published contents *@
                        <Div Class="accordion-item">
                            <h2 Class="accordion-header" id="heading3">
                                <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.ContentSectionTable3" /></Div>
                                <button class="accordion-button collapsed" type="button" onclick="@(()=>ToggleAccordion(DashboardTab.MyPublishedContents))" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse3">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.ContentSectionTable3))
                                </button>
                            </h2>
                            <Div id="collapse4" Class="accordion-collapse collapse" aria-labelledby="heading3" data-bs-parent="#accordionExample">
                                <Div Class="accordion-body">
                                    <Fields Class="_downpad d-flex pt-2 pb-2">
                                        <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <TextEdit @bind-Text="@(myPublishedContentInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                        </Field>
                                        <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <AntDesign.SimpleSelect @bind-Value="myPublishedContentInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                <SelectOptions>
                                                    <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                        <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                    </Repeater>
                                                </SelectOptions>
                                            </AntDesign.SimpleSelect>
                                            <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.MyPublishedContents,true))"><Icon class="fas fa-search pr-0" /></Button>
                                        </Field>
                                    </Fields>
                                    <DataGrid Class="table-nth"
                                              TItem="@DashboardPublishedDataItem"
                                              Data="@MyPublishedContents"
                                              PageSize="myPublishedContentInputRequest.PageSize"
                                              ShowPageSizes
                                              ShowPager
                                              Responsive
                                              ReadData="@((args)=>OnReadPubliahedData(args,DashboardTab.MyPublishedContents))"
                                              TotalItems="@MyPublishedContentsCount"
                                              CurrentPage="myPublishedContentInputRequest.PageNo"
                                              SortChanged="@((args) => OnPubliahedSortChanged( args, DashboardTab.MyPublishedContents))"
                                              SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                            @if (!IsAccordianLoading)
                                            {
                                                <Div>No data found.</Div>
                                            }
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                            {
                                                <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                    <DisplayTemplate Context="currentRow">
                                                        <Tooltip Text="View">
                                                            <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                        </Tooltip>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            }
                                            <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.EntityId)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.RevisionId)" Caption="Revision Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.CombinedTitle)" Caption="Title" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.ContentType)" Caption="Type" Width="11%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.Country)" Caption="Country" Width="12%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                <DisplayTemplate>
                                                    <div class="Countryhim">
                                                        @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                    </div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                            <DataGridColumn Field="@nameof(DashboardPublishedDataItem.PublishedBy)" Caption="Published by" />
                                            <DataGridColumn Caption="Actions" Width="14%" Sortable="false" HeaderCellClass="_actionscenter">
                                                <DisplayTemplate>
                                                    <Button>
                                                        <Tooltip Text="Preview">
                                                            <Icon Name="IconName.Eye" Clicked="e=> NavigateToPublishedDraft(context as DashboardPublishedDataItem)" />
                                                        </Tooltip>
                                                        @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                        {
                                                            <Tooltip Text="Edit">
                                                                @{
                                                                    var policyId_MypublishedDraft = (context as DashboardPublishedDataItem)?.EntityId;
                                                                }
                                                                <Icon Name="IconName.Pen" Clicked="e=> NavigateToPublishedDraftEdit(context as DashboardPublishedDataItem)" />
                                                            </Tooltip>
                                                        }
                                                    </Button>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                        </DataGridColumns>
                                        <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                        <TotalItemsTemplate Context="currentRow">
                                            <Badge TextColor="TextColor.Dark">
                                                @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                            </Badge>
                                        </TotalItemsTemplate>
                                    </DataGrid>
                                </Div>
                            </Div>
                        </Div>
                    </Div>
                </Div>
            </Column>
        </Row>
        <Row Class="mt-4 mb-4">
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <Div Class="_mydrafts">
                    <Div Class="flex-header">
                        <Heading Class="_dashh3">
                            @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.OthersContentHeading))
                            <AdminEditbut Key="@DashboardPageConfiguration.OthersContentHeading" />
                        </Heading>
                    </Div>
                    <Div Class="accordion MyAccordion pb-2" id="accordion1Example">

                        <Div Class="accordion-item">
                            <h2 Class="accordion-header" id="heading8">
                                <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.ReturnedToMeForCorrection" /></Div>
                                <button class="accordion-button collapsed" type="button" onclick="@(()=>ToggleAccordion(DashboardTab.OthersDraftsSentForCorrectionInputs))" data-bs-toggle="collapse" data-bs-target="#collapse8" aria-expanded="true" aria-controls="collapse8">
                                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.ReturnedToMeForCorrection))
                                </button>
                            </h2>
                            @* Drafts which have been returned to me for correction/inputs *@
                            <Div id="collapse8" Class="accordion-collapse collapse" aria-labelledby="heading8" data-bs-parent="#accordion1Example">
                                <Div Class="accordion-body">
                                    <Fields Class="_downpad d-flex pt-2 pb-2">
                                        <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <TextEdit @bind-Text="@(sentBackForCorrInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                        </Field>
                                        <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                            <AntDesign.SimpleSelect @bind-Value="sentBackForCorrInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                <SelectOptions>
                                                    <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                        <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                    </Repeater>
                                                </SelectOptions>
                                            </AntDesign.SimpleSelect>
                                            <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.OthersDraftsSentForCorrectionInputs,true))"><Icon class="fas fa-search pr-0" /></Button>
                                        </Field>
                                    </Fields>
                                    <DataGrid Class="table-nth"
                                              TItem="@DashboardDataItem"
                                              Data="@SentBackToMeForCorrResults"
                                              PageSize="sentBackForCorrInputRequest.PageSize"
                                              ShowPageSizes
                                              ShowPager
                                              Responsive
                                              ReadData="@((args)=>OnReadData(args,DashboardTab.OthersDraftsSentForCorrectionInputs))"
                                              TotalItems="@sentBackForCorrCount"
                                              CurrentPage="sentBackForCorrInputRequest.PageNo"
                                              SortChanged="@((args) => OnSortChanged( args, DashboardTab.OthersDraftsSentForCorrectionInputs))"
                                              SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                            @if (!IsAccordianLoading)
                                            {
                                                <Div>No data found.</Div>
                                            }
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                            {
                                                <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                    <DisplayTemplate Context="currentRow">
                                                        <Tooltip Text="View">
                                                            <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                        </Tooltip>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            }
                                            <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                            <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                <DisplayTemplate>
                                                    <div class="Countryhim">
                                                        @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                    </div>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                            <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                            <DataGridColumn Caption="Actions" Sortable="false" Width="14%" HeaderCellClass="_actionscenter">
                                                <DisplayTemplate>
                                                    <Button>
                                                        <Tooltip Text="Preview">
                                                            <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                        </Tooltip>                                                        
                                                        
                                                        <Tooltip Text="Edit">
                                                            @{
                                                                var policyId_MypublishedDraft = (context as DashboardDataItem)?.EntityId;
                                                            }
                                                            <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                        </Tooltip>                                                                                                              
                                                    </Button>
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                        </DataGridColumns>
                                        <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                        <TotalItemsTemplate Context="currentRow">
                                            <Badge TextColor="TextColor.Dark">
                                                @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                            </Badge>
                                        </TotalItemsTemplate>
                                    </DataGrid>

                                </Div>
                            </Div>
                        </Div>
                        @if (CurrentUserService.UserRole != UserRole.Contributor.ToString())
                        {

                            <Div Class="accordion-item">
                                <h2 Class="accordion-header" id="heading4">
                                    <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.OthersContentTable2" /></Div>
                                    <button class="accordion-button collapsed" onclick="@(()=>ToggleAccordion(DashboardTab.OthersDraftsNeedsReview))" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="true" aria-controls="collapse4">
                                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.OthersContentTable2))

                                    </button>
                                </h2>
                                @* Other's draft needs review *@
                                <Div id="collapse5" Class="accordion-collapse collapse" aria-labelledby="heading4" data-bs-parent="#accordion1Example">
                                    <Div Class="accordion-body">
                                        <Fields Class="_downpad d-flex pt-2 pb-2">
                                            <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <TextEdit @bind-Text="@(othersDraftsNeedReviewInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                            </Field>
                                            <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <AntDesign.SimpleSelect @bind-Value="othersDraftsNeedReviewInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                    <SelectOptions>
                                                        <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                            <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                        </Repeater>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                                <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.OthersDraftsNeedsReview,true))"><Icon class="fas fa-search pr-0" /></Button>
                                            </Field>
                                        </Fields>
                                        <DataGrid Class="table-nth"
                                                  TItem="@DashboardDataItem"
                                                  Data="@OthersDraftsNeedsReviewListResults"
                                                  PageSize="othersDraftsNeedReviewInputRequest.PageSize"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  ReadData="@((args)=>OnReadData(args,DashboardTab.OthersDraftsNeedsReview))"
                                                  SortChanged="@((args) => OnSortChanged( args, DashboardTab.OthersDraftsNeedsReview))"
                                                  TotalItems="@OthersDraftsNeedsReviewCount"
                                                  CurrentPage="othersDraftsNeedReviewInputRequest.PageNo"
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                                @if (!IsAccordianLoading)
                                                {
                                                    <Div>No data found.</Div>
                                                }
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                @if (CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                {
                                                    <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Tooltip Text="View">
                                                                <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                            </Tooltip>
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                }
                                                <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                    <DisplayTemplate>
                                                        <div class="Countryhim">
                                                            @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                        </div>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                                <DataGridColumn Caption="Actions" Sortable="false" Width="14%" HeaderCellClass="_actionscenter">
                                                    <DisplayTemplate>
                                                        <Button>
                                                            <Tooltip Text="Preview">
                                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                            <Tooltip Text="Edit">
                                                                @{
                                                                    var policyId_MypublishedDraft = (context as DashboardDataItem)?.EntityId;
                                                                }
                                                                <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                        </Button>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            </DataGridColumns>
                                            <ItemsPerPageTemplate></ItemsPerPageTemplate>
                                            <TotalItemsTemplate Context="currentRow">
                                                <Badge TextColor="TextColor.Dark">
                                                    @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                                </Badge>
                                            </TotalItemsTemplate>
                                        </DataGrid>
                                    </Div>
                                </Div>
                            </Div>

                            <Div Class="accordion-item">
                                <h2 Class="accordion-header" id="heading5">
                                    <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.DelegatedToMe" /></Div>
                                    <button class="accordion-button collapsed" type="button" onclick="@(()=>ToggleAccordion(DashboardTab.DraftsDelegatedToMe))" data-bs-toggle="collapse" data-bs-target="#collapse6" aria-expanded="true" aria-controls="collapse5">
                                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.DelegatedToMe))

                                    </button>
                                </h2>
                                @* Other's drafts, which have been delegated to me for inputs *@
                                <Div id="collapse6" Class="accordion-collapse collapse" aria-labelledby="heading5" data-bs-parent="#accordion1Example">
                                    <Div Class="accordion-body">
                                        <Fields Class="_downpad d-flex pt-2 pb-2">
                                            <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <TextEdit @bind-Text="@(othersDraftInDelegateInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                            </Field>
                                            <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <AntDesign.SimpleSelect @bind-Value="othersDraftInDelegateInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                    <SelectOptions>
                                                        <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                            <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                        </Repeater>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                                <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.DraftsDelegatedToMe,true))"><Icon class="fas fa-search pr-0" /></Button>
                                            </Field>
                                        </Fields>
                                        <DataGrid Class="table-nth"
                                                  TItem="@DashboardDataItem"
                                                  Data="@OthersDraftsInDelegateResults"
                                                  PageSize="othersDraftInDelegateInputRequest.PageSize"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  ReadData="@((args)=>OnReadData(args,DashboardTab.DraftsDelegatedToMe))"
                                                  SortChanged="@((args) => OnSortChanged( args, DashboardTab.DraftsDelegatedToMe))"
                                                  TotalItems="@OthersDraftsInDelegateCount"
                                                  CurrentPage="othersDraftInDelegateInputRequest.PageNo"
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                                @if (!IsAccordianLoading)
                                                {
                                                    <Div>No data found.</Div>
                                                }
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                {
                                                    <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Tooltip Text="View">
                                                                <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                            </Tooltip>
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                }
                                                <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                    <DisplayTemplate>
                                                        <div class="Countryhim">
                                                            @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                        </div>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                                <DataGridColumn Caption="Actions" Sortable="false" Width="14%" HeaderCellClass="_actionscenter">
                                                    <DisplayTemplate>
                                                        <Button>
                                                            <Tooltip Text="Preview">
                                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                            <Tooltip Text="Edit">
                                                                @{
                                                                    var policyId_MypublishedDraft = (context as DashboardDataItem)?.EntityId;
                                                                }
                                                                <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                        </Button>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            </DataGridColumns>
                                            <TotalItemsTemplate Context="currentRow">
                                                <Badge TextColor="TextColor.Dark">
                                                    @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                                </Badge>
                                            </TotalItemsTemplate>
                                        </DataGrid>
                                    </Div>
                                </Div>
                            </Div>

                            <Div Class="accordion-item">
                                <h2 Class="accordion-header" id="heading7">
                                    <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.DelegatedByMe" /></Div>
                                    <button class="accordion-button collapsed" type="button" onclick="@(()=>ToggleAccordion(DashboardTab.DelegatedByMe))" data-bs-toggle="collapse" data-bs-target="#collapse7" aria-expanded="true" aria-controls="collapse7">
                                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.DelegatedByMe))

                                    </button>
                                </h2>
                                @* Sent to delegate by me *@
                                <Div id="collapse7" Class="accordion-collapse collapse" aria-labelledby="heading7" data-bs-parent="#accordion1Example">
                                    <Div Class="accordion-body">
                                        <Fields Class="_downpad d-flex pt-2 pb-2">
                                            <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <TextEdit @bind-Text="@(delegateByMeInputRequest.SearchText)" Placeholder="Enter title here..."></TextEdit>
                                            </Field>
                                            <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                                <AntDesign.SimpleSelect @bind-Value="delegateByMeInputRequest.ContentType" AllowClear Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                    <SelectOptions>
                                                        <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                            <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                        </Repeater>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                                <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.DelegatedByMe,true))"><Icon class="fas fa-search pr-0" /></Button>
                                            </Field>
                                        </Fields>
                                        <DataGrid Class="table-nth"
                                                  TItem="@DashboardDataItem"
                                                  Data="@DraftsDelegatedByMeResults"
                                                  PageSize="delegateByMeInputRequest.PageSize"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  ReadData="@((args)=>OnReadData(args,DashboardTab.DelegatedByMe))"
                                                  SortChanged="@((args) => OnSortChanged( args, DashboardTab.DelegatedByMe))"
                                                  TotalItems="@DraftsDelegateByMeCount"
                                                  CurrentPage="delegateByMeInputRequest.PageNo"
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                                @if (!IsAccordianLoading)
                                                {
                                                    <Div>No data found.</Div>
                                                }
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                @if (CurrentUserService.UserRole != UserRole.Contributor.ToString() && CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                {
                                                    <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Tooltip Text="View">
                                                                <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                            </Tooltip>
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                }

                                                <DataGridColumn Displayable=false Field="@nameof(DashboardDataItem.Id)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityId)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.EntityRevisionId)" Caption="Revision Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.CombinedTitle)" Caption="Title" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.ContentType)" Caption="Type" Width="11%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.Country)" Caption="Country" Width="12%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                                <DataGridColumn Field="@nameof(DashboardDataItem.RevisedDate)" Caption="Last update" Width="17%">
                                                    <DisplayTemplate>
                                                        <div class="Countryhim">
                                                            @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                        </div>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardDataItem.UserName)" Caption="Updated by" />
                                                <DataGridColumn Caption="Actions" Sortable="false" Width="14%" HeaderCellClass="_actionscenter">
                                                    <DisplayTemplate>
                                                        <Button>
                                                            <Tooltip Text="Preview">
                                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context as DashboardDataItem)" />
                                                            </Tooltip>
                                                            
                                                            @if(CurrentUserService.UserRole != UserRole.Approver.ToString())
                                                            {
                                                                <Tooltip Text="Edit">
                                                                    @{
                                                                        var policyId_MypublishedDraft = (context as DashboardDataItem)?.EntityId;
                                                                    }
                                                                    <Icon Name="IconName.Pen" Clicked="e=> NavigateToDraftEdit(context as DashboardDataItem)" />
                                                                </Tooltip>
                                                            }                                                            
                                                        </Button>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            </DataGridColumns>
                                            <TotalItemsTemplate Context="currentRow">
                                                <Badge TextColor="TextColor.Dark">
                                                    @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                                </Badge>
                                            </TotalItemsTemplate>
                                        </DataGrid>
                                    </Div>
                                </Div>
                            </Div>


                        }
                    </Div>
                </Div>
            </Column>

        </Row>

        @if (CurrentUserService.UserRole == UserRole.Admin.ToString())
        {
            <Row Class="mt-4 mb-4">
                <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                    <Div Class="_mydrafts">
                        <Div Class="flex-header">
                            <Heading Class="_dashh3">
                                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.UsersContentHeading))
                                <AdminEditbut Key="@DashboardPageConfiguration.UsersContentHeading" />
                            </Heading>

                        </Div>
                        @*User's content*@
                        <Div Class="accordion MyAccordion pb-2" id="accordion2Example">
                            <Div Class="accordion-item">
                                <h2 Class="accordion-header" id="heading4">
                                    <Div Class="_adminEditbut"><AdminEditbut Key="@DashboardPageConfiguration.UsersContentTable1" /></Div>
                                    <button class="accordion-button collapsed" onclick="@(()=>ToggleAccordion(DashboardTab.UsersContents))" type="button" data-bs-toggle="collapse" data-bs-target="#collapse9" aria-expanded="true" aria-controls="collapse9">
                                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DashboardPageConfiguration.UsersContentTable1))

                                    </button>
                                </h2>
                                <Div id="collapse9" Class="accordion-collapse collapse" aria-labelledby="heading9" data-bs-parent="#accordion2Example">
                                    <Div Class="accordion-body">
                                        <Fields Class="_downpad d-flex pt-2 pb-2">
                                            <Field Class="mt-0 mb-0 _downpad" ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                                                <AntDesign.Select DataSource="@UsersList"
                                                                  TItemValue="string"
                                                                  LabelName="@nameof(ApplicationUser.UserName)"
                                                                  ValueName="@nameof(ApplicationUser.UserName)"
                                                                  Placeholder="User"
                                                                  EnableSearch
                                                                  AllowClear
                                                                  @bind-Value="@UsersContentInputRequest.UserName"
                                                                  TItem="ApplicationUser" />
                                            </Field>
                                            <Field Class="mt-0 mb-0" ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                                                <AntDesign.SimpleSelect AllowClear @bind-Value="UsersContentInputRequest.ContentType" Placeholder="Type" OnSelectedItemChanged="HandleChange">
                                                    <SelectOptions>
                                                        <Repeater Items="@PolicyLookup.GetPolicyLooksUp()">
                                                            <AntDesign.SimpleSelectOption Value="@context" Label="@context"></AntDesign.SimpleSelectOption>
                                                        </Repeater>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                            </Field>
                                            <Field Class="mt-0 mb-0">
                                                <AntDesign.Select DataSource="@Countries"
                                                                  TItemValue="string"
                                                                  TItem="Country"
                                                                  LabelName="@nameof(Country.Name)"
                                                                  Placeholder="Country"
                                                                  ValueName="@nameof(Country.Iso3Code)"
                                                                  @bind-Value="@UsersContentInputRequest.AssignedCountryISO"
                                                                  AllowClear
                                                                  EnableSearch
                                                                  Style="width: 100%; margin-bottom: 8px;" />
                                            </Field>
                                            <Field Class="mt-0 mb-0 d-flex pr-0" ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                                                <AntDesign.SimpleSelect AllowClear @bind-Value="UsersContentInputRequest.TargetToState" Placeholder="Status">
                                                    <SelectOptions>
                                                        <AntDesign.SimpleSelectOption Value="@WorkflowStatusToState.Draft" Label="@WorkflowStatusToState.Draft"></AntDesign.SimpleSelectOption>
                                                        <AntDesign.SimpleSelectOption Value="@WorkflowStatusToState.Published" Label="@WorkflowStatusToState.Published"></AntDesign.SimpleSelectOption>
                                                        <AntDesign.SimpleSelectOption Value="@WorkflowStatusToState.NeedsReview" Label="@WorkflowStatusToState.NeedsReview"></AntDesign.SimpleSelectOption>
                                                        <AntDesign.SimpleSelectOption Value="@WorkflowStatusToState.SentForCorrection" Label="@WorkflowStatusToState.SentForCorrection"></AntDesign.SimpleSelectOption>
                                                        <AntDesign.SimpleSelectOption Value="@WorkflowStatusToState.Delegated" Label="@WorkflowStatusToState.Delegated"></AntDesign.SimpleSelectOption>
                                                    </SelectOptions>
                                                </AntDesign.SimpleSelect>
                                                <Button Class="but-yellow pl-1 pr-1 ml-1" onclick="@(()=>ToggleAccordion(DashboardTab.UsersContents,true))"><Icon class="fas fa-search pr-0" /></Button>
                                            </Field>
                                        </Fields>
                                        <DataGrid Class="table-nth"
                                                  TItem="@DashboardPublishedDataItem"
                                                  Data="@UserContentsResults"
                                                  PageSize="UsersContentInputRequest.PageSize"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  ReadData="@((args)=> OnReadPubliahedData(args,DashboardTab.UsersContents))"
                                                  SortChanged="@((args) => OnPubliahedSortChanged( args, DashboardTab.UsersContents))"
                                                  TotalItems="@UserContentsCount"
                                                  CurrentPage="UsersContentInputRequest.PageNo"
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@LoaderVisibility" />
                                                @if (!IsAccordianLoading)
                                                {
                                                    <Div>No data found.</Div>
                                                }
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                <DataGridColumn Caption="History" Sortable="false" Width="9%">
                                                    <DisplayTemplate Context="currentRow">
                                                        <Tooltip Text="View">
                                                            <Icon Clicked="e=>VisibleHistory(currentRow.EntityId,currentRow.ContentType)" Name="IconName.Clock" />
                                                        </Tooltip>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.EntityId)" Caption="Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.RevisionId)" Caption="Revision Id" Width="6%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.CombinedTitle)" Caption="Title" Width="28%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.ContentType)" Caption="Type" Width="11%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.Country)" Caption="Country" Width="12%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.PublishedYear)" Caption="Year" Width="9%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.RevisedDate)" Caption="Last update" Width="13%">
                                                    <DisplayTemplate>
                                                        <div class="Countryhim">
                                                            @context.RevisedDate?.ToString("dd-MM-yyyy")
                                                        </div>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.UserVisibleToState)" Caption="Current state" Width="20%" />
                                                <DataGridColumn Field="@nameof(DashboardPublishedDataItem.PublishedBy)" Caption="Updated by" Width="20%" />
                                                <DataGridColumn Caption="Actions" Sortable="false" Width="14%" HeaderCellClass="_actionscenter">
                                                    <DisplayTemplate>
                                                        <Button>
                                                            <Tooltip Text="Preview">
                                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToPublishedDraft(context as DashboardPublishedDataItem)" />
                                                            </Tooltip>
                                                            <Tooltip Text="Edit">
                                                                @{
                                                                    var policyId_MypublishedDraft = (context as DashboardPublishedDataItem)?.EntityId;
                                                                }
                                                                <Icon Name="IconName.Pen" Clicked="e=> NavigateToPublishedDraftEdit(context as DashboardPublishedDataItem)" />
                                                            </Tooltip>
                                                        </Button>
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                            </DataGridColumns>
                                            @* <LoadingTemplate>
                                              <s @ref="progressRef" Color="Color.Primary" Max="100" Value="progress" />
                                         </LoadingTemplate>*@
                                            <TotalItemsTemplate Context="currentRow">
                                                <Badge TextColor="TextColor.Dark">
                                                    @((currentRow.CurrentPageSize * (@currentRow.CurrentPage - 1) + 1)) - @(Math.Min(((@currentRow.CurrentPage - 1) * currentRow.CurrentPageSize) + currentRow.CurrentPageSize, currentRow.TotalItems ?? 0))  of @currentRow.TotalItems data items
                                                </Badge>
                                            </TotalItemsTemplate>
                                        </DataGrid>
                                    </Div>
                                </Div>
                            </Div>
                        </Div>
                    </Div>
                </Column>

            </Row>
        }
    </Container>
</Container>


<style>
    .ant-modal-wrap {
        z-index: 999999 !important;
    }
</style>

@code {
    public RenderFragment DraftContent { get; set; } = @<p>Are you sure to create a new draft?</p>;
    public RenderFragment UnpublishContent { get; set; } = @<p>Are you sure to unpublish?</p>;
    public RenderFragment RevertContent { get; set; } = @<p>Are you sure to revert this version?</p>;
}
