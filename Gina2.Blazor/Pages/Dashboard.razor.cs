﻿using AntDesign;
using Blazorise.DataGrid;
using Blazorise.Snackbar;
using DocumentFormat.OpenXml.Spreadsheet;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Enums;
using Gina2.Core.Interface;
using Gina2.Core.Lookups;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.DbModels.Views;
using Gina2.Services.Commitments;
using Gina2.Services.Country;
using Gina2.Services.Dashboard;
using Gina2.Services.Mechanism;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin,Approver,Contributor")]
    public partial class Dashboard : PageConfirgurationComponent
    {
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IDashboardService DashboardService { get; set; }

        [Inject]
        private ModalService ModalService { get; set; }

        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private UserManager<ApplicationUser> UserManager { get; set; }
        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }

        [Inject]
        private IMechanismRevisionService MechanismRevisionService { get; set; }

        [Inject]
        private IProgramRevisionService ProgramRevisionService { get; set; }

        [Inject]
        private ICommitmentRevisionService CommitmentRevisionService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private TimeZoneService TimeZoneService { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        [Inject]
        private ICommitmentService CommitmentService { get; set; }

        private List<ModerationRevisionLog> ModerationLog { get; set; } = new();
        int lastRevisionId;
        DashboardCounters MyCounters = new();
        List<DashboardDataItem> MyDraftsResults { get; set; } = new();
        List<DashboardDataItem> MyDraftsNeedsReviewResults { get; set; } = new();
        List<DashboardPublishedDataItem> MyPublishedContents { get; set; } = new();
        List<ModerationRevisionLog> ModerateActions { get; set; } = new();
        int MyDraftsCount { get; set; } = 0;
        int MyDraftsInReviewCount { get; set; } = 0;
        int MyDraftsSendForCorrCount { get; set; } = 0;
        int MyPublishedContentsCount { get; set; } = 0;
        int sentBackForCorrCount { get; set; } = 0;
        private bool DeleteVisible;
        private bool HistoryVisible;
        private readonly string selectedTab = "MyDrafts";
        private Blazorise.Visibility LoaderVisibility = Blazorise.Visibility.Visible;
        bool HidePublishButton { get; set; } = false;
        private List<Country> Countries { get; set; } = new List<Country>();
        public bool OndaftsVisible { get; set; } = false;
        List<DashboardDataItem> OthersDraftsNeedsReviewListResults { get; set; } = new();
        int OthersDraftsNeedsReviewCount { get; set; } = 0;
        List<DashboardDataItem> SentBackToMeForCorrResults { get; set; } = new();
        List<DashboardDataItem> OthersDraftsInDelegateResults { get; set; } = new();
        private int OthersDraftsInDelegateCount = 0;

        List<DashboardDataItem> DraftsDelegatedByMeResults { get; set; } = new();
        private int DraftsDelegateByMeCount = 0;

        List<DashboardDataItem> DraftsSentForCorrectionByMeResults { get; set; } = new();
        private int DraftsSentForCorrectionByMeCount = 0;

        public List<DashboardPublishedDataItem> UserContentsResults { get; set; } = new();
        private int UserContentsCount = 0;

        List<ApplicationUser> UsersList { get; set; } = new();
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new();
        public List<UserModel> ContributorList { get; set; } = new();
        Snackbar snackbar;
        private int RevisionId { get; set; }
        private SnackbarStack SnackbarStack { get; set; }
        private DashboardInputRequest myDraftInputRequest = new();
        private DashboardInputRequest myDraftNeedsReviewInputRequest = new();
        private DashboardInputRequest myPublishedContentInputRequest = new();
        private DashboardInputRequest othersDraftInDelegateInputRequest = new();
        private DashboardInputRequest sentBackForCorrInputRequest = new();
        private DashboardInputRequest delegateByMeInputRequest = new();
        private DashboardInputRequest sentForCorrectionByMeInputRequest = new();
        private DashboardInputRequest othersDraftsNeedReviewInputRequest = new();
        private DashboardInputRequest dashboardCountersInputRequest = new();
        private DashboardInputRequest UsersContentInputRequest = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                object data = MemoryCache.Get("submit");
                MemoryCache.Remove("submit");
                if (data != null)
                {
                    _ = OpenToaster(data.ToString(), "", AntDesign.NotificationType.Success);

                }
                Countries = await CountryService.GetCountriesAsync();
                UsersList = await UserManager.Users.ToListAsync();
                ContributorList = await GetUsersAsync();
                myDraftInputRequest.UserName = CurrentUserService.UserName;
                myDraftNeedsReviewInputRequest.UserName = CurrentUserService.UserName;
                myPublishedContentInputRequest.UserName = CurrentUserService.UserName;
                await GetDashboardCounters();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task<List<UserModel>> GetUsersAsync()
        {
            using var _dbContext = DbFactory.CreateDbContext();

            return await (from user in _dbContext.Users
                          join userRole in _dbContext.UserRoles
                         on user.Id equals userRole.UserId
                          join role in _dbContext.Roles
                          on userRole.RoleId equals role.Id
                          where user.Status == "Active"
                          select new UserModel()
                          {
                              Id = user.Id,
                              UserName = user.UserName,
                              DisplayName = String.Format("{0} {1}", user.FirstName, user.LastName)
                          }).ToListAsync();
        }

        private async Task GetDashboardCounters()
        {
            dashboardCountersInputRequest.UserName = CurrentUserService.UserName;
            if (CurrentUserService.ApproverCountryCode != null && CurrentUserService.ApproverCountryCode.Any())
            {
                dashboardCountersInputRequest.AssignedCountryISO = string.Join(",", CurrentUserService.ApproverCountryCode);
            }
            MyCounters = (await DashboardService.GetDashboardCountersAsync(dashboardCountersInputRequest, CurrentUserService.UserRole)).FirstOrDefault();
        }

        private async Task GetDashboardContent(DashboardTab dashboardTab, bool forceLoadData = false)
        {
            if (dashboardTab == DashboardTab.MyDrafts && (MyDraftsResults.Any() == false || forceLoadData))
            {
                MyDraftsResults = new();
                MyDraftsCount = 0;
                myDraftInputRequest.TargetToState = WorkflowStatusToState.Draft.ToString();
                myDraftInputRequest.DashboardTab = DashboardTab.MyDrafts;
                myDraftInputRequest.UserName = CurrentUserService.UserName;
                MyDraftsResults = await DashboardService?.GetMyContentAsync(myDraftInputRequest);
                MyDraftsCount = MyDraftsResults.FirstOrDefault() != null ? MyDraftsResults.FirstOrDefault().TotalCount : 0;

            }
            else
                if (dashboardTab == DashboardTab.MyDraftsNeedsReview && (MyDraftsNeedsReviewResults.Any() == false || forceLoadData))
            {
                myDraftNeedsReviewInputRequest.TargetToState = WorkflowStatusToState.NeedsReview.ToString();
                myDraftNeedsReviewInputRequest.UserName = CurrentUserService.UserName;
                myDraftNeedsReviewInputRequest.DashboardTab = DashboardTab.MyDraftsNeedsReview;
                MyDraftsNeedsReviewResults = await DashboardService?.GetMyContentAsync(myDraftNeedsReviewInputRequest);
                MyDraftsInReviewCount = MyDraftsNeedsReviewResults.FirstOrDefault() != null ? MyDraftsNeedsReviewResults.FirstOrDefault().TotalCount : 0;
            }
            else
                if (dashboardTab == DashboardTab.MyPublishedContents && (MyPublishedContents.Any() == false || forceLoadData))
            {
                MyPublishedContents = new();
                MyPublishedContentsCount = 0;
                myPublishedContentInputRequest.TargetToState = WorkflowStatusToState.Published.ToString();
                myPublishedContentInputRequest.UserName = CurrentUserService.UserName;
                myPublishedContentInputRequest.DashboardTab = DashboardTab.MyPublishedContents;
                myPublishedContentInputRequest.IsPublished = true;
                MyPublishedContents = await DashboardService?.GetDashboardUserPublishedContentAsync(myPublishedContentInputRequest);
                MyPublishedContentsCount = MyPublishedContents.FirstOrDefault() != null ? MyPublishedContents.FirstOrDefault().TotalCount : 0;

            }
            else
                if (dashboardTab == DashboardTab.DraftsDelegatedToMe && (OthersDraftsInDelegateResults.Any() == false || forceLoadData))
            {
                OthersDraftsInDelegateResults = new();
                OthersDraftsInDelegateCount = 0;
                othersDraftInDelegateInputRequest.TargetToState = WorkflowStatusToState.Delegated.ToString();
                othersDraftInDelegateInputRequest.UserName = CurrentUserService.UserName;
                othersDraftInDelegateInputRequest.DashboardTab = DashboardTab.DraftsDelegatedToMe;
                OthersDraftsInDelegateResults = await DashboardService?.GetMyContentAsync(othersDraftInDelegateInputRequest);
                OthersDraftsInDelegateCount = OthersDraftsInDelegateResults.FirstOrDefault() != null ? OthersDraftsInDelegateResults.FirstOrDefault().TotalCount : 0;

            }
            else
                if (dashboardTab == DashboardTab.OthersDraftsSentForCorrectionInputs && (SentBackToMeForCorrResults.Any() == false || forceLoadData))
            {
                SentBackToMeForCorrResults = new();
                sentBackForCorrCount = 0;
                sentBackForCorrInputRequest.TargetToState = WorkflowStatusToState.SentForCorrection.ToString();
                sentBackForCorrInputRequest.UserName = CurrentUserService.UserName;
                sentBackForCorrInputRequest.DashboardTab = DashboardTab.OthersDraftsSentForCorrectionInputs;
                sentBackForCorrInputRequest.UserRole = CurrentUserService.UserRole;
                SentBackToMeForCorrResults = await DashboardService?.GetMyContentAsync(sentBackForCorrInputRequest);
                sentBackForCorrCount = SentBackToMeForCorrResults.FirstOrDefault() != null ? SentBackToMeForCorrResults.FirstOrDefault().TotalCount : 0;

            }
            else
                if (dashboardTab == DashboardTab.DelegatedByMe && (DraftsDelegatedByMeResults.Any() == false || forceLoadData))
            {
                DraftsDelegatedByMeResults = new();
                DraftsDelegateByMeCount = 0;
                delegateByMeInputRequest.TargetToState = WorkflowStatusToState.Delegated.ToString();
                delegateByMeInputRequest.UserName = CurrentUserService.UserName;
                delegateByMeInputRequest.DashboardTab = DashboardTab.DelegatedByMe;
                DraftsDelegatedByMeResults = await DashboardService?.GetMyContentAsync(delegateByMeInputRequest);
                DraftsDelegateByMeCount = DraftsDelegatedByMeResults.FirstOrDefault() != null ? DraftsDelegatedByMeResults.FirstOrDefault().TotalCount : 0;
            }
            else
                if (dashboardTab == DashboardTab.SentForCorrectionByMe && (DraftsSentForCorrectionByMeResults.Any() == false || forceLoadData))
            {
                DraftsSentForCorrectionByMeResults = new();
                DraftsSentForCorrectionByMeCount = 0;
                sentForCorrectionByMeInputRequest.TargetToState = WorkflowStatusToState.SentForCorrection.ToString();
                sentForCorrectionByMeInputRequest.UserName = CurrentUserService.UserName;
                sentForCorrectionByMeInputRequest.DashboardTab = DashboardTab.SentForCorrectionByMe;
                DraftsSentForCorrectionByMeResults = await DashboardService?.GetMyContentAsync(sentForCorrectionByMeInputRequest);
                DraftsSentForCorrectionByMeCount = DraftsSentForCorrectionByMeResults.FirstOrDefault() != null ? DraftsSentForCorrectionByMeResults.FirstOrDefault().TotalCount : 0;
            }
            else
                if (dashboardTab == DashboardTab.OthersDraftsNeedsReview && (OthersDraftsNeedsReviewListResults.Any() == false || forceLoadData))
            {
                OthersDraftsNeedsReviewListResults = new();
                OthersDraftsNeedsReviewCount = 0;
                othersDraftsNeedReviewInputRequest.TargetToState = WorkflowStatusToState.NeedsReview.ToString();
                othersDraftsNeedReviewInputRequest.DashboardTab = DashboardTab.OthersDraftsNeedsReview;
                othersDraftsNeedReviewInputRequest.UserName = CurrentUserService.UserName;
                if (CurrentUserService.ApproverCountryCode != null && CurrentUserService.ApproverCountryCode.Any())
                {
                    othersDraftsNeedReviewInputRequest.AssignedCountryISO = string.Join(",", CurrentUserService.ApproverCountryCode);
                }
                OthersDraftsNeedsReviewListResults = await DashboardService?.GetMyContentAsync(othersDraftsNeedReviewInputRequest);
                OthersDraftsNeedsReviewCount = OthersDraftsNeedsReviewListResults.FirstOrDefault() != null ? OthersDraftsNeedsReviewListResults.FirstOrDefault().TotalCount : 0;
            }
            else
                if (dashboardTab == DashboardTab.UsersContents && (UserContentsResults.Any() == false || forceLoadData))
            {
                UserContentsResults = new();
                UserContentsCount = 0;

                UsersContentInputRequest.DashboardTab = DashboardTab.UsersContents;
                UsersContentInputRequest.IsPublished = UsersContentInputRequest.TargetToState == WorkflowStatusToState.Published ? true : null;
                UserContentsResults = await DashboardService?.GetDashboardUserPublishedContentAsync(UsersContentInputRequest);
                UserContentsCount = UserContentsResults.FirstOrDefault() != null ? UserContentsResults.FirstOrDefault().TotalCount : 0;

            }

            StateHasChanged();
        }

        private async Task ToggleAccordion(DashboardTab dashboardTab, bool forceLoad = false)
        {
            IsAccordianLoading = true;
            isSearchInitiated = true;
            await GetDashboardContent(dashboardTab, forceLoad);
            IsAccordianLoading = false;
        }

        private static void HandleChange(string value)
        {
            Console.WriteLine(value);
        }

        private void UserFilter(ApplicationUser user)
        {
            if (user != null)
            {
                UsersContentInputRequest.UserName = user.UserName;
            }
        }

        private void CountryFilter(Country country)
        {
            if (country != null)
            {
                UsersContentInputRequest.AssignedCountryISO = country.Iso3Code;
            }
        }

        private async Task VisibleHistory(int entityId, string contentType)
        {
            HistoryVisible = true;
            await RefreshHistoryModal(entityId, contentType);
            await JSRuntime.InvokeVoidAsync("dragPopup", "Dashdraggable", "ant-header");
        }

        private async Task RefreshHistoryModal(int entityId, string contentType)
        {
            IsLoading = true;
            ModerationLog = await DashboardService.GetModerationLogByEntityId(entityId, contentType);
            lastRevisionId = ModerationLog.Any() ? ModerationLog.Max(s => s.EntityRevisionId) : 0;
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }
        private async Task ApplyActions(string contentType, int entityId, int versionId, int logId)
        {
            switch (contentType)
            {
                case "Policies":
                    var policyRevision = await PolicyDraftService.GetPolicyRevisionToCreateAdraftByVersionIdAsync(entityId, versionId);
                    policyRevision.PolicyLog.Clear();
                    policyRevision.PolicyLog.Add(new PolicyLog
                    {
                        PolicyId = policyRevision.Id,
                        PolicyVId = policyRevision.VersionId,
                        FromState = WorkflowStatusToState.Published,
                        ToState = WorkflowStatusToState.Draft,
                        RevisedDate = DateTimeOffset.UtcNow,
                        UserName = CurrentUserService.UserName,
                    });
                    await PolicyDraftService.UnpublishPolicy(policyRevision);
                    break;

                case "Mechanisms":
                    var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsToCreateAdraftAsync(entityId, versionId);
                    mechanismRevision.MechanismLog.Clear();
                    mechanismRevision.MechanismLog.Add(new MechanismLog
                    {
                        MechanismId = mechanismRevision.Id,
                        MechanismVId = mechanismRevision.VersionId,
                        FromState = WorkflowStatusToState.Published,
                        ToState = WorkflowStatusToState.Draft,
                        RevisedDate = DateTimeOffset.UtcNow,
                        UserName = CurrentUserService.UserName,
                    });
                    await MechanismRevisionService.CreateMechanismRevision(mechanismRevision, true);
                    break;

                case "Actions":
                    var program = await ProgramRevisionService.GetProgramId(entityId);

                    var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsToCreateAdraftAsync(program.ProgramId, program.ProgramVId);
                    programRevision.ProgramLog.Clear();
                    programRevision.ProgramLog.Add(new ProgramLog
                    {
                        ProgramId = programRevision.Id,
                        ProgramVId = programRevision.VersionId,
                        FromState = WorkflowStatusToState.Published,
                        ToState = WorkflowStatusToState.Draft,
                        RevisedDate = DateTimeOffset.UtcNow,
                        UserName = CurrentUserService.UserName,
                    });
                    var actionRevision = programRevision.ActionProgramRevisionMap.Select(s => s.ActionRevision).ToList();
                    await ProgramRevisionService.CreateProgramRevision(programRevision, actionRevision, true);
                    break;

                case "smart commitments":
                    var commitment = await CommitmentRevisionService.GetCommitmentId(entityId);

                    var commitmentRevision = await CommitmentRevisionService.GetCommitmentRevisionToCreateAdraftAsync(commitment.CommitmentId, commitment.CommitmentVId);
                    commitmentRevision.CommitmentLog.Clear();
                    commitmentRevision.CommitmentLog.Add(new CommitmentLog
                    {
                        CommitmentId = commitmentRevision.Id,
                        CommitmentVId = commitmentRevision.VersionId,
                        FromState = WorkflowStatusToState.Published,
                        ToState = WorkflowStatusToState.Draft,
                        RevisedDate = DateTimeOffset.UtcNow,
                        UserName = CurrentUserService.UserName,
                    });
                    var smartCommitmentRevision = commitmentRevision.SmartCommitmentCommitmentRevisionMap.Select(s => s.SmartCommitmentRevision).ToList();
                    await CommitmentRevisionService.SaveCommitmentRevision(commitmentRevision, smartCommitmentRevision, true);
                    await DashboardService.UnpublishModerationLogByLogId(logId, contentType);
                    await CommitmentService.RemoveByIdAsync(entityId);
                    break;
            }
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
            //await RefreshHistoryModal(entityId, contentType);
        }
        public async void NavigateToDraftEdit(DashboardDataItem DashboardDataItem)
        {
            string countryISO = DashboardDataItem.CountryCode.Contains(",") ?
                                     DashboardDataItem.CountryCode.Split(',').ElementAt(0) : DashboardDataItem.CountryCode;

            switch (DashboardDataItem.ContentType.ToLower())
            {
                case "policies":
                    NavigationManager.NavigateTo($"countries/{countryISO}/policies/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}/Edit");
                    break;
                case "mechanisms":
                    NavigationManager.NavigateTo($"countries/{countryISO}/mechanisms/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}/Edit");
                    break;
                case "actions":
                    var program = await ProgramRevisionService.GetProgramId(DashboardDataItem.EntityId);
                    NavigationManager.NavigateTo($"countries/{countryISO}/programmes-and-actions/{program.ProgramId}/{program.ProgramVId}/Edit");
                    break;
                case "smart commitments":
                    var commitment = await CommitmentRevisionService.GetCommitmentId(DashboardDataItem.EntityId);
                    NavigationManager.NavigateTo($"countries/{countryISO}/commitments/{commitment.CommitmentId}/{commitment.CommitmentVId}/Edit");
                    break;
            }

        }
        public void NavigateToDraft(DashboardDataItem DashboardDataItem)
        {
            string countryISO = DashboardDataItem.CountryCode.Contains(",") ?
                                     DashboardDataItem.CountryCode.Split(',').ElementAt(0) : DashboardDataItem.CountryCode;

            switch (DashboardDataItem.ContentType.ToLower())
            {
                case "policies":
                    NavigationManager.NavigateTo($"countries/{countryISO}/policies/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}");
                    break;
                case "mechanisms":
                    NavigationManager.NavigateTo($"countries/{countryISO}/mechanisms/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}");
                    break;
                case "actions":
                    NavigationManager.NavigateTo($"countries/{countryISO}/programmes-and-actions/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}");
                    break;
                case "smart commitments":
                    NavigationManager.NavigateTo($"countries/{countryISO}/commitments/{DashboardDataItem.EntityId}/{DashboardDataItem.EntityRevisionId}");
                    break;
            }

        }

        public void NavigateToPublishedDraft(DashboardPublishedDataItem DashboardDataItem)
        {
            string countryISO = DashboardDataItem.CountryCode.Contains(",") ?
                                     DashboardDataItem.CountryCode.Split(',').ElementAt(0) : DashboardDataItem.CountryCode;

            switch (DashboardDataItem.ContentType.ToLower())
            {
                case "policies":
                    NavigationManager.NavigateTo($"countries/{countryISO}/policies/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}");
                    break;
                case "mechanisms":
                    NavigationManager.NavigateTo($"countries/{countryISO}/mechanisms/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}");
                    break;
                case "actions":
                    NavigationManager.NavigateTo($"countries/{countryISO}/programmes-and-actions/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}");
                    break;
                case "smart commitments":
                    NavigationManager.NavigateTo($"countries/{countryISO}/commitments/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}");
                    break;
            }

        }


        public async Task NavigateToPublishedDraftEdit(DashboardPublishedDataItem DashboardDataItem)
        {
            string countryISO = DashboardDataItem.CountryCode.Contains(",") ?
                                     DashboardDataItem.CountryCode.Split(',').ElementAt(0) : DashboardDataItem.CountryCode;

            switch (DashboardDataItem.ContentType.ToLower())
            {
                case "policies":
                    NavigationManager.NavigateTo($"countries/{countryISO}/policies/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}/Edit");
                    break;
                case "mechanisms":
                    NavigationManager.NavigateTo($"countries/{countryISO}/mechanisms/{DashboardDataItem.EntityId}/{DashboardDataItem.RevisionId}/Edit");
                    break;
                case "actions":
                    var program = await ProgramRevisionService.GetProgramId(DashboardDataItem.EntityId);
                    NavigationManager.NavigateTo($"countries/{countryISO}/programmes-and-actions/{program.ProgramId}/{program.ProgramVId}/Edit");
                    break;
                case "smart commitments":
                    var commitment = await CommitmentRevisionService.GetCommitmentId(DashboardDataItem.EntityId);
                    NavigationManager.NavigateTo($"countries/{countryISO}/commitments/{commitment.CommitmentId}/{commitment.CommitmentVId}/Edit");
                    break;
            }

        }

        //public async Task ApplyAction(int policyRevisionId, DashboardTab tabType)
        //{
        //    if (tabType == DashboardTab.OthersDraftsNeedsReview)
        //    {
        //        if (DicOthersDraftInReview.ContainsKey(policyRevisionId))
        //        {
        //            var workFlowYStatus = DicOthersDraftInReview[policyRevisionId].WorkFlowStatus;
        //            await CreatePolicyDetails(workFlowYStatus, policyRevisionId, false, DicOthersDraftInReview[policyRevisionId].UserId);
        //        }
        //        else
        //        {
        //            _ = OpenToaster("Validation:", "Please select action to apply.", AntDesign.NotificationType.Error);
        //        }
        //    }
        //    else
        //        if (tabType == DashboardTab.DraftsDelegatedToMe)
        //    {
        //        if (DicOthersDrftInDelegate.ContainsKey(policyRevisionId))
        //        {
        //            var workFlowYStatus = DicOthersDrftInDelegate[policyRevisionId].WorkFlowStatus;
        //            await CreatePolicyDetails(workFlowYStatus, policyRevisionId, false, DicOthersDrftInDelegate[policyRevisionId].UserId);
        //        }
        //        else
        //        {
        //            _ = OpenToaster("Validation:", "Please select action to apply.", AntDesign.NotificationType.Error);
        //        }
        //    }
        //    else
        //        if (tabType == DashboardTab.OthersDraftsSentForCorrectionInputs)
        //    {
        //        if (DicOthersDrftSendForCorr.ContainsKey(policyRevisionId))
        //        {
        //            var workFlowYStatus = DicOthersDrftSendForCorr[policyRevisionId].WorkFlowStatus;
        //            await CreatePolicyDetails(workFlowYStatus, policyRevisionId, false, DicOthersDrftSendForCorr[policyRevisionId].UserId);
        //        }
        //        else
        //        {
        //            _ = OpenToaster("Validation:", "Please select action to apply.", AntDesign.NotificationType.Error);
        //        }
        //    }
        //}

        private async Task OpenConfirm(RenderFragment renderFragment, int entityId, int versionId, string contentType, int logId)
        {
            bool unPublish = false;
            if (renderFragment == UnpublishContent)
            {
                unPublish = true;
            }
            var options = new ConfirmOptions()
            {
                Title = "Confirm your action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await ApplyActions(contentType, entityId, versionId, logId);
                    HistoryVisible = false;
                    NavigationManager.NavigateTo("admin/dashboard");
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        bool isSearchInitiated = false;
        private async Task OnReadData(DataGridReadDataEventArgs<DashboardDataItem> e, DashboardTab dashboardTab)
        {
            if (!e.CancellationToken.IsCancellationRequested)
            {
                // Update pagination parameters based on the dashboard tab
                switch (dashboardTab)
                {
                    case DashboardTab.MyDrafts:
                        myDraftInputRequest.PageNo = e.Page;
                        myDraftInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.MyDraftsNeedsReview:
                        myDraftNeedsReviewInputRequest.PageNo = e.Page;
                        myDraftNeedsReviewInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.DraftsDelegatedToMe:
                        othersDraftInDelegateInputRequest.PageNo = e.Page;
                        othersDraftInDelegateInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.DelegatedByMe:
                        delegateByMeInputRequest.PageNo = e.Page;
                        delegateByMeInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.SentForCorrectionByMe:
                        sentForCorrectionByMeInputRequest.PageNo = e.Page;
                        sentForCorrectionByMeInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.OthersDraftsNeedsReview:
                        othersDraftsNeedReviewInputRequest.PageNo = e.Page;
                        othersDraftsNeedReviewInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.OthersDraftsSentForCorrectionInputs:
                        sentBackForCorrInputRequest.PageNo = e.Page;
                        sentBackForCorrInputRequest.PageSize = e.PageSize;
                        break;
                }

                // Only load data if it's a pagination or sorting request
                if (e.ReadDataMode == DataGridReadDataMode.Paging || e.ReadDataMode == DataGridReadDataMode.Sorting)
                {
                    await LoadDashboardDataForPagination(dashboardTab);
                }
            }
        }

        private async Task OnSortChanged(DataGridSortChangedEventArgs e, DashboardTab dashboardTab)
        {
            IsAccordianLoading = true;
            await InvokeAsync(StateHasChanged);

            if (dashboardTab == DashboardTab.MyDrafts)
            {
                myDraftInputRequest.ColumnName = e.FieldName;
                myDraftInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.MyDraftsNeedsReview)
            {
                myDraftNeedsReviewInputRequest.ColumnName = e.FieldName;
                myDraftNeedsReviewInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.MyPublishedContents)
            {
                myPublishedContentInputRequest.ColumnName = e.FieldName;
                myPublishedContentInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.DraftsDelegatedToMe)
            {
                othersDraftInDelegateInputRequest.ColumnName = e.FieldName;
                othersDraftInDelegateInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.DelegatedByMe)
            {
                delegateByMeInputRequest.ColumnName = e.FieldName;
                delegateByMeInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.SentForCorrectionByMe)
            {
                sentForCorrectionByMeInputRequest.ColumnName = e.FieldName;
                sentForCorrectionByMeInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.OthersDraftsNeedsReview)
            {
                othersDraftsNeedReviewInputRequest.ColumnName = e.FieldName;
                othersDraftsNeedReviewInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.UsersContents)
            {
                UsersContentInputRequest.ColumnName = e.FieldName;
                UsersContentInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }

            await GetDashboardContent(dashboardTab, true);
            IsAccordianLoading = false;
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnPubliahedSortChanged(DataGridSortChangedEventArgs e, DashboardTab dashboardTab)
        {
            if (dashboardTab == DashboardTab.MyPublishedContents)
            {
                myPublishedContentInputRequest.ColumnName = e.FieldName;
                myPublishedContentInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            else if (dashboardTab == DashboardTab.UsersContents)
            {
                UsersContentInputRequest.ColumnName = e.FieldName;
                UsersContentInputRequest.SortOrder = e.SortDirection.ToString() == "Descending" ? "DESC" : "ASC";
            }
            await GetDashboardContent(dashboardTab, true);
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnReadPubliahedData(DataGridReadDataEventArgs<DashboardPublishedDataItem> e, DashboardTab dashboardTab)
        {
            if (!e.CancellationToken.IsCancellationRequested)
            {
                // Update pagination parameters based on the dashboard tab
                switch (dashboardTab)
                {
                    case DashboardTab.MyPublishedContents:
                        myPublishedContentInputRequest.PageNo = e.Page;
                        myPublishedContentInputRequest.PageSize = e.PageSize;
                        break;
                    case DashboardTab.UsersContents:
                        UsersContentInputRequest.PageNo = e.Page;
                        UsersContentInputRequest.PageSize = e.PageSize;
                        break;
                }

                await GetDashboardContent(dashboardTab, true);
                await InvokeAsync(StateHasChanged);
            }
        }

    }
}
