﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.DbModels;
using Gina2.Services.Language;
using Gina2.Services.MapIndicatorConfigurations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.JSInterop;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class MapIndicator : PageConfirgurationComponent
    {
        [Inject]
        private IMapIndicatorConfigurationService MapIndicatorConfigurationService { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IIndicatorTypeService IndicatorTypeService { get; set; }
        [Inject]
        private IMapIndictorService MapIndictorService { get; set; }
        private IEnumerable<MapIndicatorConfiguration> MapIndicatorConfigurations { get; set; } = Enumerable.Empty<MapIndicatorConfiguration>();
        private MapIndicatorConfiguration MapIndicatorConfiguration { get; set; } = new MapIndicatorConfiguration();
        private bool IsSavingloading { get; set; } = false;
        private readonly List<string> SymbolLookUp = new() { "<", "≤", ">", "≥" };
        public List<IndicatorType> IndicatorLookups { get; set; } = new List<IndicatorType>();
        private bool ThemesVisible;
        bool deleteModalVisible = false;
        int indicatorToBeDeleted=0;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                await GetMapIndicatorConfiguration();
                IndicatorLookups = await IndicatorTypeService.GetIndicatorLookUp();
                await Task.Run(() => IsLoading = false);
                StateHasChanged();
            }
        }

        private async Task GetMapIndicatorConfiguration()
        {
            MapIndicatorConfigurations = await MapIndicatorConfigurationService.GetAllAsync();
        }
        public async Task DeleteById(int indicatorId)
        {
            await Task.Run(() => IsLoading = true);
            var isDeleted = await MapIndicatorConfigurationService.DeleteIndicatorById(indicatorId);
            deleteModalVisible = false;
            if (isDeleted)
            {
                _ = OpenSuccessToaster("Map indicator data deleted successfully");
                await GetMapIndicatorConfiguration();
            }
            else
            {
                _ = OpenErrorToaster("Map indicator data not deleted successfully");
            }
            await Task.Run(() => IsLoading = false);
        }
        private void ShowAddForm()
        {
            ThemesVisible = true;
            MapIndicatorConfiguration = new()
            {
                ColorCode = "#0d0d0d"
            };
            StateHasChanged();
        }
        private void HideModal()
        {
            ThemesVisible = false;
            StateHasChanged();
        }
        private async Task UpdateById(MapIndicatorConfiguration mapIndicatorConfiguration)
        {

            await Task.Run(() => IsLoading = true);
            ShowAddForm();
            MapIndicatorConfiguration = new MapIndicatorConfiguration()
            {
                Id = mapIndicatorConfiguration.Id,
                IndicatorId = mapIndicatorConfiguration.IndicatorId,
                StartValue = mapIndicatorConfiguration.StartValue,
                Symbol = mapIndicatorConfiguration.Symbol,
                EndValue = mapIndicatorConfiguration.EndValue,
                ColorCode = mapIndicatorConfiguration.ColorCode,
                IndicatorOrder = mapIndicatorConfiguration.IndicatorOrder,
            };
            await Task.Run(() => IsLoading = false);
            StateHasChanged();
        }
        private async Task BtnSaveClicked()
        {
            IsSavingloading = true;
            await AddorUpdateRecord();
            MapIndicatorConfiguration = new();
            
            IsSavingloading = false;
        }
        private async Task AddorUpdateRecord()
        {
            var isSameEntryExists = await MapIndicatorConfigurationService.IsSameEntryExists(MapIndicatorConfiguration);
            HideModal();

            if (isSameEntryExists)
            {
                _ = OpenInfoToaster("Same entry already exists please try with different values");
                return;
            }
            var isupdated = await MapIndicatorConfigurationService.AddorUpdateMapIndicator(MapIndicatorConfiguration);
            
            if (isupdated)
            {
                _ = OpenSuccessToaster("Map indicator data saved successfully");
                await GetMapIndicatorConfiguration();
            }
            else
            {
                _ = OpenErrorToaster("Map indicator data save failed");
            }
            
        }
        private void OnColorChange(string color)
        {
            MapIndicatorConfiguration.ColorCode = color;
        }

        private void ShowDeleteModal(int indicatorId)
        {
            deleteModalVisible = true;
            indicatorToBeDeleted = indicatorId;
        }
    }
}
