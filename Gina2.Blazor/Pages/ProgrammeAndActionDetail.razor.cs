﻿using Gina2.Blazor.Helpers.Authenticate;
using Gina2.Core.Interface;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.MySqlRepository.Repositories;
using Microsoft.AspNetCore.Components;
using ActionProgramRevisionMap = Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions.ActionProgramRevisionMap;

namespace Gina2.Blazor.Pages
{
    public partial class ProgrammeAndActionDetail
    {
        [Inject]
        private IAuthenticateHelper AuthenticateHelper { get; set; }

        [Inject]
        private ICurrentUserService currentUserService { get; set; }

        [Inject]
        private IProgrammeAndActionRepository programmeAndActionRepository { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        [Parameter]
        public int ProgrammeAndActionCode { get; set; }

        [Parameter]
        public int VersionId { get; set; }
        
        private bool isAuthenticated;
        private string role;
        private string LoginUser;
        private string PublishedUser;
        private bool IsPublished = true;

        protected override async Task OnInitializedAsync()
        {
            isAuthenticated = true;
            (role, LoginUser) = await AuthenticateHelper.IsAuthenticated();
            var actionResult = await AuthenticateHelper
                .IsPublished<ActionProgramRevisionMap>(p => p.ActionId == ProgrammeAndActionCode);
            
            var programId = actionResult.Count > 0 ? actionResult.FirstOrDefault().ProgramId : 0;
            var actionLogs = await AuthenticateHelper.IsPublished<ProgramLog>(p => p.ProgramId == programId);
            if (actionLogs is null || actionLogs.Count == 0)
            {
                isAuthenticated = false;
                return;
            }

            if(CanAccessForPublishedLog(actionLogs))
                return;

            if (await CanAccessForLoginUserOrRole(actionLogs))
                return;

            isAuthenticated = false;
        }

        private bool CanAccessForPublishedLog(List<ProgramLog> actionLogs)
        {
            ProgramLog publishedLog = VersionId > 0
                ? actionLogs.FirstOrDefault(v => v.ProgramVId == VersionId)
                : actionLogs.FirstOrDefault(v => v.IsPublished);

            IsPublished = publishedLog?.IsPublished ?? false;
            PublishedUser = publishedLog?.UserName;

            if (IsPublished) 
                return true;

            return false;
        }

        private async Task<bool> CanAccessForLoginUserOrRole(List<ProgramLog> actionLogs)
        {
            var revisionLog = actionLogs.FirstOrDefault(v => v.ProgramVId == VersionId);

            if (revisionLog?.UserName == LoginUser || revisionLog?.DelegatedUserName == LoginUser)
            {
                return true;
            }

            if (role.Equals("admin", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }                    

            if (role.Equals("approver", StringComparison.OrdinalIgnoreCase) && revisionLog?.UserName != LoginUser)
            {
                var actionCountries = await programmeAndActionRepository
                    .GetProgramActionCountriesByPolicyIdAndRevisionId(ProgrammeAndActionCode, VersionId);
                var approverCountries = currentUserService.ApproverCountryCode;                

                if(approverCountries.Any(country => actionCountries.Contains(country)))
                {
                    return true;
                }
            }

            return false;
        }
    }
}