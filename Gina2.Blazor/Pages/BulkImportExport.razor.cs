using Blazorise;
using Gina2.Core.Methods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Linq.Dynamic.Core;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.MySqlRepository.Models;
using Gina2.DbModels.Views;
using Gina2.Services.DataTypeBulkUpload;
using Domain.ImportCSV;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class BulkImportExport : PageConfirgurationComponent, IDisposable
    {
        private List<string> DataTypeLookups { get; set; } = PolicyLookup.GetPolicyLooksUp();
        private string SelectedDataType { get; set; } = string.Empty;
        private IFileEntry FileEntry { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]

        private IDataTypeBulkUploadService DataTypeBulkUpload { get; set; }

        private object CvsFileData { get; set; }
        private bool IsDatatypeandFileSelected { get; set; } = true;
        private bool ProcessFile { get; set; } = false;
        private List<UploadStatus> uploadStatuses { get; set; } = new List<UploadStatus>();
        string isHeaderMatch = string.Empty;
        string fileName = string.Empty;
        private long ImageSize { get; set; } = 25 * (1024 * 1024); // 25 mb(kb to mb)
        Blazorise.FileEdit fileEdit;
        private bool DisableFile { get; set; } = true;
        DotNetStreamReference dotNetStreamReference = null;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }
        private void ExportClicked()
        {

            if (string.IsNullOrEmpty(SelectedDataType))
            {
                _ = OpenErrorToaster("please select data type");
                return;
            }
            
            FileDownloading fileDownloading = new();
            switch (SelectedDataType)
            {
                case "SMART commitments":
                    dotNetStreamReference = fileDownloading.CreateCSV(new List<CommitmentCSV>());
                    break;
                case "Mechanisms":
                    dotNetStreamReference = fileDownloading.CreateCSV(new List<MechanismCsv>());
                    break;
                case "Programmes and actions":
                    dotNetStreamReference = fileDownloading.CreateCSV(new List<ActionCSV>());
                    break;
                case "Policies":
                    dotNetStreamReference = fileDownloading.CreateCSV(new List<PolicyCSV>());
                    break;
                default:
                    _ = OpenErrorToaster("Wrong data type selected");
                    break;
            }
            if (dotNetStreamReference != null)
            {
                _ = JSRuntime.InvokeVoidAsync("saveAsFile", $"gina2_{SelectedDataType}.csv", dotNetStreamReference);
            }
        }
        private async Task ImportClicked()
        {
            if (string.IsNullOrEmpty(fileName))
            {
                _ = OpenErrorToaster("please upload csv file");
                return;
            }
            IsLoading = true;
            uploadStatuses = await DataTypeBulkUpload.UploadDataType(CvsFileData);
            ProcessFile = true;
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnChanged(FileChangedEventArgs e)
        {
            try
            {
                var file = e.Files.FirstOrDefault();
                if (!string.IsNullOrEmpty(SelectedDataType))
                {
                    if (file != null)
                    {
                        IsLoading = true;
                        isHeaderMatch = string.Empty;
                        ProcessFile = false;
                        if (file != null && file.Type != "text/csv")
                        {
                            isHeaderMatch = "Only csv is allowed";
                            IsLoading = false;
                            return;
                        }
                        if (file.Size >= ImageSize)
                        {
                            isHeaderMatch = "File size is too big";
                            IsLoading = false;
                            return;
                        }
                        using MemoryStream result = new MemoryStream();
                        await file.OpenReadStream(long.MaxValue).CopyToAsync(result);
                        var isfilevalidated = ReadCSVFile(result);
                        if (!string.IsNullOrEmpty(isfilevalidated))
                        {
                            IsLoading = false;
                            return;
                        }
                        fileName = file.Name;
                        IsDatatypeandFileSelected = string.IsNullOrEmpty(SelectedDataType) || CvsFileData == null || !string.IsNullOrEmpty(isfilevalidated);
                        IsLoading = false;
                    }
                }
            }
            catch (Exception ex)
            {
                isHeaderMatch = ex.Message;
                IsLoading = false;
            }
        }

        private string ReadCSVFile(MemoryStream result)
        {

            FileDownloading fileDownloading = new FileDownloading();
            switch (SelectedDataType)
            {
                case "SMART commitments":
                    CvsFileData = fileDownloading.ReadCSVFile<ImportCommitmentCSV>(result, out isHeaderMatch);
                    break;
                case "Mechanisms":
                    CvsFileData = fileDownloading.ReadCSVFile<ImportMechanismCSV>(result, out isHeaderMatch);
                    break;
                case "Programmes and actions":
                    CvsFileData = fileDownloading.ReadCSVFile<ImportActionCSV>(result, out isHeaderMatch);
                    break;
                case "Policies":
                    CvsFileData = fileDownloading.ReadCSVFile<ImportPolicyCSV>(result, out isHeaderMatch);
                    break;
                default:
                    _ = OpenErrorToaster("Wrong data type selected");
                    break;
            }

            return isHeaderMatch;
        }
        private void ShowToaster(string isHeaderMatch)
        {
            if (!string.IsNullOrEmpty(isHeaderMatch))
            {
                _ = OpenErrorToaster("Column not matched");
            }
            else
            {
                isHeaderMatch = string.Empty;
            }

        }
        private void OnSelectedItemChangedHandler(string selectedValue)
        {
            fileName = string.Empty;
            CvsFileData = new object();
            DisableFile = string.IsNullOrEmpty(SelectedDataType);
            IsDatatypeandFileSelected = string.IsNullOrEmpty(SelectedDataType) || CvsFileData == null && string.IsNullOrEmpty(fileName);

        }
        public void Dispose()
        {
            dotNetStreamReference?.Dispose();
        }
    }
}
