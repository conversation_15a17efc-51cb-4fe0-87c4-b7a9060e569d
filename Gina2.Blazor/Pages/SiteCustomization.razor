﻿@page "/admin/site-customization"
@using AntDesign.Select
@using Gina2.Core.Enums
@using Gina2.DbModels
@using Gina2.DbModels.HomeDrafts
@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA Header & Footer</PageTitle>
<Loader IsLoading="@IsLoading" />
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">

        @{
            RenderFragment footer = @<AntDesign.Template>
            </AntDesign.Template>;
        }

        <Div Class="_live-preview _site_prev">
            <AntDesign.Modal Visible="@_visible"
                            ZIndex="111111"
                            OnCancel="@HandleCancel"
                            Footer="@footer"
                            Width="1200">
                <ChildContent>
                    @* Custom content goes here *@
                    <br /><br />
                    <Header Hesderst="@Headerprev" /><br />
                    <Footer />
                </ChildContent>
            </AntDesign.Modal>
        </Div>

        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
                <Container Class="pt-5 pb-5">
                    <Div Class="downl-flex">
                        <Heading data-cy="Header&FooterTitle" Class="h-title">Header & Footer</Heading>
                        <Breadcrumb Class="bread-crumb">
                            <BreadcrumbItem>
                                <BreadcrumbLink data-cy="HomeLink" To="/">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                             <BreadcrumbItem Active>
                                <BreadcrumbLink data-cy="SiteCustomization" To="#">Layout</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbItem Active>
                                <BreadcrumbLink data-cy="SiteCustomization" To="#">Header & Footer</BreadcrumbLink>
                            </BreadcrumbItem>
                        </Breadcrumb>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Fluid Class="bg-trdot pt-3">
            <Container Class="Dashboard">
                <Row Class="mt-4 mb-4">
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="_mydrafts _HomeCurrent">
                        <Tabs SelectedTab="@selectedTab" SelectedTabChanged="@OnSelectedTabChanged">
                            <Items>
                                <Div>
                                    <Tab data-cy="Drafts" Name="Drafts">Drafts</Tab>
                                    <Tab data-cy="PublishedContents" Name="Published">Published contents</Tab>
                                </Div>
                                <Div Class="adddraft">
                                    <Button data-cy="EditLayout" Disabled="@(layoutDrafts.Where(w=>w.Status==HomeDraftModerationState.Draft.ToString()).Any())" Class="but-yellow pl-2 pr-2" Clicked="CreateDraft"><Icon Name="IconName.Pen" /> Edit layout</Button>
                                </Div>
                            </Items>
                            <Content>
                                <TabPanel Name="Drafts">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth _actions"
                                                      TItem="@LayoutCustomization"
                                                      Data="@layoutDrafts.Where(w=>w.Status!=HomeDraftModerationState.Published.ToString())"
                                                      PageSize="5"
                                                      ShowPageSizes
                                                      ShowPager
                                                      Responsive
                                                      SortMode="DataGridSortMode.Single">
                                                <EmptyTemplate>
                                                    <Div>No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.RefCreatedBy)" Caption="Created by" Width="22%" TextAlignment="TextAlignment.Start" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.CreatedDateTime)" Caption="Created date" Width="19%" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.RefUpdatedBy)" Caption="Last updated by" Width="22%" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.UpdateDateTime)" Caption="Last update" Width="19%" />
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button>
                                                                    <Tooltip  data-cy="ToolTipPen" Text="Edit">
                                                                        <Icon data-cy="ToolTipPenIcon" Clicked="@(()=>{ NavigationManager.NavigateTo($"Layout/Drafts/{currentRow.Id}/Edit",true);  })" Name="IconName.Pen" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                            <Button>
                                                                <Tooltip  data-cy="ToolTipPreview" Text="Preview">
                                                                    <Icon  data-cy="PreviewIcon" Clicked="async (e)=>await ShowPreview(currentRow.Id)" Name="IconName.Eye">

                                                                    </Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button>
                                                                    <Tooltip data-cy="ToolTipDelete" Text="Delete">
                                                                        <Icon data-cy="DeleteIcon" Name="IconName.Delete" Clicked="e=> ShowDeleteDraftModal(currentRow.Id)" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                                <TabPanel Name="Published">
                                    <Card Class="card-full">
                                        <CardBody>
                                            <DataGrid Class="table-nth"
                                                      TItem="@LayoutCustomization"
                                                      Data="@layoutDrafts.Where(w=>w.Status==HomeDraftModerationState.Published.ToString())"
                                                      PageSize="5"
                                                      ShowPageSizes
                                                      ShowPager
                                                      Responsive
                                                      SortMode="DataGridSortMode.Single"
                                                      RowStyling="@OnRowStyling">
                                                <EmptyTemplate>
                                                    <Div data-cy="NoDataFound">No data found.</Div>
                                                </EmptyTemplate>
                                                <DataGridColumns>
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.RefCreatedBy)" Caption="Created by" Width="22%" TextAlignment="TextAlignment.Start" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.CreatedDateTime)" Caption="Created date" Width="19%" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.RefUpdatedBy)" Caption="Last updated by" Width="22%" />
                                                    <DataGridColumn Field="@nameof(LayoutCustomization.UpdateDateTime)" Caption="Last update" Width="19%" />
                                                    <DataGridColumn Caption="Actions" HeaderCellClass="_actionscenter" Width="19%">
                                                        <DisplayTemplate Context="currentRow">
                                                            <Button>
                                                                <Tooltip data-cy="ToolTipEdit1" Text="Edit">
                                                                    <Icon data-cy="EditIcon1" Clicked="@(()=>{ NavigationManager.NavigateTo($"Layout/Drafts/{currentRow.Id}/Edit",true);  })" Name="IconName.Pen" />
                                                                </Tooltip>
                                                            </Button>
                                                            <Button>
                                                                <Tooltip data-cy="ToolTipPreview1" Text="Preview">
                                                                    <Icon data-cy="PreviewIcon1" Clicked="(e)=>ShowPreview(currentRow.Id)" Name="IconName.Eye"></Icon>
                                                                </Tooltip>
                                                            </Button>
                                                            @if (CurrentUserService != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                                                            {
                                                                <Button>
                                                                    <Tooltip data-cy="ToolTipDelete1" Text="Delete">
                                                                        <Icon data-cy="DeleteIcon1" Name="IconName.Delete" Clicked="e=> ShowDeleteDraftModal(currentRow.Id)" />
                                                                    </Tooltip>
                                                                </Button>
                                                            }
                                                        </DisplayTemplate>
                                                    </DataGridColumn>
                                                </DataGridColumns>
                                            </DataGrid>
                                        </CardBody>
                                    </Card>
                                </TabPanel>
                            </Content>
                        </Tabs>
                    </Column>

                </Row>
            </Container>
        </Container>

        <Modal @bind-Visible="@deleteDraftModalVisible" Class="modals-lg antdraggable _modalcenter">
            <ModalContent Centered Class="forms">
                <ModalHeader Class="ant-header">
                    <ModalTitle>Are you sure want to Delete this draft?</ModalTitle>
                </ModalHeader>
                <ModalFooter>
                    <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteDraft(draftToBeDeleted);} )">Delete</Button>
                    <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteDraftModalVisible = false)">Cancel</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

    </Authorized>
</AuthorizeView>

