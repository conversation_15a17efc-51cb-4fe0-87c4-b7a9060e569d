using Blazorise.DataGrid;
using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.DbModels.HomeDrafts;
using Gina2.Services.HomeDraftServices;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using AntDesign;
using Gina2.Core.Interface;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Lookups;
using Gina2.DbModels;
using Gina2.Services.StaticPageService;
using Gina2.Services.ViewModelResponse;
using NuGet.Protocol.Core.Types;
using Gina2.Blazor.Shared;
using Microsoft.JSInterop;
using Microsoft.AspNetCore.Authorization;
using DocumentFormat.OpenXml.Office2010.Drawing;
using DocumentFormat.OpenXml.Office2010.Excel;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin")]
    public partial class StaticPages : PageConfirgurationComponent
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private IJSRuntime JSRuntime { get; set; }

        public ApplicationUserSearchRequestModel SearchModel { get; set; } = new ApplicationUserSearchRequestModel();
        private bool IsLoading = false;
        private bool SliderModal;
        [Inject]
        private TimeZoneService timeZoneService { get; set; }
        private string previewDraftId { get; set; }
        private string StaticTableType { get; set; } = "published";
        private string rerenderTokenId { get; set; }
        string title = "Live Preview";
        string selectedTab = "Drafts";
        bool _visible = false;
        bool deleteDraftModalVisible = false;
        bool deleteStaticPageModalVisible = false;
        int draftToBeDeleted;
        int publishedToBeDeleted;
        [Inject]
        public IConfiguration Configuration { get; set; }

        [Inject]
        private IStaticPageService _staticPageService { get; set; }
        private List<StaticPageDraft> draftPages = new List<StaticPageDraft>();
        private List<StaticPage> publishedPages = new List<StaticPage>();
        private string searchQuery = "";
        private int pageNumber = 1;
        private int pageSize = 10;
        private _quillEditor quillEditorTitle;
        private _quillEditor quillEditorDescription;
        private _quillEditor quillEditorContent;
        private bool TitleError { get; set; }
        private bool ContentError { get; set; }
        private bool RedirectionUrlError { get; set; }
        private bool URLError { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        private StaticPage currentEditingPage = new StaticPage();
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsLoading = true;
                _= Search();
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            IsLoading = false;
            _ = base.OnAfterRenderAsync(firstRender);

        }
        
        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;
            return Task.CompletedTask;
        }
        private async Task ShowPreview(string draftId, string type)
        {
            await Task.Run(() =>
            {
                IsLoading = true;
            });

            await InvokeAsync(StateHasChanged);
            previewDraftId = draftId;
            StaticTableType = type;
            rerenderTokenId = Guid.NewGuid().ToString();
            _visible = true;

            await Task.Run(() =>
            {
                IsLoading = false;
            });
            await InvokeAsync(StateHasChanged);
        }
        private void HandleCancel(MouseEventArgs e)
        {
            _visible = false;
        }

        private void OnRowStyling(HomeDraft homeDraft, DataGridRowStyling styling)
        {
            if (homeDraft.IsCurrentPublished)
                styling.Style = "color: green;background: rgb(239 245 231); border: 1px solid rgb(255, 255, 255)!important;";
        }

        private async Task ShowDraftModal(int Id)
        {
            EmptyModel();
            if (Id > 0)
            {
                var response = await _staticPageService.GetStaticPageDraftById(Id);
                if (response.IsSuccess)
                    currentEditingPage = response.Result;
                else
                {
                    await OpenErrorToaster(response.Message);
                    return;
                }
            }
            else
            {
                currentEditingPage = new StaticPage();
            }
            await JsRuntime.InvokeVoidAsync("dragPopup", "Staticdraggable", "ant-header");
            SliderModal = true;
            StateHasChanged();
        }
        private async Task ShowModal(int Id)
        {

            EmptyModel();
            if (Id > 0)
            {
                var response = await _staticPageService.GetStaticPageById(Id);
                if (response.IsSuccess)
                    currentEditingPage = response.Result;
                else
                {
                    await OpenErrorToaster(response.Message);
                    return;
                }
            }
            else
            {
                currentEditingPage = new StaticPage();
            }
            await JsRuntime.InvokeVoidAsync("dragPopup", "Staticdraggable", "ant-header");
            SliderModal = true;
            StateHasChanged();
        }
        private void EmptyModel()
        {
            currentEditingPage = new StaticPage()
            {
                Id = 0,
                Title = string.Empty,
                Content = string.Empty,
                Description = string.Empty,
                URL = string.Empty,
            };
            TitleError = ContentError = URLError = false;
        }
        private async Task LoadDraftPages()
        {
            var response = await _staticPageService.GetPaginatedDraftStaticPages(searchQuery, pageNumber, pageSize);
            if (response.IsSuccess)
            {
                draftPages = response.Result;
            }
            else
            {
                await OpenToaster("Error", response.Message, AntDesign.NotificationType.Error);
            }
        }
        private async Task LoadPublishedPages()
        {
            var response = await _staticPageService.GetPaginatedPublishedStaticPages(searchQuery, pageNumber, pageSize);
            if (response.IsSuccess)
            {
                publishedPages = response.Result;
            }
            else
            {
                await OpenToaster("Error", response.Message, AntDesign.NotificationType.Error);
            }
        }
        private async Task Search()
        {
            try
            {
                await LoadPublishedPages();
                await LoadDraftPages();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {

                throw;
            }
           
        }
        private async Task CopyTextToClipboard(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                url = Configuration["App:SelfUrl"] + "pages/" + url;
                var copyStatus = await JSRuntime.InvokeAsync<string>("copyToClipboard", url);
                if (copyStatus == "Text copied to clipboard")
                {
                    await OpenSuccessToaster(copyStatus);
                }
                else
                {
                    await OpenErrorToaster(copyStatus);
                }
            }
            else
            {
                await OpenErrorToaster("url is null or empty");
            }
        }
        private async Task LoadNextPage()
        {
            // Increment pageNumber and call LoadStaticPages
            pageNumber++;
            await LoadPublishedPages();
            await LoadDraftPages();
        }
        private async Task CreateOrUpdatePage()
        {
            currentEditingPage.Title = await quillEditorTitle.GetHTML();
            currentEditingPage.Description = await quillEditorDescription.GetHTML();
            currentEditingPage.Content = await quillEditorContent.GetHTML();
            currentEditingPage.IsPublished = true;
            if (ValidateData())
            {
                return;
            }
            var isValid = await _staticPageService.VaildatePublishedUrl(currentEditingPage.URL, currentEditingPage.Id);
            if (isValid)
            {
                _ = OpenErrorToaster("Page already availalbe with the same URL");
                return;
            }
            else
            {
                currentEditingPage.Id = 0;
            }
            var response = await _staticPageService.CreateOrUpdateStaticPagePublished(currentEditingPage);
            if (response.IsSuccess)
            {
                // Optionally, reset the currentEditingPage and reload data
                currentEditingPage = new StaticPage();
                _ = OpenSuccessToaster(response.Message);
                _ = Search();
            }
            else
            {
                _ = OpenErrorToaster(response.Message);
            }
            SliderModal = false;
            EmptyModel();
            StateHasChanged();
        }
        private async Task CreateOrUpdateDraftPage()
        {
            currentEditingPage.Title = await quillEditorTitle.GetHTML();
            currentEditingPage.Description = await quillEditorDescription.GetHTML();
            currentEditingPage.Content = await quillEditorContent.GetHTML();
            currentEditingPage.IsPublished = false;
            if (ValidateData())
            {
                return;
            }
            var isvlaid = await _staticPageService.VaildateUrl(currentEditingPage.URL, currentEditingPage.Id);
            if (isvlaid)
            {
                _ = OpenErrorToaster("URL is already taken");
                return;
            }
            var response = await _staticPageService.CreateOrUpdateStaticPage(currentEditingPage);
            if (response.IsSuccess)
            {
                // Optionally, reset the currentEditingPage and reload data
                currentEditingPage = new StaticPage();
                _ = OpenSuccessToaster(response.Message);
                _ = Search();
            }
            else
            {
                _ = OpenErrorToaster(response.Message);
            }
            SliderModal = false;
            EmptyModel();
            StateHasChanged();
        }

        private bool ValidateData()
        {
            ContentError = URLError = TitleError = false;
            if (string.IsNullOrEmpty(currentEditingPage.Title))
            {
                TitleError = true;
            }
            if (string.IsNullOrEmpty(currentEditingPage.Content))
            {
                ContentError = true;
            }
            if (string.IsNullOrEmpty(currentEditingPage.URL))
            {
                URLError = true;
            }
            return ContentError || URLError || TitleError;
        }
        void OpenDeleteDraftStaticPage(int id)
        {
            draftToBeDeleted = id;
            deleteDraftModalVisible = true;
        }
        void OpenDeletePublishedStaticPage(int id)
        {
            publishedToBeDeleted = id;
            deleteStaticPageModalVisible = true;
        }
        private async Task DeleteDraftPage(int id)
        {
            var response = await _staticPageService.DeleteStaticPageDraft(id);
            if (response.IsSuccess)
            {
                // Reload data after deletion
                _ = Search();
                _ = OpenSuccessToaster(response.Message);
            }
            else
            {
                _ = OpenErrorToaster(response.Message);
            }
            draftToBeDeleted = 0;
            deleteDraftModalVisible = false;
        }
        private async Task DeletePage(int id)
        {
            var response = await _staticPageService.DeleteStaticPage(id);
            if (response.IsSuccess)
            {
                // Reload data after deletion
                _ = Search();
                _ = OpenSuccessToaster(response.Message);
            }
            else
            {
                _ = OpenErrorToaster(response.Message);
            }
            publishedToBeDeleted = 0;
            deleteStaticPageModalVisible = false;
        }
    }

}
