﻿using Blazorise.DataGrid;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Methods;
using Gina2.Services.Models;
using Gina2.Services.Search;
using Gina2.SPs;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json;
using System.Net;
using System.Text;

namespace Gina2.Blazor.Pages
{
    public partial class Content
    {
        [Parameter]
        public string SearchText { get; set; }
        [Inject]
        public ISearchService SearchService { get; set; }
        public  SubscriptionUserModel RequestModel {get;set;}
        public List<CombinedSearchResult> SubscriberContentResult { get; set; }
        private DataGrid<CombinedSearchResult> SearchDataGrid;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _= GetContentData();
                StateHasChanged();
            }

        }
        protected override async Task OnParametersSetAsync()
        {
            if (!string.IsNullOrEmpty(SearchText))
            {
                RequestModel = Deserialize();
            }
        }

        private SubscriptionUserModel Deserialize()
        {
            string inputStr = Encoding.UTF8.GetString(Convert.FromBase64String(SearchText));
            string decoderString = WebUtility.UrlDecode(inputStr);
            return JsonConvert.DeserializeObject<SubscriptionUserModel>(decoderString, new SpecialDateTimeConverter()); 
        }

        private async Task GetContentData()
        {
            try
            {
                SubscriberContentResult = await SearchService.GetContentListAsync(RequestModel.CountryISO, RequestModel.TopicId
                    , RequestModel.SelectedDateRange, RequestModel.IncludeRevisiedContent);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
