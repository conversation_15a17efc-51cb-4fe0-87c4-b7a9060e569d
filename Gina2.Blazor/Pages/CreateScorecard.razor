﻿@page "/admin/scorecards/create"
@page "/scorecards/{Id:int}/edit"
@using Gina2.Core.Methods;
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
<PageTitle>GIFNA @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.EnterScoreCard).ConvertHtmlToPlainText())</PageTitle>
<Validations @ref="ValidationCheck" ValidateOnLoad="false">
    <Container Fluid Padding="Padding.Is0">
        <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
            <Container Class="ginasearch pt-5 pb-5">
                <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                     <Div Class="item1 pl-1  pr-1">
                        <Heading Size="HeadingSize.Is3">
                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.EnterScoreCard))
                            <AdminEditbut Key="@ScorecardCreateConfiguratioKey.EnterScoreCard" />
                        </Heading>
                        <Paragraph Class="color-w subtitleediticon">
                            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardSubHeadingCreate))
                            <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecardSubHeadingCreate" />
                        </Paragraph>
                        <Breadcrumb Class="bread-crumb">
                            <BreadcrumbItem>
                                <BreadcrumbLink To="/">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                             <BreadcrumbItem Active>
                                 <BreadcrumbLink To="#">Scorecard</BreadcrumbLink>
                             </BreadcrumbItem>
                            <BreadcrumbItem Active>
                                <BreadcrumbLink To="#">Enter scorecard</BreadcrumbLink>
                            </BreadcrumbItem>
                        </Breadcrumb>
                    </Div>
                </Div>
            </Container>
        </Card>
    </Container>
    <Container Fluid Class="newdraft" Padding="Padding.Is0">
        <Container Class="pt-6 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3">
                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardHeadingCreate))
                <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecardHeadingCreate" />
            </Heading>
            <Divider Class="divi-blue" />
        </Container>
        <Container Class="form-newd">
            <Fields>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardTitleLabel)
                        <Span>*</Span>
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardTitleTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecardTitleGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <TextEdit Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardTitlePlaceHolder)" Text="@Scorecard?.Title" TextChanged="@OnTitleChanged" />
                        <ValidationError>Please enter valid title!</ValidationError>
                    </Validation>
                </Field>
            </Fields>
            <Fields>
                <Field>
                    <Div Flex="Flex.JustifyContent.Between">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardDescriptionLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardDescriptionTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecardDescriptionGroup" />
                        </FieldLabel>
                    </Div>
                    @*<RadzenHtmlEditor @bind-Value="@(description)" Change="@ChangeDescription" class="_editor" style="height: 200px; margin-bottom: 1rem;"><HtmlEditor /></RadzenHtmlEditor>*@
                    <_quillEditor value="@description" @ref="quillEditorDescriptionRef"></_quillEditor>
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardURLAliasLabel)
                        <Span>*</Span>
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardURLAliasTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecardURLAliasGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                         <TextEdit Text="@Scorecard.URL" TextChanged="@UrlChange" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecardURLAliasPlaceholder)"></TextEdit>
                        @if (!string.IsNullOrEmpty(Scorecard.URL))
                        {
                            <FieldHelp>URL will look like</FieldHelp>
                            <FieldHelp>@($"{Configuration["App:SelfUrl"]}summary/{Scorecard.URL}")</FieldHelp>
                        }
                        <ValidationError>Please enter valid url!</ValidationError>
                    </Validation>
                </Field>

                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecountryURLAliasLabel)
                        <Span>*</Span>
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecountryAliasTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@ScorecardCreateConfiguratioKey.ScorecountryAliasGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <TextEdit Disabled @bind-Text="@Scorecard.CountryUrl" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.ScorecountryAliasPlaceholder)"></TextEdit>
                        @if (!string.IsNullOrEmpty(Scorecard.CountryUrl))
                        {
                            <FieldHelp>Country URL will look like</FieldHelp>
                            <FieldHelp>@($"{Configuration["App:SelfUrl"]}summary/{Scorecard.URL}/data")</FieldHelp>
                        }
                        
                        <ValidationError>Please enter valid url!</ValidationError>
                    </Validation>
                </Field>
                @*  <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Population Count File</FieldLabel>
                <FileEdit Changed="@OnChanged" Placeholder="Select or drag and drop the image" Multiple />
                <FieldHelp>Files must be less than 25 MB     |     Allowed file types: .xlsx .csv</FieldHelp>
                </Field>*@
            </Fields>

            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.BottomTextLabel)
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ScorecardCreateConfiguratioKey.BottomTextTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@ScorecardCreateConfiguratioKey.BottomTextGroup" />
                    </FieldLabel>
                </Div>
                @*<RadzenHtmlEditor @bind-Value="@(bottomDiscrition)" Change="@ChangeBottomDescription" class="_editor" style="height: 200px; margin-bottom: 1rem;"><HtmlEditor /></RadzenHtmlEditor>*@
                <_quillEditor value="@bottomDiscrition" @ref="quillEditorBottomDescriptionRef"></_quillEditor>
            </Field>
        </Container>

        <Container Class="mt-4 pb-6">
              <Field>
            <FieldLabel>Publishing option</FieldLabel>
            <Check TValue="bool" @bind-Checked="@Scorecard.IsPublished">Published</Check>
            </Field>
            <Button Class="but-yellow" Clicked="@SaveAsync">Save</Button>
        </Container>
    </Container>
</Validations>


