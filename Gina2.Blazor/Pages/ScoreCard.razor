﻿@attribute [Route("/summary/{ScorecardURL}")]
@*@page "/scorecards/{Id:int}"
@page "/scorecards/{ScorecardURL}"*@
@using Gina2.Core.Enums
@using Gina2.DbModels;
@using System.Reflection;
@using Gina2.Core.Methods;
<PageTitle>GIFNA @Scorecard?.Title</PageTitle>
<Loader IsLoading="@isLoading" />
<AuthorizeView>
    <Authorized>
        <div class="menu-dot homeedit">
            <button class="editme" onclick="@(() => Editmenu("Editmenu"))"></button>
            <div class="editmenus" id="Editmenu" style="display:none;">
                <a href="@($"/scorecards/{Scorecard?.Id}/edit")">Edit</a>
            </div>
        </div>
    </Authorized>
</AuthorizeView>
@if (IAuthorizePublish || authenticationState.User.Identity.IsAuthenticated)
{
    <Modal @ref="modalRef" Class="modals-lg antdraggable">
        <ModalContent Centered Class="forms adminmobel">
            <ModalHeader Class="ant-header">
                <ModalTitle>@ScoreCountryName</ModalTitle>
                <NavLink class="close" onclick="@HideModal"><img src="/img/close.png" /></NavLink>
            </ModalHeader>
            <ModalBody Class="p-3 _modalscroll">
                <Paragraph>
                    @((MarkupString)ScoreName)
                </Paragraph>
                <Div Class="DataGrids search-box">
                    <DataGrid Class="_checkgrid"
                            FixedHeaderDataGridMaxHeight="500px"
                            FixedHeaderDataGridHeight="450px"
                            TItem="@ScorecardCountyMappingItem"
                            Data="@ScorecardCountyMappingItems"
                            PageSize="500">
                        <EmptyTemplate>
                            <Div>No data found.</Div>
                        </EmptyTemplate>

                        <DataGridColumns>
                            <DataGridColumn Caption="Title" Sortable="true" Width="75%">
                                <DisplayTemplate Context="row">
                                    <NavLink target="_blank" href="@row.Link">@row.EntityTitle</NavLink>
                                </DisplayTemplate>
                            </DataGridColumn>
                            <DataGridColumn Caption="Type" Sortable="true" Width="25%">
                                <DisplayTemplate Context="row">
                                    @row.EntityType
                                </DisplayTemplate>
                            </DataGridColumn>
                        </DataGridColumns>
                    </DataGrid>
                </Div>
            </ModalBody>
        </ModalContent>
    </Modal>
    <Container Fluid Padding="Padding.Is0">
        <Card Class="allbanner" Style="background-image: url(../img/scorecard.png);">
            <Container Class="ginasearch pt-5 pb-5">
                <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                    <Div Class="item0">
                        <Heading Size="HeadingSize.Is3">@Scorecard?.Title</Heading>
                    </Div>
                </Div>
            </Container>
        </Card>
    </Container>
    <Container Class="pt-4">
        <Paragraph>
            @((MarkupString)Scorecard?.Description)
        </Paragraph>
    </Container>


        @*<Heading Class="titel-scord">Score card Map</Heading>*@
        @if (Scorecard != null)
    {
        <Container Class="pt-4 pb-5">
            <Div Flex="Flex.JustifyContent.Start.AlignItems.Center" Class="downl-flex">
                <Div Flex="Flex.JustifyContent.Start" Class="_scords downl-flex">

                    <Div Flex="Flex.JustifyContent.Center">

                        @for (int i = 10; i <= 100; i += 10)
                        {
                            var redColor = width % (float)i;
                            string style = $"width:{100}%";

                            if (i < width)
                            {
                                style = $"width:{100}%";
                            }
                            else if (i > width)
                            {
                                style = $"width:{0}%";
                            }
                            if (width < i && (i - width < 10))

                            {

                                //style = $"width:{(i - width -10)}%";
                                var swidth = ((i - width - 10));
                                style = $"width:{swidth * -10}%";
                            }
                            <Div Class="score-percent-full">
                                <Div Class="score-percent" Style="@style">
                                    <Div Class="score-groups">
                                    </Div>
                                </Div>
                            </Div>
                        }
                    </Div>

                    <Heading Class="titel-percent">@width %</Heading>
                </Div>
                <Div>
                    <Paragraph Class="map-par"> @((MarkupString)Scorecard?.PopulationCountInfo)</Paragraph>
                    <Paragraph Class="map-par"> @((MarkupString)Scorecard?.PopulationDescription)</Paragraph>
                </Div>
            </Div>
        </Container>
    }



    <Container Fluid Padding="Padding.Is0">
        <div id="viewDiv1" style="background-color: #e8e9ec">
            <div id="scorecardMap" style="padding:0;margin:auto;height:750px; width:1030px;">
            </div>
        </div>
    </Container>


    <Container Class="pt-4 score-card">
        <Div Flex="Flex.JustifyContent.Center.AlignItems.Center">
            @*<Button Class="but-yellow mr-2" Clicked="ExportCSV"><Icon Class="fas arrow-bottom" /> CSV</Button>*@
            <Button Class="but-yellow" Clicked="exportMap"><Icon Class="fas export" /> Export Map</Button>
        </Div>

        <ListGroup Class="gina-10">
            <Repeater Items="@scoreMapDatas" Context="row">
                @{
                    var a = row.MapFillPatterns.ToString();
                    string style = row.MapFillPatterns.ToString() == "Stripes" ? "stripes" : row.MapFillPatterns.ToString() == "Dots" ? "dots" : "";
                    string styleclass = $"_score_icons {style}";
                }
                <ListGroupItem>
                    <Span><Div Class="@styleclass" Style="@($"background-color: {(MarkupString)@row.ColorCode}")"></Div></Span>
                    <Span>
                        <Paragraph>
                            @((MarkupString)@row.Score)
                        </Paragraph>
                    </Span>
                </ListGroupItem>
            </Repeater>
        </ListGroup>

        <Div Class="@scoreClassTable">
            <Div Class="_Score-responsive pb2">
                @*<Table Class="_Scoretable" style="@scorestyleTable">*@
                <Table Class="_Scoretable">
                    <TableHeader>
                        <TableRow>
                            @foreach (var item in Headers)
                            {
                                <TableHeaderCell>@item</TableHeaderCell>
                            }

                        </TableRow>
                    </TableHeader>
                    <TableBody>


                        @foreach (var row in Content)
                        {
                            <TableRow>

                                @foreach (var value in row)
                                {
                                    if (value.Key.Equals(Headers[0]))
                                    {
                                        <TableRowCell Class="cell1">
                                            @((MarkupString)@value.Value)
                                        </TableRowCell>
                                    }

                                    if (value.Key.Equals(Headers[1]))
                                    {
                                        var countrySplit = row.ContainsKey("Code") ? row["Code"]?.Split("|").ToList() : new List<string>();
                                        var codeSplit = row.ContainsKey("Countries") ? row["Countries"]?.Split(",").ToList() : new List<string>();
                                        <TableRowCell Class="cell2">
                                            <Repeater Items="@countrySplit" Context="country">
                                                @{
                                                    var codeAndCountry = codeSplit[countrySplit.IndexOf(country)].Trim();
                                                }
                                                <Blazorise.Link Clicked="@((e)=>@ShowScorecardCountry(row[Headers[0]],codeAndCountry))">@country@(countrySplit.Count == countrySplit.IndexOf(country) + 1 ? "" : ", ") </Blazorise.Link>
                                                @* <NavLink href="@($"/admin/Scorecards/Details/{row.ScorecardCountryMapId}/{@iso}")">@country</NavLink>,*@
                                            </Repeater>
                                        </TableRowCell>
                                    }

                                    if (!value.Key.Equals(Headers[1]) && !value.Key.Equals(Headers[0]) && !value.Key.Equals("Code"))
                                    {
                                        <TableRowCell Class="cell3">@value.Value</TableRowCell>
                                    }
                                }
                            </TableRow>

                        }

                    </TableBody>
                </Table>
            </Div>
        </Div>
    </Container>
    <Container Class="pb-7">
        <Paragraph>
            @((MarkupString)@Scorecard?.BottomDescription)
        </Paragraph>
    </Container>
}
else
{
    @if (Scorecard != null)
    {
        <UnAuthorizedView></UnAuthorizedView>
    }
}