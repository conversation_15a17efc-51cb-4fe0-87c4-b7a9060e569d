﻿@page "/admin/taxonomies/"
@using Plk.Blazor.DragDrop;
@using Domain.Vocabulary;
@using Domain.Terms;
@using Gina2.Core;
@using Gina2.Core.Models;
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Core.Methods;
@inherits PageConfirgurationComponent
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(TaxonomyPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized Context="authorizedContext">

        <Loader IsLoading="@IsLoading" />

        <SnackbarStack @ref="SnackbarStack" />

        <Modal @bind-Visible="@showTermsModal" Class="modals-lg antdraggable">
            <ModalContent Centered Class="forms">
                <ModalHeader Class="ant-header">
                    <Div Class="w-100" Flex="Flex.JustifyContent.Between.AlignItems.Baseline">
                        <ModalTitle>Terms for '@selectedVocabulary?.Name'</ModalTitle>
                        <Button Class="but-yellow pl-2 pr-2 mr-2" Clicked="@(() => AddNewTerm(TableName))">Add terms</Button>
                    </Div>
                    <CloseButton />
                </ModalHeader>
                <ModalBody>
                    @if (TableName != Gina2.Core.Constants.VocabularyTables.Topic)
                    {
                        <Row>
                            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                <Div Class="_taxonscroll">
                                    <Div Class="_taxonomies header" Flex="Flex.JustifyContent.Between">
                                        <Div Class="drag-1">
                                            Terms
                                        </Div>
                                        <Div Class="drag-2">
                                            Actions
                                        </Div>
                                    </Div>
                                    @if (isLoadingTerms)
                                    {
                                        <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="_65vh">
                                            <AntDesign.Spin Spinning="@isLoadingTerms" />
                                        </Div>
                                    }
                                    <Div Class="_scroll">
                                        <Dropzone Context="Parent" Items="@Terms" Class="_taxon" OnItemDrop="((Term item) => OnDropTerm())">
                                            <Div Class="_taxonomies _Parent" Flex="Flex.JustifyContent.Between" draggable="true">
                                                <Div Class="drag-1">
                                                    <Span><Icon Class="fa-solid fa-grip-vertical" /></Span>
                                                    <Tooltip Text="@Parent.Description">
                                                        @{
                                                            string TermId = TableName == Gina2.Core.Constants.VocabularyTables.Country ? Parent.Iso3Code : Parent.Id.ToString();
                                                        }
                                                        <NavLink href="@($"/vocabularies/{TableName}/terms/{TermId}/references")">@Parent.Name</NavLink>
                                                    </Tooltip>
                                                </Div>
                                                <Div Class="drag-2 _dropbutton">
                                                    <Tooltip Text="Edit the Term">
                                                        <Icon Class="fa-solid fa-pen" Clicked="@(async () => await EditTermPopupAsync(Parent))" />
                                                    </Tooltip>
                                                </Div>
                                                @if (TableName != Gina2.Core.Constants.VocabularyTables.Icn2Category || TableName != Gina2.Core.Constants.VocabularyTables.Partners)
                                                {
                                                    <Div Class="drag-2 _dropbutton">

                                                        <Tooltip Text="Edit the Term">
                                                            <Icon Class="fa-solid fa-plus" Clicked="@(async () => AddNewTerm(TableName, Parent.Id))" />
                                                        </Tooltip>
                                                    </Div>
                                                }
                                            </Div>
                                            @if (Parent.Icn2s != null && Parent.Icn2s.Count > 0)
                                            {
                                                <Dropzone Context="Child" Items="@Parent.Icn2s" Class="_taxon" OnItemDrop="((Term item) => OnDropTerm())">
                                                    <Div Class="_taxonomies _Child" Flex="Flex.JustifyContent.Between" draggable="true">
                                                        <Div Class="drag-1">
                                                            <Span><Icon Class="fa-solid fa-grip-vertical" /></Span>
                                                            <Tooltip Text="@Child.Description">
                                                                <NavLink href="@($"/vocabularies/{TableName}/terms/{Child.Id.ToString()}/references")">@Child.Name</NavLink>
                                                            </Tooltip>
                                                        </Div>
                                                        <Div Class="drag-2 _dropbutton">

                                                            <Tooltip Text="Edit the Term">
                                                                <Icon Class="fa-solid fa-pen" Clicked="@( () => EditTermPopupAsync(Child))" />
                                                            </Tooltip>
                                                        </Div>
                                                    </Div>
                                                </Dropzone>
                                            }

                                            @if (Parent.Partner != null && Parent.Partner.Count > 0)
                                            {
                                                <Dropzone Context="Partner" Items="@Parent.Partner" Class="_taxon" OnItemDrop="((Term item) => OnDropTerm())">
                                                    <Div Class="_taxonomies _Child" Flex="Flex.JustifyContent.Between" draggable="true">
                                                        <Div Class="drag-1">
                                                            <Icon Class="fa-solid fa-grip-vertical" />
                                                            <Tooltip Text="@Partner.Description">
                                                                <NavLink href="@($"/vocabularies/{TableName}/terms/{Partner.Id.ToString()}/references")">@Partner.Name</NavLink>
                                                            </Tooltip>
                                                        </Div>
                                                        <Div Class="drag-2 _dropbutton">

                                                            <Tooltip Text="Edit the Term">
                                                                <Icon Class="fa-solid fa-pen" Clicked="@(() => EditTermPopupAsync(Partner))" />
                                                            </Tooltip>
                                                        </Div>
                                                    </Div>
                                                </Dropzone>
                                            }
                                        </Dropzone>
                                    </Div>
                                </Div>
                            </Column>
                        </Row>
                    }
                </ModalBody>
                <ModalFooter>
                    <AntDesign.Button OnClick="@ResetTermsOrderAsync" Class="_but-light-blue pl-2 pr-2 _noborder">Reset to alphabetical</AntDesign.Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

        <Modal @bind-Visible="@showUpdateVocabularyModal" Class="modals-lg antdraggable">
            <ModalContent Centered Class="forms">
                <ModalHeader Class="ant-header">
                    <ModalTitle>Edit vocabulary</ModalTitle>
                    <CloseButton />
                </ModalHeader>
                <ModalBody>
                    <Validations ValidateOnLoad="false">
                        <Field>
                            <FieldLabel>Name <Span>*</Span></FieldLabel>
                            <Validation Validator="@ValidateText">
                                <TextEdit Placeholder="Enter Name..." @bind-Text="@selectedVocabulary.Name">
                                    <Feedback>
                                        <ValidationError>Enter valid name</ValidationError>
                                    </Feedback>
                                </TextEdit>
                            </Validation>
                        </Field>
                    </Validations>
                    
                </ModalBody>
                <ModalFooter>
                    <AntDesign.Button Loading="@isUpdatingVocabulary"
                                      Disabled="@(string.IsNullOrWhiteSpace(selectedVocabulary.Name))"
                                      Class="_but-light-blue pl-2 pr-2" OnClick="@UpdateVocabularyAsync">
                        Save
                    </AntDesign.Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

        <Modal @bind-Visible="@showAddEditTermModal" Class="modals-lg antdraggable modalw-7">
            <ModalContent Centered Class="forms _custscroll">
                <ModalHeader Class="ant-header">
                    <ModalTitle>@(isAddingTermMode ? "Add a" : "Edit the") Term</ModalTitle>
                    <CloseButton />
                </ModalHeader>
                <ModalBody>
                    <Validations ValidateOnLoad="false">
                        <Field>
                            <FieldLabel>Name <Span>*</Span></FieldLabel>
                            <Validation Validator="@ValidateText">
                                <TextEdit Placeholder="Enter Name..." @bind-Text="@TermDetail.Name">

                                    <Feedback>
                                        <ValidationError>Enter valid name</ValidationError>
                                    </Feedback>
                                </TextEdit>
                            </Validation>
                        </Field>

                        <Field>
                            <FieldLabel>Description <Span>*</Span></FieldLabel>
                            <Validation Validator="@ValidateText">
                                <MemoEdit @bind-Text="@TermDetail.Description">
                                    <Feedback>
                                        <ValidationError>Enter valid description</ValidationError>
                                    </Feedback>
                                </MemoEdit>
                            </Validation>
                        </Field>

                        @if (TableName == Gina2.Core.Constants.VocabularyTables.Region)
                        {
                            <Field>
                                <FieldLabel>Select Status<Span>*</Span></FieldLabel>
                                <Select TValue="string" @bind-SelectedValue="@TermDetail.Status">
                                    <SelectItem Value="0">Select Status</SelectItem>
                                    <Repeater Items="@CountryStatus">
                                        <SelectItem Value="context">@context</SelectItem>
                                    </Repeater>
                                </Select>
                            </Field>
                        }
                    </Validations>
                    
                </ModalBody>
                <ModalFooter>
                    <AntDesign.Button Loading="@isSavingTerm"
                                      Disabled="@(string.IsNullOrWhiteSpace(TermDetail.Name) || (
TableName == Gina2.Core.Constants.VocabularyTables.Country && string.IsNullOrEmpty(TermDetail.Iso3Code)
&& string.IsNullOrEmpty(TermDetail.Code)
)|| (
TableName == Gina2.Core.Constants.VocabularyTables.Region && string.IsNullOrEmpty(TermDetail.Code)
))"
                                      Class="_but-light-blue pl-2 pr-2"
                                      OnClick="@SaveOrUpdateNewTerms">
                        Save
                    </AntDesign.Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-5 pb-5">
                    <Div Class="downl-flex">

                        <Heading Size="HeadingSize.Is3"></Heading>
                        <Heading Class="h-title" data-cy="TaxonomiesTitle">
                            @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(TaxonomyPageConfigurationKey.Title))
                            <AdminEditbut Key="@TaxonomyPageConfigurationKey.Title" />
                        </Heading>
                        <Paragraph Class="color-w" data-cy="TaxonomiesDescription">
                            @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(TaxonomyPageConfigurationKey.Description))
                            <AdminEditbut Key="@TaxonomyPageConfigurationKey.Description" />
                        </Paragraph>
                        <Breadcrumb Class="bread-crumb">
                            <BreadcrumbItem>
                                <BreadcrumbLink To="/" data-cy="TaxonomiesHomeLink">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbItem Active data-cy="TaxonomiesLink">
                                <BreadcrumbLink To="#" >Taxonomies</BreadcrumbLink>
                            </BreadcrumbItem>
                        </Breadcrumb>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Fluid>
            <Container Class="pt-4 pb-4">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                        <Div Class="_taxonscroll" id="taxonomiesHeader">
                            <Div Class="_taxonomies header sticky" Flex="Flex.JustifyContent.Between">
                                <Div Class="drag-1" data-cy="VocabularyName">
                                    Vocabulary name
                                </Div>
                                <Div Class="drag-2" data-cy="TaxonomiesActions">
                                    Actions
                                </Div>
                            </Div>
                            <Dropzone Items="Vocabularies" id="VocabulariesList" Class="_taxon content" OnItemDrop="((VocabularyModel item) => OnDropVocabulary())">
                                <Div id="@($"{context.Name.Split(" ")[0]}FeaturesTitle")">
                                <Div Class="_taxonomies" Flex="Flex.JustifyContent.Between" draggable="true">
                                    <Div Class="drag-1" data-cy=@($"{context.Name.Split(" ")[0]}FeaturesTitle") >
                                        <Icon Class="fa-solid fa-grip-vertical" data-cy=@($"{context.Name.Split(" ")[0]}NameIcon")/>
                                        @context.Name
                                    </Div>
                                    <Div Class="drag-2 _dropbutton">
                                        <Button data-cy=@($"{context.Name.Split(" ")[0]}EditBtn") Clicked="@(async () => ShowUpdateVocabularyPopupAsync(context))">
                                            <Tooltip data-cy=@($"{context.Name.Split(" ")[0]}EditTooltip") Placement="@Blazorise.TooltipPlacement.Top" Text="Edit the vocabulary">
                                                <Icon data-cy=@($"{context.Name.Split(" ")[0]}EditIcon") Class="fa-solid fa-pen" />
                                            </Tooltip>
                                        </Button>
                                        <Button data-cy=@($"{context.Name.Split(" ")[0]}ViewBtn") Clicked="@(async () => ListTermsAsync(context))">
                                            <Tooltip data-cy=@($"{context.Name.Split(" ")[0]}ViewTooltip") Placement="@Blazorise.TooltipPlacement.Top" Text="View terms">
                                                <Icon data-cy=@($"{context.Name.Split(" ")[0]}Icon") Class="fas fa-list-ul" />
                                            </Tooltip>
                                        </Button>
                                        <Button data-cy=@($"{context.Name.Split(" ")[0]}AddBtn") Clicked="@(() => AddNewTerm(context.TableName))">
                                            <Tooltip data-cy=@($"{context.Name.Split(" ")[0]}AddTooltip") Placement="@Blazorise.TooltipPlacement.Top" Text="Add terms">
                                                <Icon data-cy=@($"{context.Name.Split(" ")[0]}AddIcon") Class="fa-solid fa-plus-square" />
                                            </Tooltip>
                                        </Button>
                                    </Div>
                                    </Div>
                                </Div>
                            </Dropzone>
                        </Div>

                        @if (IsLoading)
                        {
                            <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="_65vh">
                                <AntDesign.Spin Spinning="@isLoadingTerms" />
                            </Div>
                        }
                    </Column>
                </Row>
                <AntDesign.Button OnClick="@ResetVocabularyOrderAsync" Class="_but-light-blue pl-2 pr-2 _noborder">Reset to alphabetical</AntDesign.Button>

            </Container>
        </Container>

    </Authorized>
</AuthorizeView>
