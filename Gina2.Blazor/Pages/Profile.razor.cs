﻿using AntDesign;
using AutoMapper;
using DocumentFormat.OpenXml.Office2010.Excel;
using Domain.Programme;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Constant;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.JSInterop;
using System.ComponentModel.DataAnnotations;

namespace Gina2.Blazor.Pages
{
    [Authorize(Roles = "Admin,Approver,Contributor")]
    public partial class Profile : PageConfirgurationComponent
    {
        [Inject]
        public UserManager<ApplicationUser> _userManager { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        public ICurrentUserService _CurrentUserService { get; set; }
        [Inject]
        public Gina2.Services.Country.ICountryService _countryService { get; set; }

        [Inject]
        public ITopicService _topicService { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }
        [Inject]
        public NavigationManager _navigationManager { get; set; }
        [Inject]
        public IMapper _mapper { get; set; }

        [Required]
        public string FirstName { get; set; }
        private bool auto = true;
        public ProfileUserModel UserProfile { get; set; } = new ProfileUserModel();
        public bool IsUpdateloading { get; set; } = false;
        public static List<MySqlRepository.Models.CountryWithRegion> Regions { get; set; } = new List<MySqlRepository.Models.CountryWithRegion>();
        public static List<string> RegionCode { get; set; } = new List<string>();
        public static List<string> CountryList { get; set; } = new List<string>();
        public List<Domain.Terms.Term> TopicList { get; set; } = new List<Domain.Terms.Term>();
        public IEnumerable<TopicParent> AllParentTopics { get; set; } = Enumerable.Empty<TopicParent>();
        private IEnumerable<string> values = new[] { "leaf1", "leaf2" };
        private IEnumerable<int> topicValues;
        private bool userProfileRefresh = false;
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                _ = GetLookupData();
                _ = GetTopicLookupData();
                await RefreshContent();
            }
        }

        public async Task OnNodeLoadDelayAsync(TreeEventArgs<Domain.Terms.Term> args)
        {

            if (args != null)
            {
                var dataItem = ((Domain.Terms.Term)args.Node.DataItem);
                dataItem.Child = await GetSearchTopics(dataItem.Id);
            }
                

        }

        public async Task SelectedTopics(IEnumerable<Domain.Terms.Term> args)
        {
            topicValues = args.Select(a => a.Id).ToList();
        }

        private async Task RefreshContent()
        {
                await GetUserInfo();
                await Task.Run(() => IsLoading = false);
                StateHasChanged();
            }

        private async Task GetLookupData()
        {
            if (!RegionCode.Any() || !CountryList.Any())
            {
                Regions = await _countryService.GetCountriesWithRegion();
                RegionCode = Regions.Where(e => e.RegionCode != null).DistinctBy(e => e.RegionCode).OrderBy(e => e.RegionOrder).ThenBy(e => e.RegionCode).Select(e => e.RegionCode).ToList();
                CountryList = Regions.Distinct().OrderBy(e => e.CountryOrder).ThenBy(e => e.CountryName).Select(e => e.CountryName).ToList();
                StateHasChanged();
            }
        }
        private string GetRegionName(string regionCode)
        {
            string name = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    name = "AFR - African Region";
                    break;
                case "AMR":
                    name = "AMR - Region of the Americas";
                    break;
                case "EMR":
                    name = "EMR - Eastern Mediterranean Region";
                    break;
                case "EUR":
                    name = "EUR - European Region";
                    break;
                case "SEAR":
                    name = "SEAR - South-East Asia Region";
                    break;
                case "WPR":
                    name = "WPR - Western Pacific Region";
                    break;
                default:
                    break;
            }
            return name;
        }
        private async Task GetTopicLookupData()
        {
            if (!AllParentTopics.Any())
            { 
            AllParentTopics = await _topicService.GetAllParentTopicsAsync();
            }

            if (!TopicList.Any())
            {
                TopicList = await GetSearchTopics(SearchTopicConfigurationKey.SearchTopicId);
                StateHasChanged();
            }
        }

        private async Task<List<Domain.Terms.Term>> GetSearchTopics(int topicId)
        {
            var topics = ConvertToTerms(AllParentTopics.Where(w => w.ParentId == topicId && w.Topic.IsSelected).OrderBy(t => t.OrderKey).ToList());
            foreach (var item in topics)
            {
                item.Child = await GetChildTopic(AllParentTopics.Where(x => x.ParentId.Equals(item.Id)).OrderBy(t => t.OrderKey).ToList());
            }
            return topics;
        }

        private async Task<List<Domain.Terms.Term>> GetChildTopic(IEnumerable<Gina2.DbModels.TopicParent> topics)
        {
            List<Gina2.DbModels.TopicParent> child = new List<Gina2.DbModels.TopicParent>();
            foreach (var item in topics)
            {
                
                if (topics.Any(t => t.Topic.IsSelected))
                {
                    child.Add(item);
                }

            }
            return ConvertToTerms(child);
        }
        public void OnRegionChanged(IEnumerable<string> regionCode)
        {
            if (userProfileRefresh == true && regionCode.Count() == 0)
            {
                userProfileRefresh = false;
                return;
            }
            userProfileRefresh = false;

            if (regionCode!=null)
            {
                UserProfile.UserInterestRegion = new List<string>(regionCode.Contains("all") ? RegionCode : regionCode);
                UserProfile.UserInterestCountries = new List<string>();
                UserProfile.UserInterestCountries = Regions
                                                    .Where(e => regionCode.Contains(e.RegionCode))
                                                    .Select(e => e.CountryName).ToList();
            }
            if(regionCode == null)
            { 
                UserProfile.UserInterestCountries = new List<string>();
            }
        }

        public List<Domain.Terms.Term> ConvertToTerms(List<TopicParent> terms)
        {
            var result = _mapper.Map<List<Domain.Terms.Term>>(terms);

            return result;
        }
        private async Task GetUserInfo()
        {
            using var _dbContext = DbFactory.CreateDbContext();
            UserProfile = await (from user in _dbContext.Users
                                 where user.Email == _CurrentUserService.Email
                                 select new ProfileUserModel()
                                 {
                                     Id = user.Id,
                                     FirstName = user.FirstName,
                                     LastName = user.LastName,
                                     UserName = user.UserName,
                                     Email = user.Email,
                                     Organization = user.Organization,
                                     Status = user.Status,
                                     Country = user.Country,
                                     UserInterestCountries = _dbContext.ApplicationUserIntertestedCountries.Where(e => e.UserId == user.Id).Select(e => e.Country).ToList(),
                                     UserInterestTopics = _dbContext.ApplicationUserIntertestedTopic.Where(e => e.UserId == user.Id).Select(e => e.TopicId).ToList()
                                 }).FirstOrDefaultAsync();

            if (UserProfile.UserInterestCountries?.Any() ?? true)
            {
                List<string> AllRegion = new List<string>();
                AllRegion.AddRange(RegionCode);
                UserProfile.UserInterestRegion = AllRegion;
            }

            userProfileRefresh = true;

            if (UserProfile != null)
            {
                UserProfile.ValidateFirstName = UserProfile.FirstName;
                UserProfile.ValidateLastName = UserProfile.LastName;
                UserProfile.ValidateCountries = UserProfile?.UserInterestCountries.ToList();
                UserProfile.ValidateTopics = UserProfile?.UserInterestTopics.ToList();
            }
            StateHasChanged();
        }

        public async Task OnUpdateClicked()
        {
            IsUpdateloading = true;
            var user = await _userManager.FindByEmailAsync(UserProfile.Email);
            user.FirstName = UserProfile.FirstName;
            user.LastName = UserProfile.LastName;
            user.Country = UserProfile.Country;
            user.Organization = UserProfile.Organization;
            await AddUserInterestedCountry(UserProfile);
            await AddUserInterestedTopics(UserProfile);
            var isUpdated = await _userManager.UpdateAsync(user);
            IsUpdateloading = false;
            if (isUpdated.Succeeded)
            {
                OpenToaster("User profile Update", "User updated Successfully", AntDesign.NotificationType.Success);

                if (user.FirstName == UserProfile.ValidateFirstName && user.LastName == UserProfile.ValidateLastName) 
                {
                    await RefreshContent();
                }
                else 
                {
                _navigationManager.NavigateTo("admin/profile", true);
            }
            }
            else
            {
                OpenToaster("User profile Update", "Something went wrong", AntDesign.NotificationType.Error);
            }
        }

        private async Task AddUserInterestedTopics(ProfileUserModel user)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            if (user.UserInterestTopics != null && user.UserInterestTopics.Any())
            {
                if (!UserProfile.UserInterestTopics.SequenceEqual(UserProfile.ValidateTopics))
                {
                    var OldUserTopics = await _dbContext.ApplicationUserIntertestedTopic.Where(e => e.UserId == user.Id).ToListAsync();
                    _dbContext.ApplicationUserIntertestedTopic.RemoveRange(OldUserTopics);
                    foreach (var item in user.UserInterestTopics)
                    {
                        _dbContext.ApplicationUserIntertestedTopic.Add(new ApplicationUserIntertestedTopic()
                        {
                            UserId = user.Id,
                            TopicId = item,
                            TopicName= TopicList.FirstOrDefault(e=>e.Id==item).Name
                        });
                    }
                }
            }
            else
            {
                var OldUserTopics = await _dbContext.ApplicationUserIntertestedTopic.Where(e => e.UserId == user.Id).ToListAsync();
                _dbContext.ApplicationUserIntertestedTopic.RemoveRange(OldUserTopics);
            }
            await _dbContext.SaveChangesAsync();
        }


        private async Task AddUserInterestedCountry(ProfileUserModel user)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            if (user.UserInterestCountries != null && user.UserInterestCountries.Any())
            {
                if (!UserProfile.UserInterestCountries.SequenceEqual(UserProfile.ValidateCountries))
                {
                    var OldUserCountries = await _dbContext.ApplicationUserIntertestedCountries.Where(e => e.UserId == user.Id).ToListAsync();
                    _dbContext.ApplicationUserIntertestedCountries.RemoveRange(OldUserCountries);
                    foreach (var item in user.UserInterestCountries)
                    {
                        _dbContext.ApplicationUserIntertestedCountries.Add(new ApplicationUserIntertestedCountry()
                        {
                            UserId = user.Id,
                            Country = item
                        });
                    }
                }
            }
            else
            {
                var OldUserCountries = await _dbContext.ApplicationUserIntertestedCountries.Where(e => e.UserId == user.Id).ToListAsync();
                _dbContext.ApplicationUserIntertestedCountries.RemoveRange(OldUserCountries);
            }
            await _dbContext.SaveChangesAsync();
        }
    }
}
