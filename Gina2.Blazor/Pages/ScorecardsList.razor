@page "/admin/scorecards"
@using Gina2.DbModels
@using Gina2.Blazor.Helpers.PageConfigrationData
@using System.Globalization;
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardPageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<AuthorizeView>
    <NotAuthorized>
        <UnAuthorizedView></UnAuthorizedView>
    </NotAuthorized>
    <Authorized>
        <Modal @bind-Visible="@deleteModalVisible" Class="modals-lg antdraggable _modalcenter">
            <ModalContent Centered Class="forms">
                <ModalHeader Class="ant-header">
                    <ModalTitle>Are you sure want to delete this data?</ModalTitle>
                </ModalHeader>
                <ModalFooter>
                    <Button Class="_but-delete pl-2 pr-2" Clicked="@(async ()=> { await DeleteById();} )">Yes</Button>
                    <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => deleteModalVisible = false)">Cancel</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>

        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-5 pb-4">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                         <Div Class="item1 pl-1  pr-1">
                            <Heading Size="HeadingSize.Is3" data-cy="ScoreCardListHeaderTitle">
                                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardPageConfigurationKey.Title))
                                <AdminEditbut Key="@ScorecardPageConfigurationKey.Title" />
                            </Heading>
                            <Paragraph Class="color-w subtitleediticon" Size="HeadingSize.Is3" data-cy="ScoreCardListHeaderTitle">
                                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ScorecardPageConfigurationKey.SubTitle))
                                <AdminEditbut Key="@ScorecardPageConfigurationKey.SubTitle" />
                            </Paragraph>
                             <Breadcrumb Class="bread-crumb">
                                 <BreadcrumbItem>
                                     <BreadcrumbLink To="/">Home</BreadcrumbLink>
                                 </BreadcrumbItem>
                                 <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#">Scorecard</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#">Scorecard list</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>
        <Container Class="gina-30 search-box">
            @*<Div Flex="Flex.JustifyContent.End" Class="poli-flex mb-2"><Button Class="but-yellow pl-1 pr-1 wid-mnin">Update All</Button></Div>*@
            <Div Class="DataGrids">
                <DataGrid Class="_checkgrid"
                          FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@Scorecard"
                          Data="@ScorecardList"
                          TotalItems="@SearchModel.TotalFilteredCount"
                          PageSize="SearchModel.PageSize"
                          CurrentPage="SearchModel.PageNo"
                          ShowPageSizes
                          ShowPager
                          Responsive
                          SortMode="DataGridSortMode.Single">
                    <EmptyTemplate>
                        <Div data-cy="NoDataFoundText">No data found</Div>
                    </EmptyTemplate>

                    <DataGridColumns>
                        <DataGridColumn Caption="Scorecard" Sortable="true" Field="@nameof(Scorecard.Title)" Width="28%">
                            <DisplayTemplate Context="row">
                                <NavLink href="@($"summary/{row.URL}")" @onclick=@(() => NavigateToMap(row.URL))>@row?.Title</NavLink>
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Caption="Country table" Sortable="true" Field="@nameof(Scorecard.Title)" Width="20%">
                            <DisplayTemplate Context="row">
                                <NavLink href="@($"summary/{row.URL}/data")" @onclick="@( () => HandleCountryTableClick(row) )">Country Table</NavLink>
                            </DisplayTemplate>
                        </DataGridColumn>

                        <DataGridColumn Caption="Created on" Width="15%">
                            <DisplayTemplate Context="row">
                                    @row.DateCreated.ToString("dd/M/yyyy", CultureInfo.InvariantCulture)
                            </DisplayTemplate>
                        </DataGridColumn>

                        <DataGridColumn Caption="Updated on" Width="15%">
                            <DisplayTemplate Context="row">
                                @row.LastUpdated?.ToString("dd/M/yyyy", CultureInfo.InvariantCulture)
                            </DisplayTemplate>
                        </DataGridColumn>
                        <DataGridColumn Caption="Status" Sortable="true" Width="15%">
                            <DisplayTemplate Context="row">
                                @if(row.IsPublished)
                                {
                                    <span style="background-color: green; color: white; padding: 3px">Published</span>

                                }
                                else
                                {
                                    <span style="background-color: red; color: white; padding: 3px">Unpublished</span>
                                
                                }
                            </DisplayTemplate>
                        </DataGridColumn>

                        <DataGridColumn Caption="Delete" Width="10%">
                            <DisplayTemplate Context="row">
                                <Icon data-cy=@($"{nameof(Scorecard.Title)}Delete") Class="_pointer _colors-w" Clicked="(e)=>ShowDeleteModal(row)" Name="IconName.Delete" />
                            </DisplayTemplate>
                        </DataGridColumn>
                    </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate Context="row">
                        <Badge TextColor="TextColor.Dark">
                            @((row.CurrentPageSize * (@row.CurrentPage - 1) + 1)) - @(Math.Min(((@row.CurrentPage - 1) * row.CurrentPageSize) + row.CurrentPageSize, row.TotalItems ?? 0))  of @row.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
            </Div>
        </Container>
    </Authorized>
</AuthorizeView>