﻿@page "/admin/profile"
@using Gina2.Blazor.Models.AdminModel
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using AntDesign.Select
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Core.Methods;
@inherits PageConfirgurationComponent
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.Title).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@IsLoading" />
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/Search.png);">
        <Container Class="pt-5 pb-5">
            <Heading Class="h-title" data-cy="UserProfileHeading">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.Title))
                <AdminEditbut Key="@UserProfilePageConfigurationKey.Title" />
            </Heading>
            <CardText Class="color-w subtitleediticon">
                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.SubTitle))
                <AdminEditbut Key="@UserProfilePageConfigurationKey.SubTitle" />
            </CardText>
            <Breadcrumb Class="bread-crumb">
                <BreadcrumbItem>
                    <BreadcrumbLink data-cy="UserProfileHomeBreadcrumbLink" To="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem Active>
                    <BreadcrumbLink To="#" data-cy="UserProfileBreadcrumbLink">Profile</BreadcrumbLink>
                </BreadcrumbItem>
            </Breadcrumb>
        </Container>
    </Card>
</Container>
<Container Fluid Class="bg-trdot pt-3">
    <AntDesign.Form Layout="@AntDesign.FormLayout.Vertical" Loading="IsUpdateloading" Model="@UserProfile"
                    LabelColSpan="8"
                    WrapperColSpan="16"
                    OnFinish="OnUpdateClicked">
        <Container Class="_profile _antdesign_form">
            <Div Flex="Flex.JustifyContent.Start.AlignItems.End" Class="downl-flex">
                <Div Class="_imag">
                    @(_CurrentUserService.DisplayUserName)
                </Div>
                <Div>
                    <Heading Class="_usertitel">@(!String.IsNullOrEmpty(_CurrentUserService.FirstName) ? _CurrentUserService.FullName : _CurrentUserService.Email)</Heading>
                    <Heading Class="_roles">@_CurrentUserService.UserRole</Heading>
                </Div>
            </Div>
            <Divider Class="divi-blue" />
            <AntDesign.Row>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label data-cy="UserProfileFirstNameLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileFirstNameLabel)
                                <span class="pr-1"> * </span>
                                <Tooltip data-cy="UserProfileFirstNameTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileFirstNameTooltip)">
                                    <Button Class="but-info _tooltip" data-cy="UserProfileFirstNameBtn">
                                        <Icon data-cy="UserProfileFirstNameIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileFirstNameGroup" />
                            </label>
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileFirstNamePlaceholder)" @bind-Value="@context.FirstName" />
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileLastNameLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileLastNameLabel)
                            <span class="pr-1"> * </span>
                            <Tooltip data-cy="UserProfileLastNameTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileLastNameTooltip)">
                                <Button data-cy="UserProfileLastNameBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileLastNameIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            </label>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileLastNameGroup" />
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileLastNamePlaceholder)" @bind-Value="@context.LastName" />
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
            </AntDesign.Row>
            <AntDesign.Row>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileUserNameLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileUserNameLabel)
                            </label>
                            <Tooltip data-cy="UserProfileUserNameLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileUserNameTooltip)">
                                <Button data-cy="UserProfileUserNameLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileUserNameLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileUserNameGroup" />
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Input Disabled="true"
                                             Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileUserNamePlaceholder)"
                                             @bind-Value="@context.UserName" />
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileEmailLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileEmailLabel)
                            </label>
                            <Tooltip data-cy="UserProfileEmailLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileEmailTooltip)">
                                <Button data-cy="UserProfileEmailLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileEmailLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileEmailGroup" />
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Input Disabled="true"
                                             Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileEmailPlaceholder)"
                                             @bind-Value="@context.Email" />
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
            </AntDesign.Row>
            <AntDesign.Row>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label data-cy="UserProfileOrganizationLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileOrganizationLabel)
                                <span class="pr-1"> * </span>

                                <Tooltip data-cy="UserProfileOrganizationLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileOrganizationTooltip)">
                                    <Button data-cy="UserProfileOrganizationBtn" Class="but-info _tooltip">
                                        <Icon data-cy="UserProfileOrganizationLabelIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileOrganizationGroup" />

                            </label>
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Input Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileOrganizationPlaceholder)"
                                             @bind-Value="@context.Organization" />
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
                <AntDesign.Col Span="12" Xs="24" Sm="24" Md="12" Lg="12" Xl="12" Class="_antSelecto">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label data-cy="UserProfileNationalityLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileNationalityLabel)
                                <span class="pr-1"> *</span>
                                <Tooltip data-cy="UserProfileNationalityTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileNationalityTooltip)">
                                    <Button data-cy="UserProfileNationalityBtn" Class="but-info _tooltip">
                                        <Icon data-cy="UserProfileNationalityIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileNationalityGroup" />
                            </label>
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Select TItemValue="string"
                                              Placeholder="Select country(ies)"
                                              TItem="string"
                                              EnableSearch
                                              @bind-Value="@UserProfile.Country"
                                              Style="width: 100%; margin-bottom: 8px;">
                                <SelectOptions>
                                    @foreach (var item in CountryList)
                                    {
                                        <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item Label=@item />
                                    }
                                </SelectOptions>
                            </AntDesign.Select>
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
            </AntDesign.Row>
            <AntDesign.Row Class="_Subscription">
                <Heading>Subscription</Heading>
            </AntDesign.Row>
            <AntDesign.Row>
                <AntDesign.Col Span="8" Xs="24" Sm="24" Md="8" Lg="8" Xl="8" Class="_antSelecto">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileRegionsOfInterestLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileRegionsOfInterestLabel)
                            </label>
                            <Tooltip data-cy="UserProfileRegionsOfInterestTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileRegionsOfInterestTooltip)">
                                <Button data-cy="UserProfileRegionsOfInterestBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileRegionsOfInterestIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileRegionsOfInterestGroup" />
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Select TItemValue="string"
                                              Placeholder="Select a WHO Region(s)"
                                              TItem="string"
                                              @bind-Values="@UserProfile.UserInterestRegion"
                                              OnSelectedItemsChanged="@OnRegionChanged"
                                              EnableSearch
                                              Mode="multiple"
                                              AllowClear
                                              Style="width: 100%; margin-bottom: 8px;">
                                <SelectOptions>
                                    <AntDesign.SelectOption TItemValue="string" TItem="string" Value="@("all")" Label="All" />
                                    @foreach (var item in RegionCode)
                                    {
                                        <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item Label=@GetRegionName(item) />
                                    }
                                </SelectOptions>
                            </AntDesign.Select>
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>

                <AntDesign.Col Span="8" Xs="24" Sm="24" Md="8" Lg="8" Xl="8">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileCountriesOfInterestLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileCountriesOfInterestLabel)
                            </label>
                            <Tooltip data-cy="UserProfileCountriesOfInterestTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileCountriesOfInterestTooltip)">
                                <Button data-cy="UserProfileCountriesOfInterestBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileCountriesOfInterestIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileCountriesOfInterestGroup" />
                        </LabelTemplate>
                        <ChildContent>
                            <AntDesign.Select Mode="multiple"
                                              TItemValue="string"
                                              Placeholder="Select country(ies)"
                                              TItem="string"
                                              @bind-Values="@UserProfile.UserInterestCountries"
                                              EnableSearch
                                              AllowClear
                                              Style="width: 100%; margin-bottom: 8px;">
                                <SelectOptions>

                                    @foreach (var item in CountryList)
                                    {
                                        <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item Label=@item />
                                    }
                                </SelectOptions>
                            </AntDesign.Select>
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
                <AntDesign.Col Span="8" Xs="24" Sm="24" Md="8" Lg="8" Xl="8" Class="_antSelecto">
                    <AntDesign.FormItem>
                        <LabelTemplate>
                            <label class="pr-1" data-cy="UserProfileRegionsOfInterestLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileTopicOfInterestLabel)
                            </label>
                            <Tooltip data-cy="UserProfileRegionsOfInterestTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserProfilePageConfigurationKey.UserProfileTopicOfInterestTooltip)">
                                <Button data-cy="UserProfileRegionsOfInterestBtn" Class="but-info _tooltip">
                                    <Icon data-cy="UserProfileRegionsOfInterestIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@UserProfilePageConfigurationKey.UserProfileTopicOfInterestGroup" />
                        </LabelTemplate>
                        <ChildContent>
                           @*  <AntDesign.Select DataSource="@TopicList"
                                              @bind-Values="@UserProfile.UserInterestTopics"
                                              Mode="multiple"
                                              TItemValue="int"
                                              TItem="Domain.Topics.Topic"
                                              LabelName="@nameof(Domain.Topics.Topic.Name)"
                                              ValueName="@nameof(Domain.Topics.Topic.Id)"
                                              Placeholder="Select Topic(s)"
                                              AllowClear
                                              EnableSearch
                                              Style="width: 100%; margin-bottom: 8px;">
                            </AntDesign.Select> *@
                            <AntDesign.TreeSelect DataSource="TopicList"
                                                  TItem="Domain.Terms.Term"
                                                  TItemValue="int"
                                                  ItemValue="item=>item.Id"
                                                  ItemLabel="item=>item?.Name"
                                                  Placeholder="Please select"
                                                  OnNodeLoadDelayAsync="@OnNodeLoadDelayAsync"
                                                  AllowClear
                                                  Multiple
                                                  ChildrenExpression="node=>node?.DataItem?.Child"
                                                  TitleExpression="node=>node?.DataItem?.Name"
                                                  KeyExpression="node=>node?.DataItem?.Key"
                                                  Style="width: 100%; margin-bottom: 8px;">
                            </AntDesign.TreeSelect>
                        </ChildContent>
                    </AntDesign.FormItem>
                </AntDesign.Col>
            </AntDesign.Row>

        </Container>
        <Container Class="mt-0 pb-6">
            <Div Class="stickybottom pl-1">
                <AntDesign.Button Class="but-yellow" HtmlType="submit">
                    Update
                </AntDesign.Button>
            </Div>
        </Container>
    </AntDesign.Form>
</Container>

