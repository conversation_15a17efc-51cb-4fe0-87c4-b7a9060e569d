﻿@page "/scorecardss/{Id:int}/edit"

<Loader IsLoading="@isLoading" />
<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem href="@($"/scorecards/{Scorecard?.Id}")">View</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
<AuthorizeView Context="authorizedContext">
    <Authorized>
        <Validations @ref="ValidationCheck" Mode="ValidationMode.Manual">
        <Container Fluid Padding="Padding.Is0">
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-5 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                             <Div Class="item1 pl-1  pr-1">
                            <Heading Size="HeadingSize.Is3">Edit - Sodium Country Score Card</Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="#">Score Card</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbItem Active>
                                    <BreadcrumbLink To="#">Edit Score Card</BreadcrumbLink>
                                </BreadcrumbItem>
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>

        <Container Fluid Class="newdraft" Padding="Padding.Is0">
            <Container Padding="Padding.Is0" Class="pt-6 mobi-heing">
                <Heading Class="new-heading" Size="HeadingSize.Is3">Edit the information for Score Card</Heading>
                <Divider Class="divi-blue" />
            </Container>
            <Container Class="form-newd">
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel>Title<Span>*</Span></FieldLabel>
                        <Validation Validator="ValidationRule.IsNotEmpty">
                        
                            <TextEdit Text="@Scorecard?.Title" TextChanged="@OnTitleChanged" />
                       
                         <ValidationError>Please enter Title!</ValidationError>
                        </Validation>
                    </Field>
                </Fields>
                <Fields>
                    <Field>
                        <Div Flex="Flex.JustifyContent.Between">
                            <Div Class="item1">
                                <FieldLabel>Description</FieldLabel>
                            </Div>
                            <Div Class="item2">
                                <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            </Div>
                        </Div>
                        <RextEditors Value="@(Scorecard.Description)" Changed="@ChangeDescription"></RextEditors>
                    </Field>
                </Fields>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel>URL alias<Span>*</Span></FieldLabel>
                         <Validation Validator="ValidationRule.IsNotEmpty">
                            <TextEdit @bind-Text="@Scorecard.URL"></TextEdit>
                         <ValidationError>Please enter Title!</ValidationError>
                        </Validation>
                       
                        <FieldHelp>Optionally specify an alternative URL by which this term can be accessed. Don't add any special characters or spaces or the URL alias won't work.</FieldHelp>
                    </Field>
                    @*  <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Population Count File</FieldLabel>
                    <FileEdit Changed="@OnChanged" Placeholder="Select or drag and drop the image" Multiple />
                    <FieldHelp>Files must be less than 25 MB     |     Allowed file types: .xlsx .csv</FieldHelp>
                    </Field>*@
                </Fields>
                <Field>
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div Class="item1">
                            <FieldLabel>Population Count Info</FieldLabel>
                        </Div>
                        <Div Class="item2">
                            <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        </Div>
                    </Div>
                    <RextEditors Value="@(Scorecard.PopulationCountInfo)" Changed="@ChangePopulationInfo" ></RextEditors>
                </Field>
                  <Field>
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div Class="item1">
                            <FieldLabel>Text group bottom</FieldLabel>
                        </Div>
                        <Div Class="item2">
                            <Tooltip Text="Hello tooltip"><Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        </Div>
                    </Div>
                    <RextEditors Value="@(Scorecard.BottomDescription)" Changed="@ChangeBottomDescription" ></RextEditors>
                </Field>
            </Container>

            <Container Class="mt-4 pb-6">
              @*  <Field>
                    <FieldLabel>Publishing options</FieldLabel>
                    <Check TValue="bool" @bind-Checked="@Scorecard.IsPublished">Published</Check>
                    <FieldHelp>Only privileged users are allowed to view a taxonomy term if this option is disabled</FieldHelp>
                </Field>*@
                <Button Class="but-blues" Clicked="@SaveAsync">Save</Button>
                <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">

                </Snackbar>
            </Container>
        </Container>
        </Validations>
    </Authorized>
    <NotAuthorized>
        <UnAuthorizedView />
    </NotAuthorized>
</AuthorizeView>


