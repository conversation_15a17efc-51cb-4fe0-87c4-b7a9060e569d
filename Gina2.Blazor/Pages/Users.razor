﻿@page "/admin/users"
@using Gina2.Blazor.Models.AdminModel
@using Gina2.DbModels
@using static Gina2.Core.Constants
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
@using System.Globalization
<Loader IsLoading="@IsLoading" />
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.HeadingTop).ConvertHtmlToPlainText())</PageTitle>
<Modal @bind-Visible="@ThemesVisible" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms adminmobel">
        <ModalHeader Class="pb-0 ant-header">
            <ModalTitle>Add user role</ModalTitle>
            <NavLink class="close" onclick="@(()=> HideModal())"><img src="/img/close.png" /></NavLink>
        </ModalHeader>
        <hr />
        <AntDesign.Form Layout="@AntDesign.FormLayout.Vertical" Loading="@IsSavingloading" Model="@User"
                        LabelColSpan="8"
                        WrapperColSpan="16"
                        OnFinish="OnSavedClicked">
            <ModalBody Class="pl-2 pr-2">

                <AntDesign.Row>
                    <AntDesign.Col Span="24">
                        <AntDesign.FormItem>
                            <LabelTemplate>
                                <label class="ant-form-item-required" data-cy="UserRoleLabel">
                                    @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserRoleLabel)
                                </label>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserRoleTooltip)">
                                    <Button Class="but-info _tooltip">
                                        <Icon Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbutDoubleModel Key="@UserManagementPageConfigurationKey.UserRoleGroup" />
                            </LabelTemplate>
                            <ChildContent>
                                <AntDesign.RadioGroup TValue="string" @bind-Value="@context.Role">
                                    <AntDesign.Radio TValue="string" Value="RolesConstant.AdminRoleName">@RolesConstant.AdminRoleName</AntDesign.Radio>
                                    <AntDesign.Radio TValue="string" Value="RolesConstant.ApproverRoleName">@RolesConstant.ApproverRoleName</AntDesign.Radio>
                                    <AntDesign.Radio TValue="string" Value="RolesConstant.ContributorRoleName">@RolesConstant.ContributorRoleName</AntDesign.Radio>
                                </AntDesign.RadioGroup>
                            </ChildContent>
                        </AntDesign.FormItem>
                    </AntDesign.Col>

                    @if (@context.Role != null && @context.Role.Equals(@RolesConstant.ApproverRoleName))
                    {
                        <AntDesign.Col Span="24">
                            <AntDesign.FormItem>
                                <LabelTemplate>
                                    <label>
                                        @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserApproverRegionLabel)
                                    </label>
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserApproverRegionTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@UserManagementPageConfigurationKey.UserApproverRegionGroup" />
                                </LabelTemplate>
                                <ChildContent>
                                    <AntDesign.Select TItemValue="string"
                                                  Placeholder="Select WHO region(s)"
                                                  TItem="string"
                                                  @bind-Values="@User.ApproverRegion"
                                                  OnSelectedItemsChanged="@OnRegionChanged"
                                                      Mode="multiple"
                                                      EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 8px;">
                                        <SelectOptions>
                                            <AntDesign.SelectOption TItemValue="string" TItem="string" Value="@("all")" Label="All" />
                                            @foreach (var item in RegionCode)
                                            {
                                                <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item Label=@GetRegionName(item) />
                                            }
                                        </SelectOptions>
                                    </AntDesign.Select>
                                </ChildContent>
                            </AntDesign.FormItem>
                        </AntDesign.Col>

                        <AntDesign.Col Span="24">
                            <AntDesign.FormItem>
                                <LabelTemplate>
                                    <label class="ant-form-item-required">
                                        @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserApproverCountriesLabel)
                                    </label>
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserApproverCountriesTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbutDoubleModel Key="@UserManagementPageConfigurationKey.UserApprvoerCountriesGroup" />
                                </LabelTemplate>
                                <ChildContent>
                                    <AntDesign.Select Mode="multiple"
                                                  TItemValue="string"
                                                  Placeholder="Select country(ies)"
                                                  TItem="string"
                                                  @bind-Values="@User.ApproverCountries"
                                                  EnableSearch
                                                  AllowClear
                                                  Style="width: 100%; margin-bottom: 8px;">
                                        <SelectOptions>
                                            @foreach (var item in CountryList)
                                            {
                                                <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item Label=@item />
                                            }
                                        </SelectOptions>
                                    </AntDesign.Select>
                                </ChildContent>
                            </AntDesign.FormItem>
                        </AntDesign.Col>
                    }
                </AntDesign.Row>

            </ModalBody>

            <ModalFooter>
                <AntDesign.Button Class="but-blues apply" HtmlType="submit"> Save</AntDesign.Button>
                <AntDesign.Button Class="cancel" OnClick="@HideModal">Cancel</AntDesign.Button>
            </ModalFooter>
           
        </AntDesign.Form>
    </ModalContent>
</Modal>

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pl-1  pr-1">
                    <Heading Size="HeadingSize.Is3"  data-cy="UserHeadingTopic">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.HeadingTop))
                        <AdminEditbut Key="@UserManagementPageConfigurationKey.HeadingTop" />
                    </Heading>
                    <Paragraph Class="color-w subtitleediticon" Size="HeadingSize.Is3" data-cy="UserHeadingTopic">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.SubHeading))
                        <AdminEditbut Key="@UserManagementPageConfigurationKey.SubHeading" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="UserHomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                         <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="UserManagement" To="#">User management</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="UserLink" To="#">Users</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="pt-5 m-pt-2 pl-2 pr-2 adminuser">
    <Row>
        <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
            <Heading Size="HeadingSize.Is2" Class="Headingh3"data-cy="UserHeaderTitle">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.HeadingPage))
                <AdminEditbut Key="@UserManagementPageConfigurationKey.HeadingPage" />
            </Heading>
        </Column>
    </Row>
    <Divider Class="divi-gray" />
    <Fields Class="form-news _users-f">
        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
            <FieldLabel data-cy="UserSearchTitleLabel">
                @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchTitleLabel)
                <Tooltip data-cy="UserSearchTitleLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchTitleTooltip)">
                    <Button data-cy="UserSearchTitleLabelBtn" Class="but-info _tooltip">
                        <Icon data-cy="UserSearchTitleLabelIcon" Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@UserManagementPageConfigurationKey.UserSearchTitleGroup" />
            </FieldLabel>
            <TextEdit data-cy="UserSearchTitlePlaceholder" @bind-Text="SearchModel.Search" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchTitlePlaceholder)"></TextEdit>
        </Field>

        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD" Class="_antdesign">
            <FieldLabel data-cy="UserSearchRoleLabel">
                @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchRoleLabel)
                <Tooltip data-cy="UserSearchRolelabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchRoleTooltip)">
                    <Button data-cy="UserSearchRolelabelBtn" Class="but-info _tooltip">
                        <Icon data-cy="UserSearchRolelabelIcon" Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@UserManagementPageConfigurationKey.UserSearchRoleGroup" />
            </FieldLabel>
            <AntDesign.SimpleSelect @bind-Value="@SearchModel.Role" Placeholder="Any...">
                <SelectOptions>
                    <AntDesign.SimpleSelectOption Label="All"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Admin" Label="Admin"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Approver" Label="Approver"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Contributor" Label="Contributor"></AntDesign.SimpleSelectOption>
                </SelectOptions>
            </AntDesign.SimpleSelect>
        </Field>

        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD" Class="_antdesign">
            <FieldLabel  data-cy="UserSearchStatusLabel">
                @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchStatusLabel)
                <Tooltip data-cy="UserSearchStatusLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchStatusTooltip)">
                    <Button data-cy="UserSearchStatusLabelBtn" Class="but-info _tooltip">
                        <Icon data-cy="UserSearchStatusLabelIcon" Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@UserManagementPageConfigurationKey.UserSearchStatusGroup" />
            </FieldLabel>
            <AntDesign.SimpleSelect @bind-Value="@SearchModel.Status" Placeholder="Any...">
                <SelectOptions>
                    <AntDesign.SimpleSelectOption Label="All"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Active" Label="Active"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Blocked" Label="Blocked"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Pending" Label="Pending"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="invite sent" Label="invite sent"></AntDesign.SimpleSelectOption>
                </SelectOptions>
            </AntDesign.SimpleSelect>
        </Field>
        <Field Flex="Flex.JustifyContent.End.AlignItems.End" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD">
            <Button data-cy="FilterBtn" Class="but-blues apply w-100" Disabled="@IsSearchClicked" Loading="@IsSearchClicked" Clicked="@OnSearchClicked">Filter</Button>
        </Field>
    </Fields>

    @*<Divider Class="divi-gray" />*@

    <Fields Class="form-news _users-f">
        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_antdesign">
            <FieldLabel data-cy="UserSearchUpdateStatusLabel">
                @PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchUpdateStatusLabel)
                <Tooltip data-cy="UserSearchUpdateStatusLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(UserManagementPageConfigurationKey.UserSearchUpdateStatusTooltip)">
                    <Button data-cy="UserSearchUpdateStatusLabelBtn" Class="but-info _tooltip">
                        <Icon data-cy="UserSearchUpdateStatusLabelIcon" Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@UserManagementPageConfigurationKey.UserSearchUpdateStatusGroup" />
            </FieldLabel>
            <AntDesign.SimpleSelect @bind-Value="@UpdateUserStatus" Placeholder="Any...">
                <SelectOptions>
                    <AntDesign.SimpleSelectOption Value="Active" Label="Active"></AntDesign.SimpleSelectOption>
                    <AntDesign.SimpleSelectOption Value="Blocked" Label="Blocked"></AntDesign.SimpleSelectOption>
                </SelectOptions>
            </AntDesign.SimpleSelect>
        </Field>
        <Field data-cy="Update" Flex="Flex.JustifyContent.End.AlignItems.End" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD">
            <Button data-cy="UpdateBtn" Class="but-blues apply w-100" Loading="@IsUserStatusClicked" Disabled="@DisabledUserStatus" Clicked="@OnUserStatusClicked">Update</Button>
        </Field>
        <Field data-cy="Export" Flex="Flex.JustifyContent.End.AlignItems.End" Class="padButton" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD">
            <Button data-cy="ExportBtn" Class="but-yellow w-100" Loading="@IsUserDownloading" Clicked="@ExportUsersClicked"><Icon Class="fas fa-solid fa-upload" /> Export user</Button>
        </Field>
         <Field data-cy="Invite" Flex="Flex.JustifyContent.End.AlignItems.End" Class="padButton" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD">
            <Button data-cy="InviteUser" Class="but-yellow w-100" Loading="@IsInviationSending" Disabled="@DisabledInviateUserStatus" Clicked="@SendNewRegisterEmailCliked"><Icon Class="far fa-plus" /> Invite user</Button>
        </Field>
    </Fields>
    @if (UserResults != null &&UserResults.Count == 0)
    {
        <Divider Class="divi-blue" />
        <Div Flex="Flex.JustifyContent.Center">No results found.</Div>
        <Divider Class="divi-blue" />
    }else{
        <Layout Class="search-box pt-3 pb-3 mob-layout">
            <Layout Class="left-layout DataGrids _userdgrids _mobilUserResults">
                <LayoutContent>
                    <DataGrid @ref="SearchDataGrid"
                          FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@AppUserModel"
                          Data="@UserResults"
                          TotalItems="@SearchModel.TotalFilteredCount"
                          PageSize="SearchModel.PageSize"
                          CurrentPage="SearchModel.PageNo"
                          ReadData="@OnReadData"
                          SortChanged="@OnSortChanged"
                          ShowPageSizes
                          ShowPager
                          Responsive
                          SortMode="DataGridSortMode.Single"
                          Class="_DataGrids_action">
                        <DataGridColumns>
                            <DataGridCheckColumn Field="@nameof(AppUserModel.Check)">
                                <DisplayTemplate>
                                    <Check TValue="bool" Checked="context.Check" CheckedChanged="(e) => ToggleSelectDeselectSelection(context.UserName,e)" ReadOnly="true" Sortable="false" />
                                </DisplayTemplate>
                            </DataGridCheckColumn>
                            <DataGridColumn Field="@nameof(AppUserModel.FullName)" Caption="Name" Sortable="false" Width="10%"></DataGridColumn>
                            <DataGridColumn Field="@nameof(AppUserModel.UserName)" Caption="User name" Sortable="true" Width="15%" />
                            <DataGridColumn Field="@nameof(AppUserModel.Email)" Caption="Email" Sortable="true" Width="15%" />
                            <DataGridColumn Field="@nameof(AppUserModel.Organization)" Caption="Organization" Sortable="true" Width="15%" />
                            <DataGridColumn Field="@nameof(AppUserModel.Status)" Caption="Status" Sortable="true" Width="8%" />
                            <DataGridColumn Field="@nameof(AppUserModel.UserRoles)" Caption="Role" Sortable="true" Width="8%" />
                            <DataGridColumn Field="@nameof(AppUserModel.InterestedCountry)"Sortable="false" Caption="Interested country" Width="9%">
                                <DisplayTemplate>
                                    <div class="Countryhim">
                                        @context.InterestedCountry
                                        <div class="InterestedCountry">  @string.Join(", " ,context.InterestedCountryList)</div>
                                    </div>

                                </DisplayTemplate>
                            </DataGridColumn>
                            <DataGridColumn Field="@nameof(AppUserModel.CreatedDate)" Caption="Start date" Sortable="true" Width="10%" >
                                <DisplayTemplate>
                                    @context.CreatedDate.ToString("dd/M/yyyy", CultureInfo.InvariantCulture)
                                </DisplayTemplate>
                            </DataGridColumn>

                            <DataGridColumn GroupCellClass="_action-flex-center" Caption="Action">
                                <DisplayTemplate>
                                    <Tooltip Text="Edit"><Icon Class="_pointer" Clicked="(e)=>GetUserById(context.Id ,context.UserRoles)" Name="IconName.Pen" /></Tooltip>
                                    @if (context.IsNewRegister)
                                    {
                                        <Tooltip Text="Invite user"><Icon Class="_pointer" Clicked="(e)=>InviationBtnClicked(context)" Name="IconName.UserPlus" /></Tooltip>
                                    }

                                </DisplayTemplate>
                            </DataGridColumn>
                        </DataGridColumns>
                        <ItemsPerPageTemplate></ItemsPerPageTemplate>
                        <TotalItemsTemplate>
                            <Badge TextColor="TextColor.Dark">
                                @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                            </Badge>
                        </TotalItemsTemplate>
                    </DataGrid>

                </LayoutContent>
            </Layout>
        </Layout>
    }
   
</Container>
