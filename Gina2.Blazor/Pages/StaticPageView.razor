﻿@page "/pages/{dynamicUrl}"
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
             <DropdownMenu>
                 <DropdownItem Clicked="@(()=>{ NavigationManager.NavigateTo($"/admin/static ");  })">
                     Edit
                 </DropdownItem>
                 @*<DropdownItem href="home-moderate">Moderate</DropdownItem>
                <DropdownItem href="home-translate">Translate</DropdownItem>*@
             </DropdownMenu>
         </Dropdown>
     </Authorized>
 </AuthorizeView>
@* <Loader IsLoading="@isLoading" /> *@

@if (staticPage != null)
{
    <PageTitle>GIFNA @staticPage.Title.ConvertHtmlToPlainText()</PageTitle>
    <Container Fluid Padding="Padding.Is0">

         <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
             <Container Class="ginasearch pt-5 pb-5">
                 <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                     <Div Class="item0">
                         <Heading Size="HeadingSize.Is3">
                            <div>@(new MarkupString(staticPage.Title))</div>
                         </Heading>
                         <Paragraph class="color-w">
                            <div>@(new MarkupString(staticPage.Description))</div>
                         </Paragraph>
                     </Div>
                 </Div>
             </Container>
         </Card>

     </Container>

    <Container>
        <Layout Sider Class="search-box aboutus pt-6 pb-5 mob-layout">

             <Layout Class="left-layout pr-3">
                 <LayoutContent Class="tabsel">
                     <div>
                         @((MarkupString)staticPage.Content)
                     </div>
                 </LayoutContent>
             </Layout>
         </Layout>
     </Container>

}
else
{
    <p>Loading...</p>
}
