﻿@page "/terms-of-use"

@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">GIFNA Terms of use</Heading>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink To="">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink To="terms-of-use">Terms of use</BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>

<Container Class="text-used pt-4 pb-4">
       @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(DisclaimerPageConfigurationKey.DisclaimerBodyContent))
    <AdminEditbut Key="@DisclaimerPageConfigurationKey.DisclaimerBodyContent" />
</Container>

