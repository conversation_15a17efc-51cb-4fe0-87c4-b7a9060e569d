﻿@page "/content-administration"
@using System.ComponentModel;
<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner bord-or" Style="background-image: url(../img/Maskgroup.png);">
        <Div class="container ginasearch pt-5 pb-5 mb-5 mob-0-b">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex pt-5">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">Content Administration</Heading>
                </Div>
            </Div>
        </Div>
    </Card>
</Container>

<Container Fluid Class="bg-trdot pt-4">
    <Container Fluid Class="policy-li">
        <Tabs SelectedTab="@AdminTab" Class="container policy-list" SelectedTabChanged="@AdminTabList">
            <Items>
                <Tab Name="contentadmin">Content Administration</Tab>
                <Tab Name="pendingcontent">Pending Content</Tab>
            </Items>

            <Content>
                <TabPanel Name="contentadmin" Class="pt-4 m-pt-2">
                   <Div Flex="Flex.JustifyContent.Between" Class="downl-flex pt-5">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">Administration details</Heading>
                </Div>
                <Div Class="item2">
                    <Button Class="but-yellow mr-1"><Icon class="arrow-bottom"/> PDF</Button>
                </Div>
            </Div>
                     <Divider/>
                     <Div Class="forms">
        <Fields>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Published</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Type</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Any</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Language</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            </Fields>
            <Fields>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Country(ies)</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Creator (contains)</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Any</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Edited by (contains)</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
            </Field>
            </Fields>
            <Fields>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Funding source</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>State</FieldLabel>
                    <Select TValue="int">
                        <SelectItem Value="0">Any</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Items per page</FieldLabel>
                <Select TValue="int">
                        <SelectItem Value="0">Select</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                     <Button Class="but-blues pl-2 pr-2">Apply</Button>
                </Field>
             </Fields>
                <Fields>
                <Field ColumnSize="ColumnSize.Is9.OnTablet.Is12.OnMobile.Is9.OnDesktop.Is9.OnWidescreen.Is9.OnFullHD">
                <FieldLabel>Oberations</FieldLabel>
                <Select TValue="int">
                        <SelectItem Value="0">Please choose an operation</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                </Select>
            </Field>
            <Field Class="co-admin" ColumnSize="ColumnSize.Is3.OnTablet.Is12.OnMobile.Is3.OnDesktop.Is3.OnWidescreen.Is3.OnFullHD">
                 <Button Class="but-blues pl-2 pr-2">Execute</Button>
                </Field>
            </Fields>
            </Div>

            <Div Class="table-responsive header">
   <Table Class="table-bg" FixedHeader>
    <TableHeader id="FixedTable"> 
        <TableRow>
            <TableHeaderCell ColumnSpan="2"></TableHeaderCell>
            <TableHeaderCell Class="yellow-lights" ColumnSpan="5">Latest revision (Blank if same as published)</TableHeaderCell>
            <TableHeaderCell Class="grean-lights" ColumnSpan="3">Published Revision</TableHeaderCell>
            <TableHeaderCell Class="Blue-lights" ColumnSpan="4"></TableHeaderCell>
        </TableRow>
        <TableRow>
            <TableHeaderCell Class="gray-very-lights"><Check TValue="bool"></Check></TableHeaderCell>
            <TableHeaderCell Class="gray-very-lights">NID</TableHeaderCell>
            <TableHeaderCell Class="yellow-very-lights">Latest Revision Title</TableHeaderCell>
            <TableHeaderCell Class="yellow-very-lights">Countries</TableHeaderCell>
            <TableHeaderCell Class="yellow-very-lights">State</TableHeaderCell>
            <TableHeaderCell Class="yellow-very-lights">Edited by</TableHeaderCell>
            <TableHeaderCell Class="yellow-very-lights">Updated date</TableHeaderCell>
            <TableHeaderCell Class="green-very-lights">Revision Title</TableHeaderCell>
            <TableHeaderCell Class="green-very-lights">Modified by</TableHeaderCell>
            <TableHeaderCell Class="green-very-lights">Modified date</TableHeaderCell>
            <TableHeaderCell Class="Blue-very-lights">Type</TableHeaderCell>
            <TableHeaderCell Class="Blue-very-lights">Creator</TableHeaderCell>
            <TableHeaderCell Class="Blue-very-lights">Published Status</TableHeaderCell>
            <TableHeaderCell Class="Blue-very-lights">Created date</TableHeaderCell>
        </TableRow>
    </TableHeader>
    <TableBody>
        <TableRow>
            <TableRowCell Class="tabrl-bg"><Check TValue="bool"></Check></TableRowCell>
            <TableRowCell Class="tabrl-bg">43870</TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg">Food Safety and Standards (Prohibition and Restrictions on Sales) Second Amendment Regulations, 2021</TableRowCell>
            <TableRowCell Class="tabrl-bg"><EMAIL></TableRowCell>
            <TableRowCell Class="tabrl-bg">06/01/2022 - 13:14</TableRowCell>
            <TableRowCell Class="tabrl-bg">Policy</TableRowCell>
            <TableRowCell Class="tabrl-bg">engesveenk</TableRowCell>
            <TableRowCell Class="tabrl-bg">Published</TableRowCell>
            <TableRowCell Class="tabrl-bg">02/24/2021 - 00:06</TableRowCell>
        </TableRow>
        <TableRow>
            <TableRowCell Class="tabrl-bg"><Check TValue="bool"></Check></TableRowCell>
            <TableRowCell Class="tabrl-bg">43870</TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg">Food Safety and Standards (Prohibition and Restrictions on Sales) Second Amendment Regulations, 2021</TableRowCell>
            <TableRowCell Class="tabrl-bg"><EMAIL></TableRowCell>
            <TableRowCell Class="tabrl-bg">06/01/2022 - 13:14</TableRowCell>
            <TableRowCell Class="tabrl-bg">Policy</TableRowCell>
            <TableRowCell Class="tabrl-bg">engesveenk</TableRowCell>
            <TableRowCell Class="tabrl-bg">Published</TableRowCell>
            <TableRowCell Class="tabrl-bg">02/24/2021 - 00:06</TableRowCell>
        </TableRow>
        <TableRow>
            <TableRowCell Class="tabrl-bg"><Check TValue="bool"></Check></TableRowCell>
            <TableRowCell Class="tabrl-bg">43082</TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg"></TableRowCell>
            <TableRowCell Class="tabrl-bg">Food Safety and Standards (Prohibition and Restrictions on Sales) Second Amendment Regulations, 2021</TableRowCell>
            <TableRowCell Class="tabrl-bg"><EMAIL></TableRowCell>
            <TableRowCell Class="tabrl-bg">06/01/2022 - 13:14</TableRowCell>
            <TableRowCell Class="tabrl-bg">Policy</TableRowCell>
            <TableRowCell Class="tabrl-bg">engesveenk</TableRowCell>
            <TableRowCell Class="tabrl-bg">Published</TableRowCell>
            <TableRowCell Class="tabrl-bg">02/24/2021 - 00:06</TableRowCell>
        </TableRow>
    </TableBody>
</Table>

<Div Flex="Flex.JustifyContent.Between">
    <Div Flex="Flex.Row.AlignItems.Baseline"><Div Class="page-per">Per page:</Div> <Select TValue="int">
                        <SelectItem Value="0">10</SelectItem>
                        <SelectItem Value="1">20</SelectItem>
                        <SelectItem Value="2">30</SelectItem>
                    </Select></Div>
    <Div><Pagination>
    <PaginationItem Disabled="@isActive.First()" @onclick="Previous">
        <PaginationLink>
            <span aria-hidden="true">«</span>
        </PaginationLink>
    </PaginationItem>
    <PaginationItem Active="@isActive[0]">
        <PaginationLink Page="1" Clicked="SetActive">
            1
        </PaginationLink>
    </PaginationItem>
    <PaginationItem Active="@isActive[1]">
        <PaginationLink Page="2" Clicked="SetActive">
            2
        </PaginationLink>
    </PaginationItem>
    <PaginationItem Active="@isActive[2]">
        <PaginationLink Page="3" Clicked="SetActive">
            3
        </PaginationLink>
    </PaginationItem>
    <PaginationItem Disabled="@isActive.Last()" @onclick="Next">
        <PaginationLink>
            <span aria-hidden="true">»</span>
        </PaginationLink>
    </PaginationItem>
</Pagination></Div>
</Div>


</Div>
                </TabPanel>
                <TabPanel Name="pendingcontent" Class="pt-6 m-pt-2">
                    Pending Content
                </TabPanel>
            </Content>
        </Tabs>
    </Container>
</Container>
