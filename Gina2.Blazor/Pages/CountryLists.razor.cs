﻿using Domain.Search;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Shared;
using Gina2.Core;
using Gina2.Core.Enums;
using Gina2.DbModels;
using Gina2.DbModels.Views;
using Gina2.Services.Country;
using Gina2.Services.Models;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Pages
{
    public partial class CountryLists : PageConfirgurationComponent
    {
        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        private CountriesViewOption ViewType { get; set; }
        private IEnumerable<Region> Regions { get; set; } = new List<Region>();
        private List<ViewCountryDetails> CountryItems { get; set; } = new();
        private List<ViewCountryDetails> FullListCountryItems { get; set; } = new();
        [Inject]
        private IPolicyService _policyService { get; set; }

        private IEnumerable<SearchCount> searchCounts { get; set; } = new List<SearchCount>();
        private GlobalSearchRequest searchRequest = new() { SelectedStatus = CountryStaus.MemberState };
        private FileDownload FileDownloadChild { get; set; }
        public bool ShowCSVPanel { get; set; } = true;
        [Parameter]
        public string SelectedDatatype { get; set; } = "Policies";
        [Parameter]
        public string SelectedDataUrl { get; set; } = "policies";
        public List<string> PolicyLookups { get; set; } = new List<string>();
        
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
            await InvokeAsync(StateHasChanged);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                PolicyLookups = PolicyLookup.GetPolicyLooksUp();
                SelectedDatatype = PolicyLookups.FirstOrDefault(e => e.ToLower() == SelectedDatatype?.ToLower());
                _ = GetCountriesAndRegionsAsync();
                await JsRuntime.InvokeVoidAsync("resetRecaptcha");
                StateHasChanged();
            }
        }

        private void OnCountryStatus(int value)
        {
            searchRequest.SelectedStatus = value == 0 ? "Member State" : "Territory or Area";
        }
        private async Task GetCountriesAndRegionsAsync()
        {
            FullListCountryItems = await CountryService?.GetCountriesHaveData();
            GetCountryBySelectedDatatype();
            Regions = await CountryService.GetRegionsAsync();

            IsLoading = false;
            ViewType = CountriesViewOption.CountryList;
            _ = MemoryCache.Set(Constants.Cachekeys.Countries, FullListCountryItems, DateTime.Now.AddHours(1));
            _ = MemoryCache.Set(Constants.Cachekeys.Regions, Regions, DateTime.Now.AddHours(1));
            await InvokeAsync(StateHasChanged);
        }

        private async Task GetCountryBySelectedDatatype()
        {
            CountryItems = new List<ViewCountryDetails>();
            if (string.IsNullOrEmpty(SelectedDatatype))
            {
                CountryItems = FullListCountryItems.Select(e => new ViewCountryDetails()
                {
                    CountryCode = e.CountryCode,
                    RegionCode = e.RegionCode,
                    CountryName = e.CountryName,
                    CountryStatus = e.CountryStatus
                }).DistinctBy(e => e.CountryCode).ToList();
            }
            else
            {
                CountryItems = FullListCountryItems.Where(e => e.Type == SelectedDatatype).ToList();
            }
            await InvokeAsync(StateHasChanged);

        }
        public async Task OnSelectedItemsChanged(string value)
        {
            SelectedDatatype = value;
            SelectedDataUrl = GetDataTypeURL(value);
            await GetCountryBySelectedDatatype();
           await InvokeAsync(() => StateHasChanged());
        }
        private async Task ToggleCountryViewOption(CountriesViewOption option)
        {
            ViewType = option;
            await JsRuntime.InvokeVoidAsync("countrieslist", option);
        }

        private string GetDataTypeURL(string type)
        {
            switch (type)
            {
                case "Policies": return "policies";
                case "Mechanisms": return "mechanisms";
                case "Programmes and actions": return "programmes-and-actions";
                case "SMART commitments": return "commitments";

                default: return "policies";
            }
        }
       
    }
}