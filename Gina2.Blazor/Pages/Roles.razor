﻿@page "/admin/roles"
@using Gina2.Blazor.Models.AdminModel
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
@using Gina2.Core.Methods;
<PageTitle>GIFNA @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(RolePageConfigurationKey.Header).ConvertHtmlToPlainText())</PageTitle>
<Loader IsLoading="@IsLoading" />

<Container Fluid Padding="Padding.Is0">
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-5 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1 pt-1 pb-1 pl-2">
                    <Heading Size="HeadingSize.Is3" data-cy="RoleHeading">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(RolePageConfigurationKey.Header))
                        <AdminEditbut Key="@RolePageConfigurationKey.Header" />
                    </Heading>
                    <Paragraph Class="color-w" Size="HeadingSize.Is3" data-cy="RoleHeading">
                        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(RolePageConfigurationKey.SubHeader))
                        <AdminEditbut Key="@RolePageConfigurationKey.SubHeader" />
                    </Paragraph>
                    <Breadcrumb Class="bread-crumb">
                        <BreadcrumbItem>
                            <BreadcrumbLink data-cy="RoleHomeLink" To="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                         <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="RoleLink" To="#">
                                User management
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbItem Active>
                            <BreadcrumbLink data-cy="RoleLink" To="#">
                                Roles
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                    </Breadcrumb>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Class="pt-5 m-pt-2 pl-2 pr-2 adminuser">
    <Div Flex="Flex.JustifyContent.Between">
        <Div Class="item1">
            <Heading Size="HeadingSize.Is2" Class="Headingh3"  data-cy="RoleHeadingTitle">
                @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(RolePageConfigurationKey.PageHeading))
                <AdminEditbut Key="@RolePageConfigurationKey.PageHeading" />
            </Heading>
        </Div>
    </Div>
    <Layout Class="search-box pt-3 pb-3 mob-layout">
        <Layout Class="left-layout DataGrids over-line">
            <LayoutContent>
                <DataGrid 
                          FixedHeaderDataGridMaxHeight="500px"
                          FixedHeaderDataGridHeight="450px"
                          TItem="@RoleViewModel"
                          Data="@RoleList"
                          Responsive
                          SortMode="DataGridSortMode.Single">
                    <EmptyTemplate>
                        <Div data-cy="NoDataDound">No data found for the search criteria.</Div>
                    </EmptyTemplate>
                    <DataGridColumns>
                        <DataGridColumn Field="@nameof(RoleViewModel.Name)" Caption="Name" Sortable="true" />
                    </DataGridColumns>
                    <ItemsPerPageTemplate></ItemsPerPageTemplate>
                    <TotalItemsTemplate>
                        <Badge TextColor="TextColor.Dark">
                            @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                        </Badge>
                    </TotalItemsTemplate>
                </DataGrid>
            </LayoutContent>
        </Layout>
    </Layout>
</Container>