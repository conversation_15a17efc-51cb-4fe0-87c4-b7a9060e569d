﻿@page "/Layout/Drafts/{draftId:int}/Edit"
@using Gina2.Core.Enums
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent

@{
    RenderFragment footer = @<AntDesign.Template>
    </AntDesign.Template>;
}
<PageTitle>Edit - Site Customization</PageTitle>
<Div Class="_live-preview _site_prev">
    <AntDesign.Modal Visible="@_visible"
                     ZIndex="111111"
                     OnCancel="@HandleCancel" Footer="@footer" Width="1200">
        @* <br /><br /> *@
        <ChildContent>
            @* Custom content goes here *@
            <Header style="@Headerprev"/><br />
            <Footer />
        </ChildContent>
    </AntDesign.Modal>
</Div>
<Container Fluid Padding="Padding.Is0">
    <Loader IsLoading="@IsLoading" />
    <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
        <Container Class="ginasearch pt-7 pb-5">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3">Edit - Site Customization</Heading>
                </Div>
            </Div>
        </Container>
    </Card>
</Container>
<Container Fluid Class="newdraft" Padding="Padding.Is0">
    <Container Class="pt-6 mobi-heing">
        <Heading Class="new-heading" Flex="Flex.JustifyContent.Between" Size="HeadingSize.Is3">
            Update the information for Home
            <Div>
                <Button Class='but-yellow-w100 mr-1' Clicked="(e)=>ShowPreview(draftId)">Preview</Button>

                @if (!modifyPublishedDraft && CurrentUserService != null && CurrentUserService.UserRole != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                {
                    <Button Class='but-yello-wite' Clicked="e=> PublishDraft(draftId)">Publish</Button>
                }
            </Div>
        </Heading>
        <Divider Class="divi-blue" />
    </Container>

    <Container Class="pt-1 mobi-heing form-newd">
        <Validations ValidateOnLoad="false">
            <Field>
                <FieldLabel>Logo title</FieldLabel>
                <Fields>
                    <Validation>
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                            @*<RextEditors Changed="@OnTitleChange" Value="@draftToEdit.Title" />*@
                            <_quillEditor value="@draftToEdit.Title" @ref="quillDraftTitleRef"></_quillEditor>
                        </Field>
                    </Validation>
                </Fields>
            </Field>
            <Field>
                <FieldLabel>Header logo<Span>*</Span></FieldLabel>

                <AntDesign.Spin Tip="Uploading..." Size="small" Spinning="@isUploadingHeader">
                    <FileEdit @ref="@fileEditHeader" Changed="@AddHeaderImage" Filter=".jpg, .png" Placeholder="Select or drag and drop multiple files"
                              Progressed="@OnHeaderUploadProgress" />
                </AntDesign.Spin>
                @if (isUploadingHeader)
                {
                    <AntDesign.Progress Percent="@headerUplodedPercentage" StrokeColor=progressBarStrokeColor />
                }

                <FieldHelp>Files must be less than 100 KB     |     Allowed file types: .jpg .png     |     Dimension: 136x42px</FieldHelp>
                <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageErrorHeader))">@ImageErrorHeader</FieldLabel>
                <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageErrorHeader) ? true : false)">@draftToEdit.HeaderImage</FieldLabel>
            </Field>

            <Field>
                <FieldLabel>Footer logo<Span>*</Span></FieldLabel>

                <AntDesign.Spin Tip="Uploading..." Size="small" Spinning="@isUploadingFooter">
                    <FileEdit @ref="@fileEditFooter" Changed="@AddFooterImage" Filter=".jpg, .png" Placeholder="Select or drag and drop multiple files"
                              Progressed="@OnFooterUploadProgress" />
                </AntDesign.Spin>
                @if (isUploadingFooter)
                {
                    <AntDesign.Progress Percent="@footerUplodedPercentage" StrokeColor=progressBarStrokeColor />
                }

                <FieldHelp>Files must be less than 100 KB    |     Allowed file types: .jpg .png     |     Dimension: 373x86px</FieldHelp>
                <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageErrorFooter))">@ImageErrorFooter</FieldLabel>
                <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageErrorFooter) ? true : false)">@draftToEdit.FooterImage</FieldLabel>
            </Field>
            <Field>
                <FieldLabel>Footer title</FieldLabel>
                <Fields>
                    <Validation>
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                            @*<RextEditors Changed="@OnFooterTextChange" Value="@draftToEdit.FooterText" />*@
                            <_quillEditor value="@draftToEdit.FooterText" @ref="quillFooterTextRef"></_quillEditor>
                        </Field>
                    </Validation>
                </Fields>
            </Field>
            <Fields>
                <Field>
                    <FieldLabel>World health organization text</FieldLabel>
                    <Fields>
                        <Validation>
                            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                @*<RextEditors Changed="@OnOrgLinkChange" Value="@draftToEdit.OrganizationLink" />*@
                                <_quillEditor value="@draftToEdit.OrganizationLink" @ref="quillOrganizationLinkRef"></_quillEditor>
                            </Field>
                        </Validation>
                    </Fields>

                </Field>

            </Fields>
            <Fields>
                <Field>
                    <FieldLabel>Contact text</FieldLabel>
                    <Fields>
                        <Validation>
                            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                                @*<RextEditors Changed="@OnContactChange" Value="@draftToEdit.ContactLink" />*@
                                <_quillEditor value="@draftToEdit.ContactLink" @ref="quillContactLinkRef"></_quillEditor>
                            </Field>
                        </Validation>
                    </Fields>
                </Field>
            </Fields>
        </Validations>
        <AntDesign.Button Disabled="@(isUploadingHeader || isUploadingFooter || !canSaveSlider)"
                          OnClick="SaveAndUpdateLayout"
                          Class="but-yellow pl-2 pr-2"> Save </AntDesign.Button>
    </Container>
</Container>