﻿using Gina2.Blazor.Helpers.Authenticate;
using Gina2.Core.Interface;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.MySqlRepository.Repositories;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Pages
{
    public partial class MechanismDetail
    {
        [Inject]
        private IAuthenticateHelper AuthenticateHelper { get; set; }

        [Inject]
        private ICurrentUserService currentUserService { get; set; }

        [Inject]
        private IMechanismRepository mechanismRepository { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        [Parameter]
        public int MechanismCode { get; set; }

        [Parameter]
        public int VersionId { get; set; }
        private bool isAuthenticated;
        private string role;
        private string LoginUser;
        private string PublishedUser;
        private bool IsPublished = true;
        private List<MechanismLog> logs = new List<MechanismLog>();

        protected override async Task OnInitializedAsync()
        {
            isAuthenticated = true;
            (role, LoginUser) = await AuthenticateHelper.IsAuthenticated();

            var mechanismLogs = await AuthenticateHelper
                .IsPublished<MechanismLog>(p => p.MechanismId == MechanismCode);
            if (mechanismLogs is null || mechanismLogs.Count == 0)
            {
                isAuthenticated = false;
                return;
            }

            if (CanAccessForPublishedLog(mechanismLogs))
                return;

            if (await CanAccessForLoginUserOrRole(mechanismLogs))
                return;                      

            isAuthenticated = false;
        }

        private bool CanAccessForPublishedLog(List<MechanismLog> mechanismLogs)
        {
            MechanismLog publishedLog = VersionId > 0
                        ? mechanismLogs.FirstOrDefault(v => v.MechanismVId == VersionId)
                        : mechanismLogs.FirstOrDefault(v => v.IsPublished);

            IsPublished = publishedLog?.IsPublished ?? false;
            PublishedUser = publishedLog?.UserName;

            if (IsPublished) 
                return true;

            return false;
        }

        private async Task<bool> CanAccessForLoginUserOrRole(List<MechanismLog> mechanismLogs)
        {
            var revisionLog = mechanismLogs.FirstOrDefault(v => v.MechanismVId == VersionId);

            if (revisionLog?.UserName == LoginUser || revisionLog?.DelegatedUserName == LoginUser)
            {
                return true;
            }

            if (string.Equals(role, "admin", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            if (role.Equals("approver", StringComparison.OrdinalIgnoreCase) && revisionLog?.UserName != LoginUser)
            {
                var mechanismCountries = await mechanismRepository
                    .GetMechanismCountriesByPolicyIdAndRevisionId(MechanismCode, VersionId);
                var approverCountries = currentUserService.ApproverCountryCode;

                if (approverCountries.Any(country => mechanismCountries.Contains(country)))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
