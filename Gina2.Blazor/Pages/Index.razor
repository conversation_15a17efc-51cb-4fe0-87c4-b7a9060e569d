﻿@page "/"
@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent
@inject IConfiguration configuration
<PageTitle>GIFNA Home</PageTitle>
<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit">
            <DropdownToggle Class="aboutmenu" Split />
            <DropdownMenu>
                <DropdownItem Clicked="@(()=>{ NavigationManager.NavigateTo($"/admin/contents/Home");  })">
                    Edit
                </DropdownItem>
                @*<DropdownItem href="home-moderate">Moderate</DropdownItem>
                <DropdownItem href="home-translate">Translate</DropdownItem>*@
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>

<Div id="live-modal" Style="display:none;" Class="_homeedit">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle>Are you sure want to navigate away from this page?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="but-blues pl-2 pr-2" Clicked="@NavigateToSliderLinkOutside">Yes</Button>
            <Button Class="but-yellow pl-2 pr-2" onclick="liveedit()">No</Button>
        </ModalFooter>
    </ModalContent>
</Div>
<Div id="slider-div">
    <Repeater Items="@SlidersList">
        @{
            string FirstTitle = $"<p1 style='color:{context.FirstColor};'>{context.FirstTitle}</p1>";
            string SliderTitle = $"<p1 style='color:{context.SliderColor};'>{context.SliderTitle}</p1>";
            string LastTitle = $"<p1 style='color:{context.LastColor};'>{context.LastTitle}</p1>";
        }
        <Div Class="Sliders-banner" id="@context.ElementId">
            <Div Class="banner d-flex">
                <CardBody Class="container">
                    <Row>
                        <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is7.OnDesktop.Is7.OnWidescreen.Is7.OnFullHD">
                            <img id="site_logo" class="img-responsive" width="550" src="img/Header_logo.png" />
                            <CardTitle Class="pt-3" Size="5">@((MarkupString)FirstTitle)</CardTitle>
                                <CardTitle Size="2">@((MarkupString)SliderTitle)</CardTitle>
                                <CardText>
                                    @((MarkupString)LastTitle)
                            </CardText>

                            @if ((!string.IsNullOrEmpty(context.RedirectionUrl)))
                            {
                                <Blazorise.Link onclick="@NavigateToSliderLink">
                                    <Button Class="btn more-but">Discover  <Icon Class="fas fa-arrow-right" /></Button>
                                </Blazorise.Link>
                            }
                            @* else
                            {
                            <Button Class="btn more-but" onclick="liveedit()" data-cy="HomeDiscoverButton">Discover  <Icon Class="fas fa-arrow-right" /></Button>
                            }*@

                            @*@if (@context.RedirectionUrl.StartsWith(NavigationManager.BaseUri))
                            {
                            <Blazorise.Link onclick="@NavigateToSliderLink">
                            <Button Class="btn more-but" onclick="liveedit()">Discover  <Icon Class="fas fa-arrow-right" /></Button>
                            </Blazorise.Link>
                            }
                            else
                            {
                            <Blazorise.Link onclick="@ConfirmNavigeteModal">
                            <Button Class="btn more-but">Discover  <Icon Class="fas fa-arrow-right" /></Button>
                            </Blazorise.Link>
                            }*@
                        </Column>
                    </Row>
                </CardBody>
            </Div>
        </Div>
    </Repeater>
    @if (!string.IsNullOrEmpty(latestAddition))
    {
        <LastestsAddition Value="@latestAddition" />
    }
</Div>

<Div Class="Sliders-Spinner" Style="justify-content:center;display:flex;" Visibility="showSliderSpinner?Visibility.Visible:Visibility.Invisible" id="slider-spinner-div">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</Div>
<Div id="default-slider-div" style="display: none">
    <Div Class="Sliders-banner" Style="background-image:url(img/Slider3.png);">
        <Div Class="banner d-flex">
            <CardBody Class="container">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is7.OnDesktop.Is7.OnWidescreen.Is7.OnFullHD">
                        <CardTitle Size="5" data-cy="AboutCardtitle1">About GINA</CardTitle>
                        <CardTitle Size="2" data-cy="AboutCardtitle2">The Global Database on <br />the Implementation </CardTitle>
                        <CardText data-cy="AboutCardText">
                            of Nutrition Action
                        </CardText>
                        <Button Class="more-but" data-cy="AboutCardText" Clicked="@(() => NavigationManager.NavigateTo("/"))">Discover more <Icon Class="fas fa-arrow-right" /></Button>
                    </Column>
                </Row>
            </CardBody>
        </Div>

    </Div>
    @if (!string.IsNullOrEmpty(latestAddition))
    {
        <LastestsAddition Value="@latestAddition" />
    }
 
</Div>

<Container Fluid>
    <Container Class="pt-5 pb-6">
        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(HomeEditPageConfigurationKey.FirstSectionTitle))
        <Divider />
        <Row Class="mobile-row">
            <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="cardbox">

                @if (FeaturesList.Count == 0)
                {
                    <Div>No data found.</Div>
                }
                else
                {
                    <Repeater Items="@FeaturesList">
                        <CardBody Class="gins-card" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                            @{
                                string Title = string.IsNullOrEmpty(context.Title) ? string.Empty : context.Title;
                                //Title = !string.IsNullOrEmpty(Title) && Title.Length > 50 ? $"{Title.Substring(0, 50)}..." : Title;
                                string Description = string.IsNullOrEmpty(context.Description) ? string.Empty : context.Description;
                                Description = !string.IsNullOrEmpty(Description) && Description.Length > 220 ? $"{Description.Substring(0, 220)}..." : Description;
                                string url = !string.IsNullOrEmpty(context.RedirectionUrl) ? context.RedirectionUrl : context.DetailLink;
                            }
                            <Div Class="story-test">
                                <Heading>@((MarkupString)@Title)</Heading>
                                </Div>
                                <Div Class="overlay-box">
                                    <Div Class="over-flex">

                                        <Div>
                                            <CardTitle Size="5" data-cy=@($"{Title.Split(" ")[0]}FeaturesTitle")>@((MarkupString)@Title)</CardTitle>
                                            <Paragraph data-cy=@($"{Title.Split(" ")[0]}FeaturesDesc")>@((MarkupString)Description)</Paragraph>
                                        </Div>

                                        <NavLink href="@url" target="_blank"><Button data-cy=@($"{Title.Split(" ")[0]}ScoreCard") Class="read-more">Read more <Icon Class="fas fa-arrow-right" /></Button></NavLink>

                                    </Div>
                                </Div>
                                <Div Class="banner-images _featuresimg">
                                    <img class="banner-images" id="@context.ElementId" src="img/no-image.png" style="display: none;" />
                                    <AntDesign.Skeleton id="@context.ImageDivId" Active="true" ParagraphRows="4"></AntDesign.Skeleton>
                                </Div>
                            </CardBody>
                        </Repeater>
                }
                @if (isFeaturesLoading)
                {
                    for (int i = 0; i < 3; i++)
                    {
                        <CardBody Class="gins-card" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                            <AntDesign.Skeleton Avatar="true" Active="true" ParagraphRows="4"></AntDesign.Skeleton>
                        </CardBody>
                    }
                }
            </Column>
        </Row>
    </Container>
</Container>
<Container Fluid Class="gina-50 gina-home-bg" id="Nutritional">
    <Container>
        @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(HomeEditPageConfigurationKey.SecondSectionTitle))
        <Row Class="mobile-row pb-3 mo-p-0">
            <Repeater Items="@NutrientList">
                <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD" Class="mb-3">
                    <Div Class="resources">
                        <Div>
                            <img class="banner-images" id="@context.ElementId" src="img/no-image.png" style="display: none;" />
                            <AntDesign.Skeleton id="@context.ImageDivId" Active="true" ParagraphRows="3"></AntDesign.Skeleton>
                        </Div>
                        <Div>
                            @{
                                string title = string.IsNullOrEmpty(context.SliderTitle) ? string.Empty : context.SliderTitle;
                                title = !string.IsNullOrEmpty(title) && title.Length > 100 ? $"{title.Substring(0, 100)}..." : title;
                            }
                            <Paragraph data-cy=@($"{title.Split(" ")[0]}NutritionalPara")>
                                @((MarkupString)@title)
                            </Paragraph>
                            @*<Paragraph>
                            @((MarkupString)context.Description)
                            </Paragraph>*@
                            <NavLink href="@context.RedirectionUrl" target="_blank"><Button data-cy=@($"{title.Split(" ")[0]}ReadMore") Class="read-more">Read more</Button></NavLink>
                        </Div>
                    </Div>
                </Column>
            </Repeater>

            @if (isLoadingNutrients)
            {
                for (int i = 0; i < 9; i++)
                {
                    <Column ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD" Class="mb-3">
                        <Div Class="resources">
                            <AntDesign.Skeleton AvatarShape="square" AvatarSize="@AntDesign.SkeletonElementSize.Large"
                                                Avatar="true" Active="true" ParagraphRows="2"></AntDesign.Skeleton>
                        </Div>
                    </Column>
                }
            }
        </Row>
    </Container>
</Container>