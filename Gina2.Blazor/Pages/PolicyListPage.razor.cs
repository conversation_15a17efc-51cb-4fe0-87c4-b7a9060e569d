﻿using Gina2.Blazor.Models;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Data;

namespace Gina2.Blazor.Pages
{
    public partial class PolicyListPage
    {
        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Parameter]
        public string CountryCode { get; set; }

        public readonly string Title = "Policies";
        public readonly string TabTitle = "policies";
        public readonly string AdminTitle = "Policies";
        public readonly string TypeofTitle = "Type of policy";
        public readonly string TableTitle = "Policy";
        public readonly string DetailUrlContext = "policies";
        public string CsvKeys { get; set; }
        public string PartnerKeys { get; set; }
        public List<string> CsvData { get; set; } = new();
        public string FileName = "gina2_policies";
        private List<PublishedListItem> policies = new();
        private List<TypeList> policyTypes = new();
        private bool IsLoading = false;


        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        protected override async Task OnInitializedAsync()
        {
            IsLoading = true;
            var PolicyTypes = await PolicyService.GetAllPolicyTypes();

            foreach (var item in PolicyTypes)
            {
                policyTypes.Add(new TypeList()
                {
                    Id = item.Key,
                    Name = item.Name
                });
            }
            var Policynames = await PolicyService.GetPoliciesAsync(CountryCode);

            foreach (var item in Policynames)
            {
                if (item.PolicyType != null)
                {
                    policies.Add(new PublishedListItem()
                    {
                        Key = item.Id,
                        Title = item.Title,
                        EnglishTitle = item.EnglishTitle,
                        StartYear = item.StartYear?.ToString() ?? "-",
                        EndYear = item.EndYear?.ToString() ?? "-",
                        Category = item.PolicyTypeId?.ToString(),
                        CategoryName = item.PolicyType?.Name
                    });
                }
            }
            policies = policies.OrderByDescending(a => a.StartYear).ToList();
            IsLoading = false;
            StateHasChanged();

            //await PopulatePolicyTopicDatatable();
            StateHasChanged();
        }

        public async Task PopulatePoliciesForScoreCard()
        {
            List<List<string>> ListOfScoreCardPolicy = new List<List<string>>();
            var polices = await PolicyService.PopulatePoliciesForScoreCard();
            foreach (var item in polices)
            {
                List<string> policy = new List<string>();
                policy.Add(item.Id.ToString());
                policy.Add("Iso3Code");
                policy.Add("CountryName");
                policy.Add(item.Title);
                policy.Add(item.StartYear.ToString());
                policy.Add(item.StartMonth.ToString());
                ListOfScoreCardPolicy.Add(policy);

            }
            await JsRuntime.InvokeAsync<List<object>>("PoliciesForScorecard", ListOfScoreCardPolicy);
        }


        public async Task PopulatePolicyTopicDatatable()
        {
            List<int> ScorecardTopics = new List<int> { 2991, 3086 };

            var topics = await PolicyService.GetTopicsAsync();

            List<string> ScorecardTopicsNames = topics.Where(w => ScorecardTopics.Contains(w.Id))
                                                .Select(w => w.Name).ToList();

            var policies = await PolicyService.PopulatePoliciesForScoreCard();
            var policyTopics = await PolicyService.GetAllPolicyTopic();
            var dt = new DataTable();

            dt.Columns.Add("policyid", typeof(int));
            dt.Columns.Add("Iso3CountryCode", typeof(string));
            dt.Columns.Add("CountryName", typeof(string));
            dt.Columns.Add("PolicyTitle", typeof(string));
            dt.Columns.Add("PolicyType", typeof(string));
            dt.Columns.Add("StartYear", typeof(string));
            dt.Columns.Add("StartMonth", typeof(string));

            foreach (var item in ScorecardTopicsNames)
            {
                dt.Columns.Add(item, typeof(int));
            }

            foreach (var policyTopic in policyTopics)
            {
                DataRow dataRow = dt.NewRow();
                dataRow["policyid"] = policyTopic.PolicyId;
                dataRow["Iso3CountryCode"] = policyTopic.CountryISOCode;
                dataRow["CountryName"] = policyTopic.CountryName;
                dataRow["PolicyTitle"] = policyTopic.PolicyTitle;
                dataRow["PolicyType"] = policyTopic.PolicyTypeName;
                dataRow["StartYear"] = policyTopic.StartYear.HasValue ? policyTopic.StartYear.ToString() : "";
                dataRow["StartMonth"] = policyTopic.StartMonth.HasValue ? policyTopic.StartMonth.ToString() : "";

                for (int i = 0; i < ScorecardTopicsNames.Count(); i++)
                {
                    dataRow[ScorecardTopicsNames[i]] = policyTopic.TopicList.Contains(ScorecardTopics[i]) ? 1 : 0;
                }
                dt.Rows.Add(dataRow);
            }
            var data = JsonConvert.SerializeObject(dt);
            await JsRuntime.InvokeAsync<string>("PoliciesForScorecard", ScorecardTopicsNames, data);
        }
    }
}