﻿@page "/admin/contents/home/<USER>/{homeDraftId:int}/edit"
@using Gina2.Blazor.Helpers.PageConfigrationData
@using Gina2.Core.Enums
@using Gina2.DbModels.HomeDrafts
@using Plk.Blazor.DragDrop;
@using Gina2.DbModels;
@inherits PageConfirgurationComponent

@{
    RenderFragment footer = @<AntDesign.Template>
    </AntDesign.Template>;
}
<PageTitle>GIFNA Edit - Home</PageTitle>
<Div Class="_live-preview">
    <AntDesign.Modal Title="@title"
                     Visible="@_visible"
                     ZIndex="111111"
                     OnCancel="@HandleCancel" Footer="@footer" Width="1200">
        <Index previewDraftId="@previewDraftId" rerenderTokenId="@rerenderTokenId" />
    </AntDesign.Modal>
</Div>

<Modal @bind-Visible="@DeleteVisible" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
             <ModalTitle>Are you sure want to delete @((MarkupString)DeleteSlider.SliderTitle)?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@DeleteHomeSlider">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@DeleteFeatureVisible" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
             <ModalTitle>Are you sure want to delete @((MarkupString)DeleteFeatureSlider.Title)?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@DeleteFeature">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteFeatureVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@DeleteNutritionVisible" Class="modals-lg antdraggable _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
             <ModalTitle>Are you sure want to delete @((MarkupString)DeleteNutritionSlider.SliderTitle)?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@DeleteNutrition">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteNutritionVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@SliderModal" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle>Slider details</ModalTitle>
            <CloseButton Clicked="@(() => HideModal(SliderModal))" />
        </ModalHeader>
         <ModalBody Class="_modalscroll">
            <Validations @ref="SliderValidations" ValidateOnLoad="false">
                <Field>
                    <FieldLabel>First title <Span>*</Span></FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
@*                            <RextEditors Value="@SliderData.FirstTitle" Changed="@ChangeSliderFirstTitle">
                            </RextEditors>*@
                            <_quillEditor value="@SliderData.FirstTitle" @ref="quillSliderFirstTitleRef"></_quillEditor>
                        </Field>
                        @if (ShowSliderFirstTitle)
                        {
                            <AntDesign.Text Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                                @EmojiErrorText
                            </AntDesign.Text>
                        }
                    </Fields>
                </Field>

@*                <Field>
                    <FieldLabel>Slider Title </FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is10.OnTablet.Is10.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                            <RextEditors Value="@SliderData.SliderTitle" Changed="@ChangeSliderTitle">
                            </RextEditors>
                        </Field>
                        @if (ShowSliderSliderTitle)
                        {
                            <AntDesign.Text Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                                @EmojiErrorText
                            </AntDesign.Text>
                        }
                    </Fields>
                </Field>
                <Field>
                    <FieldLabel>Last Title</FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is10.OnTablet.Is10.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                            <RextEditors Value="@SliderData.LastTitle" Changed="@ChangeSliderLastTitle">
                            </RextEditors>
                        </Field>
                        @if (ShowSliderLastTitle)
                        {
                            <AntDesign.Text Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                                @EmojiErrorText
                            </AntDesign.Text>
                        }
                    </Fields>
                </Field>*@
                <Field>
                    <FieldLabel>Background image <Span>*</Span></FieldLabel>

                    <AntDesign.Spin Tip="Uploading..." Size="small" Spinning="@isUploadingSlider">
                        <FileEdit @ref="@fileEdit" Changed="@AddSliderImageAsync" Filter=".jpg, .png" Placeholder="Select or drag and drop multiple files"
                                  Progressed="@OnSliderProgressed" />
                    </AntDesign.Spin>
                    @if (isUploadingSlider)
                    {
                        <Progress Percent="@sliderUplodedPercentage" />
                    }

                    <FieldHelp>Files must be less than 2 MB     |     Allowed file types: .jpg .png     |     Dimension: 1920x728px</FieldHelp>
                    <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel>
                    <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageError) ? true : false)">@SliderData.BackgroundImage</FieldLabel>
                </Field>
                <Field>
                    <FieldLabel>Redirection URL</FieldLabel>
                    <TextEdit TextChanged="@OnSliderUrlChanged" Placeholder="Please provide the redirection link" Text="@SliderData.RedirectionUrl" />
                    <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(UrlError))">@UrlError</FieldLabel>
                </Field>
            </Validations>
        </ModalBody>
        <ModalFooter>
            <AntDesign.Button 
                              Loading="@isSliderSaving"
                              OnClick="SaveAndUpdateSliderDetail"
                              Class="but-yellow pl-2 pr-2">
                Save
            </AntDesign.Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@FeatureModal" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle>Feature themes details</ModalTitle>
            <CloseButton Clicked="@(() => HideModal(FeatureModal))" />
        </ModalHeader>
         <ModalBody Class="_modalscroll">
            <Validations @ref="@FeatureValidations" ValidateOnLoad="false">
                <Validation Validator="@ValidateFeatureTitle">
                    <Field>
                        <FieldLabel>Slider title <Span>*</Span></FieldLabel>
@*                        <RextEditors Value="@ScoreCardData.Title" Changed="@((e) => ChangeFeatureTitle(e))">
                        </RextEditors>*@
                        <_quillEditor value="@ScoreCardData.Title" @ref="quillScoreCardDataTitleRef"></_quillEditor>
                        @if (ShowFeatureTitleError)
                        {
                            <AntDesign.Paragraph Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                                @featureTitleError
                            </AntDesign.Paragraph>
                        }
                    </Field>
                </Validation>
                <Validation Validator="@ValidateFeatureDescription">
                    <Field>
                        <FieldLabel>Description</FieldLabel>
@*                        <RextEditors Value="@ScoreCardData.Description" Changed="@((e) => ChangeFeatureDesciption(e))">
                        </RextEditors>*@
                        <_quillEditor value="@ScoreCardData.Description" @ref="quillEditorScoreCardDataDescriptionRef"></_quillEditor>

                        @if (ShowFeatureDescriptionError)
                        {
                            <AntDesign.Paragraph Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                                @featureDescriptionError
                            </AntDesign.Paragraph>
                        }
                    </Field>
                </Validation>

                <Field>
                    <FieldLabel>Background image<Span>*</Span></FieldLabel>

                    <AntDesign.Spin Tip="Uploading..." Size="small" Spinning="@isUploadingFeature">
                        <FileEdit @ref="@fileEdit" Changed="@AddFeatureImageAsync" Filter=".jpg, .png" Placeholder="Select or drag and drop multiple files"
                                  Progressed="@OnScorecardUplaodProgressed" />
                    </AntDesign.Spin>
                    @if (isUploadingFeature)
                    {
                        <Progress Percent="@scorecardUplodedPercentage" />
                    }

                    <FieldHelp>Files must be less than 2 MB     |     Allowed file types: .jpg .png     |     Dimension: 450x410px</FieldHelp>
                    <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel>
                    <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageError) ? true : false)">@ScoreCardData.BackgroundImage</FieldLabel>
                </Field>
                <Field>
                    <FieldLabel>Redirection URL</FieldLabel>
                    <TextEdit TextChanged="@OnFeatureUrlChanged" Placeholder="Please provide the redirection link" Text="@ScoreCardData.RedirectionUrl" />
                    <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(UrlError))">@UrlError</FieldLabel>
                </Field>
            </Validations>
        </ModalBody>
        <ModalFooter>
            <AntDesign.Button Disabled="@(isUploadingFeature || !canSaveFeature)"
                              Loading="@isFeatureSaving"
                              OnClick="SaveAndUpdatFeatureDetail"
                              Class="but-yellow pl-2 pr-2">
                Save
            </AntDesign.Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@NutritionVisible" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle>Nutrition themes details</ModalTitle>
            <CloseButton Clicked="@(() => HideModal(NutritionVisible))" />
        </ModalHeader>
         <ModalBody Class="_modalscroll">
            <Field>
                <FieldLabel>Slider title <Span>*</Span></FieldLabel>
@*                <RextEditors Value="@NutritionData.SliderTitle" Changed="@((e) => ChangeNutritionTitle(e))">
                </RextEditors>*@
                <_quillEditor value="@NutritionData.SliderTitle" @ref="quillEditorNutritionDataRef"></_quillEditor>
                @if (ShowNutritionTitleErrorError)
                {
                    <AntDesign.Paragraph Style="color: #ff4d4f;clear:both;font-size:14px;padding-top:0;">
                        @nutritionTitleError
                    </AntDesign.Paragraph>
                }
            </Field>
            <Field>
                <FieldLabel>Background image <Span>*</Span></FieldLabel>
                <AntDesign.Spin Tip="Uploading..." Size="small" Spinning="@isUploadingNutrient">
                    <FileEdit @ref="@fileEdit" Changed="@AddNutrientImageAsync" Filter=".jpg, .png" Placeholder="Select or drag and drop multiple files"
                              Progressed="@OnNutrientUploadProgressed" />
                </AntDesign.Spin>
                @if (isUploadingNutrient)
                {
                    <Progress Percent="@nutritionUplodedPercentage" />
                }

                <FieldHelp>Files must be less than 2 MB     |     Allowed file types: .jpg .png     |     Dimension: 329x256px</FieldHelp>
                <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel>
                <FieldLabel hidden="@(!string.IsNullOrEmpty(ImageError) ? true : false)">@NutritionData.BackgroundImage</FieldLabel>
            </Field>
            <Field>
                <FieldLabel>Redirection URL</FieldLabel>
                <TextEdit TextChanged="@OnNutritionUrlChanged" Placeholder="Please provide the redirection link" Text="@NutritionData.RedirectionUrl" />
                <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(UrlError))">@UrlError</FieldLabel>
            </Field>
        </ModalBody>
        <ModalFooter>
            <AntDesign.Button Disabled="@(isUploadingNutrient || !canSaveNutrition)"
                              Loading="@isNutrientSaving"
                              OnClick="SaveAndUpdatNutritionDetail"
                              Class="but-yellow pl-2 pr-2">
                Save
            </AntDesign.Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<AuthorizeView Context="authorizedContext">

    <Authorized>
        <Container Fluid Padding="Padding.Is0">
            <Loader IsLoading="@IsLoading" />
            <Card Class="allbanner" Style="background-image: url(../img/abouts.png);">
                <Container Class="ginasearch pt-7 pb-5">
                    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
                        <Div Class="item1">
                            <Heading Size="HeadingSize.Is3">Edit - Home</Heading>
                            <Breadcrumb Class="bread-crumb">
                                <BreadcrumbItem>
                                    <BreadcrumbLink To="/">Home</BreadcrumbLink>
                                </BreadcrumbItem>
                                @* <BreadcrumbItem Active>
                                <BreadcrumbLink Clicked="@(()=>{ navigationManager.NavigateTo($"/admin/contents/Home");  })">
                                </BreadcrumbLink>
                                </BreadcrumbItem>*@
                            </Breadcrumb>
                        </Div>
                    </Div>
                </Container>
            </Card>
        </Container>

        <Container Fluid Class="newdraft" Padding="Padding.Is0">
            <Container Class="pt-6 mobi-heing">
                <Heading Class="new-heading" Flex="Flex.JustifyContent.Between" Size="HeadingSize.Is3">
                    Update the information for home
                    <Div>
                        <Button Class='but-yellow-w100 mr-1' Clicked="(e)=>ShowPreview(homeDraftId)">Preview</Button>

                        @if (CurrentUserService != null && CurrentUserService.UserRole != null && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
                        {
                            <Button Class='but-yello-wite' Clicked="e=> PublishDraft(homeDraftId)">Publish</Button>
                        }
                    </Div>
                </Heading>
                <Divider Class="divi-blue" />
            </Container>

            <Container Class="box-edit">
                <Div Flex="Flex.JustifyContent.Between.AlignItems.Center">
                    <Heading Class="blo-head" Size="HeadingSize.Is3">Slider</Heading>
                    <Div>
                        <Button hidden="@(HomeSliderList.Count > 0 ? true : false)" Class="but-yellow pl-2 pr-2"><Icon Class="far fa-plus" Clicked="@ShowModal" /> Add slider</Button>
                    </Div>
                </Div>

                @if (isLoadingSlider)
                {
                    <AntDesign.Skeleton Active="true"></AntDesign.Skeleton>
                }

                <Dropzone Items="@HomeSliderList" OnItemDrop="((HomeSliderDraft item) => OnDropItem(item))">
                    <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                        <Div Class="drag-1" Flex="Flex.AlignItems.Baseline">
                            <Icon Class="fa-solid fa-grip-vertical" />
                            <Icon Clicked="@(() => ShowEdit(context))" Class="fa-solid fa-pen" />
                            <p>@((MarkupString)context.FirstTitle)</p>
                        </Div>
                    </Div>
                </Dropzone>

            </Container>

            <Container Class="box-edit mt-5">
                <Div Flex="Flex.JustifyContent.Between.AlignItems.Center">
                    <Div Flex="Flex.JustifyContent.Start.AlignItems.Center" Class="_pad-0 p-inline">
                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(HomeEditPageConfigurationKey.FirstSectionTitle))
                    <AdminEditbut Key="@HomeEditPageConfigurationKey.FirstSectionTitle" />
                    </Div>
                    <Div>
                        @if (!modifyPublishedDraft || (modifyPublishedDraft && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString())))
                        {
                            <Button Clicked="@ThemesModal" Class="but-yellow pl-2 pr-2"><Icon Class="far fa-plus" /> Add feature</Button>
                        }
                    </Div>
                </Div>

                @if (isLoadingFeatures)
                {
                    <AntDesign.Skeleton Active="true"></AntDesign.Skeleton>
                }

                <Dropzone Items="HomeScoreCardList" OnItemDrop="((ScorecardDraft item) => OnDropFeature(item))">
                    <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                        <Div Class="drag-1" Flex="Flex.AlignItems.Baseline">
                            <Icon Class="fa-solid fa-grip-vertical" />
                            <Icon Clicked="@(() => ShowFeatureEdit(context))" Class="fa-solid fa-pen" />
                            <p>@((MarkupString)  @context.Title)</p>
                        </Div>
                        <Div Class="drag-2">
                            <Icon Class="fa-solid fa-trash" Clicked="@(() => ShowFeatureDelete(context))" />
                        </Div>

                    </Div>
                </Dropzone>

            </Container>

            <Container Class="box-edit mt-5">
                <Div Flex="Flex.JustifyContent.Between.AlignItems.Center">
                    <Div Class="p-inline">
                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(HomeEditPageConfigurationKey.SecondSectionTitle))
                    <AdminEditbut Key="@HomeEditPageConfigurationKey.SecondSectionTitle" />
                    </Div>
                    <Div>
                        @if (!modifyPublishedDraft || (modifyPublishedDraft && CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString())))
                        {
                            <Button Clicked="@ShowNutrition" Class="but-yellow pl-2 pr-2"><Icon Class="far fa-plus" /> Add nutritional</Button>
                        }
                    </Div>
                </Div>

                @if (isLoadingNutrients)
                {
                    <AntDesign.Skeleton Active="true"></AntDesign.Skeleton>
                }
                <Dropzone Items="HomeNutritionList" OnItemDrop="((HomeNutritionDraft item) => OnDropNutrition(item))">
                    <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                        <Div Class="drag-1" Flex="Flex.AlignItems.Baseline">
                            <Icon Class="fa-solid fa-grip-vertical" />
                            <Icon Clicked="@(() => ShowNutritionEdit(context))" Class="fa-solid fa-pen" />
                            <p>@((MarkupString)@context.SliderTitle)</p>
                        </Div>
                        <Div Class="drag-2">
                            <Icon Class="fa-solid fa-trash" Clicked="@(() => ShowNutritionDelete(context))" />
                        </Div>

                    </Div>
                </Dropzone>
            </Container>
            @if (CurrentUserService.UserRole.Equals(UserRoleEnum.Admin.ToString()))
            {
                <Container Class="box-edit mt-5">
                    <Div Flex="Flex.JustifyContent.Between.AlignItems.Center">
                        <Heading Class="blo-head" Size="HeadingSize.Is3">Latest addition</Heading>
                        <Div>
                            <Button Clicked="@SaveLatestAddition" Class="but-yellow pl-2 pr-2">Save</Button>
                            @*<Button Clicked="@ClearLatestAddition" Class="but-yellow pl-2 pr-2">Clear</Button>*@
                        </Div>
                    </Div>

                    @*<RextEditors Value="@latestAddition" Changed="@ChangeLatestAddition"></RextEditors>*@
                    <_quillEditor value="@latestAddition" @ref="quillLatestAdditionRef"></_quillEditor>
                </Container>
            }

        </Container>
    </Authorized>
    <NotAuthorized>
        <UnAuthorizedView />
    </NotAuthorized>
</AuthorizeView>