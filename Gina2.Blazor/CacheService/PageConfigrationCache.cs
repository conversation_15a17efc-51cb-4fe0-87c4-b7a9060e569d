﻿using Gina2.DbModels;
using Gina2.Services.PageConfigurations;
using Microsoft.Extensions.Caching.Distributed;
using Gina2.Blazor.Helpers;
using Gina2.Services.ScoreCarad;

namespace Gina2.Blazor.CacheService
{
    public class PageConfigrationCache 
    {
        private List<PageConfiguration> Data { get; set; }
        private string CacheCollection = "PageConfigrationCollection";
        private readonly IDistributedCache _cache;
        private readonly IServiceProvider _serviceProvider;
        public event System.Action RefreshRequested;
        private readonly ILogger<PageConfigrationCache> _logger;
        public PageConfigrationCache(IDistributedCache cache, IServiceProvider serviceProvider, ILogger<PageConfigrationCache> logger, PageConfigruationData pageConfigruationData)
        {
            _cache = cache;
            _serviceProvider = serviceProvider;
            _logger = logger;
            #if (DEBUG)
                Data = pageConfigruationData.GetData();
            #endif
        }
        private async Task Validate()
        {
            try
            {
                if (Data is null)
                {
                    Data = await _cache.GetRecordAsync<List<PageConfiguration>>(CacheCollection);
                    if (Data is null || !Data.Any())
                    {
                        await RefreshData();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error :: {ex}", ex);
                throw;
            }
        }
        //database logic there
        public async Task RefreshData()
        {
            await _cache.RemoveAsync(CacheCollection);
            await GetDatafromDatabase();
            await _cache.SetRecordAsync(CacheCollection, Data);
            RefreshRequested?.Invoke();
        }

        private async Task GetDatafromDatabase()
        {
            var configurationRepository = _serviceProvider.GetRequiredService<IPageConfigurationService>();
            Data = await configurationRepository.GetAllPageConfigurations();
        }
        public async Task<List<PageConfiguration>> GetAllRecordAsync()
        {
            await Validate();
            return Data;
        }
        public async Task<List<PageConfiguration>> GetAllRecordAsync(string page)
        {
            await Validate();
            return Data.Where(e => e.Page == page).ToList();
        }
        public async Task<List<PageConfiguration>> GetAllRecordAsync(string page, string key)
        {
            await Validate();
            return Data.Where(e => e.Page == page && e.Name.Contains(key)).ToList();
        }
        public async Task<PageConfiguration> GetRecordAsync(string page, string Name)
        {
            await Validate();
            return Data.FirstOrDefault(e => e.Page == page && e.Name == Name);
        }
        public async Task<string> GetRecordValueAsync(string page, string Name)
        {
            try
            {
                await Validate();
                return Data.FirstOrDefault(e => e.Page == page && e.Name == Name)?.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error :: {ex}", ex);
                throw;
            }
        }
        public async Task<PageConfiguration> GetRecordAsync(int pageId)
        {
            await Validate();
            return Data.FirstOrDefault(e => e.Id == pageId);
        }
    }
}