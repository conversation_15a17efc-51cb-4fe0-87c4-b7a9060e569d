
using Gina2.DbModels;

public interface IPageConfigurationCache
{
    event System.Action RefreshRequested;
    
    Task<List<PageConfiguration>> GetAllRecordAsync();
    Task<List<PageConfiguration>> GetAllRecordAsync(string page);
    Task<List<PageConfiguration>> GetAllRecordAsync(string page, string key);
    Task<PageConfiguration> GetRecordAsync(string page, string Name);
    Task<string> GetRecordValueAsync(string page, string Name);
    Task<PageConfiguration> GetRecordAsync(int pageId);
    Task RefreshData();
}