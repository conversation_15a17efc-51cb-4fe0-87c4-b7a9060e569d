﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class ProgramProfile: Profile
    {
        public ProgramProfile()
        {
            CreateMap<DbModels.Program, Domain.Programme.ProgramViewModel>()
             .ForMember(dest =>
                 dest.Id,
                 opt => opt.MapFrom(src => src.Id))
             .ForMember(dest =>
                 dest.Title,
                 opt => opt.MapFrom(src => src.Title))
             .ForMember(dest =>
                 dest.LanguageId,
                 opt => opt.MapFrom(src => src.LanguageId));
        }
        
    }
}
