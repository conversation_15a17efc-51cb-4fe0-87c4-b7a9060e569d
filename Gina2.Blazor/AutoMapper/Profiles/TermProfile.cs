﻿using AutoMapper;
using Gina2.DbModels;
using Gina2.Core.Models;
using Microsoft.AspNetCore.Components;
using Gina2.MySqlRepository.Models;
using Gina2.Core;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class TermProfile : Profile
    {
        public TermProfile()
        {
            //CreateMap<GTreeNode, DbModels.Topic>().ReverseMap()
            //    .ForMember(
            //    dest => dest.Title, opt => opt.MapFrom(src => src.Name))
            //    .ForMember(
            //    dest => dest.ParentId.ToString(), opt => opt.MapFrom(src => src.ParentId));
            CreateMap<Domain.Terms.Term, DbModels.Coordination>().ReverseMap()
            .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Coordination));
            CreateMap<Domain.Terms.Term, DbModels.Monitoring>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Monitoring));

            CreateMap<Domain.Terms.Term, DbModels.Area>().ReverseMap()
                .ForMember(
                dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(
                dest => dest.Id, opt => opt.MapFrom(s => 0))
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Area));

            CreateMap<Domain.Terms.Term, DbModels.Country>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Country))
                .ForMember(
                dest => dest.Region, opt => opt.MapFrom(src => src.CountryRegionMap))
                .ForMember(
                dest => dest.ParentId, opt => opt.MapFrom(s=> 0));// Set a defalt parent because Regioncode is string and unable to set as parentid

            CreateMap<Domain.Terms.Term, DbModels.CountryGroup>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.CountryGroup))
                 .ForMember(
               dest => dest.Child, opt => opt.MapFrom(src => src.CountryGroupMappings))
                 .MaxDepth(1).ReverseMap();

            CreateMap<Domain.Terms.Term, DbModels.CountryGroupMapping>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Country))
               .ForMember(
               dest => dest.ParentId, opt => opt.MapFrom(s => s.CountryGroupId))
               .ForMember(
               dest => dest.IsTopicParent, opt => opt.MapFrom(src => false));

            

            CreateMap<Domain.Terms.CountryRegionCode, DbModels.CountryRegionMapItem>()
                .ForMember(
                dest => dest.RegionCode, opt => opt.MapFrom(src => src.Code)).ReverseMap();
            CreateMap<Domain.Terms.Term, DbModels.IncomeGroup>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.IncomeGroup))
                .ForMember(
                dest => dest.Name, opt => opt.MapFrom(src => src.Code))
            .ForMember(
                dest => dest.Code, opt => opt.MapFrom(src => src.Code));

            CreateMap<Domain.Terms.Term, DbModels.CountryRegionMapItem>()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.Iso3Code));

            CreateMap<Domain.Terms.Term, DbModels.Region>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Region))
                .ForMember(
                dest => dest.Name, opt => opt.MapFrom(src => src.Code));

            CreateMap<Domain.Terms.Term, DbModels.Delivery>().ReverseMap().
                ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Delivery));

            CreateMap<Domain.Terms.Term, DbModels.Icn2Category>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Icn2Category))
                .ForMember(
                dest => dest.IsTopicParent, opt => opt.MapFrom(src => true))
                .ForMember(
                dest => dest.Child, opt => opt.MapFrom(src => src.Icn2s));

            CreateMap<Domain.Terms.Term, DbModels.Icn2>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Icn2))
                 .ForMember(
                dest => dest.ParentId, opt => opt.MapFrom(src => src.CategoryId))
                 .ForMember(
                dest => dest.IsTopicParent, opt => opt.MapFrom(src => false));
            
            CreateMap<Domain.Terms.Term, DbModels.Partner>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Partners))
                .ForMember(
               dest => dest.ParentId, opt => opt.MapFrom(src => src.PartnerCategoryId))
                .ForMember(
               dest => dest.IsTopicParent, opt => opt.MapFrom(src => false));
            CreateMap<Domain.Terms.Term, DbModels.Language>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Language));

            CreateMap<Domain.Terms.Term, DbModels.MechanismType>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.MechanismType));

            CreateMap<Domain.Terms.Term, DbModels.PartnerCategory>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.PartnerCategory))
                .ForMember(
                dest => dest.IsTopicParent, opt => opt.MapFrom(src => true))

                .ForMember(
                dest => dest.Child, opt => opt.MapFrom(src => src.Partners));
            CreateMap<Domain.Terms.Term, DbModels.ProgramType>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.ProgramType));

            CreateMap<Domain.Terms.Term, DbModels.Sdg>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Sdg));

            CreateMap<Domain.Terms.Term, DbModels.Micronutrient>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Micronutrient));

            CreateMap<Domain.Terms.Term, DbModels.TargetGroup>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.TargetGroup));

            CreateMap<Domain.Terms.Term, Topic>().ReverseMap()
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Topic))
                .ForMember(
                dest => dest.IsCheck, opt => opt.MapFrom(src => src.IsSelected))
                .ForMember(
                dest => dest.Key, opt => opt.MapFrom(src => src.Id))
                .ForMember(
                dest => dest.IsGrandParent, opt => opt.MapFrom(src => src.IsGrandParent))
                .ForMember(
                dest => dest.IsSearchCheck, opt => opt.MapFrom(src => src.IsSelected))
                .ForMember(
                dest => dest.IsIndeterminate, opt => opt.MapFrom(src => src.IsAllChildSelected));

            CreateMap<Domain.Terms.Term, TopicParent>().ReverseMap()
                .ForMember(
                dest => dest.Id, opt => opt.MapFrom(src => src.TopicId))
                .ForMember(
                dest => dest.Key, opt => opt.MapFrom(src => src.TopicId))
                .ForMember(
                dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.Topic))
                .ForMember(
                dest => dest.Name, opt => opt.MapFrom(src => src.Topic.Name))
                .ForMember(
                dest => dest.Description, opt => opt.MapFrom(src => src.Topic.Description))
                .ForMember(
                dest => dest.DragAndDropKey, opt => opt.MapFrom(src => src.Topic.DragAndDropKey))
                .ForMember(
                dest => dest.ParentId, opt => opt.MapFrom(src => src.ParentId))
                 .ForMember(
                dest => dest.IsIndeterminate, opt => opt.MapFrom(src => src.Topic.IsAllChildSelected))
                .ForMember(
                dest => dest.ParentId, opt => opt.MapFrom(src => src.ParentId))
                 .ForMember(
                dest => dest.IsSearchCheck, opt => opt.MapFrom(src => src.Topic.IsSelected))
                 .ForMember(
                dest => dest.IsCheck, opt => opt.MapFrom(src => src.Topic.IsSelected));
            CreateMap<Domain.Terms.Term, DbModels.PolicyType>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.PolicyType));

            CreateMap<Domain.Terms.Term, DbModels.ProblemType>().ReverseMap()
                .ForMember(dest => dest.URL, opt => opt.MapFrom(s => Constants.VocabularyTables.ProblemType));

            CreateMap<Domain.Terms.Term, DbModels.BaseTerm>().ReverseMap();
            CreateMap<Domain.Terms.Term, Models.TermDoubleTree>().ReverseMap();
            CreateMap<Domain.Terms.Term, Models.ExportCsvTerm>().ReverseMap();
            CreateMap<Domain.Terms.Term, DbModels.ActionStatus>().ReverseMap()
                .ForMember(
                dest => dest.Name, opt => opt.MapFrom(src => src.Name));


            //for TermTreeNode
            CreateMap<Domain.Terms.TermTreeNode, DbModels.CountryGroupMapping>().ReverseMap();

            CreateMap<Domain.Terms.TermTreeNode, CountryWithRegion>().ReverseMap()
            .ForMember(
               dest => dest.Iso3Code, opt => opt.MapFrom(s => s.CountryCode))
            .ForMember(
               dest => dest.Name, opt => opt.MapFrom(s => s.CountryName));

            CreateMap<Domain.Terms.TermTreeNode, DbModels.Partner>().ReverseMap();
            CreateMap<Domain.Terms.TermTreeNode, DbModels.Icn2>().ReverseMap();
        }

    }
}
