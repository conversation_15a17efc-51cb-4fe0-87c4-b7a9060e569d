﻿using AutoMapper;
using Domain.Languages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class LanguageProfile: Profile
    {
        public LanguageProfile()
        {
            CreateMap<DbModels.Language, Language>()
               .ForMember(dest =>
                   dest.Id,
                   opt => opt.MapFrom(src => src.Id))
               .ForMember(dest =>
                   dest.Name,
                   opt => opt.MapFrom(src => src.Name));

            CreateMap<DbModels.Language, Language>()
             .ForMember(dest =>
                 dest.Id,
                 opt => opt.MapFrom(src => src.Id))
             .ForMember(dest =>
                 dest.Name,
                 opt => opt.MapFrom(src => src.Name)).ReverseMap();
        }
    }
      

}
