﻿using AutoMapper;
using Domain.PolicyTopics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class PolicyTopicProfile: Profile
    {
        public PolicyTopicProfile()
        {
            CreateMap<DbModels.PolicyTopic, PolicyTopicMap>()
             .ForMember(dest =>
                 dest.PolicyId,
                 opt => opt.MapFrom(src => src.PolicyId))
             .ForMember(dest =>
                 dest.TopicId,
                 opt => opt.MapFrom(src => src.TopicId));

            CreateMap<DbModels.PolicyTopic, PolicyTopicMap>()
           .ForMember(dest =>
               dest.PolicyId,
               opt => opt.MapFrom(src => src.PolicyId))
           .ForMember(dest =>
               dest.TopicId,
               opt => opt.MapFrom(src => src.TopicId)).ReverseMap();
        }
    }
}
