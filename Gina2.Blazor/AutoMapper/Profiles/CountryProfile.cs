﻿using AutoMapper;
using Domain.Search;
using Gina2.SPs;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class CountryProfile : Profile
    {
        public CountryProfile()
        {
            CreateMap<SearchResult, CombinedSearchResult>().ReverseMap();
            CreateMap<DbModels.RefmartCountry, DbModels.Country>()
               .ForMember(dest => dest.Iso3Code, opt => opt.MapFrom(src => src.CountryCode))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
               .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.LegalStatus));

            CreateMap<DbModels.RefmartCountry, DbModels.CountryRegionMapItem>()
               .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.CountryCode))
               .ForMember(dest => dest.RegionCode, opt => opt.MapFrom(src => src.RegionCode));

            CreateMap<DbModels.RefmartCountry, DbModels.CountryIncomeGroupMapItem>()
              .ForMember(dest => dest.CountryCode, opt => opt.MapFrom(src => src.CountryCode))
              .ForMember(dest => dest.IncomeGroupCode, opt => opt.MapFrom(src => src.IncomeGroupCode));
        }
    }
}