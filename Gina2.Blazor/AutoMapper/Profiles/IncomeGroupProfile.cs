﻿using AutoMapper;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class IncomeGroupProfile : Profile
    {
        public IncomeGroupProfile()
        {
            CreateMap<DbModels.RefmartCountry, DbModels.IncomeGroup>()
               .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.IncomeGroupCode))
               .ForMember(dest => dest.Name, opt => opt.MapFrom(src => ""));
        }
    }
}
