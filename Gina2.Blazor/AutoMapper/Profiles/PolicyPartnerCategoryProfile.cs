﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Gina2.DbModels;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class PolicyPartnerCategoryProfile: Profile
    {
        public PolicyPartnerCategoryProfile()
        {
            CreateMap<Domain.PolicyPartnerCategory.PolicyPartnerCategory, PolicyCategoryPartnerMapItem>()
       .ForMember(dest =>
           dest.PartnerCategoryId,
           opt => opt.MapFrom(src => src.CategoryId))
       .ForMember(dest =>
           dest.PolicyId,
           opt => opt.MapFrom(src => src.PolicyId))
        .ForMember(dest =>
           dest.PartnerId,
           opt => opt.MapFrom(src => src.PartnerId));


            CreateMap<Domain.PolicyPartnerCategory.PolicyPartnerCategory, PolicyCategoryPartnerMapItem>()
      .ForMember(dest =>
          dest.PartnerCategoryId,
          opt => opt.MapFrom(src => src.CategoryId))
      .ForMember(dest =>
          dest.PolicyId,
          opt => opt.MapFrom(src => src.PolicyId))
       .ForMember(dest =>
          dest.PartnerId,
          opt => opt.MapFrom(src => src.PartnerId)).ReverseMap();
        }
    }
}
