﻿using AutoMapper;
using Domain.ScoreCard;
using Gina2.Blazor.Models;
using Gina2.DbModels.HomeDrafts;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class ScorecardProfile : Profile
    {
        public ScorecardProfile()
        {
            CreateMap<DbModels.Scorecard, HomeFeatureDto>();
            CreateMap<DbModels.Scorecard, Scorecard>().ReverseMap();
            CreateMap<ScorecardDraft, HomeFeatureDto>();
        }
    }
}