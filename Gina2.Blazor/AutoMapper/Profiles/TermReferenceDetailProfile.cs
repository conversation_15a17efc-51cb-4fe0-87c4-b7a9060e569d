﻿using AutoMapper;
using Gina2.Blazor.Models;
using Gina2.DbModels;
using System.Text;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class TermReferenceDetailProfile : Profile
    {
        public TermReferenceDetailProfile()
        {
            CreateMap<TermReferenceDetail, DbModels.Policy>().ReverseMap()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.PolicyCountryMap.First().CountryCode))
                .ForMember(
                dest => dest.Link, opt => opt.MapFrom(src => "policies"));
            CreateMap<TermReferenceDetail, DbModels.Program>().ReverseMap()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.ProgrammeCountryMap.First().CountryCode))
                .ForMember(
                dest => dest.Link, opt => opt.MapFrom(src => "programmes-and-actions"));
            CreateMap<TermReferenceDetail, DbModels.Mechanism>().ReverseMap()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.MechanismCountryMap.First().CountryCode))
                .ForMember(
                dest => dest.Link, opt => opt.MapFrom(src => "mechanisms"));
            CreateMap<TermReferenceDetail, DbModels.Action>().ReverseMap()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.Program.ProgrammeCountryMap.First().CountryCode))
                .ForMember(
                dest => dest.Link, opt => opt.MapFrom(src => "programmes-and-actions"))
                .ForMember(
                dest => dest.Title, opt => opt.MapFrom(src => $"{src.Program.Title} - {src.Topic.Name}"));
            CreateMap<TermReferenceDetail, DbModels.SmartCommitmentIcn2MappingItem>().ReverseMap()
                .ForMember(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.SmartCommitment.Commitment.CommitmentCountryMap.First().CountryCode))
                .ForMember(
                dest => dest.Link, opt => opt.MapFrom(src => "commitments"))
                .ForMember(
                dest => dest.Id, opt => opt.MapFrom(src => src.SmartCommitment.Id))
                .ForMember(
                dest => dest.Title, opt => opt.MapFrom(src => $"Commitment - {src.SmartCommitment.Commitment.Title} - Commitment {src.SmartCommitment.CommitmentNumber} - {src.SmartCommitment.Title}"));
            CreateMap<TermReferenceDetail, DbModels.ActionTargetGroup>().ReverseMap()
                .ForMember<string>(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.Action.Program.ProgrammeCountryMap.First().CountryCode))
                .ForMember<string>(
                dest => dest.Link, opt => opt.MapFrom(src => "programmes-and-actions"))
                .ForMember(
                dest => dest.Id, opt => opt.MapFrom(src => src.ActionId))
                .ForMember<string>(
                dest => dest.Title, opt => opt.MapFrom(src => GetName(src)));
            CreateMap<TermReferenceDetail, DbModels.ActionMicronutrients>().ReverseMap()
                .ForMember<string>(
                dest => dest.CountryCode, opt => opt.MapFrom(src => src.Action.Program.ProgrammeCountryMap.First().CountryCode))
                .ForMember<string>(
                dest => dest.Link, opt => opt.MapFrom(src => "programmes-and-actions"))
                .ForMember(
                dest => dest.Id, opt => opt.MapFrom(src => src.ActionId))
                .ForMember<string>(
                dest => dest.Title, opt => opt.MapFrom(src => GetMicroNutrientName(src)));
        }

        private static string GetName(ActionTargetGroup src)
        {
            StringBuilder title = new StringBuilder();
            string programtitle = src.Action.Program.Title;
            string actionTopic = src.Action.Topic != null ? $"{src.Action.Topic.Name}" : string.Empty;
            title.Append($"{programtitle} - {actionTopic}");
            foreach (var item in src.Action.ActionTargetGroups)
            {
                string targetname = item.TargetGroup != null ? item.TargetGroup.Name : "";
                title.Append($" - {targetname}");
            }
            return title.ToString();
        }

        private static string GetMicroNutrientName(ActionMicronutrients src)
        {
            StringBuilder title = new StringBuilder();
            string programtitle = src.Action.Program.Title;
            string actionTopic = src.Action.Topic != null ? $"{src.Action.Topic.Name}" : string.Empty;
            title.Append($"{programtitle} - {actionTopic}");
            foreach (var item in src.Action.ActionTargetGroups)
            {
                string targetname = item.TargetGroup != null ? item.TargetGroup.Name : "";
                title.Append($" - {targetname}");
            }
            return title.ToString();
        }
    }
}
