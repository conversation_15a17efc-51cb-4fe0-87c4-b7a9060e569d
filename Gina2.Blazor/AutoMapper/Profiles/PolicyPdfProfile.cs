﻿
using AutoMapper;
using Gina2.DbModels;
using Gina2.Blazor.Models.Pdf;
using Gina2.Blazor.Helpers;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class PolicyPdfProfile: Profile
    {
        public PolicyPdfProfile()
        {
            CreateMap<Policy, PolicyPdf>();
            CreateMap<Policy, PolicyPdfData>()
                .ForMember(dest => dest.Start_Date, act => act.MapFrom(src => MonthYearDisplayHelper.GetMonthAndYearString(src.StartMonth, src.StartYear)))
                .ForMember(dest => dest.End_Date, act => act.MapFrom(src => MonthYearDisplayHelper.GetMonthAndYearString(src.EndMonth,src.EndYear)))
                .ForMember(dest => dest.Published_by, act => act.MapFrom(src => src.PublishedBy))
                .ForMember(dest => dest.Published_Date, act => act.MapFrom(src => MonthYearDisplayHelper.GetMonthAndYearString(src.PublishedMonth, src.PublishedYear)))
                .ForMember(dest => dest.Is_the_policy_document_adopted, act => act.MapFrom(src => src.Adopted != null && (bool)src.Adopted ? "Yes" : "No"))
                .ForMember(dest => dest.Adopted_Date, act => act.MapFrom(src => MonthYearDisplayHelper.GetMonthAndYearString(src.AdoptedMonth, src.AdoptedYear)))
                .ForMember(dest => dest.Type_of_policy, act => act.MapFrom(src => src.PolicyType.Name));

            CreateMap<Policy, PolicyPdfExtract>()
                .ForMember(dest => dest.PolicyTypeName, act => act.MapFrom(src => src.PolicyType.Name))
                .ForMember(dest => dest.PolicyExtractByOriginalLanguage, act => act.MapFrom(src => src.Extracts));

            CreateMap<Policy, PolicyPdfDetails>()
                .ForMember(dest => dest.URL_link, act => act.MapFrom(src => src.Url))
                .ForMember(dest => dest.Further_Notes, act => act.MapFrom(src => src.FurtherNotes))
                .ForMember(dest => dest.File_upload, act => act.MapFrom(src => src.Pdfs));
        }

    }
}
