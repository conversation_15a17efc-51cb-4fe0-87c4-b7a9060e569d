﻿using AutoMapper;
using Domain.Topics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class TopicParentProfile: Profile
    {
        public TopicParentProfile()
        {
            CreateMap<DbModels.TopicParent, TopicParent>()
             .ForMember(dest =>
                 dest.ParentId,
                 opt => opt.MapFrom(src => src.ParentId))
             .ForMember(dest =>
                 dest.TopicId,
                 opt => opt.MapFrom(src => src.TopicId))
             .ForMember(dest =>
                 dest.Topic,
                 opt => opt.MapFrom(src => src.Topic));

            CreateMap<DbModels.TopicParent, TopicParent>()
            .ForMember(dest =>
                dest.ParentId,
                opt => opt.MapFrom(src => src.ParentId))
            .ForMember(dest =>
                dest.TopicId,
                opt => opt.MapFrom(src => src.TopicId))
            .ForMember(dest =>
                dest.Topic,
                opt => opt.MapFrom(src => src.Topic)).ReverseMap();
        }
    }
}
