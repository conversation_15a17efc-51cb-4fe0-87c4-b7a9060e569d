﻿using AutoMapper;
using Gina2.Blazor.Models;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using System.Text;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class TermReferenceProfile: Profile
    {
        [Inject]
        private IMapper _mapper { get; set; }
        public TermReferenceProfile()
        {
            CreateMap<TermReference, DbModels.PolicyType>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.Policies));
            CreateMap<TermReference, DbModels.MechanismType>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.Mechanisms));
            CreateMap<TermReference, ProgramType>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => GetProgramsDetails(src)));
            CreateMap<TermReference, DbModels.ActionDelivery>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.Action));
            CreateMap<TermReference, DbModels.Coordination>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.MechanismCoordination.Select(m => m.Mechanism)));
            CreateMap<TermReference, DbModels.Monitoring>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.MechanismMonitorings.Select(m => m.Mechanism)));
            CreateMap<TermReference, DbModels.Delivery>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.ActionDeliveries.Select(m => m.Action)));
            CreateMap<TermReference, DbModels.Language>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => GetLanguageDetails(src)));
            CreateMap<TermReference, DbModels.Country>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => GetCountryDetails(src)));
            CreateMap<TermReference, DbModels.Icn2>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.CommitmentICN2S));
            CreateMap<TermReference, DbModels.Partner>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => GetPartnerDetails(src)));
            CreateMap<TermReference, DbModels.TargetGroup>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.ActionTargetGroups));
            CreateMap<TermReference, DbModels.Micronutrient>().ReverseMap()
                .ForMember(
                dest => dest.TermDetails, opt => opt.MapFrom(src => src.ActionMicronutrients));
            CreateMap<TermReference, Topic>().ReverseMap()
                .ForMember<ICollection<TermReferenceDetail>>(
                dest => dest.TermDetails, opt => opt.MapFrom(src => GetTopicData(src)));
        }

        private static ICollection<TermReferenceDetail> GetTopicData(Topic src)
        {
            List<TermReferenceDetail> ReferenceList = new List<TermReferenceDetail>();
            foreach (var item in src.PolicyTopics)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Policy.Id;
                detail.Title = item.Policy.Title;
                detail.Link = "policies";
                detail.CountryCode = item.Policy.PolicyCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.MechanismTopics)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Mechanism.Id;
                detail.Title = item.Mechanism.Title;
                detail.Link = "mechanisms";
                detail.CountryCode = item.Mechanism.MechanismCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            return ReferenceList;
        }

        private static ICollection<TermReferenceDetail> GetPartnerDetails(Partner src)
        {
            List<TermReferenceDetail> ReferenceList = new List<TermReferenceDetail>();
            foreach (var item in src.PolicyCategoryPartnerMap)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Policy.Id;
                detail.Title = item.Policy.Title;
                detail.Link = "policies";
                detail.CountryCode = item.Policy.PolicyCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.MechanismPartnerCategoryMap)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Mechanism.Id;
                detail.Title = item.Mechanism.Title;
                detail.Link = "mechanisms";
                detail.CountryCode = item.Mechanism.MechanismCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.ProgramCategoryPartnerMap)
            {
                PrgramData(ReferenceList, item.Program);

            }
            return ReferenceList;
        }
        private static ICollection<TermReferenceDetail> GetCountryDetails(Country src)
        { 
            List<TermReferenceDetail> ReferenceList = new List<TermReferenceDetail>();
            foreach (var item in src.PolicyCountryMap)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Policy.Id;
                detail.Title = item.Policy.Title;
                detail.Link = "policies";
                detail.CountryCode = item.CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.MechanismCountryMap)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Mechanism.Id;
                detail.Title = item.Mechanism.Title;
                detail.Link = "mechanisms";
                detail.CountryCode = item.CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.ProgrammeCountryMap)
            {
                PrgramData(ReferenceList, item.Program);

            }
            foreach (var item in src.CommitmentCountryMap)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Commitment.Id;
                detail.Title = item.Commitment.Title;
                detail.Link = "commitments";
                detail.CountryCode = item.CountryCode;
                ReferenceList.Add(detail);
            }
            return ReferenceList;
        }
        private static ICollection<TermReferenceDetail> GetProgramsDetails(ProgramType src)
        {
            List<TermReferenceDetail> ReferenceList = new List<TermReferenceDetail>();
            foreach (var program in src.Programs)
            {
                PrgramData(ReferenceList, program);
            }
            return ReferenceList;
        }

        private static void PrgramData(List<TermReferenceDetail> ReferenceList, DbModels.Program program)
        {
            foreach (var action in program.Actions)
            {
                StringBuilder title = new StringBuilder();
                string programtitle = program.Title != null ? program.Title : "";
                string actionTopic = action.Topic != null ? $"{action.Topic.Name}" : string.Empty;
                title.Append($"{programtitle} - {actionTopic}");
                foreach (var item in action.ActionTargetGroups)
                {
                    string targetname = item.TargetGroup != null ? item.TargetGroup.Name : "";
                    title.Append($" - {targetname}");
                }

                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = action.Id;
                detail.Title = title.ToString();
                detail.Link = "programmes-and-actions";
                detail.CountryCode = program.ProgrammeCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
        }

        private static ICollection<TermReferenceDetail> GetLanguageDetails(Language src)
        {
            List<TermReferenceDetail> ReferenceList = new List<TermReferenceDetail>();
            foreach (var item in src.Policies)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Id;
                detail.Title = item.Title;
                detail.Link = "policies";
                detail.CountryCode = item.PolicyCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            foreach (var item in src.Programs)
            {
                TermReferenceDetail detail = new TermReferenceDetail();
                detail.Id = item.Id;
                detail.Title = item.Title;
                detail.Link = "programmes-and-actions";
                detail.CountryCode = item.ProgrammeCountryMap.First().CountryCode;
                ReferenceList.Add(detail);
            }
            return ReferenceList;
        }
    }
}
