﻿using AutoMapper;
using Domain.PolicyProgram;
using Domain.PolicyTypes;
using Gina2.DbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gina2.Blazor.AutoMapper.Profiles
{
    public class PolicyProfile: Profile
    {
        public PolicyProfile()
        {
            CreateMap<DbModels.PolicyType, PolicyTypeViewModel>()
             .ForMember(dest =>
                 dest.Id,
                 opt => opt.MapFrom(src => src.Id))
             .ForMember(dest =>
                 dest.Name,
                 opt => opt.MapFrom(src => src.Name));

            CreateMap<DbModels.PolicyType, PolicyTypeViewModel>()
            .ForMember(dest =>
                dest.Id,
                opt => opt.MapFrom(src => src.Id))
            .ForMember(dest =>
                dest.Name,
                opt => opt.MapFrom(src => src.Name)).ReverseMap();

            CreateMap<DbModels.ProgramPolicy, Domain.PolicyProgram.PolicyProgram>()
           .ForMember(dest =>
               dest.ProgramId,
               opt => opt.MapFrom(src => src.ProgramId))
           .ForMember(dest =>
               dest.PolicyId,
               opt => opt.MapFrom(src => src.PolicyId));

            CreateMap<DbModels.ProgramPolicy, Domain.PolicyProgram.PolicyProgram>()
            .ForMember(dest =>
                dest.ProgramId,
                opt => opt.MapFrom(src => src.ProgramId))
            .ForMember(dest =>
                dest.PolicyId,
                opt => opt.MapFrom(src => src.PolicyId)).ReverseMap();

            CreateMap<DbModels.Policy, PolicyRevision>()
           .ForMember(dest =>
              dest.Id,
              opt => opt.Ignore());
        }
    }
}
