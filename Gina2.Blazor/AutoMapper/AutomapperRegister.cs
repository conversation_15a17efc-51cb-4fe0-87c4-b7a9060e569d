﻿using AutoMapper;
using Gina2.Blazor.AutoMapper.Profiles;

namespace Gina2.Blazor.AutoMapper
{
    public static class AutomapperRegister
    {
        public static void Register(IServiceCollection services)
        {
            var mapperConfig = new MapperConfiguration(mc =>
            {
                //mc.AddProfile(new ScorecardProfile());
                //mc.AddProfile(new SliderProfile());
                //mc.AddProfile(new NutrientProfile());
                //mc.AddProfile(new PolicyProfile());
                //mc.AddProfile(new LanguageProfile());
                ////mc.AddProfile(new CategoryPartnerProfile());
                //mc.AddProfile(new PolicyPartnerCategoryProfile());
                //// mc.AddProfile(new TopicParentProfile());
                //mc.AddProfile(new PolicyTopicProfile());
                //mc.AddProfile(new ProgramProfile());
                //mc.AddProfile(new PolicyPdfProfile());
                //mc.AddProfile(new ProgramAndActionPdf());
                //mc.AddProfile(new VocabularyProfile());
                //mc.AddProfile(new CommitmentPdfDownload());
                //mc.AddProfile(new ApplicationUserProfile());
                //mc.AddProfile(new TermProfile());
                //mc.AddProfile(new TermReferenceProfile());
                //mc.AddProfile(new TermReferenceDetailProfile());
                //mc.AddProfile(new CountryProfile());
                //mc.AddProfile(new RegionProfile());
                //mc.AddProfile(new IncomeGroupProfile());
                //mc.AddProfile(new IndicatorProfile());
                //mc.AddProfile(new PolicyAttachmentProfile());
            });

          //  IMapper mapper = mapperConfig.CreateMapper();
           // services.AddSingleton(mapper);
        }
    }
}