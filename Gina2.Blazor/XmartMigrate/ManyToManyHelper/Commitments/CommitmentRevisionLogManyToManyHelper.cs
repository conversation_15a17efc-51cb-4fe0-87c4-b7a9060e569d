﻿using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Policy
{
    public sealed class CommitmentRevisionLogManyToManyHelper<TEntity> : ICommitmentBaseEntityHelper<TEntity> where TEntity : CommitmentBaseClass
    {
        public List<CommitmentBaseClass> GetAvailableNewItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords, SqlDbContext dbContext)
        {
            List<CommitmentLog> dbItems = dbRecords.Cast<CommitmentLog>().ToList();
            List<CommitmentLog> apiItems = apiRecords.Cast<CommitmentLog>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentId,i.CommitmentVId, i.ToState, i.FromState }).ToList();

            var policyIds = apiItems.Select(i => new { i.CommitmentId, i.CommitmentVId }).Distinct().ToList();

            var availablePolicyIds = dbContext.CommitmentRevision.AsTracking()
               .Where(p => policyIds.Select(x => x.CommitmentId).Contains(p.Id))
               .Where(p => policyIds.Select(x => x.CommitmentVId).Contains(p.VersionId))
               .Select(p => new { p.Id, p.VersionId })
               .ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId && ui.CommitmentVId == i.CommitmentVId && ui.ToState == i.ToState
                && ui.FromState == i.FromState) && availablePolicyIds.Any(e=>e.Id==i.CommitmentId && e.VersionId==i.CommitmentVId))
                .ToList();

            return newItems.Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetDeletingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<PolicyTopicDraft>().ToList();
            var apiItems = apiRecords.Cast<PolicyTopicDraft>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.PolicyId, i.TopicId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.PolicyId == i.PolicyId && ui.TopicId == i.TopicId))
                .Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetUpdatingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<CommitmentLog>().ToList();
            var apiItems = apiRecords.Cast<CommitmentLog>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentVId, i.CommitmentId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId
                             && ui.CommitmentVId == i.CommitmentVId && i.FromState != i.ToState))
                     .ToList();

            return items.Cast<CommitmentBaseClass>().ToList();
        }
    }
}