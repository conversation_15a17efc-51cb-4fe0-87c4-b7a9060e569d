﻿using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.MySqlRepository;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Commitments
{
    public sealed class CommitmentPolicyManyToManyHelper<TEntity> : ICommitmentBaseEntityHelper<TEntity> where TEntity : CommitmentBaseClass
    {
        public List<CommitmentBaseClass> GetAvailableNewItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords, SqlDbContext dbContext)
        {
            List<CommitmentPolicyRevision> dbItems = dbRecords.Cast<CommitmentPolicyRevision>().ToList();
            List<CommitmentPolicyRevision> apiItems = apiRecords.Cast<CommitmentPolicyRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentId,i.CommitmentVId, i.PolicyId }).ToList();

            var commitmentIds = apiItems.Select(i => new { i.CommitmentId, i.CommitmentVId }).Distinct().ToList();
            var policyIds = apiItems.Select(i => i.PolicyId).Distinct().ToList();

            var availableCommitmentIds = dbContext.CommitmentRevision
              .Where(p => commitmentIds.Select(x => x.CommitmentId).Contains(p.Id))
              .Where(p => commitmentIds.Select(x => x.CommitmentVId).Contains(p.VersionId))
              .Select(p => new { p.Id, p.VersionId })
              .ToList();
           
            List<int> availablePolicyCodes = dbContext.Policies.Where(p => policyIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId && ui.PolicyId == i.PolicyId)
                     && availableCommitmentIds.Any(x => x.Id == i.CommitmentId && x.VersionId == i.CommitmentVId)
                    && availablePolicyCodes.Contains(i.PolicyId))
                .ToList();

            return newItems.Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetDeletingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<CommitmentPolicyRevision>().ToList();
            var apiItems = apiRecords.Cast<CommitmentPolicyRevision>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.CommitmentId, i.PolicyId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId && ui.PolicyId == i.PolicyId))
                .Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetUpdatingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<CommitmentPolicyRevision>().ToList();
            var apiItems = apiRecords.Cast<CommitmentPolicyRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentId,i.CommitmentVId, i.PolicyId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId
            && ui.CommitmentVId == i.CommitmentVId
            && ui.PolicyId == i.PolicyId))
                .ToList();

            return items.Cast<CommitmentBaseClass>().ToList();
        }
    }
}
