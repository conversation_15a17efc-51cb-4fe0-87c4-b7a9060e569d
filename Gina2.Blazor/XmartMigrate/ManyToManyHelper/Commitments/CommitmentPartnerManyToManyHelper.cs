﻿using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.MySqlRepository;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Commitments
{
    public class CommitmentPartnerManyToManyHelper<TEntity> : ICommitmentBaseEntityHelper<TEntity> where TEntity : CommitmentBaseClass
    {
        public List<CommitmentBaseClass> GetAvailableNewItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords, SqlDbContext dbContext)
        {
            List<CommitmentPartnerRevision> dbItems = dbRecords.Cast<CommitmentPartnerRevision>().ToList();
            List<CommitmentPartnerRevision> apiItems = apiRecords.Cast<CommitmentPartnerRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentId,i.CommitmentVId, i.PartnerId }).ToList();

            var commitmentIds = apiItems.Select(i =>new { i.CommitmentId, i.CommitmentVId }).Distinct().ToList();
            List<int> partnerIds = apiItems.Select(i => i.PartnerId).Distinct().ToList();

            var availableCommitmentIds = dbContext.CommitmentRevision
              .Where(p => commitmentIds.Select(x => x.CommitmentId).Contains(p.Id))
              .Where(p => commitmentIds.Select(x => x.CommitmentVId).Contains(p.VersionId))
              .Select(p => new { p.Id, p.VersionId })
              .ToList();

            
            List<int> availablePartnerIds = dbContext.Partners.Where(p => partnerIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId && ui.PartnerId == i.PartnerId)
                    && availableCommitmentIds.Any(x => x.Id == i.CommitmentId && x.VersionId == i.CommitmentVId)
                    && availablePartnerIds.Contains(i.PartnerId))
                .ToList();

            return newItems.Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetDeletingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<CommitmentPartnerRevision>().ToList();
            var apiItems = apiRecords.Cast<CommitmentPartnerRevision>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.CommitmentId,i.CommitmentVId, i.PartnerId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId 
            && ui.CommitmentVId == i.CommitmentVId
            && ui.PartnerId == i.PartnerId))
                .Cast<CommitmentBaseClass>().ToList();
        }

        public List<CommitmentBaseClass> GetUpdatingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords)
        {
            var dbItems = dbRecords.Cast<CommitmentPartnerRevision>().ToList();
            var apiItems = apiRecords.Cast<CommitmentPartnerRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.CommitmentId,i.CommitmentVId , i.PartnerId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.CommitmentId == i.CommitmentId
            && ui.CommitmentVId == i.CommitmentVId
            && ui.PartnerId == i.PartnerId))
                .ToList();

            return items.Cast<CommitmentBaseClass>().ToList();
        }
    }
}