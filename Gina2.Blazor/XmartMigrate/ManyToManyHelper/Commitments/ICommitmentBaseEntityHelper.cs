﻿using Gina2.DbModels;
using Gina2.MySqlRepository;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper
{
    public interface ICommitmentBaseEntityHelper<TEntity> where TEntity : CommitmentBaseClass
    {
        List<CommitmentBaseClass> GetAvailableNewItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords, SqlDbContext dbContext);
        List<CommitmentBaseClass> GetDeletingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords);
        List<CommitmentBaseClass> GetUpdatingItems(List<CommitmentBaseClass> dbRecords, List<CommitmentBaseClass> apiRecords);
    }
}