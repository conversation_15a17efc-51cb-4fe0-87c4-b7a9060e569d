﻿using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public sealed class ActionTargetGroupsManyToManyHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionTargetGroupRevision> dbItems = dbRecords.Cast<ActionTargetGroupRevision>().ToList();
            List<ActionTargetGroupRevision> apiItems = apiRecords.Cast<ActionTargetGroupRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.TargetGroupId }).ToList();

            var actionIds = apiItems.Select(i =>new { i.ActionId, i.ActionVId }).Distinct().ToList();
            var targetGroupIds = apiItems.Select(i => i.TargetGroupId).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                            .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                           .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                           .Select(p => new { p.Id, p.VersionId })
                           .ToList();

           
            List<int> availableTargetGroupIds = dbContext.TargetGroups.Where(p => targetGroupIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.TargetGroupId == i.TargetGroupId)
                     && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                    && availableTargetGroupIds.Contains(i.TargetGroupId))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionTargetGroupRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionTargetGroupRevision>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.ActionId, i.TargetGroupId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.TargetGroupId == i.TargetGroupId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionTargetGroupRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionTargetGroupRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.TargetGroupId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.ActionId == i.ActionId
            && ui.ActionVId == i.ActionVId
            && ui.TargetGroupId == i.TargetGroupId))
                .ToList();
            
            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}