﻿using Gina2.Blazor.Models.Pdf;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public sealed class ActionSoicalDeterminantAreaManyToManyHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            var dbItems = dbRecords.Cast<ActionSocialDeterminantRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionSocialDeterminantRevision>().ToList();

            var dbUniqueItems = dbItems.Select(i => new { i.ActionId, i.ActionVId, i.SocialDeterminant }).ToList();

            var actionIds = apiItems.Select(i =>new { i.ActionId, i.ActionVId }).Distinct().ToList();

            var areaIds = apiItems.Select(i => i.SocialDeterminant).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                             .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                            .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                            .Select(p => new { p.Id, p.VersionId })
                            .ToList();

            var availableSocialDeterminantsIds = dbContext.SocialDeterminants.Where(i => areaIds.Contains(i.Name)).Select(i => i.Name).ToList();

            var newItems = apiItems
                .Where(i => !dbUniqueItems.Any(t => t.ActionId == i.ActionId && t.ActionVId == i.ActionVId
                          && t.SocialDeterminant == i.SocialDeterminant)
                    && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                    && availableSocialDeterminantsIds.Contains(i.SocialDeterminant))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionAreaRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionAreaRevision>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.ActionId, i.AreaId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.AreaId == i.AreaId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionSocialDeterminantRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionSocialDeterminantRevision>().ToList();

            var uniqueDbItems = dbItems.Select(i => new { i.ActionId,i.ActionVId ,i.SocialDeterminant }).ToList();

            var items = apiItems
                .Where(i => uniqueDbItems.Any(t => t.ActionId == i.ActionId 
                && t.ActionVId == i.ActionVId
                && t.SocialDeterminant == i.SocialDeterminant))
                .ToList();

            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}