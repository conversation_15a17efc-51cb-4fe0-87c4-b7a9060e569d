﻿using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public class ActionDeliveryManytoManyHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(
            List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionDeliveryRevision> dbItems = dbRecords.Cast<ActionDeliveryRevision>().ToList();
            List<ActionDeliveryRevision> apiItems = apiRecords.Cast<ActionDeliveryRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId, i.ActionVId, i.DeliveryId }).ToList();

            var actionIds = apiItems.Select(i => new { i.ActionId, i.ActionVId }).Distinct().ToList();
            var deliveryIds = apiItems.Select(i => i.DeliveryId).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                              .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                             .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                             .Select(p => new { p.Id, p.VersionId })
                             .ToList();

            List<int> availableDeliveryIds = dbContext.Deliveries.Where(p => deliveryIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.ActionVId==i.ActionVId
                      && ui.DeliveryId == i.DeliveryId)
                   && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                    && availableDeliveryIds.Contains(i.DeliveryId))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> existingItems, List<ActionBaseEntity> records)
        {
            var dbRecords = existingItems.Cast<ActionDeliveryRevision>().ToList();
            var extractedRecords = records.Cast<ActionDeliveryRevision>().ToList();

            var uniqueItems = extractedRecords.Select(i => new { i.ActionId, i.DeliveryId }).ToList();

            return dbRecords.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.DeliveryId == i.DeliveryId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionDeliveryRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionDeliveryRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.DeliveryId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.ActionId == i.ActionId
                             && ui.ActionVId == i.ActionVId
                             && ui.DeliveryId == i.DeliveryId)).ToList();

            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}