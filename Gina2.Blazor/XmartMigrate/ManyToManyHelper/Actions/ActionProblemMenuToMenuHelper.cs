﻿using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public sealed class ActionProblemMenuToMenuHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionProblemRevision> dbItems = dbRecords.Cast<ActionProblemRevision>().ToList();
            List<ActionProblemRevision> apiItems = apiRecords.Cast<ActionProblemRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.ProblemTypeId }).ToList();

            var actionIds = apiItems.Select(i =>new { i.ActionId, i.ActionVId }).Distinct().ToList();
            var problemTypeIds = apiItems.Select(i => i.ProblemTypeId).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                            .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                           .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                           .Select(p => new { p.Id, p.VersionId })
                           .ToList(); 
            
            List<int> availableProblemTypeIds = dbContext.ProblemTypes.Where(p => problemTypeIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId &&  ui.ActionVId == i.ActionVId  && ui.ProblemTypeId == i.ProblemTypeId)
                   && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                    && availableProblemTypeIds.Contains(i.ProblemTypeId))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> existingItems, List<ActionBaseEntity> records)
        {
            var dbRecords = existingItems.Cast<ActionProblemRevision>().ToList();
            var extractedRecords = records.Cast<ActionProblemRevision>().ToList();

            var uniqueItems = extractedRecords.Select(i => new { i.ActionId, i.ProblemTypeId }).ToList();

            return dbRecords.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.ProblemTypeId == i.ProblemTypeId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionProblemRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionProblemRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.ProblemTypeId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.ActionId == i.ActionId 
            && ui.ActionVId == i.ActionVId
            && ui.ProblemTypeId == i.ProblemTypeId))
                .ToList();

            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}