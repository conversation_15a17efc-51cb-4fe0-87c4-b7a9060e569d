﻿using Gina2.Blazor.Models.Pdf;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public sealed class ActionManyToManyHelperr<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionRevision> dbItems = dbRecords.Cast<ActionRevision>().ToList();
            List<ActionRevision> apiItems = apiRecords.Cast<ActionRevision>().ToList();

            var uniqueRecords = dbItems.Select(i => new { i.Id, i.VersionId }).ToList();

           // var programIds = apiItems.Select(i => new { i.ProgramId, i.ProgramVId }).Distinct().ToList();
            var topicIds = apiItems.Select(i => i.TopicId).Distinct().ToList();


            //var availableProgramIds = dbContext.ProgramRevision.AsTracking()
            //.Where(i => programIds.Any(e => e.ProgramId == i.Id && e.ProgramVId == i.VersionId))
            //                .Select(p => new { p.Id, p.VersionId })
            //                .ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
            .Where(i => uniqueRecords.Any(e => e.Id == i.Id && e.VersionId == i.VersionId))
                            .Select(p => new { p.Id, p.VersionId })
                            .ToList();

            List<int> availableTopicIds = dbContext.Topics.Where(p => topicIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => availableActionIds.Any(e => e.Id == i.Id && e.VersionId == i.VersionId)
              //  && availableProgramIds.Any(e => e.Id == i.ProgramId && e.VersionId == i.ProgramVId)
                && i.TopicId.HasValue && availableTopicIds.Contains(i.TopicId.Value)).ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> existingItems, List<ActionBaseEntity> records)
        {
            var dbRecords = existingItems.Cast<ActionRevision>().ToList();
            var apiRecords = records.Cast<ActionRevision>().ToList();

            var uniqueRecords = apiRecords.Select(i => i.Id).ToList();
            return dbRecords.Where(i => !uniqueRecords.Contains(i.Id))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionRevision>().ToList();
            
            var uniqueRecords = dbItems.Select(i =>new { i.Id, i.VersionId }).ToList();
            
            var items = apiItems
                   .Where(i => !uniqueRecords.Any(t => t.Id == i.Id && t.VersionId == i.VersionId)).ToList();

            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}