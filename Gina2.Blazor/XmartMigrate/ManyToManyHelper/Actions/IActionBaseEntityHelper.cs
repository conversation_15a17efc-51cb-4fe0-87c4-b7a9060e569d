﻿using Gina2.DbModels;
using Gina2.MySqlRepository;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper
{
    public interface IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext);
        List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords);
        List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords);
    }
}