﻿using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public sealed class ActionElenaManyToManyHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionElenaRevision> dbItems = dbRecords.Cast<ActionElenaRevision>().ToList();
            List<ActionElenaRevision> apiItems = apiRecords.Cast<ActionElenaRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId, i.ActionVId, i.ElenaLinkId }).ToList();

            var actionIds = apiItems.Select(i => new { i.ActionId, i.ActionVId }).Distinct().ToList();
            var targetGroupIds = apiItems.Select(i => i.ElenaLinkId).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                            .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                            .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                            .Select(p => new { p.Id, p.VersionId })
                            .ToList();


            List<int> availableElenaIds = dbContext.ElenaLinks.Where(p => targetGroupIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.ElenaLinkId == i.ElenaLinkId)
                     && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                     && availableElenaIds.Contains(i.ElenaLinkId))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionElenaRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionElenaRevision>().ToList();

            var uniqueItems = apiItems.Select(i => new { i.ActionId, i.ElenaLinkId }).ToList();

            return dbItems.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.ElenaLinkId == i.ElenaLinkId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            throw new NotImplementedException();
        }
    }
}
