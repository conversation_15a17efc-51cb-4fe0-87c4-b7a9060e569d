﻿using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.XmartMigrate.ManyToManyHelper.Actions
{
    public class ActionMicronutrientsManyToManyHelper<TEntity> : IActionBaseEntityHelper<TEntity> where TEntity : ActionBaseEntity
    {
        public List<ActionBaseEntity> GetAvailableNewItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords, SqlDbContext dbContext)
        {
            List<ActionMicronutrientRevision> dbItems = dbRecords.Cast<ActionMicronutrientRevision>().ToList();
            List<ActionMicronutrientRevision> apiItems = apiRecords.Cast<ActionMicronutrientRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.MicronutrientId }).ToList();

            var actionIds = apiItems.Select(i => new { i.ActionId, i.ActionVId }).Distinct().ToList();
            var micronutrientsIds = apiItems.Select(i => i.MicronutrientId).Distinct().ToList();

            var availableActionIds = dbContext.ActionRevision.AsTracking()
                              .Where(p => actionIds.Select(x => x.ActionId).Contains(p.Id))
                             .Where(p => actionIds.Select(x => x.ActionVId).Contains(p.VersionId))
                             .Select(p => new { p.Id, p.VersionId })
                             .ToList(); 
            
            List<int> availableMicronutrientsIds = dbContext.Micronutrients.Where(p => micronutrientsIds.Contains(p.Id)).Select(p => p.Id).ToList();

            var newItems = apiItems
                .Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.MicronutrientId == i.MicronutrientId)
                      && availableActionIds.Any(e => e.Id == i.ActionId && e.VersionId == i.ActionVId)
                    && availableMicronutrientsIds.Contains(i.MicronutrientId))
                .ToList();

            return newItems.Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetDeletingItems(List<ActionBaseEntity> existingItems, List<ActionBaseEntity> records)
        {
            var dbRecords = existingItems.Cast<ActionMicronutrientRevision>().ToList();
            var extractedRecords = records.Cast<ActionMicronutrientRevision>().ToList();

            var uniqueItems = extractedRecords.Select(i => new { i.ActionId, i.MicronutrientId }).ToList();

            return dbRecords.Where(i => !uniqueItems.Any(ui => ui.ActionId == i.ActionId && ui.MicronutrientId == i.MicronutrientId))
                .Cast<ActionBaseEntity>().ToList();
        }

        public List<ActionBaseEntity> GetUpdatingItems(List<ActionBaseEntity> dbRecords, List<ActionBaseEntity> apiRecords)
        {
            var dbItems = dbRecords.Cast<ActionMicronutrientRevision>().ToList();
            var apiItems = apiRecords.Cast<ActionMicronutrientRevision>().ToList();

            var uniqueItems = dbItems.Select(i => new { i.ActionId,i.ActionVId, i.MicronutrientId }).ToList();

            var items = apiItems.Where(i => uniqueItems.Any(ui => ui.ActionId == i.ActionId
            && ui.ActionVId == i.ActionVId
            && ui.MicronutrientId == i.MicronutrientId))
                .ToList();
            return items.Cast<ActionBaseEntity>().ToList();
        }
    }
}