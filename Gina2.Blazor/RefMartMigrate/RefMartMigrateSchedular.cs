﻿using AutoMapper;
using Gina2.Blazor.XmartMigrate;
using Gina2.Blazor.XmartMigrate.ManyToManyHelper;
using Gina2.DbModels;
using Gina2.MySqlRepository;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Collections;
using System.Text.Json;

namespace Gina2.Blazor.RefMartMigrate
{
    public class RefMartMigrateSchedular
    {
        private readonly IConfiguration Configuration;
        private readonly SqlDbContext _dbContext;
        private readonly string baseAddress;
        private DbMigrationCache migrationCache;
        private IMapper _mapper;
        ILogger<XmartMigrateSchedular> _logger;


        public RefMartMigrateSchedular(
            IConfiguration configuration,
            IDbContextFactory<SqlDbContext> dbContextFactory,
            IMapper mapper,
            ILogger<XmartMigrateSchedular> logger)
        {
            _mapper = mapper;
            Configuration = configuration;
            _dbContext = dbContextFactory.CreateDbContext() ;
            baseAddress = Configuration["RefMartApi:BaseAddress"];
            migrationCache = new();
            _logger = logger;
        }

        public async Task RunMainJobAsync()
        {
            _logger.LogInformation($"Country Xmart started");

            await ImportCountryDataAsync();

            _logger.LogInformation($"Country Xmart ended");

        }


        private async Task ImportCountryDataAsync()
        {
            IEnumerable<RefmartCountry> result = await GetDataAsync<RefmartCountry>();

            if (result == null || !result.Any())
            {
                _logger.LogInformation($"No record for Country");
                return;
            }
            await ImportCountriesAsync(result);
            await ImportRegionsAsync(result);
            await ImportIncomeGroupsAsync(result);
            await ImportCountryRegionMappings(result);
            await ImportCountryIncomeGroupMappings(result);
        }

        private async Task ImportCountriesAsync(IEnumerable<RefmartCountry> result)
        {
            var records = result.GroupBy(r => r.CountryCode).Select(g => g.First()).Cast<BaseManyToManyEntity>().ToList();
            var apiRecords = _mapper.Map<List<Country>>(records).Cast<BaseManyToManyEntity>().ToList();
            await ImportManyToManyEntities<Country>(apiRecords);
        }

        private async Task ImportRegionsAsync(IEnumerable<RefmartCountry> result)
        {
            var records = result
                .Where(r => !string.IsNullOrWhiteSpace(r.RegionCode))
                .GroupBy(r => r.RegionCode).Select(g => g.First())
                .Cast<BaseManyToManyEntity>()
                .ToList();

            var apiRecords = _mapper.Map<List<Region>>(records).Cast<BaseManyToManyEntity>().ToList();
            await ImportManyToManyEntities<Region>(apiRecords);
        }

        private async Task ImportIncomeGroupsAsync(IEnumerable<RefmartCountry> result)
        {
            var records = result
                .Where(r => !string.IsNullOrWhiteSpace(r.IncomeGroupCode))
                .GroupBy(r => r.IncomeGroupCode)
                .Select(g => g.First()).Cast<BaseManyToManyEntity>()
                .ToList();

            var apiRecords = _mapper.Map<List<IncomeGroup>>(records).Cast<BaseManyToManyEntity>().ToList();
            await ImportManyToManyEntities<IncomeGroup>(apiRecords);
        }

        private async Task ImportCountryRegionMappings(IEnumerable<RefmartCountry> result)
        {
            var records = result
               .Where(r => !string.IsNullOrWhiteSpace(r.RegionCode) && !string.IsNullOrWhiteSpace(r.CountryCode))
               .GroupBy(r => new { r.CountryCode, r.RegionCode }).Select(g => g.First())
               .Cast<BaseManyToManyEntity>()
               .ToList();

            var apiRecords = _mapper.Map<List<CountryRegionMapItem>>(records).Cast<BaseManyToManyEntity>().ToList();
            await ImportManyToManyEntities<CountryRegionMapItem>(apiRecords);
        }

        private async Task ImportCountryIncomeGroupMappings(IEnumerable<RefmartCountry> result)
        {
            var records = result
               .Where(r => !string.IsNullOrWhiteSpace(r.IncomeGroupCode) && !string.IsNullOrWhiteSpace(r.CountryCode))
               .GroupBy(r => new { r.CountryCode, r.IncomeGroupCode }).Select(g => g.First())
               .Cast<BaseManyToManyEntity>()
               .ToList();

            var apiRecords = _mapper.Map<List<CountryIncomeGroupMapItem>>(records).Cast<BaseManyToManyEntity>().ToList();
            await ImportManyToManyEntities<CountryIncomeGroupMapItem>(apiRecords);
        }

        private async Task ImportManyToManyEntities<TEntity>(List<BaseManyToManyEntity> apiRecords) where TEntity : BaseManyToManyEntity
        {
            Microsoft.EntityFrameworkCore.DbSet<TEntity> dbSet = _dbContext.Set<TEntity>();
            IMenuToManyHelper<TEntity> menuToMenuHelper = MenuToMenuHelperFactory.GetInstance<TEntity>();
            List<BaseManyToManyEntity> dbRecords = migrationCache.Get(dbSet);
            List<BaseManyToManyEntity> updatingRecords = menuToMenuHelper.GetUpdatingItems(dbRecords, apiRecords).ToList();
            List<BaseManyToManyEntity> availableNewItems = menuToMenuHelper.GetAvailableNewItems(dbRecords, apiRecords, _dbContext).ToList();
            List<BaseManyToManyEntity> deletingItems = menuToMenuHelper.GetDeletingItems(dbRecords, apiRecords).ToList();

            //Adding records
            if (availableNewItems?.Count > 0)
            {
                foreach (var item in availableNewItems)
                {
                    item.DateCreated = DateTime.UtcNow;
                    item.DateUpdated = DateTime.UtcNow;
                }

                _dbContext.AddRange(availableNewItems);
            }

            // updating records
            if (updatingRecords?.Count > 0)
            {
                foreach (var item in updatingRecords)
                {
                    item.DateUpdated = DateTimeOffset.UtcNow;
                    _dbContext.Entry(item).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                }
            }

            //Deleting records
            if (deletingItems?.Count > 0)
            {
                foreach (var item in deletingItems)
                {
                    dbSet.Remove((TEntity)item);
                }
            }

           var totalSave = await _dbContext.SaveChangesAsync();
            _logger.LogInformation($"total record added for Country ={totalSave}");
        }

        private async Task<IEnumerable<T>> GetDataAsync<T>()
        {
            var serviceProvider = new ServiceCollection().AddHttpClient().BuildServiceProvider();
            var httpClientFactory = serviceProvider.GetService<IHttpClientFactory>();
            var httpClient = httpClientFactory.CreateClient("GetAll");
            httpClient.Timeout = TimeSpan.FromMinutes(10);

            string url = $"{baseAddress}/{Core.Constants.RefMartApiAddress.Country}";
            var responseMessage = await httpClient.GetAsync(url);
            responseMessage.EnsureSuccessStatusCode();
            IEnumerable<T> result = new List<T>();
            try
            {
                var stringResponse = await responseMessage.Content.ReadAsStringAsync();
                JObject jObject = JObject.Parse(stringResponse);
                var options = new JsonSerializerOptions() { PropertyNameCaseInsensitive = true };

                if (!jObject["value"].Any())
                {
                    _logger.LogInformation($"No record found from API for endpooint ::{Core.Constants.RefMartApiAddress.Country}");
                    return new List<T>();
                }

                result = JsonSerializer.Deserialize<IEnumerable<T>>(jObject["value"].ToString(),
                new JsonSerializerOptions(options));
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"error on fetching countries data from Xmart: {ex.Message}");
            }

            return result;
        }
    }
}