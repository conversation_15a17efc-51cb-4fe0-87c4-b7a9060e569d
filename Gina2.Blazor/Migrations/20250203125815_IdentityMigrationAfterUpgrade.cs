﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Gina2.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class IdentityMigrationAfterUpgrade : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "27273ed1-e0fa-4ff8-be08-270227ee367a",
                column: "ConcurrencyStamp",
                value: null);

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "e3882f2a-7fd6-45f6-b531-dc42ce85901d",
                column: "ConcurrencyStamp",
                value: null);

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "ff1a98d8-ca10-423d-97b0-c315c264ec4b",
                column: "ConcurrencyStamp",
                value: null);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "27273ed1-e0fa-4ff8-be08-270227ee367a",
                column: "ConcurrencyStamp",
                value: "98068b7d-3549-4c1f-9d03-74b08b18d051");

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "e3882f2a-7fd6-45f6-b531-dc42ce85901d",
                column: "ConcurrencyStamp",
                value: "af668a13-0289-4768-8229-f410e16a3fa9");

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "ff1a98d8-ca10-423d-97b0-c315c264ec4b",
                column: "ConcurrencyStamp",
                value: "ed1916af-8483-47f7-9492-f45c64ddf0e4");
        }
    }
}
