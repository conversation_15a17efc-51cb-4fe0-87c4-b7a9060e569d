﻿<Modal @ref="modalRef" Class="modals-lg">
        <ModalContent Centered Class="forms adminmobel">
        <ModalHeader>
            <ModalTitle>Edit</ModalTitle>
            <NavLink class="close" onclick="@HideModal"><img src="/img/close.png"/></NavLink>
        </ModalHeader>
        <ModalBody>
            <Field>
                <FieldLabel>Title</FieldLabel>
                <RadzenHtmlEditor ValueChanged="@OnChange" class="_editor"  style="min-height: 250px; margin-bottom: 1rem;" Value="@Value">
            <HtmlEditor/>
        </RadzenHtmlEditor>
            </Field>
            <Field>
                <FieldLabel>Tooltip</FieldLabel>
                <TextEdit Placeholder="Tooltip" />
            </Field>
            <Field>
                <FieldLabel>Placeholder</FieldLabel>
                <TextEdit Placeholder="Placeholder" />
            </Field>
            <ModalFooter>
                <Button Class="but-blues apply" Clicked="@HideModal"> Save</Button>
                <Button Class="cancel" Clicked="@HideModal">Cancel</Button>
            </ModalFooter>
        </ModalBody>
    </ModalContent>
</Modal>
<Icon Clicked="@EditModal" Class="fa-solid fa-pen _editicon" />

@code {
    [Parameter]
        public EventCallback<string> Changed { get; set; }
        public string Value { get; set; }
        private async Task OnChange(string html)
        {
            Value = html;
            await Changed.InvokeAsync(html);
        }
    private Modal modalRef;

    private Task EditModal()
    {
        return modalRef.Show();
    }

    private Task HideModal()
    {
        return modalRef.Hide();
    }
}
