﻿@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent
<Div Class="">
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
        <Div Class="item1 flex-b">
        </Div>
        <Div Class="item2">
            <AuthorizeView>
                <Authorized>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem href="@($"/countries/{CountryCode}/policies/{PolicyCode}/edit")">New Draft</DropdownItem>
                            <DropdownItem href="@($"/countries/{CountryCode}/policies/{PolicyCode}")">View published</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </Authorized>
            </AuthorizeView>
        </Div>
    </Div>
</Div>

<Div Class="moderate">

    <Tabs SelectedTab="@selectedTab" Class="policies-list" SelectedTabChanged="@OnSelectedTabChanged">
        <Items>
            <Tab Name="revisions">Revisions</Tab>
            @*  <Tab Name="compare">Compare Revisions</Tab> *@
        </Items>

        <Content>
            <TabPanel Name="revisions" Class="tabsel">
                <Div Class="table-responsive _moderate">
                    <Table Class="table-nth">
                        <TableHeader ThemeContrast="ThemeContrast.Dark">
                            <TableRow>
                                <TableHeaderCell>Revision</TableHeaderCell>
                                <TableHeaderCell>Title</TableHeaderCell>
                                <TableHeaderCell>Date</TableHeaderCell>
                                <TableHeaderCell>Revision Action</TableHeaderCell>
                                <TableHeaderCell>Moderation Action</TableHeaderCell>

                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <Repeater Items="@ModerationLog">
                                @{
                                    if (RevisionId != context.EntityRevisionId)
                                    {
                                        RevisionId = context.EntityRevisionId;
                                    }
                                }
                                <TableRow>
                                    <TableRowCell>@context.EntityRevisionId</TableRowCell>
                                    <TableRowCell>
                                        <Heading Size="HeadingSize.Is3">@context.CombinedTitle</Heading>
                                        <Paragraph>Revised by <NavLink>@context.UserName</NavLink></Paragraph>
                                    </TableRowCell>
                                    <TableRowCell>
                                        @context.RevisedDate.GetDateTimeFormat()

                                    </TableRowCell>
                                    <TableRowCell Class="_actionscenter">
                                        @if (!context.IsPublished)
                                        {
                                            <Tooltip Text="Revert">
                                                <Icon Name="IconName.Redo" Clicked="e=>RevertConfirm(context, RevertContent)" />
                                            </Tooltip>
                                        }
                                        <Tooltip Text="Preview">
                                                <Icon Name="IconName.Eye" Clicked="e=> NavigateToDraft(context.EntityId,context.EntityRevisionId)"></Icon>
                                           
                                        </Tooltip>
                                        <Tooltip Text="Edit Draft">
                                                <Icon Name="IconName.Edit" Clicked="e=> NavigateToDraftEdit(context.EntityId,context.EntityRevisionId)"></Icon>
                                      
                                        </Tooltip>

                                    </TableRowCell>
                                    <TableRowCell>
                                        @{
                                            ModerateActions = ModerationLog.Where(m => m.EntityRevisionId == context.EntityRevisionId).ToList();
                                        }
                                        <Repeater Items="@ModerateActions" Context="user">
                                            @if (user.IsPublished)
                                            {
                                                <Heading Size="HeadingSize.Is3">This is the published revision.</Heading>
                                                <Paragraph class="alink"><Label Class="text-primary" @onclick="e=> OpenConfirm(UnpublishContent ,user.EntityId,user.EntityRevisionId,user.ContentType, user.Id)">Unpublish</Label></Paragraph>
                                            }
                                            <Paragraph Class="date">From @user.UserVisibleFromState.ToString() --> @user.UserVisibleToState.ToString() on @user.RevisedDate.GetDateTimeFormat() </Paragraph>
                                            <Paragraph>by <NavLink>@user.UserName</NavLink></Paragraph> 

                                        </Repeater>

                                    </TableRowCell>
                                </TableRow>
                            </Repeater>
                        </TableBody>
                    </Table>
                </Div>
            </TabPanel>
            <TabPanel Name="compare" Class="tabsel">
                <Div Class="table-responsive">
                    <Div Class="table-responsive">
                        <Table Class="table-nth">
                            <TableHeader ThemeContrast="ThemeContrast.Dark">
                                <TableRow>
                                    <TableHeaderCell>Revision</TableHeaderCell>
                                    <TableHeaderCell TextAlignment="TextAlignment.Center" ColumnSpan="2">Compare</TableHeaderCell>
                                    <TableHeaderCell>Operations</TableHeaderCell>

                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow>

                                    <TableRowCell>
                                        <NavLink href="#">Sat, 12/01/2018 - 00:17</NavLink> by <NavLink href="#">engesveenk</NavLink>
                                        <Paragraph>Bulk moderation state change</Paragraph>
                                    </TableRowCell>
                                    <TableRowCell TextAlignment="TextAlignment.Center"><Radio Value="@("Com1")"></Radio></TableRowCell>
                                    <TableRowCell TextAlignment="TextAlignment.Center"></TableRowCell>
                                    <TableRowCell><Paragraph>This is the published revision.</Paragraph></TableRowCell>

                                </TableRow>
                                <TableRow>

                                    <TableRowCell>
                                        <NavLink href="#">Sat, 12/01/2018 - 00:17</NavLink> by <NavLink href="#">engesveenk</NavLink>
                                        <Paragraph>Bulk moderation state change</Paragraph>
                                    </TableRowCell>
                                    <TableRowCell TextAlignment="TextAlignment.Center"></TableRowCell>
                                    <TableRowCell TextAlignment="TextAlignment.Center"><Radio Value="@("Com2")"></Radio></TableRowCell>
                                    <TableRowCell>
                                        <Tooltip Text="New Draft" Class="_moderaction">
                                            <Button> <Icon Name="IconName.Redo" /></Button>
                                        </Tooltip>
                                        <Tooltip Text="Delete" Class="_moderaction">
                                            <Button> <Icon Name="IconName.Delete" /></Button>
                                        </Tooltip>
                                    </TableRowCell>

                                </TableRow>

                            </TableBody>
                        </Table>
                    </Div>
                </Div>
            </TabPanel>

        </Content>
    </Tabs>
</Div>


@code {
    string selectedTab = "revisions";

    private Task OnSelectedTabChanged(string name)
    {
        selectedTab = name;

        return Task.CompletedTask;
    }


    public RenderFragment DraftContent { get; set; } = @<p>Are you sure to create a new draft?</p>;
    public RenderFragment UnpublishContent { get; set; } = @<p>Are you sure to unpublish?</p>;
    public RenderFragment RevertContent { get; set; } = @<p>Are you sure to revert this version?</p>;
}

