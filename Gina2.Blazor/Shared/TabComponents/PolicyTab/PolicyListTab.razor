﻿
<CountryTabItem />
<Div>
    <Tabs Class="policies-list" SelectedTab="@selectedTab" SelectedTabChanged="@OnSelectedTabChanged">
        <Items>
            <Tab Name="viewallpolicy">View all policies</Tab>
            <Tab Name="typeofpolicy">Type of policy</Tab>
            <li>
                <Button Class="but-yellow mob-t-hide"><Icon class="fa-solid fa-upload" /> Export Policies</Button>
            </li>
        </Items>
        <Content>
            <TabPanel Name="viewallpolicy">
                <FlattenItemList AllPolicyNames="@AllPolicyNames" />
            </TabPanel>
            <TabPanel Name="typeofpolicy">
                <CategoryItemList AllPolicyNames="@AllPolicyNames" PolicyTypes="@policyTypes" />
            </TabPanel>
        </Content>
    </Tabs>
</Div>