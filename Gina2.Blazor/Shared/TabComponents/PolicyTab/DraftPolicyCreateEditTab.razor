﻿@using Gina2.Core.Enums
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using AntDesign.Select;
@using Microsoft.AspNetCore.Components.Authorization;
@using Gina2.Blazor.Areas.Identity.IdentityServices;
@using static Gina2.Core.Constants;
@using Gina2.Core.Lookups;
@using Gina2.Blazor.Models.AdminModel;

<Loader IsLoading="@IsLoading" />
@if (PolicyDetail != null)
{
   <Validations @ref="PolicyTypeValidation" Mode="ValidationMode.Manual">
       
<Container Class="newdraft _antdesign pl-2 pr-2">
    <Container Padding="Padding.Is0" Class="pt-4 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new policy</Heading>
        <Divider Class="divi-blue" />
    </Container>

    <Container Class="form-newd">
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Title <Span>*</Span></FieldLabel>
                    <Validation Validator="ValidationRule.IsNotEmpty">
                        <TextEdit Placeholder="Enter title..." Text="@PolicyDetail.Title" TextChanged="@TitleKeyPress">
                            <Feedback>
                                <ValidationNone>Please enter title</ValidationNone>
                                <ValidationError>Enter valid title!</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Validation>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Type of policy <span>*</span></FieldLabel>
                    <Validation Validator="@ValidateSelect">
                        <Select TValue="int" class="pl-1 pr-3" SelectedValue="@(PolicyDetail.PolicyTypeId.HasValue?PolicyDetail.PolicyTypeId.Value:0)" SelectedValueChanged="@ChangePolicyType">
                            <ChildContent>
                                <SelectItem Value="0">Select Type</SelectItem>
                                <Repeater Items="@PolicyTypes">
                                    <SelectItem Value="context.Id">@context.Name</SelectItem>
                                </Repeater>
                                <FieldHelp>Read more about policy document types used in GIFNA.</FieldHelp>
                            </ChildContent>
                            <Feedback>
                                <ValidationError>Select Policy Type</ValidationError>
                            </Feedback>
                        </Select>
                    </Validation>
                </Field>
            </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Language</FieldLabel>
                <Select TValue="int?" @bind-SelectedValue="@PolicyDetail.LanguageId">
                    <SelectItem Value="-1">Select Language</SelectItem>
                    <Repeater Items="@Languages">
                        <SelectItem Value="context.Id">@context.Name</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Country(ies)</FieldLabel>

                <AntDesign.Select DataSource="@Countries"
                                  Mode="multiple"
                                  TItemValue="string"
                                  Placeholder="Select country(ies)"
                                  TItem="Country"
                                  LabelName="@nameof(Country.Name)"
                                  ValueName="@nameof(Country.Iso3Code)"
                                  OnSelectedItemsChanged="OnCountriesChanged"
                                  @bind-Values="@countryValues"
                                  AllowClear
                                  EnableSearch
                                  Style="width: 100%; margin-bottom: 8px;">
                </AntDesign.Select>
            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <FieldLabel>Province/region</FieldLabel>
                <TextEdit @bind-Text="@PolicyDetail.SubnationalGeographicArea" Placeholder="Enter Province/region.."></TextEdit>
                <FieldHelp>Please specify geographical area in case of sub-national policy</FieldHelp>
            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                <FieldLabel>Start date</FieldLabel>
                <AntDesign.DatePicker @bind-Value="@StartDate"  TValue="DateTime?" Picker="month" OnChange="DataChangingStartDate" />
                <FieldLabel Style="color: red" hidden="@isStartDate">Start date should be less than End date</FieldLabel>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                <FieldLabel>End date</FieldLabel>
                <AntDesign.DatePicker @bind-Value="@EndDate" TValue="DateTime?" Picker="month" OnChange="DataChangingEndDate" />
                <FieldLabel Style="color: red" hidden="@isEndDate">End date should be greater than Start date</FieldLabel>
            </Field>


        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                <FieldLabel>Published date</FieldLabel>
                <AntDesign.DatePicker @bind-Value="@PublishedDate" TValue="DateTime?" Picker="month" OnChange="DataChangingPublishedDate" />
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Published by</FieldLabel>
                <TextEdit @bind-Text="@PolicyDetail.PublishedBy" Placeholder="Enter Published by..."></TextEdit>
            </Field>
        </Fields>
        <Fields>

            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <FieldLabel>Is the policy document adopted?</FieldLabel>
                <RadioGroup Class="text-med" TValue="string" Name="colors" CheckedValue="@AdoptedCheckedValue" CheckedValueChanged="@AdoptedChanged">
                    <Radio Value="@("na")">N/A</Radio>
                    <Radio Value="@("yes")">Yes</Radio>
                    <Radio Value="@("infrom")">No / No information</Radio>
                </RadioGroup>
            </Field>
        </Fields>

        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                <FieldLabel>Adopted date</FieldLabel>
                <AntDesign.DatePicker Disabled="@IsAdoptedVisible" @bind-Value="@AdoptedDate" TValue="DateTime?" Picker="month" OnChange="DataChangingAdoptedDate" />
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Adopted by</FieldLabel>
                <TextEdit Disabled="@IsAdoptedVisible" @bind-Text="@PolicyDetail.AdoptedBy" Placeholder="Enter Adopted By..."></TextEdit>
            </Field>
        </Fields>
        <Field>
            <FieldLabel>Partners involved</FieldLabel>
        </Field>
        <Div Flex="Flex.JustifyContent.Between" Class="_tabsbox downl-flex">
            <Tabs SelectedTab="@selectedTab" Class="from-tab" SelectedTabChanged="@OnSelectedTabChanged">
                <Items>
                    <Repeater Items="@PartnerCategoryPartners">
                        <Tab Name="@context.Id.ToString()">@context.Name</Tab>
                    </Repeater>
                </Items>
                <Content>
                    <Repeater Context="context" Items="@PartnerCategoryPartners.OrderBy(pc => pc.DateUpdated)">
                        <TabPanel Name="@context.Id.ToString()">
                            <Heading Size="HeadingSize.Is5" Class="pt-1 pb-1">Please indicate @context.Name involved in implementation</Heading>
                            <ListGroup Class="ulgroup">
                                <Repeater Context="context1" Items="@context.Partners.OrderBy(p => p.DateUpdated)">
                                    @{
                                        bool IsPartner = false;
                                        if (PolicyPartnerCategoryPartnerList != null && PolicyPartnerCategoryPartnerList.Any(a => a.PartnerId == context1.Id))
                                        {
                                            IsPartner = true;
                                        }
                                    }
                                    <ListGroupItem>
                                        <Check Checked="@IsPartner"
                                               CheckedChanged="e => {PartnerCheckboxClicked(context1.Id, e.Value);}"
                                               TValue="bool?">@context1.Name</Check>
                                    </ListGroupItem>
                                </Repeater>
                            </ListGroup>
                            <Field>
                                <FieldLabel>@context.Name detail(s)</FieldLabel>
                                        <RadzenHtmlEditor @bind-Value="@GovernmentDetails" Change="@ChangeGovernmentDetails" class="_editor" style="height: 200px; margin-bottom: 1rem;"><RadzenHtmlEditorSeparator /><RadzenHtmlEditorBold /><RadzenHtmlEditorItalic /><RadzenHtmlEditorUnderline /><RadzenHtmlEditorStrikeThrough /><RadzenHtmlEditorSeparator /><RadzenHtmlEditorRemoveFormat /></RadzenHtmlEditor>
                            </Field>
                        </TabPanel>
                    </Repeater>
                </Content>
            </Tabs>
        </Div>
    </Container>

    <Container Class="form-newd mt-4">

        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Extracts</FieldLabel>
                    <FieldHelp>Please list the goals, objectives or targets related to nutrition in the Policy document, if any.</FieldHelp>
                </Div>
            </Div>
            <RextEditors Changed="@OnGoalsChanged" Value="@policyGoals" />
        </Field>

    </Container>
    <Container Class="mt-4 w-98 mo-m-0">
        <Row>
            <Column Class="form-newd mr-1 mo-mr-0 w-100 Topicspolicy">

                <Div Flex="Flex.JustifyContent.Between">
                    <Div><FieldLabel>Topics</FieldLabel></Div>
                    <Div>

                        <Button Clicked="@VisibleExpandButton" Disabled ="@isExpandingOrCOllapsing" Class="but-gray">@ExpandCollapseName</Button>
                        <Button Clicked="ClearTopics" Disabled="@(!canClearTopics)" Class="but-gray">Clear</Button>
                    </Div>
                </Div>
                <Divider Padding="Padding.Is0" Class="m-0" />
                <AntDesign.Spin Spinning="@isExpandingOrCOllapsing">
                    <AntDesign.Tree @ref="topicTree"
                                    ShowIcon
                                    ShowExpand
                                    MatchedClass="site-tree-search-value"
                                    DataSource="TopicList"
                                    TItem="GTreeNode"
                                    TitleExpression="x => x.DataItem.Title"
                                    ChildrenExpression="x => x.DataItem.Children"
                                    KeyExpression="x => x.DataItem.TopicId.ToString()"
                                    OnCheck="x => TopicTreeCheckboxClicked(x)"
                                    DefaultCheckedKeys="@topicKeys"
                                    DefaultExpandedKeys="@topicKeys"
                                    Checkable>
                    </AntDesign.Tree>
                </AntDesign.Spin>
            </Column>
            <Column Class="form-newd gina-new ml-1 w-100">
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel Style="font-weight: bold;">Link to existing action records</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Button Clicked="ClearActionLinks" Disabled="@(!canClearActionLinks)" Class="but-gray">Clear</Button>
                    </Div>
                </Div>

                <Divider />

                <Addons Class="pb-3">
                    <Addon AddonType="AddonType.Start">
                        <Button Color="Color.Light">
                            <Icon Name="IconName.Search" />
                        </Button>
                    </Addon>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit Text="@ActionLinkFilterText" TextChanged="@ActionLinkFilter" Placeholder="Some text value..." />
                    </Addon>
                </Addons>
                <Div Class="w-100 overflow-scroll _minheight">
                    <Repeater Items="@ListOfActionRecords">
                        @{
                            bool IsActionLink = false;
                            if (ProgramList != null && ProgramList.Contains(context.Id.ToString()))
                            {
                                IsActionLink = true;
                            }
                        }
                        <Check Checked="@IsActionLink" TValue="bool?" CheckedChanged="e => {LinkToActionCheckboxClicked(context.Id, e.Value);}">@context.Title</Check>
                    </Repeater>
                </Div>
            </Column>
        </Row>
    </Container>

    <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>URL link</FieldLabel>
            <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
            <TextEdit @bind-Text="@PolicyDetail.Url" Placeholder="Enter URL here..."></TextEdit>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Further notes</FieldLabel>
                </Div>
            </Div>
                    <RadzenHtmlEditor id="furtherNotes" @bind-Value="@PolicyDetail.FurtherNotes" class="_editor" style="height: 200px; margin-bottom: 1rem;"><RadzenHtmlEditorSeparator /><RadzenHtmlEditorBold /><RadzenHtmlEditorItalic /><RadzenHtmlEditorUnderline /><RadzenHtmlEditorStrikeThrough /><RadzenHtmlEditorSeparator /><RadzenHtmlEditorRemoveFormat /></RadzenHtmlEditor>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Reference</FieldLabel>
                </Div>
            </Div>
            <RadzenHtmlEditor id="references" @bind-Value="@PolicyDetail.References" class="_editor" style="height: 200px; margin-bottom: 1rem;"><RadzenHtmlEditorSeparator /><RadzenHtmlEditorBold /><RadzenHtmlEditorItalic /><RadzenHtmlEditorUnderline /><RadzenHtmlEditorStrikeThrough /><RadzenHtmlEditorSeparator /><RadzenHtmlEditorRemoveFormat /></RadzenHtmlEditor>
        </Field>
        <Field>
            <FieldLabel>File upload</FieldLabel>
            <FileEdit @ref="@FileEdit" Changed="@OnChanged" Filter=".pdf" Placeholder="Select or drag and drop multiple files" Multiple />
            <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel><br />
            <FieldLabel Visibility="string.IsNullOrEmpty(FileDisplayName)?Visibility.Invisible:Visibility.Visible">@FileDisplayName</FieldLabel><br />
            <FieldLabel Style="color: grey;margin-bottom: -6px;">Files must be less than <span style="color: black">25 MB</span>.</FieldLabel>
            <FieldLabel Style="color: grey">Allowed file types: <span style="color: black">pdf</span>.</FieldLabel>
        </Field>
    </Container>
    <Container Class="form-newd mt-4">
    <Fields>
        @*<Field>
                <FieldLabel>Moderation notes <Span>*</Span></FieldLabel>
               <Validation Validator="ValidationRule.IsNotEmpty">
                    <MemoEdit @bind-Text="@PolicyDetail.ModerationNote" Rows="3">
                        <Feedback>
                            <ValidationNone>Please enter moderation note</ValidationNone>
                        </Feedback>
                    </MemoEdit>
                    <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivatons.</FieldHelp>
                </Validation>
             </Field>*@
            </Fields>
            </Container>
    <Container Class="mt-4 pb-6">
        <Div Class="stickybottom">
            
            <Fields>
                <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    
                    <AntDesign.SimpleSelect TItem="string"
                                            TItemValue="string"
                                            @bind-Value = "@selectedNextWorkflowStatus"
                                            Placeholder="Select an action to Submit"
                                            OnSelectedItemChanged="e=>OnnReveiw(e)">
                        <SelectOptions>
                            @foreach (var item in RoleBaseWorkFlowLookUps)
                            {
                                <Tooltip ShowArrow=true Placement="TooltipPlacement.RightEnd" Title=@(item.Description)>
                                    <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Text Label=@item.Value />
                                </Tooltip>
                            }
                        </SelectOptions>
                    </AntDesign.SimpleSelect>
                </Field>
                @if (UseridVisible)
            {
                    <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                   @* <FieldLabel>Select User id</FieldLabel>*@

                    <AntDesign.Select 
                        DataSource="@ContributorList"
                        TItemValue="string"
                        Placeholder="Select User Id"
                        TItem="UserModel"
                        LabelName="@nameof(UserModel.DisplayName)"
                        ValueName="@nameof(UserModel.Id)"
                        OnSelectedItemChanged="OnContributorChanged"
                        @bind-Value="@selectedContributorValue"
                        AllowClear
                        EnableSearch
                        Style="width: 100%; margin-bottom: 0px;">
                    </AntDesign.Select>
                    <FieldHelp>Type who you want to send this content</FieldHelp>
                </Field>
            }
                <Field >
                    <Button Class="but-yellow" Clicked="e=> OnSubmit()">Submit</Button>
                </Field>
            </Fields>
        </Div>
    </Container>
</Container>
</Validations>
 
}
