﻿using AntDesign;
using AutoMapper;
using Blazorise.DataGrid;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.Pdf;
using Gina2.Blazor.Pages;
using Gina2.Core;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.DbModels.PolicyDrafts;
using Gina2.Services.Country;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using Newtonsoft.Json.Linq;
using System.Drawing.Design;
using System.Text;
using System.Text.RegularExpressions;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class PolicyDetailTab : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        [Inject]
        public IMapper _mapper { get; set; }

        [Inject]
        FileStateService FileStateService {get; set;}

        [Inject]
        private IPolicyService PolicyService { get; set; }
        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }
        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }

        [Inject]
        private IConfiguration Configuration { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [CascadingParameter(Name = "PolicyCode")]
        public int PolicyCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int VersionId { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }
        // private bool ExportPdfLoading { get; set; }
        private bool ExportCsvLoading { get; set; }

        // private string PolicyCountriesList { get; set; } = string.Empty;
        private string PolicyTypeName { get; set; }
        private List<PartnerDetail> PartnerItems { get; set; } = new List<PartnerDetail>();
        private List<Models.PolicyTopic> Topics { get; set; } = new List<Models.PolicyTopic>();
        private bool isLoading = true;
        // private List<PolicyCategoryPartnerMapItem> PolicyPartnerList { get; set; } = new();
        private List<DbModels.PolicyDrafts.PolicyTopicDraft> PolicyTopicList { get; set; } = new();

        // [Inject]
        // private TimeZoneService TimeZoneService { get; set; }

        public class ListTopics
        {
            public string ParentName { get; set; }
            public List<ListTopics> ChildName { get; set; }
            public bool isSelected { get; set; }
        }

        private List<ListTopics> TopicList = new List<ListTopics>();
        // public class ViewPolicyPublished
        // {
        //     public string Title { get; set; }
        //     public string Paragraph { get; set; }
        // }

        private PolicyRevision PolicyDetail { get; set; } = new();

        private bool revision = false;
        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private IEnumerable<PolicyLog> policyLogInfo = Enumerable.Empty<PolicyLog>();
        // private PolicyAttachment policyAttachment { get; set; } = new();
        private List<int> topicparent { get; set; } = new List<int>();
        private bool EnablePhase2Features = false;
        // public CloudBlobContainer CloudBlobContainer
        // {
        //     get
        //     {
        //         string blobStorageConnectionString = new AppSettingsHelper(Configuration).GetBlobStorageConnectionString();
        //         if (string.IsNullOrWhiteSpace(blobStorageConnectionString))
        //         {
        //             return null;
        //         }

        //         CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
        //         CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
        //         return cloudBlobClient.GetContainerReference(Constants.BlobStorage.DataTypeContainerName);
        //     }
        // }

        protected override void OnInitialized()
        {
            object data = MemoryCache.Get("submit");
            MemoryCache.Remove("submit");
            if (data != null)
            {
                _ = OpenToaster(data.ToString(), "", AntDesign.NotificationType.Success);

            }
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            isLoading = true;
            if (firstRender)
            {
                EnablePhase2Features = Convert.ToBoolean(Configuration["EnablePhase2Features"]);

                if (PolicyService != null && CountryService != null && PolicyService != null)
                {
                    PolicyDetail = await PolicyDraftService.GetPolicyRevisionDetailsAsync(PolicyCode, VersionId);
                    if (PolicyDetail == null)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }
                    if(!string.IsNullOrEmpty(CountryCode))
                    {
                        var IsPolicyBelongsToCountryInURL = PolicyDetail.PolicyCountryDrafts.FirstOrDefault(c=>c.CountryCode == CountryCode)  != null? true: false;
                        if ( !IsPolicyBelongsToCountryInURL)
                        {
                            NavigationManager.NavigateTo("NotFound");
                            return;
                        }
                    }
                    //PolicyDetail.PolicyProgramRevision = await PolicyService.GetProgramPoliciesAsync(PolicyCode);
                    //PolicyDetail.FurtherNotes = PolicyDetail.FurtherNotes.GetLink();
                    //PolicyDetail.Extracts = PolicyDetail.Extracts.GetLink();
                    //PolicyDetail.References = PolicyDetail.References.GetLink();

                    CountryCode = CountryCode != null ? CountryCode : PolicyDetail.PolicyCountryDrafts.FirstOrDefault()?.CountryCode;
                    policyLogInfo = await PolicyDraftService.GetAllPolicyLogByPolicyId(PolicyCode);
                    PolicyTypeName = PolicyDetail.PolicyType?.Name;
                    GetPartnersAsync();
                    await GetTopicsAsync();
                    isLoading = false;
                    await InvokeAsync(StateHasChanged);
                    await JS.InvokeVoidAsync("detailhiddenbg", "policyhiddenbg");
                    _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "policybgdetailhidden");
                }
            }
            isLoading = false;
        }

        //private string GetLink(string content)
        //{
        //    string withoutAnchors = Regex.Replace(content, @"<a[^>]*>.*?<\/a>", "");
        //    string pattern = @"(?<!href=[""'])https:\/\/[^\s'""<>]+|(?<!href=[""'])http:\/\/[^\s'""<>]+";
        //    MatchCollection matches = Regex.Matches(withoutAnchors, pattern);
        //    List<string> checkExist = new List<string>();
        //    foreach (Match match in matches.Distinct().ToList())
        //    {
        //        if (!checkExist.Contains(match.Value))
        //        {
        //            string value = $"<a href=\"{match.Value}\" target=\"_blank\">{match.Value}</a>";
        //            content = content.Replace(match.Value, value);
        //            checkExist.Add(match.Value);
        //        }

        //    }

        //    return content;
        //}

        // private async Task DownLoadPdf(string uri)
        // {
        //     isLoading = true;
        //     string fileName = uri.Replace("public://", ""); 
        //     if (!string.IsNullOrEmpty(fileName))
        //     {
        //         try
        //         {
        //             CloudBlockBlob cloudBlockBlob = CloudBlobContainer.GetBlockBlobReference(fileName);
        //             if (await cloudBlockBlob.ExistsAsync())
        //             {
        //                 await cloudBlockBlob.FetchAttributesAsync();
        //                 byte[] arr = new byte[cloudBlockBlob.Properties.Length];
        //                 await cloudBlockBlob.DownloadToByteArrayAsync(arr, 0);
        //                 await JS.InvokeVoidAsync("BlazorDownloadFile", fileName, "application/pdf", arr);
        //             }
        //             else
        //             {
        //                 await OpenErrorToaster("File not found");
        //             }
        //         }
        //         catch (Exception ex)
        //         {
                    
        //             throw new Exception("Error while downloading the file: {ex}",ex);
        //         }
               
        //     }
        //     isLoading = false;
        // }

        private async Task GetTopicsAsync()
        {
            PolicyTopicList = PolicyDetail.PolicyTopicDrafts.ToList();
            var DbAllTopics = await PolicyService.GetTopicsAsync();
            AllTopics = DbAllTopics;//.Where(a => a.AllowedInPolicy == true).ToList();
            AllParentTopics = await PolicyService.GetParentTopics();
            var First = AllParentTopics.Where(x => x.ParentId.Equals(DbAllTopics.Where(t => t.Name.Equals("Policy")).FirstOrDefault().Id)).OrderBy(t => t.OrderKey);

            await GetSlectedValue();
            await GetTopicTreeView(First);
            var topicChilds = new List<string>();

            foreach (var item in TopicList.Where(w => w.ChildName.Any()))
            {
                foreach (var child in item.ChildName)
                {
                    topicChilds.Add(child.ParentName);
                }
            }
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> First)
        {
            foreach (var item in First)
            {
                if (topicparent.Any(x => x == item.TopicId) || PolicyTopicList.Any(x => x.TopicId == item.TopicId))
                {
                    TopicList.Add(new ListTopics()
                    {
                        ParentName = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        ChildName = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                        isSelected = topicparent.Any(x => x == item.TopicId)
                    });
                }

            }
        }

        private async Task<List<ListTopics>> GetChildTopicTreeView(IEnumerable<TopicParent> First)
        {
            List<ListTopics> child = new List<ListTopics>();
            foreach (var item in First)
            {
                if (topicparent.Any(x => x == item.TopicId) || PolicyTopicList.Any(x => x.TopicId == item.TopicId))
                {
                    ListTopics itemchild = new ListTopics();
                    child.Add(new ListTopics()
                    {
                        ParentName = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        ChildName = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                        isSelected = topicparent.Any(x => x == item.TopicId) ||
                            PolicyTopicList.Any(x => x.TopicId == item.TopicId) ? true : false
                    });
                }

            }
            return child;
        }
        private async Task GetSlectedValue()
        {
            foreach (var item in PolicyTopicList)
            {
                var topic = AllParentTopics.Where(x => x.TopicId == item.TopicId).ToList();
                foreach (var value in topic)
                {
                    if (AllTopics.Any(x => x.Id == value.TopicId))
                    {
                        topicparent.Add(value.ParentId);
                        await GetParentArray(value);
                    }
                }
            }
        }

        private async Task GetParentArray(TopicParent parent)
        {
            var parentId = AllParentTopics.Where(x => x.TopicId == parent.ParentId)?.FirstOrDefault();
            if (parentId != null)
            {
                topicparent.Add(parentId.ParentId);
                await GetParentArray(parentId);
            }
        }

        private void GetPartnersAsync()
        {
            //PolicyPartnerList = PolicyDetail.PolicyCatogeryPartnerMap.ToList();
            List<PartnerDetail> partnerCategoryId = PolicyDetail.PolicyCategoryPartnerDrafts
    .Select(x => new PartnerDetail() { PartnerName = x.PartnerCategory?.Name, PartnerCategoryId = x.PartnerCategoryId, Key = x.PartnerCategory.DragAndDropKey })
    .Concat(PolicyDetail.PolicyPartnerCategoryPartnerDrafts.Select(x => new PartnerDetail() { PartnerName = x.PartnerCategory?.Name, PartnerCategoryId = x.PartnerCategoryId, Key = x.PartnerCategory.DragAndDropKey }))
    .DistinctBy(x => x.PartnerCategoryId)
    .ToList();
            foreach (var item in partnerCategoryId.OrderBy(p => p.Key))
            {
                List<PartnerOrder> partner = new List<PartnerOrder>();

                partner = PolicyDetail.PolicyPartnerCategoryPartnerDrafts.Where(x => x.PartnerCategoryId == item.PartnerCategoryId).Select(x => new PartnerOrder() { Name = x?.Partner?.Name, DragAndDropKey = x.Partner != null ? x.Partner.DragAndDropKey : 0 }).OrderBy(x => x.DragAndDropKey).ToList();
                var details = PolicyDetail.PolicyCategoryPartnerDrafts?.FirstOrDefault(a => a.PartnerCategoryId == item.PartnerCategoryId);
                //partner.Add(new() { Name = partnerName?.Partner?.Name, DragAndDropKey = partnerName != null && partnerName?.Partner != null ? partnerName.Partner.DragAndDropKey : 0 });
                PartnerItems.Add(new PartnerDetail()
                {
                    PartnerName = item.PartnerName,
                    PartnerCategoryId = item.PartnerCategoryId,
                    PartnerData = partner,
                    PartnerDetails = details?.Details ?? String.Empty,
                    Key = item.Key
                });
            }
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }

        private async Task Download()
        {
            ExportCsvLoading = true;
            List<int> policyIds = new List<int>
            {
                PolicyCode
            };
            var search = new GlobalSearchRequest() { DownloadByDataItem = true };
            var data = await FileDownloadService.GetPolicyDataforCSV(search, policyIds);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());
                await JsRuntime.InvokeVoidAsync("saveAsFile", $"Policies.csv", fileData);
            }
            ExportCsvLoading = false;
            StateHasChanged();
        }
//         private async Task DownloadPdf()
//         {
//             ExportPdfLoading = true;
//             var PolicyTitle = _mapper.Map<PolicyPdf>(PolicyDetail);
//             var PolicyData = _mapper.Map<PolicyPdfData>(PolicyDetail);

//             var PolicyExtracts = _mapper.Map<PolicyPdfExtract>(PolicyDetail);
//             var PolicyDetails = _mapper.Map<PolicyPdfDetails>(PolicyDetail);

//             var PolicyDatadictionary = JObject.FromObject(PolicyData).ToObject<Dictionary<string, string>>();
//             var PolicyDetailsdictionary = JObject.FromObject(PolicyDetails).ToObject<Dictionary<string, string>>();
//             StringBuilder htmlData = new StringBuilder();
//             StringBuilder htmlDetails = new StringBuilder();
//             StringBuilder partners = new StringBuilder();
//             StringBuilder topics = new StringBuilder();
//             MarkupString extracts = (MarkupString)PolicyExtracts.PolicyExtractByOriginalLanguage;
//             if (PolicyDatadictionary != null)
//             {
//                 StringBuilder RowData = new StringBuilder();
//                 StringBuilder DataValue = new StringBuilder();
//                 int index = 0;
//                 foreach (var item in PolicyDatadictionary)
//                 {
//                     if (!string.IsNullOrEmpty(item.Value))
//                     {
//                         index++;
//                         RowData.Append($"<td align='left'><b>{item.Key.Replace("_", " ")}</b></td>");
//                         DataValue.Append($"<td align='left'><b>{item.Value}</b></td>");
//                         if (index % 2 == 0 || index == PolicyDatadictionary.Count)
//                         {
//                             htmlData.Append($@" <tr>{RowData}</tr><tr>{DataValue}</tr>");
//                             RowData = new StringBuilder();
//                             DataValue = new StringBuilder();
//                         }
//                     }
//                 }
//             }

//             if (PolicyDetailsdictionary != null)
//             {
//                 foreach (var item in PolicyDetailsdictionary)
//                 {
//                     htmlDetails.Append($@" <tr>
//                                              <td align='left' width:'48%'><b>{item.Key}</b></td>
//                                            </tr>
//                                            <tr>
//                                             <td align='left'>{item.Value}</td>
//                                            </tr>");
//                 }
//             }

//             if (TopicList.Any())
//             {
//                 //topics.Append("<tr><td><table id='topic'><tr><td><b>Topics</b></td></tr>");
//                 foreach (var item in TopicList)
//                 {
//                     topics.Append($"&bull;{item.ParentName}<br />");
//                     if (item.ChildName != null)
//                     {
//                         foreach (var child in item.ChildName)
//                         {
//                             topics.Append($"-{child.ParentName}<br />");
//                         }
//                     }
//                 }
//                 // topics.Append("</table></td></tr>");
//             }

//             if (PartnerItems.Any())
//             {
//                 //  partners.Append($"<tr><td><table id='partner'><tr><td>Parnters Involved</td></tr>");
//                 foreach (var partner in PartnerItems)
//                 {
//                     partners.Append($"&bull;{partner.PartnerName}<br />");
//                     foreach (var partnerData in partner.PartnerData)
//                     {
//                         partners.Append($"-{partnerData.Name}<br />");
//                     }
//                     if (!string.IsNullOrEmpty(partner.PartnerDetails))
//                     {
//                         partners.Append($"Details: {partner.PartnerDetails}<br />");
//                     }
//                 }
//                 // partners.Append("</table></td></tr>");
//             }

//             var html = $@"<html style=""font-family: PTSans;"">
// <body style=""font-family: 'Cairo', sans-serif !important;""><table id='table'>
//              <tr>
//                   <td>
//                       <table id='header'>
//                           <tr>
//                               <td>Global database on the Implementation of Nutrition Action (GIFNA)</td>
//                           </tr>
//                       </table>
//                   </td>
//              </tr>
//              <tr>
//                   <td>
//                       <table id='subheader'>
//                           <tr>
//                               <td>{PolicyTitle.CombinedTitle}</td>
//                           </tr>
//                       </table>
//                       <table id='table2'>
//                         <tr>
//                           <td align='left'>
//                             Country(ies):{string.Join(", ", PolicyDetail.PolicyCountryDrafts.Select(w => w.Country.Name))}
//                           </td>
//                           <td>
//                           </td>
//                         </tr>
//                         {htmlData}
//                         </table>
//                         <table id='table3'>
//                         <tr>{htmlDetails}</tr>
//                       </table>
//                   </td>
//              </tr>
//              <tr>
//                   <td>
//                       <table id='colorheader'>
//                           <tr>
//                               <td align='left'>
//                                   Goals
//                               </td>
//                           <tr>
//                       </table>
//                   </td>
//              </tr>
//              <tr>
//                   <td>
//                     <table id='table4'>
//                           <tr>
//                               <td align='left'>
//                                 {extracts}
//                               </td>
//                           <tr>
//                     </table>
//                   </td>
//              </tr>
//              <tr>
//                   <td>
//                       <table id='table5'>
//                           <tr>
//                             <td>
//                                 <b>URL link:</b><br />
//                                 <p>https://www.humanitarianresponse.info/system/files/documents/files/undaf_gmb_2016_final_0.pdf</p>
//                             </td>
//                           </tr>
//                           <tr>
//                                <td>
//                                   <b>File upload:</b><br />
//                                   <p>GMB 2016 UNDAF.pdf</p>
//                                </td>
//                           </tr>
//                       </table>
//                   </td>
//              </tr>
//              <tr>
//                   <td>
//                       <table id='table6'>
//                           <tr>
//                               <td align='left'><b>Policy topics:</b></td>
//                               <td align='right'><b>Partners in policy implementation</b></td>
//                           </tr>
//                           <tr>
//                               <td align='left'>
//                                 {topics}
//                               </td>
//                               <td align='left'>
//                                 {partners}
//                               </td>
//                           </tr>
//                       </table>
//                   </td>
//              </tr>
//             <tr>
//                   <td>
//                       <table id='footer'>
//                           <tr>
//                               <td>
//                               </td>
//                               <td>
//                                   © World Health Organization <br/>
//                                   2012. All rights reserved.
//                               </td>
//                               <td>
//                                   5 October 2022
//                               </td>
//                               <td>
//                                   {NavigationManager.Uri}
//                               </td>
//                           </tr>
//                        </table>
//                     </td>
//              </tr>
//             </table></body></html>";
//             ExportPdfLoading = false;
//             await JsRuntime.InvokeVoidAsync("saveAsPdf", $"Policy - {PolicyDetail.Id}.pdf", html);


//         }
        
        private string GetPartnersCsv(List<PartnerDetail> partnerDetail)
        {
            StringBuilder partner = new StringBuilder();

            foreach (var item in partnerDetail)
            {
                partner.AppendLine($"<li>{item.PartnerName}</li>");
                //string child = GetPartnersCsv(item.PartnerDetails);
                foreach (var child in item.PartnerDetails)
                {
                    partner.AppendLine($"<li>{child}</li>");
                }
            }

            return partner.ToString();
        }

        private string GetTopicsCsv(List<ListTopics> topicDetails)
        {
            StringBuilder topic = new StringBuilder();

            foreach (var item in topicDetails)
            {
                topic.AppendLine($"<li>{item.ParentName}</li>");
                if (item.ChildName != null)
                {
                    string topicchild = GetTopicsCsv(item.ChildName);
                    topic.AppendLine($"<li>{topicchild}</li>");
                }
            }

            return topic.ToString();
        }

        string selectedTab = "Goals";
        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;

            return Task.CompletedTask;
        }

        private async Task NavigateToPdfPreview(string fileDisplayName, string fileBlobName)
        {
            // Pass fileBlobName to the PdfPreview component (stored in a shared state or service)
            FileStateService.FileBlobName = fileBlobName; // Requires shared state or service
            // Pass the fileDisplayName as part of the URL
            var url = $"/pdf-preview/{Uri.EscapeDataString(fileDisplayName)}";
            // open the link in a new tab
            await JsRuntime.InvokeVoidAsync("window.open", url, "_blank");
        }
        
    }
}
