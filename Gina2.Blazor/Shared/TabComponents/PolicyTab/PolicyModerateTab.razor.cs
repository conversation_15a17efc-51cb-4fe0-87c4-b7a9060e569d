﻿using AntDesign;
using Blazorise;
using Domain.PolicyPartnerCategory;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Pages;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.Services.Commitments;
using Gina2.Services.Dashboard;
using Gina2.Services.Mechanism;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class PolicyModerateTab : PageConfirgurationComponent
    {
        [CascadingParameter(Name = "PolicyCode")] public int PolicyCode { get; set; }
        [CascadingParameter(Name = "CountryCode")] public string CountryCode { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IDashboardService DashboardService { get; set; }
        [Inject]
        private AntDesign.ModalService ModalService { get; set; }

        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }

        [Inject]
        private IMechanismRevisionService MechanismRevisionService { get; set; }

        [Inject]
        private IProgramRevisionService ProgramRevisionService { get; set; }

        [Inject]
        private ICommitmentRevisionService CommitmentRevisionService { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private IPolicyService PolicyService { get; set; }

        private List<ModerationRevisionLog> ModerationLog { get; set; } = new();
        private string containType = "Policies";
        private int lastRevisionId;
        private int RevisionId { get; set; }
        List<ModerationRevisionLog> ModerateActions { get; set; } = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {

                ModerationLog = await DashboardService.GetModerationLogByEntityId(PolicyCode, containType);
                lastRevisionId = ModerationLog.Any() ?  ModerationLog.First().EntityRevisionId : 0;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task OpenConfirm(RenderFragment renderFragment, int entityId, int versionId, string contentType, int logId)
        {
            bool unPublish = false;
            if (renderFragment == UnpublishContent)
            {
                unPublish = true;
            }
            var options = new ConfirmOptions()
            {
                Title = "Confirm your action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await ApplyActions(contentType, entityId, versionId, logId);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertConfirm(ModerationRevisionLog log, RenderFragment renderFragment)
        {
            var options = new ConfirmOptions()
            {
                Title = "Confirm revert action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await RevertAction(log);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }
        private async Task RevertAction(ModerationRevisionLog log)
        {
            await PolicyDraftService.Revert(log.EntityId, log.EntityRevisionId, log.ToState, CurrentUserService.UserName);
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
        }
        private async Task ApplyActions(string contentType, int entityId, int versionId, int logId)
        {
            switch (contentType)
            {
                case "Policies":
                    {
                        var policyRevision = await PolicyDraftService.GetPolicyRevisionToCreateAdraftByVersionIdAsync(entityId, versionId);
                        policyRevision.PolicyLog.Clear();
                        policyRevision.PolicyLog.Add(new PolicyLog
                        {
                            PolicyId = policyRevision.Id,
                            PolicyVId = policyRevision.VersionId,
                            FromState = WorkflowStatusToState.Published,
                            ToState = WorkflowStatusToState.Draft,
                            RevisedDate = DateTimeOffset.UtcNow,
                            UserName = CurrentUserService.UserName,
                        });
                        await PolicyDraftService.UnpublishPolicy(policyRevision);
                        await DashboardService.UnpublishModerationLogByLogId(logId, containType);
                        await PolicyService.RemoveByIdAsync(entityId);

                        break;
                    }

                default:
                    throw new Exception($"Moderation is not implemented for, {contentType}");

                //case "Mechanisms":
                //    {
                //        var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(entityId, versionId);
                //        mechanismRevision.MechanismLog.Clear();
                //        mechanismRevision.MechanismLog.Add(new MechanismLog
                //        {
                //            MechanismId = mechanismRevision.Id,
                //            MechanismVId = mechanismRevision.VersionId,
                //            FromState = WorkflowStatusToState.Published,
                //            ToState = WorkflowStatusToState.Draft,
                //            RevisedDate = DateTimeOffset.UtcNow,
                //            UserName = CurrentUserService.UserName,
                //        });
                //        await MechanismRevisionService.CreateMechanismRevision(mechanismRevision, true);
                //        break;
                //    }

                //case "Actions":
                //    {
                //        var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsAsync(entityId, versionId);
                //        programRevision.ProgramLog.Clear();
                //        programRevision.ProgramLog.Add(new ProgramLog
                //        {
                //            ProgramId = programRevision.Id,
                //            ProgramVId = programRevision.VersionId,
                //            FromState = WorkflowStatusToState.Published,
                //            ToState = WorkflowStatusToState.Draft,
                //            RevisedDate = DateTimeOffset.UtcNow,
                //            UserName = CurrentUserService.UserName,
                //        });
                //        var actionRevision = programRevision.ActionProgramRevisionMap.Select(s => s.ActionRevision).ToList();
                //        await ProgramRevisionService.CreateProgramRevision(programRevision, actionRevision, true);
                //        break;
                //    }

                //case "Smart commitments":
                //    {
                //        var commitmentRevision = await CommitmentRevisionService.GetCommitmentRevisionAsync(entityId, versionId);
                //        commitmentRevision.CommitmentLog.Clear();
                //        commitmentRevision.CommitmentLog.Add(new CommitmentLog
                //        {
                //            CommitmentId = commitmentRevision.Id,
                //            CommitmentVId = commitmentRevision.VersionId,
                //            FromState = WorkflowStatusToState.Published,
                //            ToState = WorkflowStatusToState.Draft,
                //            RevisedDate = DateTimeOffset.UtcNow,
                //            UserName = CurrentUserService.UserName,
                //        });
                //        var smartCommitmentRevision = commitmentRevision.SmartCommitmentCommitmentRevisionMap.Select(s => s.SmartCommitmentRevision).ToList();
                //        await CommitmentRevisionService.SaveCommitmentRevision(commitmentRevision, smartCommitmentRevision, true);
                //        break;
                //    }
            }
            _ = OpenSuccessToaster("Unpublished successfully");
            NavigationManager.NavigateTo("admin/dashboard");
        }

        public async void NavigateToDraftEdit(int entityId, int revisionId)
        {

            switch (containType.ToLower())
            {
                case "policies":
                    {
                        var policyRevison = await PolicyDraftService.GetPolicyRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{policyRevison.PolicyCountryDrafts.First().CountryCode}/policies/{entityId}/{revisionId}/Edit");
                        break;
                    }

                case "mechanisms":
                    {
                        var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{mechanismRevision.MechanismCountryMapRevision.First().CountryCode}/mechanisms/{entityId}/{revisionId}/Edit");
                        break;
                    }

                case "actions":
                    {
                        var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{programRevision.ProgrammeCountryMapRevision.First().CountryCode}/programmes-and-actions/{entityId}/{revisionId}/Edit");
                        break;
                    }

                case "smart commitments":
                    {
                        var commitmentRevision = await CommitmentRevisionService.GetCommitmentRevisionAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{commitmentRevision.CommitmentCountryMapRevision.First().CountryCode}/commitments/{entityId}/{revisionId}/Edit");
                        break;
                    }
            }
        }
        public async void NavigateToDraft(int entityId, int revisionId)
        {

            switch (containType.ToLower())
            {
                case "policies":
                    {
                        var policyRevison = await PolicyDraftService.GetPolicyRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{policyRevison.PolicyCountryDrafts.First().CountryCode}/policies/{entityId}/{revisionId}");
                        break;
                    }

                case "mechanisms":
                    {
                        var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{mechanismRevision.MechanismCountryMapRevision.First().CountryCode}/mechanisms/{entityId}/{revisionId}");
                        break;
                    }

                case "actions":
                    {
                        var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{programRevision.ProgrammeCountryMapRevision.First().CountryCode}/programmes-and-actions/{entityId}/{revisionId}");
                        break;
                    }

                case "smart commitments":
                    {
                        var commitmentRevision = await CommitmentRevisionService.GetCommitmentRevisionAsync(entityId, revisionId);
                        NavigationManager.NavigateTo($"countries/{commitmentRevision.CommitmentCountryMapRevision.First().CountryCode}/commitments/{entityId}/{revisionId}/Edit");
                        break;
                    }
            }
        }
    }
}
