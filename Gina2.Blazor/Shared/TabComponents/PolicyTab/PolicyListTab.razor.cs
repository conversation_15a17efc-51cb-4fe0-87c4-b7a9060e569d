﻿using Domain.PolicyTypes;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class PolicyListTab
    {
        [Inject]
        private IPolicyService PolicyService { get; set; }        

        [Parameter]
        public EventCallback<(string, int)> OnChangingTab { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "CountryName")]
        public string CountryName { get; set; }        
        private IEnumerable<Domain.Policy.Policy> AllPolicyNames { get; set; }
        private IEnumerable<PolicyType> policyTypes { get; set; }        

        private string selectedTab = "viewallpolicy";
        protected override async Task OnInitializedAsync()
        {
            policyTypes = await PolicyService.GetAllPolicyTypes();
        }

        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;

            return Task.CompletedTask;
        }
    }
}
