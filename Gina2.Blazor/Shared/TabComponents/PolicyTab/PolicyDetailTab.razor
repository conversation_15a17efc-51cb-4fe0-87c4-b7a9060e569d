﻿@using System.IO
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using Microsoft.WindowsAzure.Storage
@using Microsoft.WindowsAzure.Storage.Blob
@using Gina2.DbModels.PolicyDrafts
@using Gina2.Core.Methods;
@inject IJSRuntime JS
@inherits PageConfirgurationComponent;
<Loader IsLoading="@isLoading" />

<Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
    <Div Class="item1">
        <Button hidden="@(CountryCode != null ? false : true)" data-cy="BackToPolicies" Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/policies"))">
            <Icon data-cy="BackToPoliciesIcon" Class="fas fa-chevron-left"></Icon> Back to Policies
        </Button>
    </Div>
    <Div Class="item2">
        <Button Loading="@ExportCsvLoading" Clicked="@Download" data-cy="CSVBtn" Class="but-yellow"><Icon class="arrow-bottom" /> CSV</Button>
        @if (EnablePhase2Features)
        {
            <AuthorizeView>
                <Authorized>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem target="_blank" href="@(VersionId != 0 ? $"/countries/{CountryCode}/policies/{PolicyCode}/{VersionId}/edit" : $"/countries/{CountryCode}/policies/{PolicyCode}/edit")">
                                Edit
                            </DropdownItem>
                            @if (EnablePhase2Features)
                            {
                                <DropdownItem target="_blank" href="@($"/countries/{CountryCode}/policies/{PolicyCode}/moderate")">
                                    Moderate
                                </DropdownItem>
                            }
                        </DropdownMenu>
                    </Dropdown>
                </Authorized>
            </AuthorizeView>
        }

    </Div>


</Div>

<Heading data-cy="PolicyTitle" Class="Headingtab alltab_D_h3" Size="HeadingSize.Is3"> Policy - @PolicyDetail?.CombinedTitle</Heading>
<Divider />
<Div>
    <div class="_BGDetail" id="policybgdetailhidden">
        <ListGroup Class="list-ta mt-1 pb-4">


            <ListGroupItem>
                @if (!string.IsNullOrEmpty(PolicyDetail.StartMonth) || PolicyDetail.StartYear.HasValue)
                {
                    <span data-cy="DateText" class="titelspan">Start date: </span>
                    <span data-cy="DatePara" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(PolicyDetail.StartMonth != null ? int.Parse(PolicyDetail.StartMonth) : null, PolicyDetail.StartYear)</span>

                }

            </ListGroupItem>
            <ListGroupItem>

                @if (!string.IsNullOrEmpty(PolicyDetail.EndMonth) || PolicyDetail.EndYear.HasValue)
                {
                    <span data-cy="EndDate" class="titelspan">End date: </span>
                    <span data-cy="EndDatePara" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(PolicyDetail.EndMonth != null ? int.Parse(PolicyDetail.EndMonth) : null, PolicyDetail.EndYear)</span>
                }
            </ListGroupItem>

            <ListGroupItem>

                @if (!string.IsNullOrEmpty(PolicyDetail.PublishedBy))
                {
                    <span data-cy="PublishedBy" class="titelspan">Published by: </span>
                    <span data-cy="PublishedByPara" class="paragr"> @PolicyDetail.PublishedBy</span>
                }
            </ListGroupItem>

            <ListGroupItem>

                @if (!string.IsNullOrEmpty(PolicyDetail.PublishedMonth) || PolicyDetail.PublishedYear.HasValue)
                {
                    <span data-cy="PublishedYear" class="titelspan">Published date: </span>
                    <span class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(!string.IsNullOrEmpty(PolicyDetail.PublishedMonth) ? int.Parse(PolicyDetail.PublishedMonth) : null, PolicyDetail.PublishedYear)</span>
                }
            </ListGroupItem>



            @*@if (PolicyDetail.Adopted.HasValue)
            {
                <ListGroupItem>
                    <span data-cy="policydocument" class="titelspan">Is the policy document adopted?: </span>
                    <span data-cy="policydocumentyesno" class="paragr"> @(PolicyDetail.Adopted == true ? "Yes" : "No / No information")</span>
                </ListGroupItem>


            }*@
            @if (PolicyDetail.Adopted.HasValue && PolicyDetail.Adopted == true)
            {
                <ListGroupItem>

                    @if (!string.IsNullOrEmpty(PolicyDetail.AdoptedBy))
                    {

                        <span data-cy="AdoptedBy" class="titelspan">Adopted by: </span>
                        <span data-cy="AdoptedByPara" class="paragr"> @PolicyDetail.AdoptedBy</span>
                    }
                </ListGroupItem>

                <ListGroupItem>

                    @if (!string.IsNullOrEmpty(PolicyDetail.AdoptedMonth) || PolicyDetail.AdoptedYear.HasValue)
                    {
                        <span data-cy="AdoptedYear" class="titelspan">Adopted date: </span>
                        <span data-cy="AdoptedYearPara" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(PolicyDetail.AdoptedMonth != null ? int.Parse(PolicyDetail.AdoptedMonth) : null, PolicyDetail.AdoptedYear)</span>
                    }
                </ListGroupItem>


            }
            <ListGroupItem>

                @if (!string.IsNullOrEmpty(PolicyTypeName))
                {
                    <span data-cy="TypeOfPolicy" class="titelspan">Type of policy: </span>
                    <span data-cy="TypeOfPolicyPara" class="paragr">@PolicyTypeName </span>
                }
            </ListGroupItem>

            @if (!string.IsNullOrEmpty(PolicyDetail.SubnationalGeographicArea))
            {
                <ListGroupItem>
                    <span data-cy="Province/Region" class="titelspan">Province/Region: </span>
                    <span data-cy="Province/RegionPara" class="paragr"> @PolicyDetail.SubnationalGeographicArea </span>
                </ListGroupItem>
            }
        </ListGroup>
    </div>

    <Layout Sider Class="search-box pt-2 pb-5 mob-layout">

        <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel extractshed">
                <Div Class="revisiontitle">
                    <Heading Size="HeadingSize.Is3" Class="extracts" data-cy="ExtractsHeading">Policy extracts</Heading>
                </Div>
                @*<Div Class="_listExyra">
                    @if (!string.IsNullOrEmpty(PolicyDetail.Goals))
                    {
                        MarkupString marukupText = ((MarkupString)PolicyDetail.Goals);
                        <ReadMoreOrLessMarukup Content="@marukupText" TruncateLength="100" />
                    }
                </Div>*@
                <Div Class="_listExyra">

                    @if (!string.IsNullOrEmpty(PolicyDetail.Extracts))
                    {
                        MarkupString marukupText = ((MarkupString)PolicyDetail.Extracts.Trim().GetLink());
                        <ReadMoreOrLessMarukup Content="@marukupText" TruncateLength="5000" />
                    }
                </Div>
                <ListGroup Class="list-view mt-3 _policybox" id="policyhiddenbg">
                    @if (!string.IsNullOrEmpty(PolicyDetail.Url))
                    {
                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">URL link: </Heading>
                            <Div Class="_policyright"><a href="@PolicyDetail.Url" target="_blank" rel="noopener noreferrer">@PolicyDetail.Url</a></Div>
                        </ListGroupItem>
                    }

                    @if (PolicyDetail.PolicyAttachmentDrafts != null && PolicyDetail.PolicyAttachmentDrafts.Any())
                    {
                        <ListGroupItem hidden="@(!PolicyDetail.PolicyAttachmentDrafts.Any())">
                            <Heading Size="HeadingSize.Is5" data-cy="FileUploadHeading">File upload: </Heading>
                            <Div Class="_policyright">
                                @foreach (var item in PolicyDetail.PolicyAttachmentDrafts)
                                {
                                    @if (item.FileDisplayName != null)
                                    {
                                        <a href="javascript:void(0);" @onclick="() => NavigateToPdfPreview(item.FileDisplayName, item.Url)">
                                            <img src="img/picture_as_pdf.png" class="pr-1" />
                                            @item.FileDisplayName
                                        </a>
                                        <br />
                                    }

                                }
                            </Div>
                        </ListGroupItem>

                    }

                    @if (!string.IsNullOrEmpty(PolicyDetail.References))
                    {
                        <ListGroupItem hidden="@(string.IsNullOrEmpty(PolicyDetail.References))">
                            <Heading data-cy="ReferenceHeading" Size="HeadingSize.Is5">Reference:  </Heading>
                            <Div Class="_furtherNotes _policyright description-text">
                                @((MarkupString)PolicyDetail.References.GetLink())
                            </Div>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(PolicyDetail.FurtherNotes))
                    {
                        <ListGroupItem hidden="@(string.IsNullOrEmpty(PolicyDetail.FurtherNotes))">
                            <Heading Size="HeadingSize.Is5">Further notes:  </Heading>
                            <Div Class="_furtherNotes _policyright description-text">
                                @((MarkupString)PolicyDetail.FurtherNotes.GetLink())
                            </Div>
                        </ListGroupItem>
                    }


                </ListGroup>
                <AuthorizeView Roles="Admin">
                    <Div>
                        <Accordion Class="accor-fbox">
                            <Collapse Visible="@revision">
                                <CollapseHeader>
                                    <Heading Class="head-but mt-4 m-0 d-flex aling-items-center" Size="HeadingSize.Is4">
                                    <Span Class="revisiontitle">Revision log</Span>
                                        <Button Clicked="@(()=>revision = !revision)"></Button>
                                    </Heading>
                                </CollapseHeader>
                                <CollapseBody Class="tab-0">
                                    <Div Class="DataGrids">
                                        <DataGrid Class="table-nth _actions"
                                                  TItem="@PolicyLog"
                                                  Data="@policyLogInfo"
                                                  PageSize="5"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <Div>No data found.</Div>
                                            </EmptyTemplate>
                                            <DataGridColumns>

                                                <DataGridColumn Caption="Date" TextAlignment="TextAlignment.Start"
                                                                Width="40%">
                                                    <DisplayTemplate Context="displayContext">
                                                        @displayContext.RevisedDate.GetDayWithFormatedDate()
                                                    </DisplayTemplate>
                                                </DataGridColumn>

                                                <DataGridColumn Field="@nameof(PolicyLog.UserName)" Caption="User" Width="19%" />
                                                <DataGridColumn Field="@nameof(PolicyLog.OtherNotes)" Caption="Log" Width="22%" />
                                                <DataGridColumn Field="@nameof(PolicyLog.ToState)" Caption="State" Width="19%" />
                                            </DataGridColumns>
                                        </DataGrid>
                                    </Div>
                                </CollapseBody>
                            </Collapse>
                        </Accordion>
                    </Div>
                </AuthorizeView>
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>

                @*PageSideBar*@
                <Div Class="accordion-Search" id="accordionExample">
                    <div class="accordion">
                        <button data-cy="CountryBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            Country(ies):
                        </button>
                        <div id="collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body _siderdetail_tab">
                                <Div Class="downl-flex cunt-box _flex_countr">
                                    @if (PolicyDetail.PolicyCountryDrafts != null && PolicyDetail.PolicyCountryDrafts.Any())
                                    {
                                        foreach (var item in PolicyDetail.PolicyCountryDrafts.OrderBy(p => p.Country.Name))
                                        {
                                            <a target="_blank" href='/countries/@item.CountryCode/policies'>@item.Country.Name</a>
                                        }
                                    }
                                </Div>
                            </div>
                        </div>
                        @if (PartnerItems != null && PartnerItems.Count > 0)
                        {
                            <button data-cy="PartnersPolicyBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                Partners in policy implementation
                            </button>
                            <div id="collapse2" class="accordion-collapse collapse show">
                                <div class="accordion-body p-0 _padding_t-b8 ">
                                    <Bar Class="Verticalbar _itemalinebar" Mode="BarMode.VerticalInline"
                                         CollapseMode="BarCollapseMode.Small">
                                        <BarMenu>
                                            <BarStart>
                                                <Repeater Items="@PartnerItems" Context="partner">
                                                    <BarItem>
                                                        <BarDropdown>
                                                            <BarDropdownToggle>
                                                                @partner.PartnerName
                                                            </BarDropdownToggle>
                                                            <BarDropdownMenu>
                                                                <Repeater Items="partner.PartnerData.OrderBy(p => p.DragAndDropKey)" Context="partnerData">
                                                                    <BarDropdownItem>@partnerData.Name</BarDropdownItem>
                                                                </Repeater>
                                                                <BarDropdown>
                                                                    @if (!string.IsNullOrEmpty(partner.PartnerDetails))
                                                                    {
                                                                        <BarDropdownToggle Class="_Moredetails">
                                                                            More details
                                                                        </BarDropdownToggle>
                                                                        <BarDropdownMenu Class="_PartnerDetails">
                                                                            <BarDropdownItem>@((MarkupString)@partner.PartnerDetails)</BarDropdownItem>
                                                                        </BarDropdownMenu>
                                                                    }
                                                                </BarDropdown>
                                                            </BarDropdownMenu>

                                                        </BarDropdown>
                                                    </BarItem>
                                                </Repeater>
                                            </BarStart>
                                        </BarMenu>
                                    </Bar>
                                </div>
                            </div>
                        }
                        @if (TopicList != null && TopicList.Count > 0)
                        {
                            <button data-cy="TopicsBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                Policy topics
                            </button>
                            <div id="collapse3" class="accordion-collapse collapse show" aria-labelledby="headingOne">
                                <div class="accordion-body _itemalinebar p-0  _padding_t-b8">

                                    <!-- started there code-->
                                    <AntDesign.Tree ShowIcon
                                                    MatchedClass="site-tree-search-value"
                                                    DataSource="TopicList"
                                                    TitleExpression="x => x.DataItem.ParentName"
                                                    ChildrenExpression="x =>  x.DataItem.ChildName"
                                                    DefaultExpandAll=true />
                                </div>

                            </div>
                        }
                        @if (PolicyDetail.PolicyProgramRevision != null && PolicyDetail.PolicyProgramRevision.Any())
                        {
                            <button data-cy="LinkToActionsBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                Link to action(s):
                            </button>
                            <div id="collapse4" class="accordion-collapse collapse show" aria-labelledby="headingOne">
                                <div class="accordion-body _siderdetail_tab _linktoaction">
                                    <Repeater Items="@PolicyDetail.PolicyProgramRevision">
                                        <a target="_blank" href="@($"countries/{context.Program.ProgrammeCountryMap.FirstOrDefault().CountryCode}/programmes-and-actions/{context.Program.Actions.FirstOrDefault(e => e.ProgramId == context.ProgramId).Id}")" class="_allpad-17">@context.Program.Title</a>
                                    </Repeater>
                                </div>
                            </div>
                        }
                    </div>
                </Div>
            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Div>