﻿using Blazorise;
using Blazorise.Snackbar;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class PolicyNewDraftTab
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        string selectedTab = "Tab1";
        [CascadingParameter(Name = "PolicyCode")] public int PolicyCode { get; set; }
        [CascadingParameter(Name = "CountryCode")] public string CountryCode { get; set; }

        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;

            return Task.CompletedTask;
        }

        Task OnChanged(FileChangedEventArgs e)
        {
            return Task.CompletedTask;
        }
        Snackbar snackbar;
        ObservableCollection<string> ItemData { get; set; } = new ObservableCollection<string>() {
            "Cabinet/Presidency",
            "Food and agriculture",
            "Social welfare",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text"
        };

        ObservableCollection<string> TypeOfPolicyData { get; set; } = new ObservableCollection<string>() {
            "Select Type",
            "Comprehensive national nutrition policy, strategy or plan",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };

        ObservableCollection<string> LanguageData { get; set; } = new ObservableCollection<string>() {
            "Select Language",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };
        ObservableCollection<string> Countries { get; set; } = new ObservableCollection<string>() {
            "Select Countries",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };

        ObservableCollection<string> ListOfActionRecords { get; set; } = new ObservableCollection<string>() {
            "Number of children below 6 months who are exclusively breastfedChild deaths",
            "A roda dos alimentos [Food wheel guide ]",
            "A2Z: The USAID Micronutrient and Child Blindness",
            "A2Z: The USAID Micronutrient and Child Blindness Project",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Diapaga (2012)",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Danané",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Diapaga",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Fada N’Gourma",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Toulepleu"
        };

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }
    }
}
