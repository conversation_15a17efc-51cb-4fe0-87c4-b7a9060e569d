﻿<Container>
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
        <Div Class="item1 flex-b">
            <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/policies"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to Policies
            </Button>
            @*<Div><Button Class="back"><Icon Class="fas fa-chevron-left" Clicked=@(() => OnChangingTab.InvokeAsync(("Policy", PolicyCode)))></Icon></Button></Div> <Heading Size="HeadingSize.Is3">New Draft Policy</Heading>*@

        </Div>
        <Div Class="item2">
            <Button Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
            <AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot">
            <DropdownToggle Color="Color.Primary" Split />
            <DropdownMenu>
                            <DropdownItem href="@($"/policies/{PolicyCode}")">View published</DropdownItem>
                            <DropdownItem href="@($"/policies/{PolicyCode}/moderate")">Moderate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
        </Div>
    </Div>
    @*<Breadcrumb Class="bread-crumb pl-2">
        <BreadcrumbItem>
            <BreadcrumbLink To="#">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
            <BreadcrumbLink To="policies">New Policy</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem Active>
            <BreadcrumbLink To="#">New Policy</BreadcrumbLink>
        </BreadcrumbItem>
    </Breadcrumb>*@



    <Container Class="newdraft" Padding="Padding.Is0">
        <Container Padding="Padding.Is0" Class="pt-3 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new Policy</Heading>
            <Divider Class="divi-blue" />
        </Container>

        <Container Class="form-newd">
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Title<Span>*</Span></FieldLabel>
                    <TextEdit Placeholder="Enter Title Here"></TextEdit>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Type of Policy</FieldLabel>
                    <Select TValue="int" class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                        <FieldHelp>Read more about Policy document types used in GIFNA.</FieldHelp>
                    </Select>
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Language</FieldLabel>
                    <Select TValue="int">
                        <Repeater Items="@LanguageData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                    </Select>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Countries</FieldLabel>
                    <Select TValue="int">
                        <Repeater Items="@Countries">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                    </Select>
                </Field>
            </Fields>
            <Field>
                <FieldLabel>Province/Region</FieldLabel>
                <RextEditors />
                <FieldHelp>Please specify geographical area in case of sub-national Policy</FieldHelp>
            </Field>
            <Fields>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel>Start date</FieldLabel>
                    <GinaDate />
                </Field>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel>End date</FieldLabel>
                    <GinaDate />
                </Field>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel>Published Date</FieldLabel>
                    <GinaDate />
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Published by</FieldLabel>
                    <TextEdit Placeholder="Enter title here..."></TextEdit>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Is the Policy document adopted?</FieldLabel>
                    <RadioGroup Class="text-med" TValue="string" Name="colors">
                        <Radio Value="@("na")">N/A</Radio>
                        <Radio Value="@("yes")">Yes</Radio>
                        <Radio Value="@("infrom")">No / No information</Radio>
                    </RadioGroup>
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Adopted Date</FieldLabel>
                    <GinaDate />
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Adopted by</FieldLabel>
                    <TextEdit Placeholder="Enter title here..."></TextEdit>
                </Field>
            </Fields>
            <Field>
                <FieldLabel>Partners Involved</FieldLabel>
            </Field>
            <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
                <Items>
                    <Tab Name="Tab1">Government</Tab>
                    <Tab Name="Tab2">Bilateral and d...</Tab>
                    <Tab Name="Tab3">UN agencies</Tab>
                    <Tab Name="Tab4">International N...</Tab>
                    <Tab Name="Tab5">Intergovernm...</Tab>
                    <Tab Name="Tab6">National NG...</Tab>
                    <Tab Name="Tab7">Research / A...</Tab>
                    <Tab Name="Tab8">Private Sector</Tab>
                    <Tab Name="Tab9">Other</Tab>
                </Items>
                <Content>
                    <TabPanel Name="Tab1">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the government sector(s) involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab2">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate bilateral agencies and donors involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab3">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the UN agencies involved in implementation of the policy</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab4">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab5">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab6">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab7">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate Research / Academia involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab8">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the private sectors involved in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                    <TabPanel Name="Tab9">
                        <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate any other information necessary in implementation</Heading>
                        <ListGroup Class="ulgroup">
                            <Repeater Items="@ItemData">
                                <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                            </Repeater>
                        </ListGroup>
                        <Field>
                            <FieldLabel>Government detail(s)</FieldLabel>
                            <RextEditors />
                        </Field>

                    </TabPanel>
                </Content>
            </Tabs>
        </Container>

        <Container Class="form-newd mt-4">

            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Goals, objectives or targets related to nutrition</FieldLabel>
                        <FieldHelp>Please list the goals, objectives or targets related to nutrition in the Policy document, if any.</FieldHelp>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>

                <RextEditors />
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Strategies and activities related to nutrition</FieldLabel>
                        <FieldHelp>Please list strategies, activities or interventions related to nutrition that are proposed in the policy document, if any.</FieldHelp>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>M&E Indicators related to nutrition</FieldLabel>
                        <FieldHelp>Please list the monitoring and evaluation indicators related to nutrition in the Policy document, if any.</FieldHelp>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Legislation details</FieldLabel>
                        <FieldHelp>If the Policy document is a legislation (e.g. law, regulations), please list the relevant details that are related to nutrition.</FieldHelp>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <FieldLabel>M&E Indicator types</FieldLabel>
            <Field Class="gina-new">

                <Check TValue="bool">Outcome indicators</Check>
                <Check TValue="bool">Process indicators</Check>
            </Field>
        </Container>
        <Container Class="mt-4 w-98 mo-m-0">
            <Row>
                <Column Class="form-newd mr-1 mo-mr-0 w-100 Topicspolicy">
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div><FieldLabel>Topics</FieldLabel></Div>
                        <Div>
                            
                           <Button Class="but-gray">Expand All</Button>
                           <Button Class="but-gray">Collapse All</Button>
                           <Button Class="but-gray">Clear</Button>
                        </Div>
                            
                            
                    </Div>
                    <Divider Padding="Padding.Is0" Class="m-0" />
                    <GinaTreeView />
                </Column>
                <Column Class="form-newd gina-new ml-1 w-100">
                    <FieldLabel>Link to existing action records</FieldLabel>
                    <Divider />
                    <Addons Class="pb-3">
                        <Addon AddonType="AddonType.Start">
                            <Button Color="Color.Light">
                                <Icon Name="IconName.Search" />
                            </Button>
                        </Addon>
                        <Addon AddonType="AddonType.Body">
                            <TextEdit Placeholder="Some text value..." />
                        </Addon>
                    </Addons>
                    <Repeater Items="@ListOfActionRecords">
                        <Check TValue="bool">@context</Check>
                    </Repeater>
                </Column>
            </Row>
        </Container>

        <Container Class="form-newd mt-4">
            <Field>
                <FieldLabel>URL link</FieldLabel>
                <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
                <TextEdit Placeholder="Enter title here..."></TextEdit>
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Further notes</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>

                <RextEditors />
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Reference</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                        <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <Field>
                <FieldLabel>File upload</FieldLabel>
                <FileEdit Changed="@OnChanged" Placeholder="Select or drag and drop multiple files" Multiple />
                @*<Div>
                    <img src="img/svg/dow.svg" />
                    Select or drag and drop multiple files
                    </Div>*@
            </Field>
        </Container>
        <Container Class="mt-4 pb-6">
            <Div Class="stickybottom">
            <Button Class="but-yellow" Clicked="@(()=>snackbar.Show())">Save Draft</Button>
             <Button Class="but-by-yellow" >Submit</Button>
            <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
                <SnackbarBody>
                    New content: Your draft will be placed in moderation.
                </SnackbarBody>
            </Snackbar>
            </Div>
        </Container>
    </Container>
</Container>
