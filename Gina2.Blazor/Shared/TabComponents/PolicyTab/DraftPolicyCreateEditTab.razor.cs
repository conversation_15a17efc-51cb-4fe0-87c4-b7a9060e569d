﻿using AntDesign;
using Blazorise;
using Blazorise.Snackbar;
using Domain.PolicyTopics;
using Domain.PolicyTypes;
using Domain.Programme;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Interface;
using Gina2.Core.Lookups;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.PolicyDrafts;
using Gina2.Services.Country;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class DraftPolicyCreateEditTab
    {
        private bool IsLoading = false;

        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        AntDesign.IMessageService Message { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private UserManager<ApplicationUser> UserManager { get; set; }
        [Parameter]
        public PolicyRevision PolicyDetail { get; set; } = new();
        [Parameter]
        public PolicyCountryMapItem PolicyCountry { get; set; } = new();
        [Inject]
        private IPolicyService PolicyService { get; set; }
        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }
        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private IProgrammeService ProgramService { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }
        [CascadingParameter(Name = "PolicyCode")] public int PolicyCode { get; set; }

        private Blazorise.Validations PolicyTypeValidation;
        public int NewPolicyRevisionId { get; set; }
        private long ImageSize { get; set; } = 25000000;
        private string BlobFileName { get; set; } = string.Empty;
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new List<RoleBaseWorkFlowLookUp>();
        private string ImageError { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }
        private ObservableCollection<PolicyTypeViewModel> PolicyTypes { get; set; } = new ObservableCollection<PolicyTypeViewModel>();
        private List<PolicyAttachmentDraft> PolicyAttachments { get; set; } = new List<PolicyAttachmentDraft>();
        private ObservableCollection<Domain.Languages.Language> Languages { get; set; } = new ObservableCollection<Domain.Languages.Language>();
        private ObservableCollection<DbModels.Country> Countries { get; set; } = new ObservableCollection<DbModels.Country>();
        private ObservableCollection<PartnerCategory> PartnerCategoryPartners { get; set; } = new ObservableCollection<PartnerCategory>();
        private IEnumerable<string> countryValues;
        private List<UserModel> ContributorList { get; set; } = new List<UserModel>();
        private Blazorise.FileEdit FileEdit { get; set; }
        private string ImageType { get; set; }
        private string selectedTab = "Tab1";
        private string FileDisplayName { get; set; }
        private byte[] ImageBytes { get; set; }
        public List<Domain.Programme.ProgramViewModel> ListOfActionRecords { get; set; } = new List<Domain.Programme.ProgramViewModel>();
        public List<Domain.Programme.ProgramViewModel> DuplicateListOfActionRecords { get; set; } = new List<Domain.Programme.ProgramViewModel>();
        public List<string> ProgramList { get; set; } = new List<string>();
        public List<string> DuplicateProgramList { get; set; } = new List<string>();
        public List<PolicyCategoryPartnerDraft> PolicyPartnerCategoryDetailsList { get; set; } = new();
        public List<PolicyCategoryPartnerDraft> DuplicatePolicyPartnerCategoryDetailsList { get; set; } = new();

        public List<PolicyPartnerCategoryPartnerDraft> PolicyPartnerCategoryPartnerList { get; set; } = new();
        public List<PolicyPartnerCategoryPartnerDraft> DuplicatePolicyPartnerCategoryPartnerList { get; set; } = new();
        public List<PolicyTopicDraft> PolicyTopicList { get; set; } = new List<PolicyTopicDraft>();
        public List<PolicyTopicDraft> DuplicatePolicyTopicList { get; set; } = new List<PolicyTopicDraft>();
        private List<GTreeNode> TopicList = new ();
        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private string policyGoals = string.Empty;
        private bool isEndDate = true;
        private bool isStartDate = true;
        private DateTime? PublishedDate;
        private DateTime? AdoptedDate;
        public string AdoptedCheckedValue { get; set; } = "infrom";
        public string GovernmentDetails { get; set; }
        public int? AdoptedMonth { get; set; }
        private DateTime? StartDate;
        private DateTime? EndDate;
        private string[] topicKeys;
        private string selectedNextWorkflowStatus;
        [Parameter] public string[] SelectedIds { get; set; }
        Tree<GTreeNode> topicTree;
        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;
            PolicyPartnerCategory policyPartnerFirst = PolicyPartnerCategoryDetailsList.Where(z => z.PartnerCategoryId == Int32.Parse(selectedTab)).Select(a => new PolicyPartnerCategory { PartnerCategoryId = a.PartnerCategoryId, Details = a.Details }).FirstOrDefault();
            if (policyPartnerFirst != null)
            {
                GovernmentDetails = policyPartnerFirst.Details;
            }
            else
            {
                GovernmentDetails = String.Empty;
            }
            return Task.CompletedTask;
        }

        private void LinkToActionCheckboxClicked(int programId, bool checkedValue)
        {
            if ((bool)checkedValue)
            {
                if (!ProgramList.Contains(programId.ToString()))
                {
                    ProgramList.Add(programId.ToString());
                }
            }
            else
            {
                if (ProgramList.Contains(programId.ToString()))
                {
                    ProgramList.Remove(programId.ToString());
                }
            }
            canClearActionLinks = ProgramList.Count > 0;
        }

        string selectedContributorValue;

         protected override async Task OnInitializedAsync()
        {
            IsLoading = true;
            RoleBaseWorkFlowLookUps = RoleBaseWorkFlowLookUp.RoleBaseWorkFlowLookUps(CurrentUserService.UserRole);
            PolicyDetail = await PolicyDraftService.GetAllPolicyDetailsByPolicyIdAsync(PolicyCode);
            if (PolicyDetail != null)
            {
                //PolicyDetail.StartMonthNumber = PolicyDetail.SetMonthNumberFromName(PolicyDetail.StartMonth);
                //PolicyDetail.EndMonthNumber = PolicyDetail.SetMonthNumberFromName(PolicyDetail.EndMonth);
                //PolicyDetail.AdoptedMonthNumber = PolicyDetail.SetMonthNumberFromName(PolicyDetail.AdoptedMonth);
                //PolicyDetail.ModerationNote = "Edited by" + CurrentUserService.FullName;
                //selectedNextWorkflowStatus = RoleBaseWorkFlowLookUps.Any(w => w.Id == PolicyDetail.ModerationActionsToY) ? PolicyDetail.ModerationActionsToY : WorkflowStatusY.Draft.ToString();
                PopulateTopics();
                await GetTopicsAsync();
                IsLoading = false;
            }
            
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                if (PolicyDetail == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                IsLoading = true;
                PolicyTypes = new ObservableCollection<PolicyTypeViewModel>(await PolicyService?.GetPolicyTypes());
                StateHasChanged();
                await GetLanguagesAsync();
                await GetCountriesAsync();
                await GetPartnerCategoriesPartners();
                await GetListOfActionRecords();
                 GetUsersAsync("Contributor");
                if (PolicyCode >0)
                {
                    if (PolicyDetail != null)
                    {
                        await PopulatePolicy();
                        PopulateCountryToggles();
                        PopulatePolicyPartnerCategories();
                        PopulatePolicyCategoryPartners();
                        PopulateProgramList();
                    }
                }
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            _ = base.OnAfterRenderAsync(firstRender);
        }

        private void OnContributorChanged(UserModel value)
        {
            selectedContributorValue = value.Id;
        }

        private void OnCountriesChanged(IEnumerable<Country> countries)
        {
            if (countries != null)
            {
                countryValues = countries.Select(c => c.Iso3Code).ToList();
            }
            else
            {
                countryValues.ToList().Clear();
            }
        }

        private void ChangeGovernmentDetails(string value)
        {
            var details = PolicyPartnerCategoryDetailsList.Where(p => p.PartnerCategoryId == Int32.Parse(selectedTab)).FirstOrDefault();
            if (details != null)
            {
                if (string.IsNullOrEmpty(value))
                {
                    PolicyPartnerCategoryDetailsList.Remove(details);
                }
                details.Details = value;
            }
            else
            {
                if (!string.IsNullOrEmpty(value))
                {
                    PolicyPartnerCategoryDetailsList.Add(new PolicyCategoryPartnerDraft()
                    {
                        PartnerCategoryId = Int32.Parse(selectedTab),
                        Details = value
                    });
                }
            }
        }

        private void GetUsersAsync(string roleName)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var query = from user in _dbContext.Users.ToList()
                        join userRole in _dbContext.UserRoles
                       on user.Id equals userRole.UserId
                        join role in _dbContext.Roles
                        on userRole.RoleId equals role.Id
                        where user.Status == "Active"
                        select new AppUserModel()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            UserName = user.UserName,
                            Email = user.Email,
                            Organization = user.Organization,
                            Status = user.Status,
                            UserRoles = role.Name
                        };
            var userList = query.ToList();

            foreach (var item in userList)
            {
                ContributorList.Add(new UserModel()
                {
                    Id = item.Id,
                    UserName = item.UserName,
                    DisplayName = String.Format("{0} {1}", item.FirstName, item.LastName)
                }); ;
            }
        }

        private void TopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            var node = checkedValue.Node;
            var parentId = node.DataItem.ParentId;
            var topicId = node.DataItem.TopicId;
            var title = node.DataItem.Title;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            var newpolicyTopic = new PolicyTopicMap()
            {
                PolicyId = NewPolicyRevisionId,
                TopicId = topicId
            };
            if ((bool)node.Checked)
            {
                AddChildren(node.DataItem);
            }
            else
            {
                RemoveChildren(node.DataItem);
            }
            canClearTopics = PolicyTopicList.Count > 0;
        }

        private void AddChildren(GTreeNode gTreeNode)
        {

            var children = gTreeNode.Children;
            int topicId = gTreeNode.TopicId;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            if (children.Count > 0)
            {
                foreach (var item in children)
                {
                    AddChildren(item);
                }
            }
            else
            {
                if (policyTopic == null)
                {
                    PolicyTopicList.Add(new PolicyTopicDraft() { PolicyId = NewPolicyRevisionId, TopicId = topicId });
                }
            }
        }

        private void RemoveChildren(GTreeNode gTreeNode)
        {
            var children = gTreeNode.Children;
            int topicId = gTreeNode.TopicId;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            if (children.Count > 0)
            {
                foreach (var item in children)
                {
                    RemoveChildren(item);
                }
            }
            else
            {
                if (policyTopic != null)
                {
                    PolicyTopicList.Remove(policyTopic);
                }
            }
        }

        private void ClearTopics()
        {
            if (PolicyTopicList != null && PolicyTopicList.Count > 0)
            {
                PolicyTopicList.Clear();
                topicTree.UncheckAll();
                StateHasChanged();
            }
        }

        string ActionLinkFilterText = string.Empty;
        private void ClearActionLinks()
        {
            if (ProgramList != null && ProgramList.Count > 0)
            {
                ProgramList.Clear();
                ActionLinkFilterText = string.Empty;
                ListOfActionRecords = new List<ProgramViewModel>(DuplicateListOfActionRecords).ToList();
                canClearActionLinks = false;
                StateHasChanged();
            }
        }
        public bool UseridVisible { get; set; } = false;
        private void OnnReveiw(string value)
        {
            selectedNextWorkflowStatus = value;
            if (selectedNextWorkflowStatus == "Delegated")
            {
                UseridVisible = true;
            }
            else if (selectedNextWorkflowStatus == "Sent for correction")
            {
                UseridVisible = true;
            }
            else
            {
                UseridVisible = false;
            }
            StateHasChanged();
        }
        private async void OnSubmit()
        {
            if (!string.IsNullOrEmpty(selectedNextWorkflowStatus))
            {
                await CreatePolicyDetails(selectedNextWorkflowStatus);
            }
            else
            {
                await Message.Error("Please select an  action.");
            }
        }

        private void PartnerCheckboxClicked(int partnerId, bool checkedValue)
        {

            var CategoryId = Int32.Parse(selectedTab);
            var policyPartnerCatDetail = PolicyPartnerCategoryDetailsList.Where(a => a.PartnerCategoryId == CategoryId).FirstOrDefault();
            var newpolicyPartnerCatDetail = new PolicyCategoryPartnerDraft()
            {
                PartnerCategoryId = CategoryId,
                PolicyId = NewPolicyRevisionId,
                Details = $"{GovernmentDetails}"
            };
            var policyPartnerCatPartner = PolicyPartnerCategoryPartnerList.Where(a => a.PartnerCategoryId == CategoryId && a.PartnerId == partnerId).FirstOrDefault();

            var newpolicyPartnerCatPartner = new PolicyPartnerCategoryPartnerDraft()
            {
                PartnerCategoryId = CategoryId,
                PolicyId = NewPolicyRevisionId,
                PartnerId = partnerId
            };
            if ((bool)checkedValue)
            {
                if (policyPartnerCatDetail == null && !string.IsNullOrEmpty(GovernmentDetails))
                {
                    PolicyPartnerCategoryDetailsList.Add(newpolicyPartnerCatDetail);
                }
                if (policyPartnerCatPartner == null)
                {
                    PolicyPartnerCategoryPartnerList.Add(newpolicyPartnerCatPartner);
                }
            }
            else
            {
                if (policyPartnerCatDetail != null)
                {
                    PolicyPartnerCategoryDetailsList.Remove(policyPartnerCatDetail);
                }
                if (policyPartnerCatPartner != null)
                {
                    PolicyPartnerCategoryPartnerList.Remove(policyPartnerCatPartner);
                }
            }
        }

        private async Task GetLanguagesAsync()
        {
            var dbLanguages = await PolicyService?.GetLanguagesAsync();
            Languages = new ObservableCollection<Domain.Languages.Language>(dbLanguages);
            if (PolicyDetail != null && PolicyDetail.LanguageId == null)
            {
                PolicyDetail.LanguageId = Languages.Where(e => e.Name == "English").FirstOrDefault().Id;
            }
            StateHasChanged();
        }

        private async Task GetCountriesAsync()
        {
            var dbCountries = await CountryService?.GetActiveCountries();
            Countries = new ObservableCollection<DbModels.Country>(dbCountries);
            StateHasChanged();
        }

        private async Task GetPartnerCategoriesPartners()
        {
            var dbPartnerCategoryPartners = await PolicyService?.GetPartnerCategoryPartnersAsync();
            PartnerCategoryPartners = new ObservableCollection<PartnerCategory>(dbPartnerCategoryPartners);
            selectedTab = PartnerCategoryPartners.FirstOrDefault().Id.ToString();
            StateHasChanged();
        }

        private async Task GetListOfActionRecords()
        {
            var dbPrograms = await ProgramService?.GetPrograms(2481);
            ListOfActionRecords = dbPrograms;
            DuplicateListOfActionRecords = new List<ProgramViewModel>(dbPrograms);
            StateHasChanged();
        }
       
        private void PopulateProgramList()
        {
            //foreach (var programPolicy in PolicyDetail.ProgramPolicyDrafts)
            //{
            //    ProgramList.Add(programPolicy.ProgramId.ToString());
            //}
            //DuplicateProgramList = new List<string>(ProgramList);
        }

        private void PopulatePolicyCategoryPartners()
        {
            //foreach (var partner in PolicyDetail.PolicyPartnerCategoryPartnerDrafts)
            //{
            //    PolicyPartnerCategoryPartnerDraft policyPartner = new()
            //    {
            //        PolicyId = partner.PolicyId,
            //        PartnerCategoryId = partner.PartnerCategoryId,
            //        PartnerId = partner.PartnerId,
            //    };
            //    PolicyPartnerCategoryPartnerList.Add(policyPartner);
            //}
            //DuplicatePolicyPartnerCategoryPartnerList = new List<PolicyPartnerCategoryPartnerDraft>(PolicyPartnerCategoryPartnerList);
        }

        private void PopulateTopics()
        {
            //topicKeys = new string[PolicyDetail.PolicyTopicDrafts.Count];
            //int topicIndex = 0;
            //foreach (var policyTopic in PolicyDetail.PolicyTopicDrafts)
            //{
            //    PolicyTopicDraft policyTopicMap = new ()
            //    {
            //        PolicyId = policyTopic.PolicyId,
            //        TopicId = policyTopic.TopicId,
            //    };
            //    PolicyTopicList.Add(policyTopicMap);
            //    topicKeys[topicIndex] = policyTopic.TopicId.ToString();
            //    topicIndex++;
            //}
            //DuplicatePolicyTopicList = new List<PolicyTopicDraft>(PolicyTopicList);
        }

        private void PopulatePolicyPartnerCategories()
        {
            //foreach (var partnerCategory in PolicyDetail.PolicyCategoryPartnerDrafts)
            //{
            //    PolicyCategoryPartnerDraft policyPartner = new();
            //    PolicyPartnerCategoryDetailsList.Add(policyPartner);
            //}
            //DuplicatePolicyPartnerCategoryDetailsList = new List<PolicyCategoryPartnerDraft>(PolicyPartnerCategoryDetailsList);
            //PolicyCategoryPartnerDraft policyPartnerFirst = PolicyPartnerCategoryDetailsList.Select(a => new PolicyCategoryPartnerDraft { PartnerCategoryId = a.PartnerCategoryId, Details = a.Details }).FirstOrDefault();
            //if (policyPartnerFirst != null)
            //{
            //    selectedTab = policyPartnerFirst.PartnerCategoryId.ToString();
            //    GovernmentDetails = policyPartnerFirst.Details;
            //}
        }

        private async Task PopulatePolicy()
        {
            AdoptedCheckedValue = GetAdoptedValue(PolicyDetail.Adopted);
            int? startYear = Convert.ToInt32(PolicyDetail.StartYear) == 0 ? null : Convert.ToInt32(PolicyDetail.StartYear);
           // int? startMonth = PolicyDetail.SetMonthNumberFromName(PolicyDetail.StartMonth); 
            int? endYear = Convert.ToInt32(PolicyDetail.EndYear) == 0 ? null : Convert.ToInt32(PolicyDetail.EndYear);
           // int? endMonth = PolicyDetail.SetMonthNumberFromName(PolicyDetail.EndMonth); 
          //  int? adoptedMonth = PolicyDetail.SetMonthNumberFromName(PolicyDetail.AdoptedMonth); 
            int? adoptedYear = Convert.ToInt32(PolicyDetail.AdoptedYear) == 0 ? null : PolicyDetail.AdoptedYear;
            int? publishMonth = Convert.ToInt32(PolicyDetail.PublishedMonth) == 0 ? null : Convert.ToInt32(PolicyDetail.PublishedMonth);
            int? publishYear = Convert.ToInt32(PolicyDetail.PublishedYear) == 0 ? null : Convert.ToInt32(PolicyDetail.PublishedYear);

         //   StartDate = MonthYearDisplayHelper.GetMonthAndYearDate(startMonth, startYear);
          //  EndDate = MonthYearDisplayHelper.GetMonthAndYearEndDate(endMonth, endYear);
            PublishedDate = MonthYearDisplayHelper.GetMonthAndYearDate(publishMonth, publishYear);
          //  AdoptedDate = MonthYearDisplayHelper.GetMonthAndYearDate(adoptedMonth, adoptedYear);
         //   policyGoals = PolicyDetail.PolicyExtractByOriginalLanguage;
            await GetPolicyAttachment();
            isSaveDisable = false;
        }

        private async Task GetPolicyAttachment()
        {
            var policyAttachment = await PolicyDraftService.GetAllPolicyAttachmentAsync(PolicyCode);
            if (policyAttachment != null)
            {
                PolicyAttachments.Add(policyAttachment);
                FileDisplayName = policyAttachment.FileDisplayName;
                StateHasChanged();
            }
        }

        bool IsAdoptedVisible = false;

        Task AdoptedChanged(string value)
        {
            AdoptedCheckedValue = value;
            if (value == "yes")
            {
                IsAdoptedVisible = false;
            }
            else
            {
                IsAdoptedVisible = true;
                AdoptedDate = null;
                PolicyDetail.AdoptedBy = null;
            }
            return Task.CompletedTask;
        }

        private static string GetAdoptedValue(bool? adopted)
        {
            if (adopted == null)
            {
                return "infrom";
            }
            else
            {
                if (adopted == true)
                {
                    return "yes";
                }
                else
                {
                    return "na";
                }
            }
        }

        private void PopulateCountryToggles()
        {
            //countryValues = new List<string>(PolicyDetail.PolicyCountryDrafts.Select(s => s.CountryCode));
        }

        private void OnGoalsChanged(string value)
        {
            policyGoals = value;
        }

        private async Task GetTopicsAsync()
        {
            AllTopics = await PolicyService.GetTopicsAsync();
            AllParentTopics = await PolicyService.GetParentTopics();
            
            var First = AllParentTopics.Where(x => x.ParentId.Equals(AllTopics.Where(t => t.Name.Equals("Policy")).FirstOrDefault().Id));
            await GetTopicTreeView(First);
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> First)
        {
            foreach (var item in First)
            {
                TopicList.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList()),
                    IsSelected = false
                });
            }
        }

        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<TopicParent> First)
        {
            List<GTreeNode> child = new();
            foreach (var item in First)
            {
                GTreeNode itemchild = new();
                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList()),
                    IsSelected = false
                });
            }
            return child;
        }

        List<PolicyTopicMap> Child = new ();
        private async Task<List<PolicyTopicMap>> GetPolicyTopics(PolicyTopicMap policyTopicMap)
        {

            List<TopicParent> children = AllParentTopics.Where(z => z.ParentId == policyTopicMap.TopicId).ToList();
            if (children != null && children.Count > 0)
            {
                foreach (var item1 in children)
                {
                    _ = GetPolicyTopics(new PolicyTopicMap() { PolicyId = policyTopicMap.PolicyId, TopicId = item1.TopicId });
                }
            }
            else
            {
                Child.Add(new PolicyTopicMap()
                {
                    PolicyId = policyTopicMap.PolicyId,
                    TopicId = policyTopicMap.TopicId
                });
            }

            return Child;
        }

        private async Task CreatePolicyDetails(string nextStatusToApply)
        {
            if (await PolicyTypeValidation.ValidateAll() && isStartDate && isEndDate)
            {
                IsLoading = true;
                var user = await UserManager.FindByIdAsync(CurrentUserService.UserId);
                var addtionalInfo = new AdditionalInfo()
                {
                    NextStatusToApply = nextStatusToApply,
                    UserFullName = $"{user.FirstName} {user.LastName}"
                };
                if (nextStatusToApply == WorkflowStatusToState.Delegated)
                {
                    var contributor = await UserManager.FindByIdAsync(selectedContributorValue);
                    addtionalInfo.DelegatedUserName = $"{contributor.FirstName} {contributor.LastName}";
                }


                if (user != null)
                {
                     UpdatePolicy(addtionalInfo);
                    await SaveImageToAzureStorage();

                    if (nextStatusToApply == WorkflowStatusToState.Published)
                    {
                        await PolicyService.UpsertPolicyChildFromDraft(NewPolicyRevisionId);
                    }
                     Reset();
                }

                if (countryValues != null && countryValues.Any())
                {
                    if (addtionalInfo.NextStatusToApply == WorkflowStatusToState.Published)
                    {
                        NavigationManager.NavigateTo($"policies/{PolicyDetail.Id}");
                    }
                    else
                    {
                        NavigationManager.NavigateTo($"admin/dashboard");
                    }

                }
                else
                {
                    NavigationManager.NavigateTo($"countries");
                }
                IsLoading = false;
            }
        }



        private void Reset()
        {
            FileDisplayName = string.Empty;
            BlobFileName = string.Empty;
            ImageError = string.Empty;
            FileEdit.Reset().AsTask();
        }

        private async Task OnChanged(FileChangedEventArgs args)
        {
            try
            {
                ImageError = string.Empty;
                var files = args.Files;
                if (files.Length > 1)
                {
                    return;
                }
                foreach (var file in files)
                {
                    if (file.Size <= ImageSize)
                    {
                        await using MemoryStream fs = new ();
                        await file.OpenReadStream(maxAllowedSize: ImageSize).CopyToAsync(fs);
                        ImageBytes = GetBytes(fs);
                        ImageType = file.Type;
                        FileDisplayName = file.Name;
                        var shortFileName = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                        BlobFileName = $"{Guid.NewGuid():N}-{shortFileName}";
                        FileInfo fi = new (shortFileName);
                        CreateFileDbData(BlobFileName, fi.Extension, FileDisplayName);
                        ImageError = string.Empty;
                    }
                    else
                    {
                        ImageError = "File Size is too long";
                    }
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }
        }

        private void CreateFileDbData(string bolbFileName, string fileType, string fileDisplayName)
        {
            PolicyAttachmentDraft policyAttachment = new ()
            {
                FileName = bolbFileName,
                FileType = fileType,
                FileDisplayName = fileDisplayName
            };
            PolicyAttachments.Clear();
            PolicyAttachments.Add(policyAttachment);
        }

        public static byte[] GetBytes(Stream stream)
        {
            var bytes = new byte[stream.Length];
            stream.Seek(0, SeekOrigin.Begin);
            stream.ReadAsync(bytes, 0, bytes.Length);
            stream.Dispose();
            return bytes;
        }

        private async Task SaveImageToAzureStorage()
        {
            string blobStorageConnectionString = new Helpers.AppSettingsHelper(Configuration).GetBlobStorageConnectionString();

            if (string.IsNullOrWhiteSpace(blobStorageConnectionString)
                || string.IsNullOrWhiteSpace(BlobFileName)
                || (ImageBytes == null && ImageBytes?.Length == 0)
                || string.IsNullOrWhiteSpace(ImageType))
            {
                // TODO: show error
                return;
            }
            CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
            CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
            CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Gina2.Core.Constants.BlobStorage.ContainerName);
            CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(BlobFileName);
            await cloudBlockBlob.DeleteIfExistsAsync();
            cloudBlockBlob.Properties.ContentType = ImageType;
            await cloudBlockBlob.UploadFromByteArrayAsync(ImageBytes, 0, ImageBytes.Length);
            ImageBytes = null;
            ImageType = string.Empty;
        }

        private void DataChangingStartDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            if (!EndDate.HasValue || (dateTimeChangedEventArgs.Date < EndDate))
            {
                isStartDate = true;
                isEndDate = true;
            }
            else
            {
                isStartDate = false;
            }

            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            PolicyDetail.StartYear = Convert.ToInt32(Year);
          //  PolicyDetail.StartMonth = PolicyDetail.SetMonthNameFromNumber(Convert.ToInt32(month));
          //  PolicyDetail.StartMonthNumber = Convert.ToInt32(month);
        }

        string ExpandCollapseName = "Expand All";
        bool isExpandingOrCOllapsing = false;
        public async Task VisibleExpandButton()
        {
            await Task.Run(() =>
            {
                isExpandingOrCOllapsing = true;
            });

            if (ExpandCollapseName == "Expand All")
            {
                topicTree.ExpandAll();
                ExpandCollapseName = "Collapse All";
            }
            else
            {
                topicTree.CollapseAll();
                ExpandCollapseName = "Expand All";
            }
            await Task.Run(() =>
            {
                isExpandingOrCOllapsing = false;
            });
            StateHasChanged();
        }

        private void DataChangingEndDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            if (!StartDate.HasValue || (dateTimeChangedEventArgs.Date > StartDate))
            {
                isEndDate = true;
                isStartDate = true;
            }
            else
            {
                isEndDate = false;
            }

            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            PolicyDetail.EndYear = Convert.ToInt32(Year);
           // PolicyDetail.EndMonth = PolicyDetail.SetMonthNameFromNumber(Convert.ToInt32(month));
           // PolicyDetail.EndMonthNumber = Convert.ToInt32(month);
            
        }

        private void DataChangingPublishedDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            PolicyDetail.PublishedYear = Convert.ToInt32(Year);
            PolicyDetail.PublishedMonth = month;
        }

        private void DataChangingAdoptedDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            PolicyDetail.AdoptedYear = Convert.ToInt32(Year);
            //PolicyDetail.AdoptedMonth = PolicyDetail.SetMonthNameFromNumber(Convert.ToInt32(month));
           // PolicyDetail.AdoptedMonthNumber = Convert.ToInt32(month);
        }

      
        public static void ValidateSelect(ValidatorEventArgs e)
        {
            var selectedValue = Convert.ToInt32(e.Value);
            e.Status = selectedValue != 0 ? ValidationStatus.Success : ValidationStatus.Error;
        }

        private bool canClearTopics = false;
        private bool canClearActionLinks = false;
        public void ValidateEndDate(ValidatorEventArgs e)
        {
            var selectedValue = Convert.ToDateTime(e.Value);
            e.Status = (selectedValue > StartDate) ? ValidationStatus.Success : ValidationStatus.Error;
        }

        private bool isSaveDisable { get; set; } = true;

        private Task TitleKeyPress(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                isSaveDisable = true;
            }
            else
            {
                if (PolicyDetail.PolicyTypeId != 0)
                {
                    isSaveDisable = false;
                }
                else
                {
                    isSaveDisable = true;
                }
            }
            PolicyDetail.Title = value;
            return Task.CompletedTask;
        }

        private Task ChangePolicyType(int value)
        {
            if (value == 0)
            {
                isSaveDisable = true;
            }
            else
            {
                if (!string.IsNullOrEmpty(PolicyDetail.Title))
                {
                    isSaveDisable = false;

                }
                else
                {
                    isSaveDisable = true;
                }
            }
            PolicyDetail.PolicyTypeId = value;
            return Task.CompletedTask;
        }

        private Task ActionLinkFilter(string value)
        {
            ListOfActionRecords = new List<ProgramViewModel>(DuplicateListOfActionRecords.Where(c => (c.Title ?? String.Empty).ToLower().Contains((value ?? String.Empty).ToLower())).ToList());
            ActionLinkFilterText = value;
            return Task.CompletedTask;
        }


        private async void UpdatePolicy(AdditionalInfo additionalInfo)
        {
            isSaveDisable = true;
            
            Policy policy = new ()
            {
               // Id = PolicyDetail.Id.Value,
                Adopted = GetAdoptedDbValue(AdoptedCheckedValue),
                AdoptedBy = AdoptedCheckedValue == "yes" ? PolicyDetail.AdoptedBy : null,
                AdoptedYear = AdoptedCheckedValue == "yes" ? PolicyDetail.AdoptedYear : null,
               // AdoptedMonth = AdoptedCheckedValue == "yes" ? PolicyDetail.AdoptedMonthNumber : null,
                SubnationalGeographicArea = PolicyDetail.SubnationalGeographicArea,
                StartYear = PolicyDetail.StartYear.HasValue ? PolicyDetail.StartYear : null,
             //   StartMonth = PolicyDetail.StartMonthNumber,
                EndYear = PolicyDetail.EndYear ?? null,
             //   EndMonth = PolicyDetail.EndMonthNumber,
                PublishedYear = PolicyDetail.PublishedYear.HasValue ? Convert.ToInt32(PolicyDetail.PublishedYear) : null,
                PublishedMonth = string.IsNullOrEmpty(PolicyDetail.PublishedMonth) ? null : Convert.ToInt32(PolicyDetail.PublishedMonth),
                Title = PolicyDetail.Title,
                PolicyTypeId = PolicyDetail.PolicyTypeId.Value,
                LanguageId = PolicyDetail.LanguageId,
                PublishedBy = PolicyDetail.PublishedBy,
                Url = PolicyDetail.Url,
                FurtherNotes = $"{PolicyDetail.FurtherNotes}",
                References = $"{PolicyDetail.References}",
                Extracts = $"{policyGoals}",
                IsDraft = additionalInfo.NextStatusToApply != WorkflowStatusToState.Published,
            };

            var policySavedInfo = await PolicyService.UpdatePolicy(policy, additionalInfo);
            NewPolicyRevisionId = policySavedInfo.PolicyRevisionId;
        }

       private static bool? GetAdoptedDbValue(string value)
        {
            if (value == "yes")
            {
                return true;
            }
            else if (value == "na")
            {
                return false;
            }
            else
            {
                return null;
            }
        }


        private async Task SavePolicyDrafts()
        {
            foreach (var policyTopic in PolicyTopicList)
            {
                policyTopic.PolicyId = NewPolicyRevisionId;
            }

            foreach (var policyPartner in PolicyPartnerCategoryDetailsList)
            {
                policyPartner.PolicyId = NewPolicyRevisionId;
            }

            foreach (var attachment in PolicyAttachments)
            {
                attachment.PolicyId = NewPolicyRevisionId;
            }

            foreach (var policyPartnerCategory in PolicyPartnerCategoryPartnerList)
            {
                policyPartnerCategory.PolicyId = NewPolicyRevisionId;
            }

            List<PolicyCountryDraft> policyCountryMapItems = new();
            foreach (var code in countryValues)
            {
                PolicyCountryDraft policyCountry = new ()
                {
                    PolicyId = NewPolicyRevisionId,
                    CountryCode = code
                };
                policyCountryMapItems.Add(policyCountry);
            }

         //  await PolicyDraftService.SavePolicyDraftTables(PolicyTopicList, PolicyPartnerCategoryDetailsList, PolicyAttachments, PolicyPartnerCategoryPartnerList, policyCountryMapItems);
        }


    }
}