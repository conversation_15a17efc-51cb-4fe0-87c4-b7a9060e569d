@using Gina2.Core.Constant
@using Gina2.Core.Enums
@using Gina2.Core.Models;
@using AntDesign.Datepicker;
@using Gina2.DbModels;
@using AntDesign.Select;
@using Microsoft.AspNetCore.Components.Authorization;
@using Gina2.Blazor.Areas.Identity.IdentityServices;
@using static Gina2.Core.Constants
@using Gina2.Core.Lookups;
@using Domain.Programme;
@using Gina2.Blazor.Models.AdminModel;
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
<Loader IsLoading="@IsLoading" />
<Validations @ref="PolicyTypeValidation" Mode="ValidationMode.Manual" ValidateOnLoad>
    <Div Class="newdraft _antdesign pl-2 pr-2">
        @if (PolicyCode >= 0 && CountryCode != null)
        {
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
                <Div Class="item1 flex-b">
                    <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/policies"))">
                        <Icon Class="fas fa-chevron-left"></Icon> Back to policies
                    </Button>
                </Div>
                <Div Class="item2">
                    @*<Button Loading="@ExportCsvLoading" Clicked="@Download" Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>*@
                </Div>
            </Div>
        }
        <Div Class="pt-5 m-pt-2 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3" data-cy="NewPolicyheaderTitle">
                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyHeading))
                <AdminEditbut Key="PolicyHeading" />
            </Heading>
            <Divider Class="divi-blue" />
        </Div>
        <Div Class="form-newd">
            <Fields>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel data-cy="NewPolicyTitleLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitleLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="PolicyTitleTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitleTooltip)">
                            <Button data-cy="NewPolicyTitleBtn" Class="but-info _tooltip">
                                <Icon data-cy="NewPolicyTitleIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyTitleGroup" />
                    </FieldLabel>
                    <TextEdit data-cy="NewPolicyPlaceholder" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitlePlaceHolder)" Text="@PolicyDetail?.Title" TextChanged="@TitleKeyPress">
                    </TextEdit>
                    @if (PolicyTitle)
                    {
                        <Span Class="text-danger" data-cy="ValidTitleText"> Enter valid title</Span>
                    }
                </Field>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel data-cy="PolicyTitleEnglishLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitleEnglishLabel)
                        <Tooltip data-cy="PolicyTitleEnglishLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitleEnglishTooltip)">
                            <Button data-cy="PolicyTitleEnglishLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                        </Tooltip> <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyTitleEnglishGroup" />
                    </FieldLabel>
                    <TextEdit data-cy="PolicyTitleEnglishLabelPlaceholder" Disabled="@isEnglishTitleDisabled" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTitleEnglishPlaceHolder)" Text="@PolicyDetail?.EnglishTitle" TextChanged="@EnglishTitleKeyPress">

                    </TextEdit>
                    @if (PolicyEnglishTitle)
                    {
                        <Span Class="text-danger" data-cy="ValidEnglishTitle"> Enter valid english title</Span>
                    }
                </Field>
                <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel data-cy="PolicyLanguageLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLanguageLabel)
                        <Tooltip data-cy="PolicyLanguageLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLanguageTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyLanguageGroup" />
                    </FieldLabel>
                    @*<Select TValue="int?" SelectedValue="@PolicyDetail.LanguageId" SelectedValueChanged="@EnableDisableEnglishTitle">
                        <SelectItem Value="-1" data-cy="SelectLanguageOption">Select Language</SelectItem>
                        <Repeater Items="@Languages">
                            <SelectItem Value="context.Id">@context.Name</SelectItem>
                        </Repeater>
                    </Select>*@
                    <AntDesign.Select DataSource="@Languages"
                                      TItemValue="int?"
                                      Placeholder=""
                                      TItem="Domain.Languages.Language"
                                      LabelName="@nameof(Language.Name)"
                                      ValueName="@nameof(Language.Id)"
                                      OnSelectedItemChanged="(async e => await EnableDisableEnglishTitle(e != null ? e.Id : PolicyDetail.LanguageId))"
                                      Value="@PolicyDetail.LanguageId"
                                      DefaultValue="@PolicyDetail.LanguageId"
                                      EnableSearch
                                      Style="width: 100%; margin-bottom: 8px;">
                    </AntDesign.Select>
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                    <FieldLabel data-cy="PolicyTypeLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTypeLabel)
                        <span>*</span> <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTypeTooltip)">
                            <Button data-cy="PolicyTypeLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                        </Tooltip> <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyTypeGroup" />
                    </FieldLabel>
                    <AntDesign.Select DataSource="@PolicyTypes"
                                      TItemValue="int?"
                                      TItem="Domain.PolicyTypes.PolicyTypeViewModel"
                                      LabelName="@nameof(Domain.PolicyTypes.PolicyTypeViewModel.Name)"
                                      ValueName="@nameof(Domain.PolicyTypes.PolicyTypeViewModel.Id)"
                                      OnSelectedItemChanged="(async e => await ChangePolicyType(e != null ? e.Id : PolicyDetail.PolicyTypeId))"
                                      Value="@PolicyDetail.PolicyTypeId"
                                      DefaultValue="@PolicyDetail.PolicyTypeId"
                                      EnableSearch
                                      Style="width: 100%; margin-bottom: 8px;">
                    </AntDesign.Select>
                </Field>
            </Fields>
            @if (Otherid == PolicyDetail.PolicyTypeId)
            {
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="SpecifyTextLabel">
                        If other, please specify
                        <TextEdit Placeholder="" @bind-Text="@PolicyDetail.OtherPolicyType">
                        </TextEdit>
                    </FieldLabel>
                </Field>
            </Fields>
            }
            else
            {
                @(PolicyDetail.OtherPolicyType= string.Empty);
            }
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="PolicyCountryLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyCountryLabel)
                        <span>*</span> <Tooltip data-cy="PolicyCountryLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyCountryTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyCountryGroup" />
                    </FieldLabel>
                    <AntDesign.Select DataSource="@Countries"
                                      Mode="multiple"
                                      TItemValue="string"
                                      Placeholder=""
                                      TItem="Country"
                                      LabelName="@nameof(Country.Name)"
                                      ValueName="@nameof(Country.Iso3Code)"
                                      OnSelectedItemsChanged="OnCountriesChanged"
                                       @bind-Values="@countryValues"
                                            AllowClear
                                      OnClearSelected="ClearAllCountries"
                                            EnableSearch
                                      Style="width: 100%; margin-bottom: 8px;">
                    </AntDesign.Select>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="PolicyRegionLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyRegionLabel)
                        <Tooltip data-cy="PolicyRegionLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyRegionTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyRegionGroup" />
                    </FieldLabel>
                    <TextEdit data-cy="PolicyRegionLabelPlaceholder" Text="@PolicyDetail?.SubnationalGeographicArea" TextChanged="@ValidateProvinceRegion" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyRegionPlaceholder)">
                    </TextEdit>
                    @if (ProvinceRegion)
                    {
                        <Span Class="text-danger" data-cy="PolicyProvince/RegionLabelText"> Enter valid Province/Region</Span>
                    }
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                    <FieldLabel data-cy="PolicyStartDateLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStartDateLabel)
                        <Tooltip data-cy="PolicyStartDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStartDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyStartDateGroup" />
                    </FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyStartMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStartMonthLabel) </FieldLabel>
                            <AntDesign.DatePicker @bind-Value="@DateForStartMonth" AllowClear="true" OnClearClick="@ClearStartMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingStartMonth" Picker="@AntDesign.DatePickerType.Month" />
                        </Field>
                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyDateYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStartYearLabel) </FieldLabel>
                            <AntDesign.DatePicker AllowClear="true" OnClearClick="@ClearStartYear" @bind-Value="@DateForStartYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingStartYear" />
                        </Field>
                    </Fields>
                    <FieldLabel data-cy="PolicyStartDateText" Style="color: red" hidden="@IsStartDate">Start date should be less than End date</FieldLabel>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                    <FieldLabel data-cy="PolicyEndDateLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyEndDateLabel)
                        <Tooltip data-cy="PolicyEndDateTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyEndDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyEndDateGroup" />
                    </FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyEndMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyEndMonthLabel)</FieldLabel>
                            <AntDesign.DatePicker @bind-Value="@DateForEndMonth" AllowClear="true" OnClearClick="@ClearEndDate" TValue="DateTime?" Format="MMM" OnChange="DataChangingEndMonth" Picker="@AntDesign.DatePickerType.Month" DisabledDate="DisableEndMonth"/>
                        </Field>

                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyEndYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyEndYearLabel)  </FieldLabel>
                            <AntDesign.DatePicker @bind-Value="@DateForEndYear" TValue="DateTime?" AllowClear="true" OnClearClick="@ClearEndYear" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingEndYear" DisabledDate="DisableEndDate"/>
                        </Field>
                    </Fields>
                    <FieldLabel data-cy="PolicyEndDateText" Style="color: red" hidden="@IsEndDate">End date should be greater than Start date</FieldLabel>
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                    <FieldLabel data-cy="PolicyPublishDateLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishDateLabel)
                        <Tooltip data-cy="PolicyPublishDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyPublishDateGroup" />
                    </FieldLabel>
                    <Fields>
                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyPublishMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishMonthLabel)</FieldLabel>
                            <AntDesign.DatePicker @bind-Value="@DateForPublishMonth" AllowClear="true" OnClearClick="@ClearPublishedMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingPublishMonth" Picker="@AntDesign.DatePickerType.Month" />
                        </Field>

                        <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                            <FieldLabel data-cy="PolicyPublishYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishYearLabel)  <span>*</span></FieldLabel>
                            <AntDesign.DatePicker @bind-Value="@DateForPublishYear" AllowClear="true" OnClearClick="@ClearPublishedYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingPublishYear" />

                        </Field>
                    </Fields>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="PolicyPublishByLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishByLabel) <Tooltip Text="Published by"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyPublishByGroup" />
                    </FieldLabel>
                    <TextEdit Text="@PolicyDetail?.PublishedBy" data-cy="PolicyPublishByLabelPlaceholder" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyPublishByPlaceholder)" TextChanged="@ValidatePublishedBy">
                    </TextEdit>
                    @if (PublishedBy)
                    {
                        <Span Class="text-danger" data-cy="PolicyPublishByValidationText"> Enter valid published by</Span>
                    }
                </Field>
            </Fields>
            <Fields>
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                    <FieldLabel data-cy="PolicyAdoptedByLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyDocAdoptedLabel)
                        <Tooltip data-cy="Tooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyDocAdoptedTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyDocAdoptedGroup" />
                    </FieldLabel>
                    <RadioGroup data-cy="PolicyAdoptedByLabelRadioGroup" Class="text-med" TValue="string" Name="colors" CheckedValue="@AdoptedCheckedValue" CheckedValueChanged="@AdoptedChanged">
                        <Radio data-cy="RadioBtnN/A" Value="@("na")">N/A</Radio>
                        <Radio data-cy="RadioBtnYes" Value="@("yes")">Yes</Radio>
                        <Radio data-cy="RadioBtnInfo" Value="@("infrom")">No / No information</Radio>
                    </RadioGroup>
                </Field>
            </Fields>
            @if (IsAdoptedVisible)
            {
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker">
                        <FieldLabel data-cy="PolicyAdoptedDateLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedDateLabel)
                            <Tooltip data-cy="PolicyAdoptedDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyAdoptedDateGroup" />
                        </FieldLabel>
                        <Fields>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyAdoptedMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedMonthLabel)</FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@DateForAdoptedMonth" AllowClear="true" OnClearClick="@ClearAdoptedMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" Picker="@AntDesign.DatePickerType.Month" OnChange="DataChangingAdoptedMonth" />
                            </Field>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyAdoptedYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedYearLabel)</FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@DateForAdoptedYear" AllowClear="true" OnClearClick="@ClearAdoptedYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingAdoptedYear" />
                            </Field>
                        </Fields>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="PolicyAdoptedByLabel2">
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedByLabel)
                            <Tooltip data-cy="PolicyAdoptedByLabel2Tooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedByTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyAdoptedByGroup" />
                        </FieldLabel>
                        <TextEdit Text="@PolicyDetail.AdoptedBy" data-cy="PolicyAdoptedByPlaceholder" TextChanged="@ValidateAdoptedBy" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyAdoptedByPlaceholder)"></TextEdit>
                        @if (AdoptedBy)
                        {
                            <Span data-cy="PolicyAdoptedByValidationText" Class="text-danger"> Enter valid adopted by</Span>
                        }
                    </Field>
                </Fields>
            }
            <Field>
                <FieldLabel data-cy="PartnersInvolvedLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PartnersInvolvedLabel)
                    <Tooltip data-cy="PartnersInvolvedLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PartnersInvolvedTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                    <AdminEditbut Key="@PolicyCreateConfigurationKey.PartnersInvolvedGroup" />
                </FieldLabel>
            </Field>
            <Div Flex="Flex.JustifyContent.Between" Class="_tabsbox downl-flex">
                <Tabs @bind-SelectedTab="@selectedTab" Class="from-tab">
                    <Items>
                        <Repeater Items="@PartnerCategoryPartners.OrderBy(pc => pc.DragAndDropKey)">
                            <Tab Name="@context.Id.ToString()">@context.Name</Tab>
                        </Repeater>
                    </Items>
                    <Content>
                        <Repeater Context="context" Items="@PartnerCategoryPartners.OrderBy(pc => pc.DragAndDropKey)">
                            @{
                                string partnerName = context.Name.Replace(" ", "").Replace("/", "");
                            }
                            <TabPanel Name="@context.Id.ToString()">
                                <Heading Size="HeadingSize.Is5" Class="pt-1 pb-1">@context.Name involved in implementation</Heading>
                                <ListGroup Class="ulgroup">
                                    <Repeater Context="context1" Items="@context.Partners.OrderBy(p => p.DragAndDropKey)">
                                        @{
                                            bool IsPartner = false;
                                            if (PolicyPartnerCategoryPartnerList != null && PolicyPartnerCategoryPartnerList.Any(a => a.PartnerId == context1.Id))
                                            {
                                                IsPartner = true;
                                            }
                                        }
                                        <ListGroupItem>
                                            <Check Checked="@IsPartner"
                                                   CheckedChanged="e => {PartnerCheckboxClicked(context1.Id, e.Value);}"
                                                   TValue="bool?">@context1.Name</Check>
                                        </ListGroupItem>
                                    </Repeater>
                                </ListGroup>
                                <Field>
                                    <FieldLabel>@context.Name detail(s)</FieldLabel>
                                </Field>
                            </TabPanel>
                        </Repeater>
                        @if(Details.Any())
                        {
                            <Repeater Items="@PartnerCategoryPartners">
                            <Div style="@(selectedTab == context.Id.ToString() ? "display : block" : "display : none")">
                                <_quillEditor value="@Details[context.Id.ToString()]" @ref="quillEditorGovernmentDetailsRef[context.Name]"></_quillEditor>

                            </Div>
                            </Repeater>
                        }
                    </Content>
                </Tabs>
            </Div>
        </Div>
        <Div Class="form-newd mt-4">
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyGoalsLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyGoalsTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyGoalsGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@PolicyDetail.Extracts" @ref="quillEditorGoalsRef"></_quillEditor>
            </Field>

           @* <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStratogiesLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyStratogiesTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyStratogiesGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@PolicyDetail.Strategies" @ref="quillEditorStratogiesRef"></_quillEditor>
            </Field>

            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyIndicatorLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyIndicatorTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyIndicatorGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@PolicyDetail.Indicators" @ref="quillEditorIndicatorRef"></_quillEditor>
            </Field>

            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLegislationLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLegislationTooltip )"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyLegislationGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@PolicyDetail.Legislations" @ref="quillEditorLegislationRef"></_quillEditor>
            </Field>*@
        </Div>



        <Div Class="form-newd mt-4 Topicspolicy">

            <Div Flex="Flex.JustifyContent.Between">
                <Div>
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTopicLabel)
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyTopicTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyTopicGroup" />
                    </FieldLabel>
                </Div>
                <Div>

                    <Button Clicked="@VisibleExpandButton" Disabled="@isExpandingOrCOllapsing" Class="but-gray">@ExpandCollapseName</Button>
                    <Button Clicked="ClearTopics" Disabled="@(!canClearTopics)" Class="but-gray">Clear</Button>
                </Div>
            </Div>
            <Divider Padding="Padding.Is0" Class="mt-0" />
            @if (RunTreeCode)
            {
                <AntDesign.Spin Spinning="@isExpandingOrCOllapsing">
                    <AntDesign.Tree @ref="topicTree"
                                        ShowIcon
                                    MatchedClass="site-tree-search-value"
                                    DataSource="TopicList"
                                    TItem="GTreeNode"
                                    TitleExpression="x => x.DataItem.Title"
                                    ChildrenExpression="x => x.DataItem.Children"
                                    KeyExpression="x => x.DataItem.Key"
                                    OnCheck="x => TopicTreeCheckboxClicked(x)"
                                    CheckOnClickNode="true"
                                    DefaultCheckedKeys="@topicKeys"
                                        Checkable>
                </AntDesign.Tree>
            </AntDesign.Spin>

            }
        </Div>
        <Div Class="form-newd mt-4 gina-new">
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel Style="font-weight: bold !important;">
                        @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLinkToActionLabel)
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyLinkToActionTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                        <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyLinkToActionGroup" />
                    </FieldLabel>
                </Div>
            </Div>
            <Divider Class="mt-0" />
            <Div Class="w-100">
                @if (ListOfActionRecords == null || !ListOfActionRecords.Any())
                {
                    <p>No actions available.</p>
                }
                else
                {
                    <AntDesign.Select DataSource="@ListOfActionRecords"
                                @bind-Values="@ProgramList"
                                  Mode="multiple"
                                  TItemValue="int"
                                  TItem="ProgramViewModel"
                                  LabelName="@nameof(ProgramViewModel.Title)"
                                  ValueName="@nameof(ProgramViewModel.Id)"
                                        EnableSearch
                                        AllowClear
                                  Style="width: 100%; margin-bottom: 8px;" />
                }
            </Div>
        </Div>
        <Div Class="form-newd mt-4">
            <Field>
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyURLLinkLabel) <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyURLLinkTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                    <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyURLLinkGroup" />
                </FieldLabel>
                <TextEdit Text="@PolicyDetail.Url" TextChanged="@ValidateUrl" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyURLLinkPlaceholder)"></TextEdit>
                @if (ValidUrl)
                {
                    <Span Class="text-danger"> Enter valid url</Span>
                }
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyFurtherNoteLabel)<Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyFurtherNoteTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyFurtherNoteGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                @*<RadzenHtmlEditor id="furtherNotes" @bind-Value="@PolicyDetail.FurtherNotes" class="_editor" style="height: 200px; margin-bottom: 1rem;"><HtmlEditor /></RadzenHtmlEditor>*@
                <_quillEditor value="@PolicyDetail.FurtherNotes" @ref="quillEditorFurtherNotesRef"></_quillEditor>
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyReferenceLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyReferenceTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                            <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyReferenceGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                @*<RadzenHtmlEditor id="references" @bind-Value="@PolicyDetail.References" class="_editor" style="height: 200px; margin-bottom: 1rem;"><HtmlEditor /></RadzenHtmlEditor>*@
                <_quillEditor value="@PolicyDetail.References" @ref="quillEditorReferencesRef"></_quillEditor>
            </Field>
            <Field Class="choosefile">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyFileUploadLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyFileUploadTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                    <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyFileUploadGroup" />
                </FieldLabel>
                <FileEdit @ref="@FileEdit"  Changed="@OnChanged" Filter=".pdf" Placeholder="Choose File" Multiple />
                @if (IsAccordianLoading)
                {
                    <div class="spinner-border text-primary mt-1" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                }
                <FieldLabel Style="color: red" hidden="@(string.IsNullOrEmpty(ImageError))">@ImageError</FieldLabel><br />
                <FieldLabel Style="color: grey;margin-bottom: -6px;">Files must be less than <span style="color: black">25 MB</span>.</FieldLabel>
                <FieldLabel Style="color: grey">Allowed file types: <span style="color: black">pdf</span>.</FieldLabel>
                <Repeater Items="@PolicyAttachments">
                        <div>
                            <Span>@context.FileDisplayName </Span> <Span>
                            <Tooltip Text="Remove">
                                <Button Clicked="e => RemoveFile(context)" Class="but-info _tooltip">
                                    <Icon Name="IconName.Delete" />
                                </Button>
                            </Tooltip>
                        </Span>
                    </div>
                </Repeater>
            </Field>
        </Div>
        <Div Class="form-newd mt-4">
           <Div Class="row">
               <Div Class="col-lg-6 col-12">
                    <Fields>
                        <Field>
                            <FieldLabel>
                                @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyModerationNoteLabel) <Span>*</Span>
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyModerationNoteTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                                <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyModerationNoteGroup" />
                            </FieldLabel>
                            <TextEdit Disabled Text="@UserName">
                            </TextEdit>
                            <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivations.</FieldHelp>
                            @if (IsModerationNotes)
                            {
                                <Span Class="text-danger"> Enter valid moderation notes</Span>
                            }
                        </Field>
                    </Fields>
               </Div>
                <Div Class="col-lg-6 col-12">
                    <Fields>
                        <Field>
                            <FieldLabel>
                                @* @PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyModerationNoteLabel) *@
                                Other notes
                                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(PolicyCreateConfigurationKey.PolicyModerationNoteTooltip)">
                                    <Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                                <AdminEditbut Key="@PolicyCreateConfigurationKey.PolicyModerationNoteGroup" />
                            </FieldLabel>
                            <TextEdit  Text="@ModerationNotes" TextChanged="@ValidateModerationNotes">
                            </TextEdit>
                        </Field>
                    </Fields>
                </Div>
           </Div>
        </Div>

        <Div Class="mt-4 pb-6">
            <Div Class="stickybottom">

                <Fields>
                    <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">

                        <AntDesign.SimpleSelect TItem="string"
                                                TItemValue="string"
                        @bind-Value="@selectedNextWorkflowStatus"
                                                DefaultValue="@defaultWorkflowStatus"
                                                OnSelectedItemChanged="e=>OnnReveiw(e)">
                            <SelectOptions>
                                @foreach (var item in RoleBaseWorkFlowLookUps)
                                {
                                    <Tooltip ShowArrow=true Placement="TooltipPlacement.RightEnd" Title=@(item.Description)>
                                        <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Value Label=@item.Text />
                                    </Tooltip>
                                }
                            </SelectOptions>
                        </AntDesign.SimpleSelect>
                    </Field>
                    @if (UseridVisible)
                    {
                        <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                            @* <FieldLabel>Select User id</FieldLabel>*@

                            <AntDesign.Select DataSource="@ContributorList"
                                              TItemValue="string"
                                              Placeholder=""
                                              TItem="UserModel"
                                              LabelName="@nameof(UserModel.UserName)"
                                              ValueName="@nameof(UserModel.UserName)"
                            @bind-Value="@selectedMailValue"
                                                    AllowClear
                                                    EnableSearch
                                              Style="width: 100%; margin-bottom: 0px;">
                            </AntDesign.Select>
                            <FieldHelp>Type who you want to send this content</FieldHelp>
                        </Field>
                    }
                    <Field>
                        <Button Disabled="@CanSave()" Class="but-yellow" Clicked="e=> OnSubmit()">Save</Button>
                    </Field>
                </Fields>
            </Div>
        </Div>
    </Div>
</Validations>
