using AntDesign;
using Blazorise;
using Blazorise.Snackbar;
using DocumentFormat.OpenXml.Office2010.Excel;
using Domain.PolicyProgram;
using Domain.PolicyTopics;
using Domain.PolicyTypes;
using Domain.Programme;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Blazor.Pages;
using Gina2.Core.Enums;
using Gina2.Core.Extensions;
using Gina2.Core.Interface;
using Gina2.Core.Lookups;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.PolicyDrafts;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Country;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.JSInterop;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using static Gina2.Core.Constants;
using Microsoft.AspNetCore.Session;
using Microsoft.Extensions.Caching.Memory;
using DocumentFormat.OpenXml.ExtendedProperties;
using Org.BouncyCastle.Utilities;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using static Gina2.Core.Constants.LocalizationKeys;
using System.Diagnostics.PerformanceData;

namespace Gina2.Blazor.Shared.TabComponents.PolicyTab
{
    public partial class PolicyCreateEditTab : PageConfirgurationComponent
    {
        [Inject]
        private ILogger<PolicyCreateEditTab> _logger { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private ICurrentUserServiceExtended CurrentUserService { get; set; }

        [Inject]
        private UserManager<ApplicationUser> UserManager { get; set; }
        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Parameter]
        public PolicyRevision PolicyDetail { get; set; } = new();

        [Parameter]
        public PolicyCountryMapItem PolicyCountry { get; set; } = new();

        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IProgrammeService ProgramService { get; set; }
        [Inject]
        private IEmailServices EmailServices { get; set; }

        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        [CascadingParameter(Name = "PolicyCode")]
        public int PolicyCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int? VersionId { get; set; }

        private Validations PolicyTypeValidation;
        private Validation textValidate;
        public int NewPolicyId { get; set; }

        public int NewPolicyRevisionId { get; set; }
        private long ImageSize { get; set; } = 25 * (1024 * 1024); // 25 mb(kb to mb)

        private bool IsAccordianLoading { get; set; }
        private string BlobFileName { get; set; } = string.Empty;
        private string ImageError { get; set; }
        private int Otherid { get; set; }
        private Dictionary<string, string> Details = new Dictionary<string, string>();

        private string PlaceHolderStartYear { get; set; } = string.Empty;
        private string PlaceHolderStartMonth { get; set; } = string.Empty;
        private string PlaceHolderEndMonth { get; set; }
        private string PlaceHolderEndYear { get; set; }
        private DateTime? DateForStartYear { get; set; }
        private DateTime? DateForStartMonth { get; set; }
        private DateTime? DateForEndYear { get; set; }
        private DateTime? DateForEndMonth { get; set; }
        private DateTime? DateForPublishYear { get; set; }
        private DateTime? DateForPublishMonth { get; set; }
        private DateTime? DateForAdoptedYear { get; set; }
        private DateTime? DateForAdoptedMonth { get; set; }

        [Inject]
        private IConfiguration Configuration { get; set; }
        private SnackbarStack SnackbarStack { get; set; }

        private ObservableCollection<PolicyTypeViewModel> PolicyTypes { get; set; } = new ObservableCollection<PolicyTypeViewModel>();
        private List<PolicyAttachmentDraft> PolicyAttachments { get; set; } = new List<PolicyAttachmentDraft>();
        private List<PolicyAttachment> DuplicatePolicyAttachments { get; set; } = new List<PolicyAttachment>();
        private ObservableCollection<Domain.Languages.Language> Languages { get; set; } = new ObservableCollection<Domain.Languages.Language>();
        private ObservableCollection<DbModels.Country> Countries { get; set; } = new ObservableCollection<DbModels.Country>();
        private ObservableCollection<PartnerCategory> PartnerCategoryPartners { get; set; } = new ObservableCollection<PartnerCategory>();
        private IEnumerable<string> countryValues;
        private IEnumerable<string> DuplicateCountryValues;
        private List<UserModel> ContributorList { get; set; } = new List<UserModel>();
        private FileEdit FileEdit { get; set; }
        private string ImageType { get; set; }
        private bool ExportCsvLoading { get; set; }

        private string selectedTab = "";
        private string FileDisplayName { get; set; }
        private Dictionary<string, byte[]> ImageBytes { get; set; } = new Dictionary<string, byte[]>();
        public List<Domain.Programme.ProgramViewModel> ListOfActionRecords { get; set; } = new List<Domain.Programme.ProgramViewModel>();
        public IEnumerable<int> ProgramList { get; set; } = new List<int>();
        public List<int> DuplicateProgramList { get; set; } = new List<int>();
        public List<PolicyPartnerCategory> PartnerCategoryWithDetails { get; set; } = new List<PolicyPartnerCategory>();
        public List<PolicyPartnerCategory> DuplicatePolicyPartnerCategoryDetailsList { get; set; } = new List<PolicyPartnerCategory>();

        public List<Domain.PolicyPartnerCategory.PolicyPartnerCategory> PolicyPartnerCategoryPartnerList { get; set; } = new List<Domain.PolicyPartnerCategory.PolicyPartnerCategory>();
        public List<Domain.PolicyPartnerCategory.PolicyPartnerCategory> DuplicatePolicyPartnerCategoryPartnerList { get; set; } = new List<Domain.PolicyPartnerCategory.PolicyPartnerCategory>();

        public List<PolicyTopicMap> PolicyTopicList { get; set; } = new List<PolicyTopicMap>();
        public List<PolicyTopicMap> DuplicatePolicyTopicList { get; set; } = new List<PolicyTopicMap>();
        private List<GTreeNode> TopicList { get; set; } = new();
        private List<Topic> AllTopics { get; set; } = new();
        private List<TopicParent> AllParentTopics { get; set; } = new();
        private string policyGoals { get; set; } = string.Empty;
        private bool IsEndDate { get; set; } = true;
        private bool IsStartDate { get; set; } = true;
        private bool RunTreeCode { get; set; } = false;
        public string AdoptedCheckedValue { get; set; } = "infrom";
        public string GovernmentDetails { get; set; }
        public int? AdoptedMonth { get; set; }

        private DateTime? StartDate;
        private DateTime? EndDate;
        private string[] topicKeys;
        public bool isEnglishTitleDisabled = false;
        public bool PolicyTitle = false;
        public bool PolicyEnglishTitle = false;
        public bool ProvinceRegion = false;
        public bool PublishedBy = false;
        public bool AdoptedBy = false;
        public bool ValidUrl = false;
        public bool IsModerationNotes = false;
        public string ModerationNotes = string.Empty;
        public string UserName = string.Empty;

        [Parameter] public string[] SelectedIds { get; set; }

        Tree<GTreeNode> topicTree;

        private string selectedNextWorkflowStatus = "Draft";
        private string defaultWorkflowStatus = "Draft";
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new List<RoleBaseWorkFlowLookUp>();
        public bool UseridVisible { get; set; } = false;

        //private _quillEditor quillEditorGovernmentDetailsRef;
        private Dictionary<string, _quillEditor> quillEditorGovernmentDetailsRef = new Dictionary<string, _quillEditor>();
        private _quillEditor quillEditorPolicyExtractByOriginalLanguageRef;
        private _quillEditor quillEditorFurtherNotesRef;
        private _quillEditor quillEditorReferencesRef;
        private _quillEditor quillEditorGoalsRef;
        private _quillEditor quillEditorStratogiesRef;
        private _quillEditor quillEditorIndicatorRef;
        private _quillEditor quillEditorLegislationRef;

        protected override async Task OnInitializedAsync()
        {
            _ = GetPartnerCategoriesPartners();
        }
        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;
            PolicyPartnerCategory policyPartnerFirst = PartnerCategoryWithDetails.Where(z => z.PartnerCategoryId == Int32.Parse(selectedTab)).Select(a => new PolicyPartnerCategory { PartnerCategoryId = a.PartnerCategoryId, Details = a.Details }).FirstOrDefault();
            if (policyPartnerFirst != null)
            {
                GovernmentDetails = policyPartnerFirst.Details;
            }
            else
            {
                GovernmentDetails = String.Empty;
            }
            return Task.CompletedTask;
        }

        private void ClearAllCountries()
        {
            countryValues = new List<string>();
            ListOfActionRecords = new List<ProgramViewModel>();
            StateHasChanged();
        }

        private async void OnCountriesChanged(IEnumerable<Country> countries)
        {
            try
            {
                if (countries != null && countries.Any())
                {
                    var country = countries.Select(c => c.Iso3Code).Distinct().ToList();
                    var dbPrograms = await CountryService?.GetActionCsvs(country);
                    ListOfActionRecords = dbPrograms ?? new List<ProgramViewModel>();  // Always initialize
                }
                else
                {
                    ProgramList = Enumerable.Empty<int>();
                    countryValues?.ToList()?.Clear();
                    ListOfActionRecords = new List<ProgramViewModel>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnCountriesChanged: {ex.Message}");
                ListOfActionRecords = new List<ProgramViewModel>();
            }
            finally
            {
                StateHasChanged();  // Trigger UI update
            }
        }


        string selectedMailValue;
        private void OnContributorChanged(UserModel value)
        {
            selectedMailValue = value.Id;
        }

        private async void OnnReveiw(string value)
        {
            selectedNextWorkflowStatus = value;
            if (selectedNextWorkflowStatus == WorkflowStatusToState.Delegated.ToString())
            {
                UseridVisible = true;
            }
            else if (selectedNextWorkflowStatus == WorkflowStatusToState.SentForCorrection.ToString())
            {
                UseridVisible = true;
                if (PolicyDetail.Id != 0)
                {
                    var policyRevisionHistory = await PolicyDraftService.GetAllPolicyLogByPolicyId(PolicyDetail.Id);

                    if (policyRevisionHistory != null)
                    {
                        selectedMailValue = policyRevisionHistory.OrderBy(s => s.RevisedDate).FirstOrDefault().UserName;
                    }
                }

            }
            else
            {
                UseridVisible = false;
            }
            StateHasChanged();
        }
        private async void OnSubmit()
        {

            if (!string.IsNullOrEmpty(selectedNextWorkflowStatus))
            {
                if (selectedNextWorkflowStatus.Equals("delegated") && string.IsNullOrEmpty(selectedMailValue))
                {
                    await OpenValidationToaster("Please select User");
                    return;
                }

                if (string.IsNullOrEmpty(PolicyDetail.Title))
                {
                    await OpenValidationToaster("Please fill the title");
                    return;
                }

                if (PolicyDetail.PolicyTypeId == null)
                {
                    await OpenValidationToaster("Please select policy type");
                    return;
                }

                if (countryValues == null || !countryValues.Any())
                {
                    await OpenValidationToaster("Please select at least one country");
                    return;
                }

                if (!DateForPublishYear.HasValue)
                {
                    await OpenValidationToaster("Please select publish year");
                    return;
                }
                if (UseridVisible)
                {
                    if (string.IsNullOrEmpty(selectedMailValue))
                    {
                        await OpenValidationToaster("Please select user");
                        return;
                    }
                }
                if (await PolicyTypeValidation.ValidateAll())
                {
                    await CreatePolicyDetails(selectedNextWorkflowStatus);
                }
                else
                {
                    await OpenValidationToaster("Please complete the mandatory or valid fields");
                }
            }

        }

        private void TopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            var node = checkedValue.Node;
            var parentId = node.DataItem.ParentId;
            var topicId = node.DataItem.TopicId;
            var title = node.DataItem.Title;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            var newpolicyTopic = new PolicyTopicMap()
            {
                PolicyId = NewPolicyId,
                TopicId = topicId
            };
            if ((bool)node.Checked)
            {
                AddChildren(node.DataItem);
            }
            else
            {
                RemoveChildren(node.DataItem);
            }
            canClearTopics = PolicyTopicList.Count > 0;
        }

        private void AddChildren(GTreeNode gTreeNode)
        {

            var children = gTreeNode.Children;
            int topicId = gTreeNode.TopicId;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            if (children.Count > 0)
            {
                foreach (var item in children)
                {
                    AddChildren(item);
                }
            }
            else
            {
                if (policyTopic == null)
                {
                    PolicyTopicList.Add(new PolicyTopicMap() { PolicyId = NewPolicyId, TopicId = topicId });
                }
            }
        }

        private void RemoveChildren(GTreeNode gTreeNode)
        {
            var children = gTreeNode.Children;
            int topicId = gTreeNode.TopicId;
            var policyTopic = PolicyTopicList.Where(a => a.TopicId == topicId).FirstOrDefault();
            if (children.Count > 0)
            {
                foreach (var item in children)
                {
                    RemoveChildren(item);
                }
            }
            else
            {
                if (policyTopic != null)
                {
                    PolicyTopicList.Remove(policyTopic);
                }
            }
        }

        private void ClearTopics()
        {
            if (PolicyTopicList != null && PolicyTopicList.Count > 0)
            {
                PolicyTopicList.Clear();
                topicTree.UncheckAll();
                canClearTopics = PolicyTopicList.Count > 0;
                StateHasChanged();
            }
        }

        private async void PartnerCheckboxClicked(int partnerId, bool checkedValue)
        {
            var CategoryId = Int32.Parse(selectedTab);
            var policyPartnerCatDetail = PartnerCategoryWithDetails.Where(a => a.PartnerCategoryId == CategoryId).FirstOrDefault();
            var newpolicyPartnerCatDetail = new PolicyPartnerCategory()
            {
                PartnerCategoryId = CategoryId,
                PolicyId = NewPolicyId,
                Details = $"{GovernmentDetails}"
            };
            var policyPartnerCatPartner = PolicyPartnerCategoryPartnerList.Where(a => a.CategoryId == CategoryId && a.PartnerId == partnerId).FirstOrDefault();

            var newpolicyPartnerCatPartner = new Domain.PolicyPartnerCategory.PolicyPartnerCategory()
            {
                CategoryId = CategoryId,
                PolicyId = NewPolicyId,
                PartnerId = partnerId
            };
            if ((bool)checkedValue)
            {
                if (policyPartnerCatDetail == null)
                {
                    PartnerCategoryWithDetails.Add(newpolicyPartnerCatDetail);
                }
                if (policyPartnerCatPartner == null)
                {
                    PolicyPartnerCategoryPartnerList.Add(newpolicyPartnerCatPartner);
                }
            }
            else
            {
                if (policyPartnerCatDetail != null)
                {
                    PartnerCategoryWithDetails.Remove(policyPartnerCatDetail);
                }
                if (policyPartnerCatPartner != null)
                {
                    PolicyPartnerCategoryPartnerList.Remove(policyPartnerCatPartner);
                }
            }
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }

        private async Task GetPolicyTypesAsync()
        {
            var dbPolicyTypes = await PolicyService?.GetPolicyTypes();
            PolicyTypes = new ObservableCollection<PolicyTypeViewModel>(dbPolicyTypes);
            Otherid = PolicyTypes.FirstOrDefault(p => p.Name.Equals("Other, please specify")).Id;
            StateHasChanged();
        }
        private void GetUsersAsync()
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var userList = (from user in _dbContext.Users.ToList()
                            join userRole in _dbContext.UserRoles
                           on user.Id equals userRole.UserId
                            join role in _dbContext.Roles
                            on userRole.RoleId equals role.Id
                            where user.Status == "Active"
                            select new AppUserModel()
                            {
                                Id = user.Id,
                                FirstName = user.FirstName,
                                LastName = user.LastName,
                                UserName = user.UserName,
                                Email = user.Email,
                                Organization = user.Organization,
                                Status = user.Status,
                                UserRoles = role.Name
                            }).ToList();

            foreach (var item in userList)
            {
                ContributorList.Add(new UserModel()
                {
                    Id = item.Id,
                    UserName = item.UserName,
                    DisplayName = String.Format("{0} {1}", item.FirstName, item.LastName)
                }); ;
            }
        }

        private async Task GetLanguagesAsync()
        {
            var dbLanguages = await PolicyService?.GetLanguagesAsync();
            Languages = new ObservableCollection<Domain.Languages.Language>(dbLanguages);
            if (PolicyDetail != null && PolicyDetail.LanguageId == null)
            {
                PolicyDetail.LanguageId = Languages.Where(e => e.Name == "English").FirstOrDefault()?.Id;
                isEnglishTitleDisabled = true;
            }
            StateHasChanged();
        }

        private async Task GetCountriesAsync()
        {
            var dbCountries = await CountryService.GetActiveCountries();
            Countries = new ObservableCollection<DbModels.Country>(dbCountries);
            StateHasChanged();
        }

        private async Task GetPartnerCategoriesPartners()
        {
            var dbPartnerCategoryPartners = await PolicyService?.GetPartnerCategoryPartnersAsync();
            PartnerCategoryPartners = new ObservableCollection<PartnerCategory>(dbPartnerCategoryPartners);
            selectedTab = PartnerCategoryPartners.FirstOrDefault()?.Id.ToString();
            PartnerCategoryPartners.ForEach(x => Details[x.Id.ToString()] = string.Empty);
            StateHasChanged();
        }

        private void GetListOfActionRecords()
        {
            var dbPrograms = new List<ProgramViewModel>();
            ListOfActionRecords = dbPrograms;

            StateHasChanged();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                try
                {
                    PolicyDetail.Adopted = false;
                    AllTopics = await PolicyService.GetTopicsAsync();
                    AllParentTopics = await PolicyService.GetParentTopics();
                    if (PolicyCode != 0)
                    {
                        PolicyDetail = await PolicyDraftService.GetPolicyRevisionDetailsAsync(PolicyCode, VersionId);
                        if (PolicyDetail == null)
                        {
                            NavigationManager.NavigateTo("NotFound");
                            return;
                        }
                        defaultWorkflowStatus = PolicyDetail.PolicyLog.FirstOrDefault()?.ToState;
                        PolicyAttachments = PolicyDetail.PolicyAttachmentDrafts.ToList();

                        if (PolicyDetail != null && PolicyDetail.PolicyTopicDrafts.Any())
                        {
                            PopulateTopics();
                        }
                    }
                    await GetTopicsAsync();
                    pageConfigrationCache.RefreshRequested += RefreshMe;
                    await GetConfigration();
                    RoleBaseWorkFlowLookUps = RoleBaseWorkFlowLookUp.RoleBaseWorkFlowLookUps(CurrentUserService.UserRole); ;

                    GetUsersAsync();
                    _ = GetPolicyTypesAsync();
                    _ = GetLanguagesAsync();
                    _ = GetCountriesAsync();
                    _ = EnableDisableEnglishTitle(PolicyDetail?.LanguageId);

                    if (PolicyCode > 0 && CountryCode != null)
                    {
                        if (PolicyDetail != null)
                        {
                            await PopulatePolicy();
                            selectedNextWorkflowStatus = "Published";

                            var updatedValue = PolicyDetail.PolicyLog.FirstOrDefault()?.ToState;
                            if (!string.IsNullOrEmpty(updatedValue) && (WorkflowStatusToState.SentForCorrection == updatedValue || WorkflowStatusToState.Delegated == updatedValue))
                            {
                                UseridVisible = true;
                                selectedMailValue = PolicyDetail.PolicyLog.FirstOrDefault()?.UserName;
                            }
                            if (PolicyDetail.Adopted.HasValue && PolicyDetail.Adopted == true)
                            {
                                IsAdoptedVisible = true;
                                AdoptedCheckedValue = "yes";
                            }
                            if (PolicyDetail.Adopted.HasValue && PolicyDetail.Adopted == false)
                            {
                                IsAdoptedVisible = false;
                                AdoptedCheckedValue = "infrom";
                            }
                            if (PolicyDetail.Adopted == null)
                            {
                                AdoptedCheckedValue = "na";
                            }

                            PopulateCountryToggles();
                            PopulatePolicyPartnerCategories();
                            PopulatePolicyCategoryPartners();
                            try
                            {
                                var dbPrograms = await CountryService?.GetActionCsvs(countryValues.ToList());

                                ListOfActionRecords = dbPrograms;

                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex.Message);
                            }
                            var createOrEdit = VersionId != 0 ? "Edited" : "Created";
                            UserName = $"{createOrEdit} by {CurrentUserService.UserName}";
                            ModerationNotes = PolicyDetail.PolicyLog.FirstOrDefault().OtherNotes;

                        }
                    }
                    else
                    {
                        UserName = $"Created by {CurrentUserService.UserName}";
                    }
                    //ModerationNotes = PolicyDetail.PolicyLog.FirstOrDefault().OtherNotes;
                    IsLoading = false;
                    await InvokeAsync(StateHasChanged);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error: {ex.Message}, stack trace:{ex.StackTrace}");
                    Console.Write($"Error: {ex.Message}, stack trace:{ex.StackTrace}");
                }
            }
        }


        private void PopulatePolicyCategoryPartners()
        {
            foreach (var partner in PolicyDetail.PolicyPartnerCategoryPartnerDrafts)
            {
                Domain.PolicyPartnerCategory.PolicyPartnerCategory policyPartner = new()
                {
                    PolicyId = partner.PolicyId,
                    CategoryId = partner.PartnerCategoryId,
                    PartnerId = partner.PartnerId,
                };
                PolicyPartnerCategoryPartnerList.Add(policyPartner);
            }
            DuplicatePolicyPartnerCategoryPartnerList = new List<Domain.PolicyPartnerCategory.PolicyPartnerCategory>(PolicyPartnerCategoryPartnerList);
        }

        private void PopulateTopics()
        {
            // Use List instead of preallocated array
            var topicKeysList = new List<string>(PolicyDetail.PolicyTopicDrafts.Count);

            // Use HashSet for O(1) lookups
            var parentTopicIds = new HashSet<int>(AllParentTopics.Select(p => p.ParentId));

            // Preallocate PolicyTopicList with expected size
            PolicyTopicList = new List<PolicyTopicMap>(PolicyDetail.PolicyTopicDrafts.Count);

            foreach (var policyTopic in PolicyDetail.PolicyTopicDrafts)
            {
                var policyTopicMap = new PolicyTopicMap
                {
                    PolicyId = policyTopic.PolicyId,
                    TopicId = policyTopic.TopicId,
                };
                PolicyTopicList.Add(policyTopicMap);

                if (policyTopic?.Topic?.TopicParents is { } topicParentList)
                {
                    foreach (var topicParent in topicParentList)
                    {
                        if (!parentTopicIds.Contains(topicParent.TopicId))
                        {
                            topicKeysList.Add($"{topicParent.ParentId}-{policyTopic.TopicId}");
                        }
                    }
                }
            }

            topicKeys = topicKeysList.ToArray();
            canClearTopics = topicKeys.Length > 0;

            // Duplicate list efficiently
            DuplicatePolicyTopicList = new List<PolicyTopicMap>(PolicyTopicList);
        }


        private void PopulatePolicyPartnerCategories()
        {
            foreach (var partnerCategory in PolicyDetail.PolicyCategoryPartnerDrafts)
            {
                Details[partnerCategory.PartnerCategoryId.ToString()] = partnerCategory.Details;
                PolicyPartnerCategory policyPartner = new()
                {
                    PolicyId = partnerCategory.PolicyId,
                    PartnerCategoryId = partnerCategory.PartnerCategoryId,
                    Details = partnerCategory.Details,
                };
                PartnerCategoryWithDetails.Add(policyPartner);
            }
            DuplicatePolicyPartnerCategoryDetailsList = new List<PolicyPartnerCategory>(PartnerCategoryWithDetails);
            PolicyPartnerCategory policyPartnerFirst = PartnerCategoryWithDetails.Select(a => new PolicyPartnerCategory { PartnerCategoryId = a.PartnerCategoryId, Details = a.Details }).FirstOrDefault();
            //if (policyPartnerFirst != null)
            //{
            //    selectedTab = policyPartnerFirst.PartnerCategoryId.ToString();
            //    GovernmentDetails = policyPartnerFirst.Details;
            //}
        }

        private async Task PopulatePolicy()
        {
            AdoptedCheckedValue = GetAdoptedValue(PolicyDetail.Adopted);
            if (PolicyDetail.StartYear.HasValue)
            {
                DateForStartYear = new DateTime(PolicyDetail.StartYear.Value, 1, 1);
            }
            if (!string.IsNullOrEmpty(PolicyDetail.StartMonth))
            {
                DateForStartMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(PolicyDetail.StartMonth), 1);
            }
            if (PolicyDetail.EndYear.HasValue && PolicyDetail.EndYear != 0)
            {
                DateForEndYear = new DateTime(PolicyDetail.EndYear.Value, 1, 1);
            }
            if (!string.IsNullOrEmpty(PolicyDetail.EndMonth))
            {
                DateForEndMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(PolicyDetail.EndMonth), 1);
            }

            if (PolicyDetail.PublishedYear.HasValue)
            {
                DateForPublishYear = new DateTime(PolicyDetail.PublishedYear.Value, 1, 1);
            }
            if (!string.IsNullOrEmpty(PolicyDetail.PublishedMonth) && PolicyDetail.PublishedMonth != "0")
            {
                DateForPublishMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(PolicyDetail.PublishedMonth), 1);
            }

            if (PolicyDetail.AdoptedYear.HasValue && PolicyDetail.AdoptedYear != 0)
            {
                DateForAdoptedYear = new DateTime(PolicyDetail.AdoptedYear.Value, 1, 1);
            }
            if (!string.IsNullOrEmpty(PolicyDetail.AdoptedMonth))
            {
                DateForAdoptedMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(PolicyDetail.AdoptedMonth), 1);
            }
            policyGoals = PolicyDetail.Extracts;
            isSaveDisable = false;
        }


        bool IsAdoptedVisible = false;

        Task AdoptedChanged(string value)
        {
            AdoptedCheckedValue = value;
            if (value == "yes")
            {
                PolicyDetail.Adopted = true;
                IsAdoptedVisible = true;
            }
            if (value == "infrom")
            {
                PolicyDetail.Adopted = false;
                IsAdoptedVisible = false;
                DateForAdoptedMonth = null;
                DateForAdoptedYear = null;
            }
            if (value == "na")
            {
                PolicyDetail.Adopted = null;
            }
            StateHasChanged();
            return Task.CompletedTask;
        }

        private static string GetAdoptedValue(bool? adopted)
        {
            if (adopted == null)
            {
                return "infrom";
            }
            else if (adopted == true)
            {
                return "yes";
            }
            else
            {
                return "na";
            }
        }

        private void PopulateCountryToggles()
        {
            countryValues = new List<string>(PolicyDetail.PolicyCountryDrafts.Select(s => s.CountryCode));
            DuplicateCountryValues = new List<string>(countryValues);
        }

        private void OnGoalsChanged(string value)
        {
            policyGoals = value;
        }

        private async Task GetTopicsAsync()
        {
            var policyTopicId = AllTopics.FirstOrDefault(t => t.Name == "Policy")?.Id;

            if (policyTopicId == null)
                return;

            var firstParentTopics = AllParentTopics
                .Where(pt => pt.ParentId == policyTopicId)
                .OrderBy(pt => pt.OrderKey)
                .ToList();

            await GetTopicTreeView(firstParentTopics);
            RunTreeCode = true;
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> parents)
        {
            TopicList.AddRange(parents.Select(pt => new GTreeNode
            {
                TopicId = pt.TopicId,
                ParentId = pt.ParentId,
                Key = $"{pt.ParentId}-{pt.TopicId}",
                Title = AllTopics.FirstOrDefault(t => t.Id == pt.TopicId)?.Name,
                Children = GetChildTopicTreeView(AllParentTopics
                    .Where(x => x.ParentId == pt.TopicId)
                    .OrderBy(t => t.OrderKey)
                    .ToList()).Result,
                IsSelected = false
            }));
        }
        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<TopicParent> parents)
        {
            var child = new List<GTreeNode>();

            foreach (var parent in parents.OrderBy(p => p.OrderKey))
            {
                var topic = AllTopics.FirstOrDefault(t => t.Id == parent.TopicId);

                if (topic == null)
                    continue;

                child.Add(new GTreeNode
                {
                    TopicId = parent.TopicId,
                    ParentId = parent.ParentId,
                    Key = $"{parent.ParentId}-{parent.TopicId}",
                    Title = topic.Name,
                    Children = await GetChildTopicTreeView(
                        AllParentTopics.Where(pt => pt.ParentId == parent.TopicId)
                                       .ToList()),
                    IsSelected = false
                });
            }

            return child;
        }

        private async Task CreatePolicyDetails(string nextStatusToApply)
        {
            IsLoading = true;
            var user = await UserManager.FindByIdAsync(CurrentUserService.UserId);

            if (user != null)
            {
                try
                {
                    await SaveImageToAzureStorage();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error occured while uploading file to blob storage: {ex.Message}");
                }
                await CreatePolicyRevision();
                await Reset();
            }

            if (selectedNextWorkflowStatus == WorkflowStatusToState.Published)
            {
                MemoryCache.Set("submit", "Policy published successfully");
                string topCountry = countryValues.FirstOrDefault();
                NavigationManager.NavigateTo($"/countries/{topCountry}/policies/{PolicyDetail.Id}");
            }
            else if (selectedNextWorkflowStatus == WorkflowStatusToState.NeedsReview)
            {
                MemoryCache.Set("submit", "Policy sent for review successfully");
                NavigationManager.NavigateTo("admin/dashboard");
            }
            else if (selectedNextWorkflowStatus == WorkflowStatusToState.SentForCorrection)
            {
                MemoryCache.Set("submit", "Policy sent for correction successfully");
                NavigationManager.NavigateTo("admin/dashboard");
            }
            else if (selectedNextWorkflowStatus == WorkflowStatusToState.Delegated)
            {
                MemoryCache.Set("submit", "Policy is delegated successfully");
                NavigationManager.NavigateTo("admin/dashboard");
            }
            else if (selectedNextWorkflowStatus == WorkflowStatusToState.Draft)
            {
                MemoryCache.Set("submit", "Policy draft created successfully");
                NavigationManager.NavigateTo("admin/dashboard");
            }
            IsLoading = false;
        }

        private async Task CreatePolicyRevision()
        {
            var policyLog = new PolicyLog();
            policyLog.RevisedDate = DateTime.UtcNow;
            policyLog.UserName = CurrentUserService.UserName;
            policyLog.OtherNotes = ModerationNotes;
            policyLog.ToState = selectedNextWorkflowStatus;
            policyLog.IsPublished = selectedNextWorkflowStatus == WorkflowStatusToState.Published;
            if (
                selectedNextWorkflowStatus == WorkflowStatusToState.Delegated
                || selectedNextWorkflowStatus == WorkflowStatusToState.SentForCorrection
            )
            {
                policyLog.DelegatedDate = DateTime.UtcNow;
                policyLog.DelegatedUserName = selectedMailValue;
            }
            if (PolicyDetail.Id == 0)
            {
                policyLog.FromState = selectedNextWorkflowStatus;
            }
            else
            {
                policyLog.PolicyId = PolicyDetail.Id;
                policyLog.FromState = PolicyDetail
                    .PolicyLog.OrderByDescending(s => s.Id)
                    ?.FirstOrDefault()
                    ?.ToState;
            }
            PolicyDetail.PolicyType = null;
            PolicyDetail.PolicyLog.Clear();
            PolicyDetail.PolicyCountryDrafts.Clear();
            PolicyDetail.PolicyAttachmentDrafts.Clear();
            PolicyDetail.PolicyCategoryPartnerDrafts.Clear();
            PolicyDetail.PolicyPartnerCategoryPartnerDrafts.Clear();
            PolicyDetail.PolicyProgramRevision.Clear();
            PolicyDetail.PolicyTopicDrafts.Clear();
            PolicyDetail.FurtherNotes = (
                await quillEditorFurtherNotesRef.GetHTML()
            ).SanitizeContent();
            PolicyDetail.References = (await quillEditorReferencesRef.GetHTML()).SanitizeContent();
            PolicyDetail.Extracts = (await quillEditorGoalsRef.GetHTML()).SanitizeContent();
            //PolicyDetail.Strategies = (await quillEditorStratogiesRef.GetHTML()).SanitizeContent();
            //PolicyDetail.Indicators = (await quillEditorIndicatorRef.GetHTML()).SanitizeContent();
            //PolicyDetail.Legislations = (await quillEditorLegislationRef.GetHTML()).SanitizeContent();
            PolicyDetail.PolicyLog.Add(policyLog);
            PolicyDetail.PolicyTopicDrafts = PolicyTopicList
                .Select(pt => new PolicyTopicDraft
                {
                    PolicyId = PolicyDetail.Id,
                    TopicId = pt.TopicId,
                    PolicyVId = PolicyDetail.VersionId,
                })
                .ToList();
            foreach (var policyPartner in PartnerCategoryPartners)
            {
                string details = (
                    await quillEditorGovernmentDetailsRef[
                        PartnerCategoryPartners.FirstOrDefault(p => p.Id == policyPartner.Id).Name
                    ]
                        .GetHTML()
                ).SanitizeContent();
                if (!string.IsNullOrEmpty(details))
                {
                    PolicyDetail.PolicyCategoryPartnerDrafts.Add(
                        new PolicyCategoryPartnerDraft()
                        {
                            Details = details,
                            PartnerCategoryId = policyPartner.Id,
                            PolicyId = PolicyDetail.Id,
                            PolicyVId = PolicyDetail.VersionId,
                        }
                    );
                }
            }
            foreach (var attachment in PolicyAttachments)
            {
                PolicyDetail.PolicyAttachmentDrafts.Add(
                    new PolicyAttachmentDraft()
                    {
                        FileDisplayName = attachment.FileDisplayName,
                        PolicyId = PolicyDetail.Id,
                        Url = attachment.Url,
                        FileType = attachment.FileType,
                        FileName = attachment.FileName,
                        PolicyVId = PolicyDetail.VersionId,
                    }
                );
            }
            foreach (var policyPartnerCategory in PolicyPartnerCategoryPartnerList)
            {
                PolicyDetail.PolicyPartnerCategoryPartnerDrafts.Add(
                    new PolicyPartnerCategoryPartnerDraft()
                    {
                        PartnerCategoryId = policyPartnerCategory.CategoryId,
                        PartnerId = policyPartnerCategory.PartnerId,
                        PolicyId = PolicyDetail.Id,
                        PolicyVId = PolicyDetail.VersionId,
                    }
                );
            }
            foreach (var code in countryValues)
            {
                PolicyDetail.PolicyCountryDrafts.Add(
                    new PolicyCountryDraft()
                    {
                        PolicyId = PolicyDetail.Id,
                        CountryCode = code,
                        PolicyVId = PolicyDetail.VersionId,
                    }
                );
            }
            foreach (var programId in ProgramList)
            {
                PolicyDetail.PolicyProgramRevision.Add(
                    new PolicyProgramRevision() { ProgramId = programId }
                );
            }
            try
            {
                await PolicyDraftService.SaveToPolicyRevision(PolicyDetail);
                string baseUrl = NavigationManager.BaseUri;
                string subject = "GIFNA data for your attention and action";
                string logedInUser = string.IsNullOrWhiteSpace(CurrentUserService.FullName)
                    ? CurrentUserService.Email
                    : CurrentUserService.FullName;
                string countryNames = string.Join(
                    ", ",
                    Countries.Where(c => countryValues.Contains(c.Iso3Code)).Select(c => c.Name)
                );
                string dataType = ContentType.Policies.GetDescription();
                string firstCountryCode =
                    Countries.OrderBy(x => x.Name).FirstOrDefault()?.Iso3Code ?? string.Empty;
                string titleLink =
                    $"{baseUrl}countries/{firstCountryCode}/policies/{PolicyDetail.Id}/{PolicyDetail.VersionId}";
                if (policyLog.ToState == WorkflowStatusToState.Delegated)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(
                            selectedMailValue
                        );
                        ContentDelegationEmailModel delegationModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = PolicyDetail.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink,
                        };
                        await EmailServices.SendEmailByTemplateAsync(
                            selectedMailValue,
                            subject,
                            TemplateType.DelegateEmail,
                            delegationModel
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
                else if (policyLog.ToState == WorkflowStatusToState.NeedsReview)
                {
                    var approverUsers = await CurrentUserService.GetApproverUsersByCountry(
                        countryValues.ToHashSet()
                    );
                    var tasks = approverUsers.Select(async user =>
                    {
                        try
                        {
                            ContentReviewEmailModel reviewModel = new()
                            {
                                BaseUrl = baseUrl,
                                ReceivedBy = user.FullNameOrEmail,
                                CountryNames = countryNames,
                                ContentTitle = PolicyDetail.Title,
                                SubmitedBy = logedInUser,
                                OtherNotes = ModerationNotes,
                                DataType = dataType,
                                TitleLink = titleLink,
                            };
                            await EmailServices.SendEmailByTemplateAsync(
                                user.Email,
                                subject,
                                TemplateType.NeedsReview,
                                reviewModel
                            );
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Failed to send email to {user.Email}");
                        }
                    });
                    await Task.WhenAll(tasks);
                }
                else if (policyLog.ToState == WorkflowStatusToState.SentForCorrection)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(
                            selectedMailValue
                        );
                        ContentCorrectionEmailModel correctionModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = PolicyDetail.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink,
                        };
                        await EmailServices.SendEmailByTemplateAsync(
                            selectedMailValue,
                            subject,
                            TemplateType.SendForCorrection,
                            correctionModel
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    $"an error has been occured while saving new policy revision: {ex.Message}/n and the stack trace {ex.StackTrace}"
                );
            }
        }



        private async Task Reset()
        {
            FileDisplayName = string.Empty;
            BlobFileName = string.Empty;
            ImageError = string.Empty;
            await FileEdit.Reset().AsTask();
        }

        private async Task OnChanged(FileChangedEventArgs args)
        {
            try
            {
                ImageError = string.Empty;
                IsAccordianLoading = true;
                var files = args.Files;
                if (files != null && files.Any(e => e.Type != "application/pdf"))
                {
                    ImageError = "Only pdf is allowed";
                    IsAccordianLoading = false;
                    return;
                }
                foreach (var file in files)
                {
                    if (file.Size <= ImageSize)
                    {
                        await using MemoryStream fs = new();
                        await file.OpenReadStream(maxAllowedSize: ImageSize).CopyToAsync(fs);
                        var shortFileName = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                        BlobFileName = $"{Guid.NewGuid():N}-{shortFileName}";
                        ImageBytes.Add(BlobFileName, GetBytes(fs));
                        FileDisplayName = file.Name;
                        FileInfo fi = new(shortFileName);
                        CreateFileDbData(BlobFileName, fi.Extension, FileDisplayName);
                        ImageError = string.Empty;
                    }
                    else
                    {
                        ImageError = "File size is too big";
                    }
                }
                IsAccordianLoading = false;
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }
        }

        private void CreateFileDbData(string bolbFileName, string fileType, string fileDisplayName)
        {
            PolicyAttachmentDraft policyAttachment = new()
            {
                FileName = bolbFileName,
                FileType = fileType,
                FileDisplayName = fileDisplayName,
                Url = BlobFileName
            };
            PolicyAttachments.Add(policyAttachment);
        }

        public static byte[] GetBytes(Stream stream)
        {
            var bytes = new byte[stream.Length];
            stream.Seek(0, SeekOrigin.Begin);
            stream.ReadAsync(bytes, 0, bytes.Length);
            stream.Dispose();
            return bytes;
        }

        private async Task SaveImageToAzureStorage()
        {
            string blobStorageConnectionString = new Helpers.AppSettingsHelper(Configuration).GetBlobStorageConnectionString();

            if (string.IsNullOrWhiteSpace(blobStorageConnectionString)
                || string.IsNullOrWhiteSpace(BlobFileName)
                || !ImageBytes.Any())
            {
                // TODO: show error
                return;
            }
            CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
            CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
            CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Gina2.Core.Constants.BlobStorage.DataTypeContainerName);
            foreach (var item in ImageBytes)
            {
                CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(item.Key);
                await cloudBlockBlob.DeleteIfExistsAsync();
                cloudBlockBlob.Properties.ContentType = "application/pdf";
                await cloudBlockBlob.UploadFromByteArrayAsync(item.Value, 0, item.Value.Length);
            }
            ImageBytes = null;
            ImageType = string.Empty;
        }

        private void DataChangingStartMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartMonth = dateTimeChangedEventArgs.Date;
            PolicyDetail.StartMonth = dateTimeChangedEventArgs.Date.Value.Month.ToString();
        }
        private void DataChangingStartYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartYear = dateTimeChangedEventArgs.Date;
            PolicyDetail.StartYear = dateTimeChangedEventArgs.Date.Value.Year;
        }

        private void ClearStartMonth()
        {
            PolicyDetail.StartMonth = null;
        }

        private void ClearStartYear()
        {
            PolicyDetail.StartYear = null;
        }

        private void ClearEndDate()
        {
            PolicyDetail.EndMonth = null;
        }

        private void ClearEndYear()
        {
            PolicyDetail.EndYear = null;
        }

        private void ClearPublishedMonth()
        {
            PolicyDetail.PublishedMonth = null;
        }
        private void ClearPublishedYear()
        {
            PolicyDetail.PublishedYear = null;
        }

        private void ClearAdoptedMonth()
        {
            PolicyDetail.AdoptedMonth = null;
        }

        private void ClearAdoptedYear()
        {
            PolicyDetail.AdoptedYear = null;
        }
        private void DataChangingEndMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForEndMonth = dateTimeChangedEventArgs.Date;
            PolicyDetail.EndMonth = dateTimeChangedEventArgs.Date.Value.Month.ToString();
        }

        private void DataChangingEndYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForEndYear = dateTimeChangedEventArgs.Date;
            PolicyDetail.EndYear = dateTimeChangedEventArgs.Date.Value.Year;

        }
        private bool DisableEndMonth(DateTime endDate)
        {
            DateTime? createdDateTime = null;
            if (DateForStartMonth.HasValue && DateForStartYear.HasValue)
            {
                int year = DateForStartYear.Value.Year;
                int month = DateForStartMonth.Value.Month;

                // Assuming you want the first day of the selected month and year
                createdDateTime = new DateTime(year, month, 1);

            }
            return createdDateTime.HasValue && endDate.Date < createdDateTime.Value.Date;
        }

        private bool DisableEndDate(DateTime endDate)
        {
            return DateForStartYear.HasValue && endDate.Year < DateForStartYear.Value.Year;
        }
        private void DataChangingPublishMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForPublishMonth = dateTimeChangedEventArgs.Date;
            PolicyDetail.PublishedMonth = dateTimeChangedEventArgs.Date.Value.Month.ToString();
        }

        private void DataChangingPublishYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForPublishYear = dateTimeChangedEventArgs.Date;
            PolicyDetail.PublishedYear = dateTimeChangedEventArgs.Date.Value.Year;
        }


        string ExpandCollapseName = "Expand all";
        bool isExpandingOrCOllapsing = false;
        public async Task VisibleExpandButton()
        {
            await Task.Run(() =>
            {
                isExpandingOrCOllapsing = true;
            });

            if (ExpandCollapseName == "Expand all")
            {
                topicTree.ExpandAll();
                ExpandCollapseName = "Collapse all";
            }
            else
            {
                topicTree.CollapseAll();
                ExpandCollapseName = "Expand all";
            }
            await Task.Run(() =>
            {
                isExpandingOrCOllapsing = false;
            });
            StateHasChanged();
        }

        private void DataChangingAdoptedMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForAdoptedMonth = dateTimeChangedEventArgs.Date;
            PolicyDetail.AdoptedMonth = dateTimeChangedEventArgs.Date.Value.Month.ToString();
        }

        private void DataChangingAdoptedYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForAdoptedYear = dateTimeChangedEventArgs.Date;
            PolicyDetail.AdoptedYear = dateTimeChangedEventArgs.Date.Value.Year;
        }
        public static void ValidateSelect(ValidatorEventArgs e)
        {
            var selectedValue = Convert.ToInt32(e.Value);
            e.Status = selectedValue != 0 ? ValidationStatus.Success : ValidationStatus.Error;
        }


        public void ValidatePublishedBy(string value)
        {
            PublishedBy = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            PolicyDetail.PublishedBy = value;
        }
        public void ValidateProvinceRegion(string value)
        {
            ProvinceRegion = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            PolicyDetail.SubnationalGeographicArea = value;
        }

        public void ValidateAdoptedBy(string value)
        {
            AdoptedBy = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            PolicyDetail.AdoptedBy = value;
        }

        public void ValidateUrl(string value)
        {
            AdoptedBy = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            PolicyDetail.Url = value;
        }

        public void ValidateModerationNotes(string value)
        {
            IsModerationNotes = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ModerationNotes = value;
        }

        private bool canClearTopics = false;

        private Validation validateText { get; set; }
        public void ValidateEndDate(ValidatorEventArgs e)
        {
            var selectedValue = Convert.ToDateTime(e.Value);
            e.Status = (selectedValue > StartDate) ? ValidationStatus.Success : ValidationStatus.Error;
        }
        private bool isSaveDisable { get; set; } = true;

        private void TitleKeyPress(string value)
        {
            PolicyTitle = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}") ? true : false;
            PolicyDetail.Title = value;
        }

        private void EnglishTitleKeyPress(string value)
        {
            PolicyEnglishTitle = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}") ? true : false;
            PolicyDetail.EnglishTitle = value;
        }

        private Task ChangePolicyType(int? value)
        {
            if (value == 0)
            {
                isSaveDisable = true;
            }
            else
            {
                if (!string.IsNullOrEmpty(PolicyDetail.Title))
                {
                    isSaveDisable = false;

                }
                else
                {
                    isSaveDisable = true;
                }
            }
            PolicyDetail.PolicyTypeId = value;
            return Task.CompletedTask;
        }

        private static bool? GetAdoptedDbValue(string value)
        {
            if (value == "yes")
            {
                return true;
            }
            else if (value == "na")
            {
                return false;
            }
            else
            {
                return null;
            }
        }

        private async Task SavePolicyAttachments()
        {
            List<PolicyAttachment> policyAttachmentList = new List<PolicyAttachment>();
            foreach (var attachment in PolicyAttachments)
            {
                PolicyAttachment policyAttachment = new PolicyAttachment();
                policyAttachment.PolicyId = NewPolicyId;
                policyAttachment.Url = attachment.Url;
                policyAttachment.FileDisplayName = attachment.FileDisplayName;
                policyAttachment.FileName = attachment.FileName;
                policyAttachment.FileType = attachment.FileType;
                policyAttachmentList.Add(policyAttachment);
                ;
            }
            await PolicyService.SavePolicyAttachment(policyAttachmentList);
        }


        private async Task Download()
        {
            ExportCsvLoading = true;
            List<int> policyIds = new List<int>
            {
                PolicyCode
            };
            var search = new GlobalSearchRequest() { DownloadByDataItem = true };
            var data = await FileDownloadService.GetPolicyDataforCSV(search, policyIds);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());
                await JsRuntime.InvokeVoidAsync("saveAsFile", $"Policies.csv", fileData);
            }
            ExportCsvLoading = false;
            StateHasChanged();
        }
        private async Task EnableDisableEnglishTitle(int? selectedLang)
        {
            PolicyDetail.LanguageId = selectedLang;
            if (selectedLang.HasValue && selectedLang.Value != 2481)
            {
                isEnglishTitleDisabled = false;
            }
            else
            {
                isEnglishTitleDisabled = true;
                PolicyDetail.EnglishTitle = String.Empty;
            }
            await InvokeAsync(StateHasChanged);
        }

        public bool CanSave()
        {
            return PolicyTitle || PolicyEnglishTitle || AdoptedBy || ValidUrl
                || PublishedBy || ProvinceRegion || IsModerationNotes || !string.IsNullOrEmpty(ImageError);
        }

        public void RemoveFile(PolicyAttachmentDraft file)
        {
            PolicyAttachments.Remove(file);
            ImageBytes.Remove(file.Url);
        }
    }
}
