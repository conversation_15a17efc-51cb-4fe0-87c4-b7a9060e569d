﻿<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit tabedit">
            <DropdownToggle Class="aboutmenu" Color="Color.Primary" Split />
            <DropdownMenu>
                <DropdownItem href="@($"/commitments/{MechanismCode}")">View published</DropdownItem>
                <DropdownItem href="@($"/commitments/{MechanismCode}/moderate")">Moderate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
<Container Fluid Padding="Padding.Is0">
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
        <Div Class="item1 flex-b">
            <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/commitments"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to Mechanisms
            </Button>
        </Div>
        <Div Class="item2">
             <Button Class="but-yellow"><Icon class="arrow-bottom" /> PDF</Button>
            <Button Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
        </Div>
    </Div>
</Container>
<Container Fluid Class="newdraft" Padding="Padding.Is0">
    <Container Padding="Padding.Is0" Class="pt-6 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new commitments</Heading>
        <Divider Class="divi-blue" />
    </Container>
    <Container Class="form-newd">
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Title<Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Enter policy title here..."></TextEdit>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Type<Span>*</Span></FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@TypeOfPolicyData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Country(ies)</FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@LanguageData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Year of establishment</FieldLabel>
                <GinaDate />
            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>Lead government agency in mechanism</FieldLabel>
                <TextEdit Placeholder="Prime Minister's Office"></TextEdit>
            </Field>
        </Fields>

        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Government</Tab>
                <Tab Name="Tab2">Bilateral and d...</Tab>
                <Tab Name="Tab3">UN agencies</Tab>
                <Tab Name="Tab4">International N...</Tab>
                <Tab Name="Tab5">Intergovernm...</Tab>
                <Tab Name="Tab6">National NG...</Tab>
                <Tab Name="Tab7">Research / A...</Tab>
                <Tab Name="Tab8">Private Sector</Tab>
                <Tab Name="Tab9">Other</Tab>
            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the government sector(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab2">
                    Content for profile.
                </TabPanel>
                <TabPanel Name="Tab3">
                    Content for messages.
                </TabPanel>
                <TabPanel Name="Tab4">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab5">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab6">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab7">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab8">
                    Content for settings.
                </TabPanel>
                <TabPanel Name="Tab9">
                    Content for settings.
                </TabPanel>
            </Content>
        </Tabs>
    </Container>

    <Container Class="form-newd mt-4">
        <Heading Size="HeadingSize.Is5" Class="ginah5mec">Function(s)</Heading>
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Coordination</Tab>
                <Tab Name="Tab2">Monitoring</Tab>

            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Coordination</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                </TabPanel>
                <TabPanel Name="Tab2">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Monitoring</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                </TabPanel>
                <Field Class="pt-2">
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div Class="item1">
                            <FieldLabel>Mandate</FieldLabel>
                        </Div>
                        <Div Class="item2">
                            <Select TValue="int">
                                <SelectItem Value="0">Filtered HTML</SelectItem>
                                <SelectItem Value="1">Full HTML</SelectItem>
                                <SelectItem Value="2">Plain text</SelectItem>
                                <SelectItem Value="3">PHP code</SelectItem>
                            </Select>
                            <Tooltip Text="Disable rich-text More information about text formats">
                                <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                            </Tooltip>
                        </Div>
                    </Div>
                    <RextEditors />
                </Field>
            </Content>
        </Tabs>
    </Container>
    <Container Class="mt-4 w-98 mo-m-0">
        <Row>
            <Column Class="form-newd mr-1 mo-mr-0 w-100">
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Topics</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Button Class="but-gray">Clear</Button>
                    </Div>
                </Div>
                <Divider Padding="Padding.Is0" Class="m-0" />
                <GinaTreeView />
            </Column>
        </Row>
    </Container>
    <Container Class="mt-4 w-98 mo-m-0">
        <Row>
            <Column Class="form-newd mr-1 mo-mr-0 w-100">
                <FieldLabel Class="gina-bold">Link to policy(ies)</FieldLabel>
                <Divider />
                <Addons Class="pb-3">
                    <Addon AddonType="AddonType.Start">
                        <Button Color="Color.Light">
                            <Icon Name="IconName.Search" />
                        </Button>
                    </Addon>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit Placeholder="Some text value..." />
                    </Addon>
                </Addons>
                <Repeater Items="@ListOfActionRecords">
                    <Check TValue="bool">@context</Check>
                </Repeater>
            </Column>
            <Column Class="form-newd gina-new ml-1 w-100">
                <FieldLabel Class="gina-bold">Link to action(s)</FieldLabel>
                <Divider />
                <Addons Class="pb-3">
                    <Addon AddonType="AddonType.Start">
                        <Button Color="Color.Light">
                            <Icon Name="IconName.Search" />
                        </Button>
                    </Addon>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit Placeholder="Some text value..." />
                    </Addon>
                </Addons>
                <Repeater Items="@ListOfActionRecords">
                    <Check TValue="bool">@context</Check>
                </Repeater>
            </Column>
        </Row>
    </Container>

    <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>Lessons learnt</FieldLabel>
            <RextEditors></RextEditors>
        </Field>
        <Field>
            <FieldLabel>URL link</FieldLabel>
            <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
            <TextEdit Placeholder="Enter policy title here..."></TextEdit>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Notes</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>

            <RextEditors />
        </Field>
    </Container>
    <Container Class="mt-4 pb-6">
        <Button Class="but-blues" Clicked="@(()=>snackbar.Show())">Save</Button>
        <br />
        <Heading Class="alert-warn mt-3">New content: Your draft will be placed in moderation.</Heading>
        <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
            <SnackbarBody>
                New content: Your draft will be placed in moderation.
            </SnackbarBody>
        </Snackbar>
    </Container>
</Container>
