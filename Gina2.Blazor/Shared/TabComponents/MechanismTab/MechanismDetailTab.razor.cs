﻿using AutoMapper;
using Domain.TopicList;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.Pdf;
using Gina2.Blazor.Pages;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using Gina2.MySqlRepository.Models;
using Gina2.Services.FileDownload;
using Gina2.Services.Mechanism;
using Gina2.Services.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Gina2.Blazor.Shared.TabComponents.MechanismTab
{
    public partial class MechanismDetailTab : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }

        [Inject]
        public IMapper _mapper { get; set; }

        [Inject]
        private IMechanismService MechanismService { get; set; }
        [Inject]
        private IMechanismRevisionService MechanismRevisionService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }

        [CascadingParameter(Name = "MechanismCode")]
        public int MechanismCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int VersionId { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        private string CountryList { get; set; }
        private MechanismRevision MechanismDetail = new();
        private List<PartnerDetail> PartnerItems { get; set; } = new List<PartnerDetail>();
        private string mechanismUrl = string.Empty;

        private bool revision = false;

        private List<Models.PolicyTopic> Topics { get; set; } = new List<Models.PolicyTopic>();
        private List<MechanismPartnerCategoryMapItem> MechanismPartnerList { get; set; } = new();
        private List<DbModels.MechanismTopic> MechanismTopicList { get; set; } = new();

        private List<ListTopics> TopicList = new List<ListTopics>();

        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private List<int> topicparent { get; set; } = new List<int>();
        private IEnumerable<MechanismLog> mechanismLogInfo = Enumerable.Empty<MechanismLog>();
        private bool EnablePhase2Features = false;
        private bool ExportPdfLoading { get; set; }
        private bool ExportCsvLoading { get; set; }

        protected override void OnInitialized()
        {
            object data = MemoryCache.Get("submit");
            MemoryCache.Remove("submit");
            if (data != null)
            {
                _ = OpenToaster(data.ToString(), "", AntDesign.NotificationType.Success);

            }
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                EnablePhase2Features = Convert.ToBoolean(Configuration["EnablePhase2Features"]);
                MechanismDetail = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(MechanismCode, VersionId);

                if (MechanismDetail == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                if(!string.IsNullOrEmpty(CountryCode))
                {
                    var IsMechanismBelongsToCountryInURL = MechanismDetail.MechanismCountryMapRevision.FirstOrDefault(c=> c.CountryCode == CountryCode)  != null? true: false;
                    if ( !IsMechanismBelongsToCountryInURL)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }
                }
                mechanismLogInfo = await MechanismRevisionService.GetLogByMechanismId(MechanismCode);
                GetMechansimDetailsAsync();
                await GetTopicsAsync();
                await InvokeAsync(StateHasChanged);
                _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "mechanismhiddenbg");
                _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "mechanismbgdetailhidden");
            }
        }
                
        private void GetMechansimDetailsAsync()
        {
            SetMechanismUrl();

            ConcatCountryNames(MechanismDetail.MechanismCountryMapRevision.ToList());

            foreach (var item in MechanismDetail.MechanismPartnerRevision.OrderBy(m => m.PartnerCategory.DragAndDropKey))
            {
                var parent = PartnerItems.Where(x => x.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault();
                if (parent != null)
                {
                    parent.PartnerData.Add(new() { Name = item.Partner.Name, DragAndDropKey = item.Partner.DragAndDropKey });
                }
                else
                {
                    List<PartnerOrder> partner = new List<PartnerOrder>();
                    partner.Add(new() { Name = item.Partner.Name, DragAndDropKey = item.Partner.DragAndDropKey });
                    PartnerItems.Add(new PartnerDetail()
                    {
                        PartnerName = item.PartnerCategory.Name,
                        PartnerCategoryId = item.PartnerCategoryId,
                        PartnerData = partner,
                        PartnerDetails = MechanismDetail.MechanismPartnerCategoryDetailsRevision.Where(w => w.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault()?.Details ?? string.Empty
                    });
                }
            }
        }

        private void SetMechanismUrl()
        {
            if (!string.IsNullOrEmpty(MechanismDetail?.Url))
            {
                string url = MechanismDetail.Url;

                if (Uri.TryCreate(url, UriKind.Absolute, out Uri uriResult)
                && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
                {
                    mechanismUrl = url;
                }
            }
        }

        private async Task GetTopicsAsync()
        {
            MechanismTopicList = await MechanismService.GetMechanismTopicsAsync(MechanismCode);
            AllTopics = await MechanismService.GetTopicsAsync();
            AllParentTopics = await MechanismService.GetParentTopics();
            var First = AllParentTopics.Where(x => x.ParentId.Equals(AllTopics.Where(t => t.Name.Equals("Mechanism")).FirstOrDefault().Id)).OrderBy(t => t.OrderKey);
            await GetSlectedValue();
            await GetTopicTreeView(First);
            var topicChilds = new List<string>();
            foreach (var item in TopicList.Where(w => w.ChildName.Any()))
            {
                foreach (var child in item.ChildName)
                {
                    topicChilds.Add(child.ParentName);
                }
            }
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> First)
        {
            foreach (var item in First)
            {
                if (topicparent.Any(x => x == item.TopicId) || MechanismTopicList.Any(x => x.TopicId == item.TopicId))
                {
                    TopicList.Add(new ListTopics()
                    {
                        ParentName = AllTopics.Where(t => t.Id == item.TopicId).First().Name,
                        ChildName = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                        isSelected = topicparent.Any(x => x == item.TopicId)
                    });
                }
            }
        }

        private async Task<List<ListTopics>> GetChildTopicTreeView(IEnumerable<TopicParent> First)
        {
            List<ListTopics> child = new List<ListTopics>();
            foreach (var item in First)
            {
                if (topicparent.Any(x => x == item.TopicId) || MechanismTopicList.Any(x => x.TopicId == item.TopicId))
                {
                    ListTopics itemchild = new ListTopics();
                    child.Add(new ListTopics()
                    {
                        ParentName = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                        ChildName = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                        isSelected = topicparent.Any(x => x == item.TopicId) ||
                            MechanismTopicList.Any(x => x.TopicId == item.TopicId) ? true : false
                    });
                }
            }
            return child;
        }

        private async Task GetSlectedValue()
        {
            foreach (var item in MechanismTopicList)
            {
                var topic = AllParentTopics.Where(x => x.TopicId == item.TopicId).ToList();
                foreach (var value in topic)
                {
                    if (AllTopics.Any(x => x.Id == value.TopicId))
                    {
                        topicparent.Add(value.ParentId);
                        await GetParentArray(value);
                    }
                }
            }
        }

        private async Task GetParentArray(TopicParent parent)
        {
            var parentId = AllParentTopics.Where(x => x.TopicId == parent.ParentId)?.FirstOrDefault();
            if (parentId != null)
            {
                topicparent.Add(parentId.ParentId);
                await GetParentArray(parentId);
            }
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }

        private async Task DownloadCsv()
        {
            ExportCsvLoading = true;

            List<int> mechanismCode = new List<int>
            {
                MechanismCode
            };
            var search = new GlobalSearchRequest() { DownloadByDataItem = true };
            var data = await FileDownloadService.GetMechanismsforCSV(search, mechanismCode);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());
                await JsRuntime.InvokeVoidAsync("saveAsFile", "Mechanisms.csv", fileData);
            }

            ExportCsvLoading = false;
        }

        private async Task DownloadPdf()
        {
            ExportPdfLoading = true;
            var Mechanism = new MechanismPdf()
            {
                EnglishTitle = MechanismDetail.EnglishTitle,
                LeadGovernmentAgency = MechanismDetail.LeadGovernmentAgency,
                LessonsLearnt = MechanismDetail.LessonsLearnt,
                Mandate = MechanismDetail.Mandate,
                Notes = MechanismDetail.Notes,
                StartMonth = MechanismDetail.StartMonth,
                StartYear = MechanismDetail.StartYear,
                Url = MechanismDetail.Url,
                References = MechanismDetail.References,
            };

            var MechanismDatadictionary = JObject.FromObject(Mechanism).ToObject<Dictionary<string, string>>();
            var mechanismType = await MechanismService.GetMechanismTypeName(MechanismDetail.MechanismTypeId);
            StringBuilder htmlDetails = new StringBuilder();
            int index = 0;
            if (MechanismDatadictionary != null)
            {
                foreach (var item in MechanismDatadictionary)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                    {
                        index++;
                        htmlDetails.Append($@"<tr>
                  <td>
                      <table id='colorheader{index}'>
                          <tr>
                              <td align='left'>
                                  {item.Key}
                              </td>
                          <tr>
                      </table>
                  </td>
              </tr>
               <tr>
                  <td><table id='value{index}'>
                          <tr>
                              <td align='left'>{item.Value}</td>
                          <tr>
                      </table>
                  </td>

              </tr>");
                    }
                }
            }
            StringBuilder coordinations = new StringBuilder();
            StringBuilder monitorings = new StringBuilder();
            StringBuilder topics = new StringBuilder();
            StringBuilder partners = new StringBuilder();
            if (MechanismDetail.MechanismCoordinationRevision.Any())
            {
                coordinations.Append("<tr><td>Coordination</td></tr>");
            }
            foreach (var item in MechanismDetail.MechanismCoordinationRevision)
            {
                coordinations.Append("<tr><td>" + item.Coordination.Name + "</td></tr>");
            }
            if (MechanismDetail.MechanismMonitoringRevision.Any())
            {
                monitorings.Append("<tr id='monitoring'><td>Monitoring");
                foreach (var item in MechanismDetail.MechanismMonitoringRevision)
                {
                    monitorings.Append($"<tr><td>{item.Monitoring.Name}</td></tr>");
                }
            }
            if (TopicList.Any())
            {
                topics.Append("<tr><td><table id='topic'><tr><td>Topics</td>");
                foreach (var item in TopicList)
                {
                    topics.Append($"<tr><td>{item.ParentName}</td></tr>");
                    if (item.ChildName != null)
                    {
                        foreach (var child in item.ChildName)
                        {
                            topics.Append($"<tr><td>&nbsp;&nbsp;&nbsp;{child.ParentName}</td></tr>");
                        }
                    }
                }
                topics.Append("</td></table></td></tr>");
            }
            if (PartnerItems.Any())
            {
                partners.Append($"<tr><td><table id='partner'><tr><td>Parnters Involved</td>");
                foreach (var partner in PartnerItems)
                {
                    partners.Append($"<tr><td>{partner.PartnerName}</td></tr>");
                    foreach (var partnerData in partner.PartnerData)
                    {
                        partners.Append($"<tr><td>&nbsp;&nbsp;&nbsp;{partnerData.Name}</td></tr>");
                    }
                    if (!string.IsNullOrEmpty(partner.PartnerDetails))
                    {
                        partners.Append($"<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Details: {partner.PartnerDetails}</td></tr>");
                    }
                }
                partners.Append("</td></table></td></tr>");
            }
            string partnterAndTopicTogether = "<tr><table id='topic-partner'><tr><td>" + topics.ToString() + "</td> <td>" + partners.ToString() + "</td></tr></table></tr>";

            string html = @$"<table id='table'>
              <tr>
                  <td>
                      <table id='header'>
                          <tr>
                              <td align='center'>Global database on the Implementation<br/>of Nutrition Action (GIFNA)</td>
                              <td></td>
                          </tr>
                      </table>
                  </td>
              </tr>
              <tr>
                  <td>
                      <table id='subheader'>
                          <tr>
                              <td>{MechanismDetail.Title} </td>
                          </tr>
                      </table>
                  </td>
              </tr>
                <tr>
                   <td> <table style='border:1px solid;' id='table-body'>
                          <tr>
                              <td>Type of mechanism: {mechanismType} </td>
                              <td>Country(ies): {string.Join(", ", MechanismDetail.MechanismCountryMapRevision.Select(w => w.Country.Name))} </td>
                          </tr>
                          {coordinations}
                          {monitorings}
                      </table>
                    </td>
                </tr>
                            {htmlDetails}

                       </table>
                  </td>
              </tr>
            {topics}
            {partners}
            <tr>
                  <td>
                      <table id='footer'>
                          <tr>
                              <td>
                              </td>
                              <td>
                                  © World Health Organization <br/>
                                  2012. All rights reserved.
                              </td>
                              <td>
                                  {DateTime.Now.Date.ToString("MMM dd yyyy")}
                              </td>
                              <td>
                                  {NavigationManager.Uri}
                              </td>
                          </tr>
            </table>
            </td>
              </tr>
            </table>";
            ExportPdfLoading = false;
            await JsRuntime.InvokeVoidAsync("CommitmentsPdf", $"Mechanism - {MechanismDetail.Title}.pdf", html, index);
        }

        private string GetTopicsCsv(List<ListTopics> topicDetails)
        {
            StringBuilder topic = new StringBuilder();

            foreach (var item in topicDetails)
            {
                topic.AppendLine($"<li>{item.ParentName}</li>");
                if (item.ChildName != null)
                {
                    string topicchild = GetTopicsCsv(item.ChildName);
                    topic.AppendLine($"<li>{topicchild}</li>");
                }
            }

            return topic.ToString();
        }

        private void ConcatCountryNames(List<MechanismCountryMapRevision> mechanismCountryMapItems)
        {
            List<string> countries = new List<string>();
            foreach (var item in mechanismCountryMapItems)
            {
                countries.Add($"<a href='mechanisms/{item.MechanismId}'>{item.Country.Name}</a>");
            }
            CountryList = String.Join(", ", countries);
        }

        private MarkupString GetFormattedNotes(string value) => (MarkupString)value;
    }
}