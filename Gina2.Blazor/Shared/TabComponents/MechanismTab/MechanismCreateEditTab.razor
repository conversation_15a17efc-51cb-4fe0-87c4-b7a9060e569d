﻿@using Gina2.Blazor.Models.AdminModel
@using Gina2.Core.Models
@using Gina2.DbModels
@using Gina2.Blazor.Helpers.PageConfigrationData
@using static Gina2.Core.Constants;
@inherits PageConfirgurationComponent

<Loader IsLoading="@IsLoading" />
@if (mechanism != null)
{
    <Div Fluid Class="newdraft _antdesign pl-2 pr-2">
        <Div hidden="@(MechanismCode > 0 ? false : true)" Class="item1 flex-b">
            <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/mechanisms"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to Mechanisms
            </Button>
            @*<Div><Button Class="back"><Icon Class="fas fa-chevron-left" Clicked=@(() => OnChangingTab.InvokeAsync(("Policy", PolicyCode)))></Icon></Button></Div> <Heading Size="HeadingSize.Is3">New Draft Policy</Heading>*@

        </Div>
        <Div Class="pt-5 m-pt-2 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3" data-cy="MechanismHeading">
                @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismHeading))
                <AdminEditbut Key="@MechanismPageConfigurationKey.MechanismHeading" />
            </Heading>
            <Divider Class="divi-blue" />
        </Div>
        <Validations @ref="validations" ValidateOnLoad="false">
            <Div Class="form-newd">
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="MechanismTitleLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitleLabel)
                            <Span>*</Span>
                            <Tooltip data-cy="MechanismTitleTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitleTooltip)">
                                <Button data-cy="MechanismTitleTooltipBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismTitleIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismTitleGroup" />
                        </FieldLabel>
                        <Validation Validator="@ValidateByTitle">
                            <TextEdit data-cy="MechanismTitlePlaceHolder" Text="@mechanism.Title" TextChanged="@OnChangeTitle" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitlePlaceHolder)">
                                <Feedback>
                                    @* <ValidationError data-cy="MechanismTitleError">Enter valid title</ValidationError> *@
                                </Feedback>
                            </TextEdit>
                        </Validation>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="MechanismTitleEnglishLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitleEnglishLabel)
                            <Tooltip data-cy="MechanismTitleEnglishTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitleEnglishTooltip)">
                                <Button data-cy="MechanismTitleEnglishTooltipBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                            </Tooltip> <AdminEditbut Key="@MechanismPageConfigurationKey.MechanismTitleEnglishGroup" />
                        </FieldLabel>
                        <TextEdit data-cy="MechanismTitleEnglishPlaceHolder" Disabled="@isEnglishTitleDisabled" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTitleEnglishPlaceHolder)" Text="@mechanism?.EnglishTitle" TextChanged="@EnglishTitleKeyPress">

                        </TextEdit>
                        @if (MechanismEnglishTitle)
                        {
                            <Span Class="text-danger" data-cy="MechanismTitleEnglishError"> Enter valid english title</Span>
                        }
                    </Field>

                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="MechanismTypeLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTypeLabel)
                            <Span>*</Span>
                            <Tooltip data-cy="MechanismTypeTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTypeTooltip)">
                                <Button data-cy="MechanismTypeTooltipBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismTypeTooltipIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismTypeGroup" />
                        </FieldLabel>
                        <AntDesign.Select DataSource="@MechanismTypes"
                                      TItemValue="int?"
                                      TItem="MechanismType"
                                      LabelName="@nameof(MechanismType.Name)"
                                      ValueName="@nameof(MechanismType.Id)"
                                      OnSelectedItemChanged="(async e => await ShowHideOtherType(e != null ? e.Id : mechanism.MechanismTypeId))"
                                      Value="@mechanism.MechanismTypeId"
                                      EnableSearch
                                      Style="width: 100%; margin-bottom: 8px;">
                        </AntDesign.Select>
                    </Field>
                </Fields>

                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Style="34px;">
                        <FieldLabel data-cy="MechanismCountryLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismCountryLabel)
                            <Span>*</Span>
                            <Tooltip data-cy="MechanismCountryLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismCountryTooltip)">
                                <Button data-cy="MechanismCountryLabelTooltipBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismCountryLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismCountryGroup" />
                        </FieldLabel>

                        <AntDesign.Select DataSource="@Countries"
                                      Mode="multiple"
                                      TItemValue="string"
                                      TItem="Country"
                                      LabelName="@nameof(Country.Name)"
                                      ValueName="@nameof(Country.Iso3Code)"
                                      OnSelectedItemsChanged="LoadItemsByCountries" ,
                                      @bind-values="@selectedCountries"
                                      AllowClear
                                      EnableSearch>
                        </AntDesign.Select>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker _date-pickers">
                        <FieldLabel data-cy="MechanismStartDateLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismStartDateLabel)
                            <Tooltip data-cy="MechanismStartDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismStartDateTooltip)">
                                <Button data-cy="MechanismStartDateLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismStartDateLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismStartDateGroup" />
                        </FieldLabel>
                        @*<AntDesign.DatePicker TValue="DateTime?" Value="StartDateTime" Picker="@AntDesign.DatePickerType.Month" OnChange="ChangingMonth" Style="Height:38px" />*@
                        <Fields>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyStartMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismStartMonthLabel) </FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@DateForStartMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingStartMonth" Picker="@AntDesign.DatePickerType.Month" />
                            </Field>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyStartYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismStartYearLabel) </FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@DateForStartYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingStartYear" />
                            </Field>
                        </Fields>
                    </Field>
                </Fields>
                <Fields>


                    <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                        <FieldLabel data-cy="MechanismLeadGovLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLeadGovLabel)
                            <Tooltip data-cy="MechanismLeadGovLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLeadGovTooltip)">
                                <Button data-cy="MechanismLeadGovLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismLeadGovLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismLeadGovGroup" />
                        </FieldLabel>
                        <TextEdit data-cy="MechanismLeadGovLabelPlaceHolder" @bind-Text="mechanism.LeadGovernmentAgency" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLeadGovPlaceHolder)">
                            <Feedback>
                                <ValidationError data-cy="EnterValidGovAgencyError">Enter valid governmnt agency</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Fields>


                <Heading Size="HeadingSize.Is5" Class="ginah5mec" data-cy="PartnersInvolvedLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.PartnersInvolvedLabel)
                    <Tooltip data-cy="PartnersInvolvedLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.PartnersInvolvedTooltip)">
                        <Button data-cy="PartnersInvolvedLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="PartnersInvolvedLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.PartnersInvolvedGroup" />
                </Heading>
                <Div Flex="Flex.JustifyContent.Between" Class="_tabsbox downl-flex">
                    <Tabs SelectedTab="@selectedTab" Class="from-tab" SelectedTabChanged="@PartnerTableChanged">
                        <Items>
                            <Repeater Items="@PartnerCategoryPartners.OrderBy(pc => pc.DragAndDropKey)">
                                <Tab Name="@context.Id.ToString()">@context.Name</Tab>
                            </Repeater>
                        </Items>
                        <Content>
                            <Repeater Context="context" Items="@PartnerCategoryPartners.OrderBy(pc => pc.DragAndDropKey)">
                                @{
                                    string partnerName = context.Name.Replace(" ", "").Replace("/", "");
                                }
                                <TabPanel Name="@context.Id.ToString()">
                                    <Heading data-cy="IndicateHeading" Size="HeadingSize.Is5" Class="pt-1 pb-1">Please indicate @context.Name involved in implementation</Heading>
                                    <ListGroup Class="ulgroup">
                                        <Repeater Context="contextPartner" Items="@context.Partners.OrderBy(pc => pc.DragAndDropKey)">
                                            <ListGroupItem><Check @bind-Checked="contextPartner.IsSelected" TValue="bool">@contextPartner.Name</Check></ListGroupItem>
                                        </Repeater>
                                    </ListGroup>
                                    <Field>
                                        <FieldLabel data-cy="NameDetails">@context.Name detail(s)</FieldLabel>
                                    </Field>
                                </TabPanel>
                            </Repeater>
                            @if (Details.Any())
                            {
                                <Repeater Items="@PartnerCategoryPartners">
                                    <Div style="@(selectedTab == context.Id.ToString() ? "display : block" : "display : none")">
                                        <_quillEditor value="@Details[context.Id.ToString()]" @ref="quillEditorGovernmentDetailsRef[context.Name]"></_quillEditor>

                                    </Div>
                                </Repeater>
                            }
                        </Content>
                    </Tabs>
                </Div>
            </Div>

            <Div Class="form-newd mt-4">
                <Heading Size="HeadingSize.Is5" Class="ginah5mec" data-cy="FunctionsLabelHeading">
                    @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.FunctionsLabel)
                    <Tooltip data-cy="FunctionsLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.FunctionsTooltip)">
                        <Button data-cy="FunctionsLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="FunctionsLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.FunctionsGroup" />
                </Heading>
                <Div Flex="Flex.JustifyContent.Between" Class="_tabsbox downl-flex">
                    <Tabs SelectedTab="@selectedFunctionTab" Class="from-tab" SelectedTabChanged="@OnFuncationTabChanged">
                        <Items>
                            <Tab data-cy="coordination" Name="coordinationTab">Coordination</Tab>
                            <Tab data-cy="monitoring" Name="mechanismTab">Monitoring</Tab>

                        </Items>
                        <Content>
                            <TabPanel Name="coordinationTab">
                                <Heading Size="HeadingSize.Is5" data-cy="CoordinationHeading" Class="pt-1 pb-1">Coordination</Heading>
                                <ListGroup Class="ulgroup">
                                    <Repeater Items="@Coordinations">
                                        <ListGroupItem data-cy="@context.Name.Replace(" ", "")"><Check TValue="bool" @bind-Checked="@(context.IsChecked)">@context.Name</Check></ListGroupItem>
                                    </Repeater>
                                </ListGroup>
                            </TabPanel>
                            <TabPanel Name="mechanismTab">
                                <Heading Size="HeadingSize.Is5" Class="pt-1 pb-1" data-cy="MonitoringHeading">Monitoring</Heading>
                                <ListGroup Class="ulgroup">
                                    <Repeater Items="@Monitorings">
                                        <ListGroupItem><Check TValue="bool" @bind-Checked="@(context.IsChecked)">@context.Name</Check></ListGroupItem>
                                    </Repeater>
                                </ListGroup>
                            </TabPanel>
                        </Content>
                    </Tabs>
                </Div>
            </Div>
            <Div Class="mt-4 w-98 mo-m-0">
                <Field>
                    <FieldLabel data-cy="MechanismURLLinkLabel" Style="font-weight: 700;">
                        @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismMandateLabel)
                        <Tooltip data-cy="MechanismMandateLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismMandateTooltip)">
                            <Button data-cy="MechanismMandateLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="MechanismMandateLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismMandateGroup" />
                    </FieldLabel>
                    <_quillEditor value="@mechanism.Mandate" @ref="quillEditorMandateRef"></_quillEditor>
                </Field>
            </Div>
            <Div Class="mt-4 w-98 mo-m-0">
                <Row>
                    <Column Class="form-newd mr-1 mo-mr-0 w-100 Topicspolicy">

                        <Div Flex="Flex.JustifyContent.Between">
                            <Div>
                                <FieldLabel data-cy="MechanismTopicLabel">
                                    @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTopicLabel)
                                    <Tooltip data-cy="MechanismTopicLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismTopicTooltip)">
                                        <Button data-cy="MechanismTopicLabelBtn" Class="but-info _tooltip">
                                            <Icon data-cy="MechanismTopicLabelIcon" Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismTopicGroup" />
                                </FieldLabel>
                            </Div>
                            <Div>
                                <Button data-cy="ExpandCollapseNameBtn" Clicked="@TollgleExpanned" Class="but-gray">@ExpandCollapseName</Button>
                                <Button data-cy="ClearBtn" Clicked="ClearTopics" Class="but-gray">Clear</Button>
                            </Div>
                        </Div>

                        <Divider Padding="Padding.Is0" Class="m-0 mb-4" />
                        @if (runTreeCode)
                        {
                            <AntDesign.Spin Spinning="@isExpandingOrCOllapsing">
                                <AntDesign.Tree @ref="topicTree" ShowIcon
                                        MatchedClass="site-tree-search-value"
                                        DataSource="TopicList"
                                        TItem="GTreeNode"
                                        TitleExpression="x => x.DataItem.Title"
                                        ChildrenExpression="x => x.DataItem.Children"
                                        OnCheck="x => TopicTreeCheckboxClicked(x)"
                                        KeyExpression="x => x.DataItem.TopicId.ToString()"
                                        DefaultCheckedKeys="selectedTopics"
                                        Checkable>
                                </AntDesign.Tree>
                            </AntDesign.Spin>
                        }


                        <Field Display="@(showOtherTopics? Display.Block: Display.None)" ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="pt-3">
                            <FieldLabel data-cy="OtherTopics">Other Topics</FieldLabel>
                            <MemoEdit Text="@mechanism.OtherTopics" Rows="3" TextChanged="@OnOtherTopics"></MemoEdit>
                        </Field>
                    </Column>
                </Row>
            </Div>
            <Div Class="mt-4 w-98 mo-m-0">
                <Row>
                    <Column Class="form-newd mr-1 mo-mr-0 w-100">
                        <FieldLabel data-cy="MechanismLinkToPoliciesLabel" Class="gina-bold">
                            @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLinkToPoliciesLabel)
                            <Tooltip data-cy="MechanismLinkToPoliciesLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLinkToPoliciesTooltip)">
                                <Button data-cy="MechanismLinkToPoliciesLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="MechanismLinkToPoliciesLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismLinkToPoliciesGroup" />
                        </FieldLabel>
                        <Div Class="w-100">
                            <AntDesign.Select DataSource="@MechanismPolicyByCountries"
                                          @bind-Values="@PolicyList"
                                          Mode="multiple"
                                          TItemValue="int"
                                          TItem="Domain.Mechanism.MechanismPolicy"
                                          LabelName="@nameof(Domain.Mechanism.MechanismPolicy.PolicyTitle)"
                                          ValueName="@nameof(Domain.Mechanism.MechanismPolicy.PolicyId)"
                                          EnableSearch
                                          AllowClear
                                          Style="width: 100%; margin-bottom: 8px;" />
                        </Div>
                    </Column>
                </Row>
            </Div>

            <Div Class="form-newd mt-4">
                <Field>
                    <FieldLabel data-cy="MechanismLessonLearntLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLessonLearntLabel)
                        <Tooltip data-cy="MechanismLessonLearntLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismLessonLearntTooltip)">
                            <Button data-cy="MechanismLessonLearntLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="MechanismLessonLearntIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismLessonLearntGroup" />
                    </FieldLabel>
                    @*<RadzenHtmlEditor id="references" @bind-Value="@mechanism.LessonsLearnt" class="_editor" style="height: 200px; margin-bottom: 1rem;"><HtmlEditor /></RadzenHtmlEditor>*@
                    <_quillEditor value="@mechanism.LessonsLearnt" @ref="quillEditorLessonsLearntRef"></_quillEditor>
                </Field>
                <Field>
                    <FieldLabel data-cy="MechanismURLLinkLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismURLLinkLabel)
                        <Tooltip data-cy="MechanismURLLinkLabelToolTip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismURLLinkTooltip)">
                            <Button data-cy="MechanismURLLinkLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="MechanismURLLinkLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismURLLinkGroup" />
                    </FieldLabel>
                    <TextEdit data-cy="MechanismURLLinkPlaceholder" @bind-Text="mechanism.Url" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismURLLinkPlaceholder)"></TextEdit>
                </Field>
                <Field>
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div Class="item1">
                            <FieldLabel data-cy="NotesLabel">Notes</FieldLabel>
                        </Div>
                    </Div>
                    @*<RextEditors Changed="@OnChangeNotes" Value="@(mechanism.Notes)" />*@
                    <_quillEditor value="@mechanism.Notes" @ref="quillEditorNotesRef"></_quillEditor>
                </Field>

            </Div>
        </Validations>
        <Div Class="form-newd mt-4">
            <Div Class="row">
                <Div Class="col-lg-6 col-12">
                    <Fields>
                        <Field>
                            <FieldLabel data-cy="MechanismModerationNoteLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNoteLabel)
                                <Tooltip data-cy="MechanismModerationNoteLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNoteTooltip)">
                                    <Button data-cy="MechanismModerationNoteBtn" Class="but-info _tooltip">
                                        <Icon data-cy="MechanismModerationNoteIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismModerationNoteGroup" /> <Span>*</Span>
                            </FieldLabel>
                            <TextEdit Disabled Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNotePlaceholder)"
                                  Text="@UserName"></TextEdit>
                            <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivations.</FieldHelp>
                            @if (IsModerationNotes)
                            {
                                <Span Class="text-danger" data-cy="EnterValidModeration"> Enter valid moderation notes</Span>
                            }
                        </Field>
                    </Fields>
                </Div>
                <Div Class="col-lg-6 col-12">
                    <Fields>
                        <Field>
                            <FieldLabel data-cy="MechanismModerationNoteLabel">
                                Other notes
                                @* @PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNoteLabel) *@
                                <Tooltip data-cy="MechanismModerationNoteLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNoteTooltip)">
                                    <Button data-cy="MechanismModerationNoteBtn" Class="but-info _tooltip">
                                        <Icon data-cy="MechanismModerationNoteIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip><AdminEditbut Key="@MechanismPageConfigurationKey.MechanismModerationNoteGroup" />
                            </FieldLabel>
                            <TextEdit Placeholder="@PageConfigurations.GetPageConfigrationValueByName(MechanismPageConfigurationKey.MechanismModerationNotePlaceholder)"
                                  @bind-Text="@ModerationNotes"></TextEdit>
                            @if (IsModerationNotes)
                            {
                                <Span Class="text-danger" data-cy="EnterValidModeration"> Enter valid moderation notes</Span>
                            }
                        </Field>
                    </Fields>
                </Div>
            </Div>
        </Div>
        <Div Class="mt-4 pb-6">
            <Div Class="stickybottom">
                <Fields>
                    <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">

                        <AntDesign.SimpleSelect TItem="string"
                                            TItemValue="string"
                                            @bind-Value="@nextWorkflowStatus"
                                            DefaultValue="@(WorkflowStatusToState.Draft.ToString())"
                                            Placeholder="Select an action to Submit"
                                            OnSelectedItemChanged="e=>OnWorkflowStatusChange(e)">
                            <SelectOptions>
                                @foreach (var item in RoleBaseWorkFlowLookUps)
                                {
                                    <Tooltip ShowArrow=true Placement="TooltipPlacement.RightEnd" Title=@(item.Description)>
                                        <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Value Label=@item.Text />
                                    </Tooltip>
                                }
                            </SelectOptions>
                        </AntDesign.SimpleSelect>
                    </Field>
                    @if (UseridVisible)
                    {
                        <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                            @* <FieldLabel>Select User id</FieldLabel>*@
                            <AntDesign.Select DataSource="@ContributorList"
                                      TItemValue="string"
                                      Placeholder="Select User Id"
                                      TItem="UserModel"
                                      LabelName="@nameof(UserModel.UserName)"
                                      ValueName="@nameof(UserModel.UserName)"
                                      @bind-Value="@selectedMailValue"
                                      AllowClear
                                      EnableSearch>
                            </AntDesign.Select>
                            <FieldHelp>Type who you want to send this content</FieldHelp>
                        </Field>
                    }
                    <Field>
                        <Button data-cy="SubmitBtn" Class="but-yellow" Clicked="e=> CreateMechanism()">Save</Button>
                    </Field>
                </Fields>

                <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
                    <SnackbarBody>
                        New content: Your draft will be placed in moderation.
                    </SnackbarBody>
                </Snackbar>
            </Div>
        </Div>
    </Div>
}


<style>
    .without-html .rz-html-editor-button {
        display: none;
    }
</style>

