﻿using AntDesign;
using Blazorise;
using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Enums;
using Gina2.Core.Extensions;
using Gina2.Core.Interface;
using Gina2.Core.Lookups;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.Services.Country;
using Gina2.Services.Mechanism;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.MechanismTab
{
    public partial class MechanismCreateEditTab : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private ILogger<MechanismCreateEditTab> _logger { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private ICurrentUserServiceExtended CurrentUserService { get; set; }
        [Inject]
        protected IJSRuntime JSRuntime { get; set; } = null!;

        [Inject]
        private IMechanismService MechanismService { get; set; }

        [Inject]
        private IMechanismRevisionService MechanismRevisionService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IPolicyService PolicyService { get; set; }
        [Inject]
        private IEmailServices EmailServices { get; set; }

        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        private _quillEditor quillEditorLessonsLearntRef;
        private _quillEditor quillEditorMandateRef;
        private _quillEditor quillEditorNotesRef;
        private Dictionary<string, _quillEditor> quillEditorGovernmentDetailsRef = new Dictionary<string, _quillEditor>();

        private MechanismRevision mechanism { get; set; } = new();

        string selectedTab = "coordinationTab";
        string selectedFunctionTab = "coordinationTab";

        [CascadingParameter(Name = "MechanismCode")]
        public int MechanismCode { get; set; }
        [CascadingParameter(Name = "PolicyCode")]
        public int PolicyCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int? VersionId { get; set; }

        public List<PolicyPartnerCategory> PolicyPartnerCategoryDetailsList { get; set; } = new List<PolicyPartnerCategory>();
        public List<Domain.PolicyPartnerCategory.PolicyPartnerCategory> PolicyPartnerCategoryPartnerList { get; set; } = new List<Domain.PolicyPartnerCategory.PolicyPartnerCategory>();
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new List<RoleBaseWorkFlowLookUp>();

        Snackbar snackbar;
        List<MechanismType> MechanismTypes { get; set; } = new();
        List<Coordination> Coordinations { get; set; } = new();
        List<Domain.Mechanism.MechanismPolicy> MechanismPolicyByCountries { get; set; } = new List<Domain.Mechanism.MechanismPolicy>();
        public IEnumerable<int> PolicyList { get; set; } = new List<int>();

        List<Domain.Mechanism.MechanismPolicy> DuplicatePolicyCountryMapItems { get; set; } = new();

        List<Monitoring> Monitorings { get; set; } = new();
        List<Country> Countries { get; set; } = new();
        IEnumerable<PartnerCategory> PartnerCategories { get; set; } = Enumerable.Empty<PartnerCategory>();
        private List<GTreeNode> TopicList = new();
        private List<int> MechanismTopics = new();
        ObservableCollection<PartnerCategory> PartnerCategoryPartners { get; set; } = new ObservableCollection<PartnerCategory>();
        private List<DbModels.PolicyTopic> PolicyTopicList { get; set; } = new();
        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private List<PolicyCategoryPartnerMapItem> PolicyPartnerList { get; set; } = new();
        public IEnumerable<string> selectedCountries { get; set; }
        private List<PartnerDetail> PartnerItems { get; set; } = new List<PartnerDetail>();
        Tree<GTreeNode> topicTree;
        Validations validations;
        private DateTime? StartDateTime = null;
        private List<UserModel> ContributorList { get; set; } = new List<UserModel>();
        private string[] selectedTopics;
        bool isEdit = false;
        bool showOtherTopics = false;
        bool showOtherMechanismType = false;
        public bool IsModerationNotes = false;
        public string UserName = string.Empty;
        public string ModerationNotes = string.Empty;
        private Dictionary<string, string> Details = new Dictionary<string, string>();
        string ExpandCollapseName = "Expand all";
        bool isExpandingOrCOllapsing = false;
        public bool isEnglishTitleDisabled = false;
        public bool MechanismEnglishTitle = false;
        private string nextWorkflowStatus = string.Empty;
        private bool runTreeCode = false;
        string selectedMailValue;
        public bool UseridVisible { get; set; } = false;
        private DateTime? DateForStartYear { get; set; }
        private DateTime? DateForStartMonth { get; set; }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }

        private void TollgleExpanned()
        {
            isExpandingOrCOllapsing = true;
            if (ExpandCollapseName == "Expand all")
            {
                topicTree.ExpandAll();
                ExpandCollapseName = "Collapse all";
            }
            else
            {
                topicTree.CollapseAll();
                ExpandCollapseName = "Expand all";
            }
            isExpandingOrCOllapsing = false;
            StateHasChanged();
        }

        private void PartnerTableChanged(string name)
        {
            selectedTab = name;
        }
        private void OnFuncationTabChanged(string name)
        {
            selectedFunctionTab = name;
        }

        private async Task GetUsersAsync()
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var query = from user in _dbContext.Users.ToList()
                        join userRole in _dbContext.UserRoles
                       on user.Id equals userRole.UserId
                        join role in _dbContext.Roles
                        on userRole.RoleId equals role.Id
                        select new AppUserModel()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            UserName = user.UserName,
                            Email = user.Email,
                            Organization = user.Organization,
                            Status = user.Status,
                            UserRoles = role.Name
                        };

            var userList = query.ToList();

            foreach (var item in userList)
            {
                ContributorList.Add(new UserModel()
                {
                    Id = item.Id,
                    UserName = item.UserName,
                    DisplayName = item.UserName
                }); ;
            }
        }

        protected override async Task OnInitializedAsync()
        {
            await GetPartnerCategoriesPartners();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await base.OnInitializedAsync();
                if (MechanismCode > 0)
                {
                    mechanism = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(MechanismCode, VersionId);
                    if (mechanism == null)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }

                    if (mechanism != null && mechanism.MechanismTopicRevision.Any())
                    {
                        selectedTopics = mechanism.MechanismTopicRevision.Select(w => w.TopicId.ToString()).ToArray();
                    }
                    var createOrEdit = VersionId != 0 ? "Edited" : "Created";
                    UserName = $"{createOrEdit} by {CurrentUserService.UserName}";
                    ModerationNotes = mechanism.MechanismLog.FirstOrDefault().OtherNotes;
                }
                else
                {
                    UserName = $"Created by {CurrentUserService.UserName}";
                }
                //ModerationNotes = mechanism.MechanismLog.FirstOrDefault().OtherNotes;
                await GetTopicsAsync();

                MechanismTypes = await MechanismService.GetMechanismTypes();
                Coordinations = await MechanismService.GetCoordinations();
                Monitorings = await MechanismService.GetMonitorings();
                Countries = await CountryService.GetActiveCountries();
                //await GetPartnersAsync();
                await GetUsersAsync();
                if (mechanism.MechanismCountryMapRevision.Any())
                {
                    selectedCountries = mechanism.MechanismCountryMapRevision.Select(w => w.CountryCode).ToList();
                    MechanismPolicyByCountries = (await MechanismService.GetPoliciesByCoutries(selectedCountries)).ToList();
                }
                if(mechanism.MechanismPolicyRevision.Any())
                {
                    PolicyList = mechanism.MechanismPolicyRevision.Select(p => p.PolicyId).ToList();
                }
                SetMechanism();
                RoleBaseWorkFlowLookUps = RoleBaseWorkFlowLookUp.RoleBaseWorkFlowLookUps(CurrentUserService.UserRole);
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            await base.OnAfterRenderAsync(firstRender);
        }
        private async Task GetPartnerCategoriesPartners()
        {
            var dbPartnerCategoryPartners = await PolicyService?.GetPartnerCategoryPartnersAsync();
            PartnerCategoryPartners = new ObservableCollection<PartnerCategory>(dbPartnerCategoryPartners as List<PartnerCategory>);
            selectedTab = PartnerCategoryPartners.First().Id.ToString();
            PartnerCategoryPartners.ForEach(x => Details[x.Id.ToString()] = string.Empty);
            StateHasChanged();

        }

        private void SetMechanism()
        {
            if (mechanism != null)
            {
                isEdit = true;     

                if (mechanism.StartYear.HasValue || mechanism.StartMonth.HasValue)
                {
                    //DateTime date = new DateTime((int)mechanism.StartYear, 1, (int)mechanism.StartMonth);
                    int? year = mechanism.StartYear != null && mechanism.StartYear != 0 ? mechanism.StartYear.Value : 1;
                    int? month = mechanism.StartMonth != null  && mechanism.StartMonth != 0 ? mechanism.StartMonth.Value : 1;
                    DateForStartYear = year != 1 ? new DateTime((int)year, 1, 1) : null;
                    DateForStartMonth = month != 1 ?  new DateTime(1, (int)month, 1) : null;
                    StartDateTime = new DateTime((int)year, (int)month, 1);
                }
                foreach (var pcp in PartnerCategoryPartners)
                {
                    foreach (var partner in pcp.Partners)
                    {
                        if (mechanism.MechanismPartnerRevision.FirstOrDefault(w => w.PartnerId == partner.Id && w.PartnerCategoryId == partner.PartnerCategoryId) != null)
                        {
                            partner.IsSelected = true;
                        }

                        if (mechanism.MechanismPartnerCategoryDetailsRevision.FirstOrDefault(w => w.PartnerCategoryId == partner.PartnerCategoryId) != null)
                        {
                            pcp.Details = mechanism.MechanismPartnerCategoryDetailsRevision.FirstOrDefault(w => w.PartnerCategoryId == partner.PartnerCategoryId)?.Details ?? "";
                            Details[partner.PartnerCategoryId.ToString()] = pcp.Details;
                        }
                    }
                }

                Coordinations.Where(s => mechanism.MechanismCoordinationRevision
                             .Select(s => s.CoordinationId).Contains(s.Id))
                             .Select(ss => { ss.IsChecked = true; return ss; }).ToList();

                Monitorings.Where(s => mechanism.MechanismMonitoringRevision
                            .Select(s => s.MonitoringId).Contains(s.Id))
                            .Select(ss => { ss.IsChecked = true; return ss; }).ToList();

                MechanismTopics = mechanism.MechanismTopicRevision.Select(w => w.TopicId).ToList();
                ShowHideMechanisOtherTopics();
                ShowHideOtherType(mechanism.MechanismTypeId);
            }


        }

        private void ChangingMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            mechanism.StartYear = dateTimeChangedEventArgs.Date.Value.Year;
            mechanism.StartMonth = dateTimeChangedEventArgs.Date.Value.Month;
            StartDateTime = new DateTime(mechanism.StartYear.Value, mechanism.StartMonth.Value, 1);
        }
        private void OnWorkflowStatusChange(string value)
        {
            nextWorkflowStatus = value;
            if (value == WorkflowStatusToState.Delegated)
            {
                UseridVisible = true;
            }
            else if (value == WorkflowStatusToState.SentForCorrection)
            {
                UseridVisible = true;
            }
            else
            {
                UseridVisible = false;
            }
            StateHasChanged();
        }

        private async Task GetTopicsAsync()
        {
            AllTopics = await PolicyService.GetTopicsAsync();
            AllParentTopics = await PolicyService.GetParentTopics();
            var mechanismGrandParent = AllParentTopics.Where(x => x.ParentId.Equals(3043)).OrderBy(t => t.OrderKey).ToList();
            await GetTopicTreeView(mechanismGrandParent);
        }
        private async Task GetTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            foreach (var item in First)
            {
                TopicList.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                    IsSelected = false
                });
            }
            runTreeCode = true;
        }

        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<Gina2.DbModels.TopicParent> First)
        {
            List<GTreeNode> child = new List<GTreeNode>();
            foreach (var item in First)
            {
                GTreeNode itemchild = new GTreeNode();
                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).OrderBy(t => t.OrderKey).ToList()),
                    IsSelected = false
                });
            }
            return child;
        }


        private async Task GetPartnersAsync()
        {
            PolicyPartnerList = await PolicyService.GetPartnerCategoriesAsync(PolicyCode);
            foreach (var item in PolicyPartnerList)
            {
                Details[item.PartnerCategoryId.ToString()] = item.Partner.Name;
                var parent = PartnerItems.Where(x => x.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault();
                if (parent != null)
                {
                    parent.PartnerName = item.Partner.Name;
                }
                else
                {
                    PartnerItems.Add(new PartnerDetail()
                    {
                        PartnerName = item.PartnerCategory.Name,
                        PartnerCategoryId = item.PartnerCategoryId,
                        PartnerDetails = item.Partner.Name
                    });

                }
            }
        }
        private void TopicTreeCheckboxClicked(TreeEventArgs<Gina2.Core.Models.GTreeNode> checkedValue)
        {
            ShowHideMechanisOtherTopics();
        }
        private void ShowHideMechanisOtherTopics()
        {
            if (topicTree != null && topicTree.CheckedKeys.Contains(Core.Constants.MechanismOtherTypeId.ToString()))
            {
                showOtherTopics = true;
            }
            else
            {
                showOtherTopics = false;
                mechanism.OtherTopics = String.Empty;
            }
        }

        private async Task ShowHideOtherType(int? value)
        {
            mechanism.MechanismTypeId = (int)value;
            if (value == MechanismOtherTypeId)
            {
                showOtherMechanismType = true;
            }
            else
            {
                showOtherMechanismType = false;
                mechanism.MechanismOtherType = String.Empty;
            }
        }

        private void ClearTopics()
        {
            topicTree.UncheckAll();
            MechanismTopics.Clear();
            showOtherTopics = false;
            StateHasChanged();
        }


        private async void LoadItemsByCountries(IEnumerable<Country> countries)
        {

            if (selectedCountries != null && selectedCountries.Any())
            {
                var mechanismPolicies = await MechanismService.GetPoliciesByCoutries(selectedCountries);
                MechanismPolicyByCountries = mechanismPolicies.DistinctBy(p => p.PolicyId).OrderBy(p => p.PolicyId).ToList();
            }
            else
            {
                MechanismPolicyByCountries = new();
            }
            DuplicatePolicyCountryMapItems = MechanismPolicyByCountries;
            await InvokeAsync(StateHasChanged);
        }

        private async void PolicyCountryMapItemsFilter(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                MechanismPolicyByCountries = DuplicatePolicyCountryMapItems;
            }
            else
            {
                //MechanismPolicyByCountries = DuplicatePolicyCountryMapItems.Where(w => w.Policy.Title.ToLower().Contains(value.ToLower())).ToList();
            }
            await InvokeAsync(StateHasChanged);
        }

        public async void ClearActionLinks()
        {
            //MechanismPolicyByCountries.Select(s => { s.IsSelected = false; return s; }).ToList();
            await InvokeAsync(StateHasChanged);
        }

        private void OnMandateChange(string value)
        {
            mechanism.Mandate = value;
        }

        public void OnLessonsLearntChange(string value)
        {
            mechanism.LessonsLearnt = value;
        }

        public void OnChangeNotes(string value)
        {
            mechanism.Notes = value;
        }
        public void OnOtherTopics(string value)
        {
            mechanism.OtherTopics = value;
        }

        public void ValidateModerationNotes(string value)
        {
            IsModerationNotes = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ModerationNotes = value;
        }

        public async void CreateMechanism()
        {
            if (string.IsNullOrEmpty(mechanism.Title))
            {
                await OpenValidationToaster("Enter valid title");
                return;
            }
            if (mechanism.MechanismTypeId == 0)
            {
                await OpenValidationToaster("Please select mechanism type");
                return;
            }

            if (selectedCountries == null || !selectedCountries.Any())
            {
                await OpenValidationToaster("Please select at least one country");
                return;
            }

            if (await validations.ValidateAll())
            {
                IsLoading = true;

                var mechanismLog = new MechanismLog();
                mechanismLog.RevisedDate = DateTime.UtcNow;
                mechanismLog.UserName = CurrentUserService.UserName;
                mechanismLog.OtherNotes = ModerationNotes;
                mechanismLog.ToState = nextWorkflowStatus;
                mechanismLog.Log = ModerationNotes;
                mechanismLog.IsPublished = nextWorkflowStatus == WorkflowStatusToState.Published;
                if (nextWorkflowStatus == WorkflowStatusToState.Delegated || nextWorkflowStatus == WorkflowStatusToState.SentForCorrection)
                {
                    mechanismLog.DelegatedDate = DateTime.UtcNow;
                    mechanismLog.DelegatedUserName = selectedMailValue;
                }
                if (mechanism.Id == 0)
                {
                    mechanismLog.FromState = WorkflowStatusToState.Draft;
                }
                else
                {
                    mechanismLog.MechanismId = mechanism.Id;
                    mechanismLog.FromState = mechanism.MechanismLog.OrderByDescending(s => s.Id).First().ToState;
                }

                ClearMechanismChildObjects();
                mechanism.MechanismLog.Add(mechanismLog);
                mechanism.MechanismCoordinationRevision = Coordinations
                           .Where(w => w.IsChecked)
                           .Select(s => new MechanismCoordinationRevision
                           {
                               CoordinationId = s.Id,
                               MechanismId = mechanism.Id,
                               MechanismVId = mechanism.VersionId,
                           }).ToList();

                mechanism.MechanismMonitoringRevision = Monitorings
                   .Where(w => w.IsChecked).ToList().Select(s => new MechanismMonitoringRevision
                   {
                       MonitoringId = s.Id,
                       MechanismId = mechanism.Id,
                       MechanismVId = mechanism.VersionId,
                   }).ToList();
                mechanism.LessonsLearnt =await quillEditorLessonsLearntRef.GetHTML();
                mechanism.Mandate = await quillEditorMandateRef.GetHTML();
                mechanism.Notes = await quillEditorNotesRef.GetHTML();
                foreach (var partnerCategoryPartner in PartnerCategoryPartners)
                {
                    foreach (var partner in partnerCategoryPartner.Partners.Where(w => w.IsSelected))
                    {
                        if (!mechanism.MechanismPartnerCategoryDetailsRevision.Where(w => w.PartnerCategoryId == (int)partner?.PartnerCategoryId).Any())
                        {
                            mechanism.MechanismPartnerCategoryDetailsRevision.Add(new MechanismPartnerCategoryDetailsRevision()
                            {
                                MechanismId = mechanism.Id,
                                PartnerCategoryId = (int)partner?.PartnerCategoryId,
                                Details = (await quillEditorGovernmentDetailsRef[partnerCategoryPartner.Name].GetHTML()).SanitizeContent(),
                                MechanismVId = mechanism.VersionId,
                            });
                        }

                        if (!mechanism.MechanismPartnerRevision.Where(w => w.PartnerCategoryId == (int)partner?.PartnerCategoryId && w.PartnerId == partner.Id).Any())
                        {
                            mechanism.MechanismPartnerRevision.Add(new MechanismPartnerRevision
                            {
                                MechanismId = mechanism.Id,
                                PartnerCategoryId = (int)partner?.PartnerCategoryId,
                                PartnerId = partner.Id,
                                MechanismVId = mechanism.VersionId,
                            });
                        }
                    }
                }

                mechanism.MechanismCountryMapRevision = selectedCountries.Select(s => new MechanismCountryMapRevision
                {
                    MechanismId = mechanism.Id,
                    MechanismVId = mechanism.VersionId,
                    CountryCode = s,
                }).ToList();

                MechanismTopics = topicTree.CheckedKeys.Select(s => Convert.ToInt32(s)).ToList();

                mechanism.MechanismTopicRevision = MechanismTopics.Distinct().Select(w => new MechanismTopicRevision()
                {
                    MechanismId = mechanism.Id,
                    MechanismVId = mechanism.VersionId,
                    TopicId = w
                }).ToList();

                mechanism.MechanismPolicyRevision = MechanismPolicyByCountries.Where(w => PolicyList.Contains(w.PolicyId))
                    .Select(s => new MechanismPolicyRevision()
                    {
                        MechanismId = mechanism.Id,
                        MechanismVId = mechanism.VersionId,
                        PolicyId = s.PolicyId
                    }).ToList();


                var mechanismRevision = await MechanismRevisionService.CreateMechanismRevision(mechanism);
                
                string baseUrl = NavigationManager.BaseUri;
                string subject = "GIFNA data for your attention and action";
                string logedInUser = string.IsNullOrWhiteSpace(CurrentUserService.FullName)
                                                    ? CurrentUserService.Email
                                                    : CurrentUserService.FullName;
                string countryNames = string.Join(", ", Countries.Where(c => selectedCountries.Contains(c.Iso3Code)).Select(c => c.Name));
                string dataType = ContentType.Mechanisms.GetDescription();

                string firstCountryCode = Countries.OrderBy(x => x.Name).FirstOrDefault()?.Iso3Code ?? string.Empty;
                string titleLink = $"{baseUrl}countries/{firstCountryCode}/mechanisms/{mechanism.Id}/{mechanism.VersionId}";

                if (mechanismLog.ToState == WorkflowStatusToState.Delegated)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentDelegationEmailModel delegationModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = mechanism.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.DelegateEmail, delegationModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
                else if (mechanismLog.ToState == WorkflowStatusToState.NeedsReview)
                {
                    var approverUsers = await CurrentUserService.GetApproverUsersByCountry(selectedCountries.ToHashSet());                    
                    var tasks = approverUsers.Select(async user =>
                    {
                        try
                        {
                            ContentReviewEmailModel reviewModel = new()
                            {
                                BaseUrl = baseUrl,
                                ReceivedBy = user.FullNameOrEmail,
                                CountryNames = countryNames,
                                ContentTitle = mechanism.Title,
                                SubmitedBy = logedInUser,
                                OtherNotes = ModerationNotes,
                                DataType = dataType,
                                TitleLink = titleLink
                            };

                            await EmailServices.SendEmailByTemplateAsync(user.Email, subject, TemplateType.NeedsReview, reviewModel);                            
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Failed to send email to {user.Email}");
                        }
                    });

                    await Task.WhenAll(tasks);
                }
                else if (mechanismLog.ToState == WorkflowStatusToState.SentForCorrection)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentCorrectionEmailModel correctionModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = mechanism.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.SendForCorrection, correctionModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
                
                if (nextWorkflowStatus == WorkflowStatusToState.Published.ToString())
                {
                    string topCountry = mechanism.MechanismCountryMapRevision.First().CountryCode;
                    MemoryCache.Set("submit", "Mechanism published sucessfully");
                    NavigationManager.NavigateTo($"/countries/{topCountry}/mechanisms/{mechanismRevision.Id}");
                }
                else if (nextWorkflowStatus == WorkflowStatusToState.NeedsReview)
                {
                    MemoryCache.Set("submit", "Mechanism sent for review successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextWorkflowStatus == WorkflowStatusToState.SentForCorrection)
                {
                    MemoryCache.Set("submit", "Mechanism sent for correction successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextWorkflowStatus == WorkflowStatusToState.Delegated)
                {
                    MemoryCache.Set("submit", "Mechanism is delegated successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextWorkflowStatus == WorkflowStatusToState.Draft)
                {
                    MemoryCache.Set("submit", "Mechanism draft created successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                if (isEdit == false)
                {
                    mechanism = new();
                }
                
                IsLoading = false;
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("scrollToFirstError");
            }
        }

        public void ClearMechanismChildObjects()
        {
            mechanism.MechanismLog.Clear();
            mechanism.MechanismCoordinationRevision.Clear();
            mechanism.MechanismMonitoringRevision.Clear();
            mechanism.MechanismPartnerCategoryDetailsRevision.Clear();
            mechanism.MechanismPartnerRevision.Clear();
            mechanism.MechanismCountryMapRevision.Clear();
            mechanism.MechanismTopicRevision.Clear();
            mechanism.MechanismPolicyRevision.Clear();
        }
        private void ValidateByTitle(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }        
        private void ValidateByType(ValidatorEventArgs e)
        {
            if (e.Value == null || e.Value.ToString() == "0")
            {
                e.Status = ValidationStatus.Error;
            }
        }

        private void OnChangeTitle(string value)
        {
            mechanism.Title = value;
        }

        private void DataChangingStartMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartMonth = dateTimeChangedEventArgs.Date;
            mechanism.StartMonth = DateForStartMonth!= null ? dateTimeChangedEventArgs.Date.Value.Month : null;
        }
        private void DataChangingStartYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartYear = dateTimeChangedEventArgs.Date;
           
            mechanism.StartYear = DateForStartYear != null ? dateTimeChangedEventArgs.Date.Value.Year : null;
            
        }
        private void EnglishTitleKeyPress(string value)
        {
            MechanismEnglishTitle = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            mechanism.EnglishTitle = value;
        }
    }
}
