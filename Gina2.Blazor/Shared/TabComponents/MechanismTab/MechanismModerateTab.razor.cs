﻿using AntDesign;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.MechanismRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.Services.Dashboard;
using Gina2.Services.Mechanism;
using Gina2.Services.Policy;
// using Irony.Parsing;
using Microsoft.AspNetCore.Components;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.MechanismTab
{
    public partial class MechanismModerateTab
    {
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private IMechanismRevisionService MechanismRevisionService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private IDashboardService DashboardService { get; set; }
        [Inject]
        private ModalService ModalService { get; set; }
        [Inject]
        private IMechanismService MechanismService { get; set; }
        [CascadingParameter(Name = "MechanismCode")] public int MechanismCode { get; set; }
        [CascadingParameter(Name = "VersionId")] public int VersionId { get; set; }
        [CascadingParameter(Name = "CountryCode")] public string CountryCode { get; set; }
        [Parameter] public string PolicyName { get; set; }
        private string containType = "Mechanisms";
        private List<ModerationRevisionLog> ModerationLog { get; set; } = new();
        private int lastRevisionId;
        private int RevisionId { get; set; }
        List<ModerationRevisionLog> ModerateActions { get; set; } = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                ModerationLog = await DashboardService.GetModerationLogByEntityId(MechanismCode, containType);
                lastRevisionId = ModerationLog.First().EntityRevisionId;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task OpenConfirm(RenderFragment renderFragment, int entityId, int versionId, string contentType, int logId)
        {
            bool unPublish = false;
            if (renderFragment == UnpublishContent)
            {
                unPublish = true;
            }
            var options = new ConfirmOptions()
            {
                Title = "Confirm your action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await ApplyActions(contentType, entityId, versionId, logId);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertConfirm(ModerationRevisionLog log, RenderFragment renderFragment)
        {
            var options = new ConfirmOptions()
            {
                Title = "Confirm revert action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await RevertAction(log);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertAction(ModerationRevisionLog log)
        {
            await MechanismRevisionService.Revert(log.EntityId, log.EntityRevisionId, log.ToState, CurrentUserService.UserName);
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
        }

        private async Task ApplyActions(string contentType, int entityId, int versionId, int logId)
        {
            var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsToCreateAdraftAsync(entityId, versionId);
            mechanismRevision.MechanismLog.Clear();
            mechanismRevision.MechanismLog.Add(new MechanismLog
            {
                MechanismId = mechanismRevision.Id,
                MechanismVId = mechanismRevision.VersionId,
                FromState = WorkflowStatusToState.Published,
                ToState = WorkflowStatusToState.Draft,
                RevisedDate = DateTimeOffset.UtcNow,
                UserName = CurrentUserService.UserName,
            });
            await MechanismRevisionService.CreateMechanismRevision(mechanismRevision, true);
            await DashboardService.UnpublishModerationLogByLogId(logId, containType);
            await MechanismService.RemoveByIdAsync(entityId);
            _ = OpenSuccessToaster("Unpublished successfully");
            NavigationManager.NavigateTo("admin/dashboard");
        }

        public async void NavigateToDraftEdit(int entityId, int revisionId)
        {

            var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(entityId, revisionId);
            NavigationManager.NavigateTo($"countries/{mechanismRevision.MechanismCountryMapRevision.First().CountryCode}/mechanisms/{entityId}/{revisionId}");
        }
        public async void NavigateToDraft(int entityId, int revisionId)
        {

            var mechanismRevision = await MechanismRevisionService.GetMechanismRevisionDetailsAsync(entityId, revisionId);
            NavigationManager.NavigateTo($"countries/{mechanismRevision.MechanismCountryMapRevision.First().CountryCode}/mechanisms/{entityId}/{revisionId}");
        }


    }
}
