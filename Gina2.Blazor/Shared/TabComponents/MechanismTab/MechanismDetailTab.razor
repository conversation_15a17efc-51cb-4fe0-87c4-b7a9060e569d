﻿@using Gina2.Blazor.Helpers
@using Gina2.DbModels.MechanismRevisions
@using Gina2.Core.Methods;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent;
<Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
    <Div Class="item1">

        <Button hidden="@(CountryCode != null ? false : true)" Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/mechanisms"))">
            <Icon Class="fas fa-chevron-left"></Icon> Back to Mechanisms
        </Button>

    </Div>
    <Div Class="item2">
        @*<Button Loading="@ExportPdfLoading" Clicked="@DownloadPdf" Class="but-yellow mr-1"  data-cy="PDFBtn"><Icon class="arrow-bottom"  data-cy="PDFBtnIcon"/> PDF</Button>*@
        <Button Loading="@ExportCsvLoading" Clicked="@DownloadCsv" Class="but-yellow" data-cy="CSVBtn"><Icon class="arrow-bottom" data-cy="CSVBtnIcon" /> CSV</Button>
        @if (EnablePhase2Features)
        {
            <AuthorizeView>
                <Authorized>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem href="@(VersionId != 0 ? $"/countries/{CountryCode}/mechanisms/{MechanismCode}/{VersionId}/edit" : $"/countries/{CountryCode}/mechanisms/{MechanismCode}/edit")">Edit</DropdownItem>
                            @if (EnablePhase2Features)
                            {
                                <DropdownItem href="@($"/countries/{CountryCode}/mechanisms/{MechanismCode}/moderate")">Moderate</DropdownItem>
                            }

                        </DropdownMenu>
                    </Dropdown>
                </Authorized>
            </AuthorizeView>
        }
    </Div>
</Div>

<Heading Class="Headingtab alltab_D_h3" Size="HeadingSize.Is3" data-cy="MechanismDetailTitle">Mechanism - @MechanismDetail.Title</Heading>
<Divider />
<Div>

    <Layout Sider Class="search-box pt-2 pb-5 mob-layout">

        <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel extractshed">
                <div class="_BGDetail" id="mechanismbgdetailhidden">
                    <ListGroup Class="list-ta _mechtab">
                        <ListGroupItem>
                            @if (!string.IsNullOrEmpty(MechanismDetail.LeadGovernmentAgency))
                            {
                                <div hidden="@(string.IsNullOrEmpty(MechanismDetail.LeadGovernmentAgency))">
                                    <span data-cy="LeadGovernmentAgency" class="titelspan">Lead government agency: </span>
                                    <span data-cy="MechanismDetailPara" class="paragr">@MechanismDetail.LeadGovernmentAgency</span>
                                </div>
                            }
                            @if (MechanismDetail.StartMonth.HasValue || MechanismDetail.StartYear.HasValue)
                            {
                                <div>
                                    <span data-cy="EstablishedYear" class="titelspan">Established year: </span>
                                    <span data-cy="EstablishedYearPara" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(MechanismDetail.StartMonth, MechanismDetail.StartYear)</span>
                                </div>
                            }
                        </ListGroupItem>
                        <ListGroupItem Class="_coord-par">
                            @if (MechanismDetail.MechanismCoordinationRevision != null && MechanismDetail.MechanismCoordinationRevision.Any())
                            {
                                <Div>
                                    <span data-cy="Coordinations" class="titelspan">Coordination: </span>
                                    <span class="paragr">
                                        <Repeater Items="@MechanismDetail.MechanismCoordinationRevision.OrderBy(m => m.Coordination.DragAndDropKey)">
                                            <Div Class="_program_title">
                                                <Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Coordination.Name</Span>
                                            </Div>
                                        </Repeater>
                                    </span>
                                </Div>

                            }
                            @if (MechanismDetail.MechanismMonitoringRevision != null && MechanismDetail.MechanismMonitoringRevision.Count > 0)
                            {
                                <Div class="pt-1">
                                    <span data-cy="MonitoringsBtn" class="titelspan">Monitoring: </span>
                                    <span class="paragr">
                                        <Repeater Items="@MechanismDetail.MechanismMonitoringRevision.OrderBy(m => m.Monitoring.DragAndDropKey)">
                                            <Div Class="_program_title">
                                                <Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Monitoring.Name</Span>
                                            </Div>
                                        </Repeater>
                                    </span>
                                </Div>
                            }

                        </ListGroupItem>
                    </ListGroup>
                </div>
                @{
                    string Mandate = string.IsNullOrEmpty(MechanismDetail.Mandate) ? "-" : MechanismDetail.Mandate;
                    string LessonsLearnt = string.IsNullOrEmpty(MechanismDetail.LessonsLearnt) ? "-" : MechanismDetail.LessonsLearnt;
                }
                <ListGroup Class="list-ta _MechanismDetail">
                    @if (!string.IsNullOrEmpty(MechanismDetail.Mandate))
                    {
                        <ListGroupItem Class="mechdetail">
                            <span class="titelspan">Mandate: </span>
                            <span class="paragr"> @((MarkupString)Mandate.GetLink())</span>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(MechanismDetail?.Url))
                    {
                        <ListGroupItem>
                            <span class="titelspan">URL link:  </span>
                            <span class="paragr"> <a href="@MechanismDetail?.Url" target="_blank" rel="noopener noreferrer">@MechanismDetail?.Url</a></span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(MechanismDetail.LessonsLearnt))
                    {
                        <ListGroupItem>
                            <span class="titelspan">Lesson learnt: </span>
                            <span class="paragr"> @((MarkupString)LessonsLearnt.GetLink())</span>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(MechanismDetail.Notes))
                    {
                        <ListGroupItem>
                            <span class="titelspan">Notes: </span>
                            <span class="paragr"> @((MarkupString)MechanismDetail.Notes.GetLink())</span>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(MechanismDetail.References))
                    {
                        <ListGroupItem>
                            <span class="titelspan">Reference: </span>
                            <span class="paragr"> @MechanismDetail.References.GetLink()</span>
                        </ListGroupItem>
                    }
                </ListGroup>
                <AuthorizeView Roles="Admin">
                    <Div>
                        <Accordion Class="accor-fbox">
                            <Collapse Visible="@revision">
                                <CollapseHeader>
                                    <Heading Class="head-but mt-4 m-0 d-flex align-items-center" Size="HeadingSize.Is4" data-cy="RevisionLog">
                                        <Span Class="revisiontitle">Revision log</Span> 
                                        <Button Clicked="@(()=>revision = !revision)"></Button>
                                    </Heading>
                                </CollapseHeader>
                                <CollapseBody Class="tab-0">
                                    <Div Class="DataGrids">
                                        <DataGrid Class="table-nth _actions"
                                                  TItem="@MechanismLog"
                                                  Data="@mechanismLogInfo"
                                                  PageSize="5"
                                                  ShowPageSizes
                                                  ShowPager
                                                  Responsive
                                                  SortMode="DataGridSortMode.Single">
                                            <EmptyTemplate>
                                                <Div>No data found.</Div>
                                            </EmptyTemplate>
                                            <DataGridColumns>
                                                <DataGridColumn Caption="Date" Field="@nameof(MechanismLog.RevisedDate)" TextAlignment="TextAlignment.Start"
                                                                Width="22%">
                                                    <DisplayTemplate Context="displayContext">
                                                        @displayContext.RevisedDate.GetDayWithFormatedDate()
                                                    </DisplayTemplate>
                                                </DataGridColumn>
                                                <DataGridColumn Field="@nameof(MechanismLog.UserName)" Caption="User" Width="19%" />
                                                <DataGridColumn Field="@nameof(MechanismLog.OtherNotes)" Caption="Log" Width="22%" />
                                                <DataGridColumn Field="@nameof(MechanismLog.ToState)" Caption="State" Width="19%" />
                                            </DataGridColumns>
                                        </DataGrid>
                                    </Div>
                                </CollapseBody>
                            </Collapse>
                        </Accordion>
                    </Div>
                </AuthorizeView>
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>
                <Div Class="accordion-Search" id="accordionExample">
                    <div class="accordion">
                        <button data-cy="CountryBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            Country(ies):
                        </button>
                        <div id="collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body _siderdetail_tab">
                                <Div Class="downl-flex cunt-box _flex_countr">
                                    @{
                                        if (MechanismDetail.MechanismCountryMapRevision.Any())
                                        {
                                            <Repeater Items="@MechanismDetail.MechanismCountryMapRevision.OrderBy(m => m.Country.Name)">
                                                <a target="_blank" href='/countries/@context.CountryCode/mechanisms'>@context.Country.Name</a>
                                            </Repeater>
                                        }
                                    }
                                </Div>
                            </div>
                        </div>
                        @if (PartnerItems.Any())
                        {
                            <button data-cy="PartnersInvolvedBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                Partners involved
                            </button>

                            <div id="collapse2" class="accordion-collapse collapse show">
                                <div class="accordion-body p-0 _padding_t-b8">
                                    <Bar Class="Verticalbar _itemalinebar" Mode="BarMode.VerticalInline"
                                         CollapseMode="BarCollapseMode.Small">
                                        <BarMenu>
                                            <BarStart>


                                                <Repeater Items="@PartnerItems" Context="partner">
                                                    <BarItem>
                                                        <BarDropdown>
                                                            <BarDropdownToggle>
                                                                @partner.PartnerName
                                                            </BarDropdownToggle>
                                                            <BarDropdownMenu>
                                                                <Repeater Items="partner.PartnerData.OrderBy(p => p.DragAndDropKey)" Context="partnerdetail">
                                                                    <BarDropdownItem>@partnerdetail.Name</BarDropdownItem>
                                                                </Repeater>
                                                                <BarDropdown>
                                                                    @if (!string.IsNullOrEmpty(partner.PartnerDetails))
                                                                    {
                                                                        <BarItem>
                                                                            <BarDropdown>
                                                                                <BarDropdownToggle>More details</BarDropdownToggle>
                                                                                <BarDropdownMenu>
                                                                                    <BarDropdownItem>@((MarkupString)partner.PartnerDetails)</BarDropdownItem>
                                                                                </BarDropdownMenu>
                                                                            </BarDropdown>
                                                                        </BarItem>
                                                                    }
                                                                </BarDropdown>
                                                            </BarDropdownMenu>

                                                        </BarDropdown>
                                                    </BarItem>
                                                </Repeater>


                                            </BarStart>
                                        </BarMenu>
                                    </Bar>
                                </div>
                            </div>

                        }
                        @if (TopicList.Any())
                        {
                            <button data-cy="TopicsBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                Topics
                            </button>

                            <div id="collapse3" class="accordion-collapse collapse show">
                                <div class="accordion-body _itemalinebar p-0  _padding_t-b8">
                                    @*  <ul>
                                    <AntDesign.Tree ShowIcon
                                                     MatchedClass="site-tree-search-value"
                                                     DataSource="TopicList"
                                                     TitleExpression="x => x.DataItem.ParentName"
                                                     ChildrenExpression="x =>  x.DataItem.ChildName"
                                                     DefaultExpandAll=true >
                                             <TitleTemplate Context="TopicList">
                                                 <li>@TopicList.DataItem.ParentName</li>
                                         </TitleTemplate>
                                </AntDesign.Tree>
                                    </ul> *@
                                    <AntDesign.Tree ShowIcon
                                                    MatchedClass="site-tree-search-value"
                                                    DataSource="TopicList"
                                                    TitleExpression="x => x.DataItem.ParentName"
                                                    ChildrenExpression="x =>  x.DataItem.ChildName"
                                                    DefaultExpandAll=true />
                                </div>

                            </div>
                        }
                        @if (MechanismDetail.MechanismPolicyRevision != null && MechanismDetail.MechanismPolicyRevision.Any())
                        {
                            <button data-cy=" LinkToPoliciesBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                Link to policy(ies):
                            </button>

                            <div id="collapse4" class="accordion-collapse collapse show">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="@MechanismDetail.MechanismPolicyRevision">
                                        <Div Class="_other">
                                            <a target="_blank" href="@($"/policies/{context.Policy.Id}")"><Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Policy.CombinedTitle</Span> </a>
                                        </Div>
                                    </Repeater>
                                </div>
                            </div>
                        }


                    </div>

                </Div>

            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Div>