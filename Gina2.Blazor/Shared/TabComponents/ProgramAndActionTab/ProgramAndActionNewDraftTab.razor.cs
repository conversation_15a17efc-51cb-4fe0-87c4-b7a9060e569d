﻿using Blazorise;
using Blazorise.Snackbar;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab
{
    public partial class ProgramAndActionNewDraftTab
    {
        private bool IsLoading = true;
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        private NavigationManager NavigationManager { get; set; }
        string selectedTab = "Tab1";
        [Parameter] public string PolicyName { get; set; }

        [CascadingParameter(Name = "ProgrammeAndActionCode")] public int ProgrammeAndActionCode { get; set; }
        [CascadingParameter(Name = "CountryCode")] public string CountryCode { get; set; }

        private Task OnSelectedTabChanged(string name)
        {
            selectedTab = name;

            return Task.CompletedTask;
        }

        Task OnChanged(FileChangedEventArgs e)
        {
            return Task.CompletedTask;
        }
        Snackbar snackbar;
        ObservableCollection<string> ItemData { get; set; } = new ObservableCollection<string>() {
            "Cabinet/Presidency",
            "Food and agriculture",
            "Social welfare",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text",
            "text"
        };

        ObservableCollection<string> TypeOfPolicyData { get; set; } = new ObservableCollection<string>() {
            "Select Type",
            "Comprehensive national nutrition policy, strategy or plan",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };

        ObservableCollection<string> LanguageData { get; set; } = new ObservableCollection<string>() {
            "Select Language",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };
        ObservableCollection<string> Countries { get; set; } = new ObservableCollection<string>() {
            "Select Countries",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region",
            "Region"
        };

        ObservableCollection<string> ListOfActionRecords { get; set; } = new ObservableCollection<string>() {
            "Number of children below 6 months who are exclusively breastfedChild deaths",
            "A roda dos alimentos [Food wheel guide ]",
            "A2Z: The USAID Micronutrient and Child Blindness",
            "A2Z: The USAID Micronutrient and Child Blindness Project",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Diapaga (2012)",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Danané",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Diapaga",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Fada N’Gourma",
            "ACF programme communautaire: Prise en Charge de la Malnutrition Aiguë dans le district de Toulepleu"
        };
        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }
        public class TodoItem
        {
            public string Title { get; set; }

            public bool IsDone { get; set; }

            public bool IsNew { get; set; }
        }

        public List<TodoItem> MyFirstList = new List<TodoItem>()
        {new TodoItem()
        {Title = "Item 1 - Gambia"}, new TodoItem()
        {Title = "Item 2 - Norway"}, new TodoItem()
        {Title = "Item 3 - Peru"}, new TodoItem()
        {Title = "Item 4 - Republic of Moldova"}, new TodoItem()
        {Title = "Item 5 - Saint Vincent and the Grenadines"}, };
        public class ElenaItem
        {
            public int Id { get; set; }

            public string Title { get; set; }

            public bool IsDone { get; set; }

            public bool IsNew { get; set; }
        }

        public List<ElenaItem> Elenalink = new List<ElenaItem>()
        {new ElenaItem()
        {Id = 1, Title = "Item 1 - Gambia", IsDone = false, }, new ElenaItem()
        {Id = 2, Title = "Item 2 - Norway", IsDone = false, }, new ElenaItem()
        {Id = 3, Title = "Item 3 - Peru", IsDone = false}, new ElenaItem()
        {Id = 4, Title = "Item 4 - Republic of Moldova", IsDone = false}, new ElenaItem()
        {Id = 5, Title = "Item 5 - Saint Vincent and the Grenadines", IsDone = false}, };
        private bool modalVisible;
        private bool LocationVisible;
        private Task Location()
        {
            LocationVisible = true;
            return Task.CompletedTask;
        }
        private Task HideLocation()
        {
            LocationVisible = false;
            return Task.CompletedTask;
        }
        private Task ShowModal()
        {
            modalVisible = true;
            return Task.CompletedTask;
        }

        private Task HideModal()
        {
            modalVisible = false;
            return Task.CompletedTask;
        }

        private async Task AcvectElenalink(ElenaItem detail, string activeclass)
        {
            detail.IsDone = !detail.IsDone;
            await JsRuntime.InvokeAsync<object>("addActiveElena", detail.IsDone, activeclass);
        }
        protected override async Task OnInitializedAsync()
        {
            await Task.Delay(300);
            IsLoading = false;
        }
    }
}
