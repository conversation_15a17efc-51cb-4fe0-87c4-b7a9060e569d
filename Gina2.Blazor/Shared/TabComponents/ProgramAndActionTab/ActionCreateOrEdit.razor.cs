using AntDesign;
using Blazorise;
using Blazorise.DataGrid;
using Domain.Programme;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Pages;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using NuGet.Packaging;
using System.Text.RegularExpressions;

namespace Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab
{
    public partial class ActionCreateOrEdit : PageConfirgurationComponent
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Parameter]
        public List<Topic> TopicsAction { get; set; } = new List<Topic>();

        [Parameter]
        public EventCallback<(ActionRevision, bool)> ActionData { get; set; }

        [Parameter]
        public EventCallback<int?> ChangeTopic { get; set; }

        [Parameter]
        public EventCallback<bool> CloseAction { get; set; }

        [Parameter]
        public ActionRevision ActionDetails { get; set; } = new ActionRevision()
        {
            ActionElenaRevision = new List<ActionElenaRevision>()
        };

        [Parameter]
        public string selectedTopic { get; set; }

        [Parameter]
        public List<ProblemType> ProblemTypes { get; set; } = new List<ProblemType>();

        [Parameter]
        public string[] ParentTopicIds { get; set; }

        [Parameter]
        public string[] TopicIds { get; set; }

        [Parameter]
        public List<GTreeNode> TopicList { get; set; } = new List<GTreeNode>();

        [Parameter]
        public DateTime? StartDate { get; set; }

        [Parameter]
        public DateTime? EndDate { get; set; }

        [Parameter]
        public IEnumerable<int> DefaultTargetData { get; set; } = new List<int>();

        [Parameter]
        public IEnumerable<int> DefaultelenaLinkData { get; set; } = new List<int>();

        [Parameter]
        public IEnumerable<int> DefaultDeliveryData { get; set; } = new List<int>();
        [Parameter]
        public IEnumerable<string> DefaultDeterminantData { get; set; } = new List<string>();

        [Parameter]
        public IEnumerable<ElenaLink> Elenalink { get; set; } = new List<ElenaLink>();

        [Parameter]
        public IEnumerable<TargetGroup> TargetGroupData { get; set; } = new List<TargetGroup>();

        [Parameter]
        public IEnumerable<ActionStatus> ActionStatusData { get; set; } = new List<ActionStatus>();

        [Parameter]
        public List<Delivery> DeveliveryData { get; set; } = new List<Delivery>();

        [Parameter]
        public List<CoverageType> CoverageTypeData { get; set; } = new List<CoverageType>();

        [Parameter]
        public List<Area> Areas { get; set; } = new List<Area>();

        [Parameter]
        public List<SocialDeterminant> SocialDeterminantList { get; set; } = new List<SocialDeterminant>();

        [Parameter]
        public List<ProblemSolution> problemSolution { get; set; } = new List<ProblemSolution>();

        [Parameter]
        public int? TreeTopicId { get; set; }
        [Parameter]
        public DateTime? DateForStartMonth { get; set; }
        [Parameter]
        public DateTime? DateForStartYear { get; set; }

        [Parameter]
        public DateTime? DateForEndYear { get; set; }
        [Parameter]
        public string CoverageTypeId { get; set; }

        [Parameter]
        public DateTime? DateForEndMonth { get; set; }
        private string Solution { get; set; }
        private Validations ElenaValidations;
        private int ProblemTypeId { get; set; }
        private int ProblemNumber { get; set; }
        private List<int> elenaLinkId { get; set; }
        public ElenaURLMapping ElenaData { get; set; } = new ElenaURLMapping();
        private bool validateaction { get; set; }

        private List<ActionProblemRevision> actrionProblem = new List<ActionProblemRevision>();
        private string ElenaError { get; set; }
        private string ElenaNameAddingError { get; set; }
        private string ElenaNameUpdatingError { get; set; }

        private string solutionName = string.Empty;
        private bool canAddELenaLink = false;
        private bool canUpdateELenaLink = true;
        private ElenaItem updatingElenaLink = new();
        private AntDesign.Tree<GTreeNode> topicTree;

        private _quillEditor quillEditorImplementationDetailsRef;
        private _quillEditor quillEditorImpactIndicatorsRef;
        private _quillEditor quillEditorMeSystemRef;
        private _quillEditor quillEditorBaselineRef;
        private _quillEditor quillEditorPostInterventionsRef;
        //private _quillEditor quillEditorProblemSolutionRef;
        private _quillEditor quillEditorOtherLessonsRef;
        private _quillEditor quillEditorPersonalStoryRef;

        private string ImplementationDetails = string.Empty;
        private string ImpactIndicators = string.Empty;
        private string MeSystem = string.Empty;
        private string Baseline = string.Empty;
        private string PostIntervention = string.Empty;
        private string OtherLessons = string.Empty;
        private string PersonalStory = string.Empty;
        private string CoveragePercent = string.Empty;
        private string TargetPopulation = string.Empty;
        private DataGridCommandColumn<ProblemSolution> commandColumn;
        private AntDesign.DatePicker<DateTime?> RefDateForStartYear { get; set; }
        private bool IsTopicIdSelected { get; set; }
        private bool IsProblemIdAndSolutionHasData { get; set; }
        private bool IsTopicEmpty { get; set; }
        protected override void OnParametersSet()
        {
            if (ActionDetails.StartMonth.HasValue && ActionDetails.StartMonth != 0)
            {
                DateForStartMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(ActionDetails.StartMonth), 1);
            }
            if (ActionDetails.StartYear.HasValue && ActionDetails.StartYear != 0)
            {
                DateForStartYear = new DateTime(ActionDetails.StartYear.Value, 1, 1);
            }
            if (ActionDetails.EndMonth.HasValue && ActionDetails.EndMonth != 0)
            {
                DateForEndMonth = new DateTime(DateTime.Now.Year, Convert.ToInt32(ActionDetails.EndMonth), 1);
            }
            if (ActionDetails.EndYear.HasValue && ActionDetails.EndYear != 0)
            {
                DateForEndYear = new DateTime(Convert.ToInt32(ActionDetails.EndYear), 1,1);
            }
            if (TopicList.Count > 0)
            {
                //topicTree?.CollapseAll();
            }
            StateHasChanged();
        }

        private void DataChangingStartMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartMonth = dateTimeChangedEventArgs.Date;
            if (DateForStartMonth != null)
            { 
            ActionDetails.StartMonth = dateTimeChangedEventArgs.Date.Value.Month;
            }
        }
        private void DataChangingStartYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForStartYear = dateTimeChangedEventArgs.Date;
            if (DateForStartYear != null)
            { 
            ActionDetails.StartYear = dateTimeChangedEventArgs.Date.Value.Year;
            }
        }
        private void DataChangingEndMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForEndMonth = dateTimeChangedEventArgs.Date;
            if (DateForEndMonth != null)
            {
                ActionDetails.EndMonth = dateTimeChangedEventArgs.Date.Value.Month;
            }
        }

        private void DataChangingEndYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            DateForEndYear = dateTimeChangedEventArgs.Date;
            if (DateForEndYear != null)
            { 
            ActionDetails.EndYear = dateTimeChangedEventArgs.Date.Value.Year;
            }
        }
        private bool DisableEndDate(DateTime endDate)
        {
            return DateForStartYear.HasValue && endDate.Date < DateForStartYear.Value.Date;
        }
        private bool DisableEndMonth(DateTime endDate)
        {
            DateTime? createdDateTime = null; 
            if (DateForStartMonth.HasValue && DateForStartYear.HasValue)
            {
                int year = DateForStartYear.Value.Year;
                int month = DateForStartMonth.Value.Month;

                // Assuming you want the first day of the selected month and year
                createdDateTime = new DateTime(year, month, 1);
                
            }
            return createdDateTime.HasValue && endDate.Date < createdDateTime.Value.Date;
        }
        private async Task SaveProblemAndSolution(CommandContext<ProblemSolution> problemSolutionItem)
        {
            if (!string.IsNullOrEmpty(problemSolutionItem.Item.Name))
            {
                await JsRuntime.InvokeVoidAsync("CloseProblemSolution");
                return;
            }
            ProblemTypeId = problemSolutionItem.Item.Id;

            IsProblemIdAndSolutionHasData = IsTopicIdSelected = false;

            if (ProblemTypeId == 0 || string.IsNullOrEmpty(solutionName))
            {
                IsProblemIdAndSolutionHasData = true;

                return;
            }
            if (ActionDetails.TopicId == null || ActionDetails.TopicId == 0)
            {
                IsTopicIdSelected = true;
                return;
            }
            if (ActionDetails.TopicId > 0 && ActionDetails.TopicId != 0)
            {
                ActionProblemRevision problem = new();
                problem.ProblemTypeId = ProblemTypeId;
                problem.ProblemNumber = ProblemNumber;
                actrionProblem.Add(problem);
                problemSolution.Add(new ProblemSolution()
                {
                    Id = ProblemTypeId,
                    Name = ProblemTypes.Where(p => p.Id == ProblemTypeId)?.First()?.Name,
                    Solution = problemSolutionItem.Item.Solution
                });
                ActionDetails.ActionProblemRevision.Add(problem);
                ProblemTypeId = 0;
                solutionName = string.Empty;
            }
            else
            {
                validateaction = true;
            }
            await JsRuntime.InvokeVoidAsync("CloseProblemSolution");

        }

        private async Task SaveAction()
        {
            IsTopicEmpty = false;
            if (!ActionDetails.TopicId.HasValue || ActionDetails.TopicId<=0)
            {
                IsTopicEmpty = true;
                return;
            }
            TopicList = new List<GTreeNode>();
            DeliverySelected = new List<string>();
            ActionDetails.ActionElenaRevision.Clear();
            ActionDetails.ActionTargetGroupRevision.Clear();
            ActionDetails.ActionDeliveryRevision.Clear();
            ActionDetails.ActionSocialDeterminantRevisions.Clear();
            ActionDetails.ActionElenaRevision.AddRange(DefaultelenaLinkData.Select(elenaLinkId => new ActionElenaRevision { ElenaLinkId = elenaLinkId }));
            ActionDetails.ActionTargetGroupRevision.AddRange(DefaultTargetData.Select(targetId => new ActionTargetGroupRevision { TargetGroupId = targetId }));
            ActionDetails.ActionDeliveryRevision.AddRange(DefaultDeliveryData.Select(deliveryId => new ActionDeliveryRevision { DeliveryId = deliveryId }));
            ActionDetails.ActionSocialDeterminantRevisions.AddRange(DefaultDeterminantData.Select(determinant => new ActionSocialDeterminantRevision { SocialDeterminant = determinant }));
            ActionDetails.ImplementationDetails = await quillEditorImplementationDetailsRef.GetHTML();
            ActionDetails.ImpactIndicators = await quillEditorImpactIndicatorsRef.GetHTML();
            ActionDetails.MeSystem = await quillEditorMeSystemRef.GetHTML();
            ActionDetails.Baseline = await quillEditorBaselineRef.GetHTML();
            ActionDetails.PostIntervention = await quillEditorPostInterventionsRef.GetHTML();
            ActionDetails.OtherLessons = await quillEditorOtherLessonsRef.GetHTML();
            ActionDetails.PersonalStory = await quillEditorPersonalStoryRef.GetHTML();
            ActionDetails.CoverageTypeId = CoverageTypeId;
            ActionDetails.Solution0 = problemSolution.Count >= 1 ? problemSolution[0].Solution : null;
            ActionDetails.Solution1 = problemSolution.Count >= 2 ? problemSolution[1].Solution : null;
            ActionDetails.Solution3 = problemSolution.Count >= 3 ? problemSolution[2].Solution : null;
            ActionDetails.Solution4 = problemSolution.Count >= 4 ? problemSolution[3].Solution : null;
            ActionDetails.Solution5 = problemSolution.Count >= 5 ? problemSolution[4].Solution : null;
            ActionDetails.Solution2 = problemSolution.Count >= 6 ? problemSolution[5].Solution : null;
            ActionDetails.Solution6 = problemSolution.Count >= 7 ? problemSolution[6].Solution : null;
            ActionDetails.Solution7 = problemSolution.Count >= 8 ? problemSolution[7].Solution : null;
            ActionDetails.Solution8 = problemSolution.Count >= 9 ? problemSolution[8].Solution : null;
            ActionDetails.Solution9 = problemSolution.Count >= 10 ? problemSolution[9].Solution : null;
            ImplementationDetails = string.Empty;
            ImpactIndicators = string.Empty;
            MeSystem = string.Empty;
            Baseline = string.Empty;
            PostIntervention = string.Empty;
            OtherLessons = string.Empty;
            PersonalStory = string.Empty;
            CoveragePercent = string.Empty;
            TargetPopulation = string.Empty;
            CoverageTypeId = string.Empty;
            TopicIds = null;
            await ActionData.InvokeAsync((ActionDetails, false));
        }

        private void DataChangingStartDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            ActionDetails.StartYear = Int32.Parse(Year);
            ActionDetails.StartMonth = Int32.Parse(month);
        }

        private void DataChangingEndDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            ActionDetails.EndYear = Int32.Parse(Year);
            ActionDetails.EndMonth = Int32.Parse(month);
        }

        //private async Task AddElenaData()
        //{
        //    if (ActionDetails.ActionElenaRevision.Any(a => a.ElenaLinkId == ElenaData.ElenaId)
        //        || ActionElenaLinks.Any(a => a.ElenaLinkId == ElenaData.ElenaId))
        //    {
        //        ElenaError = "Already Existing Name";
        //        return;
        //    }

        //    if (!string.IsNullOrEmpty(ElenaData.URLTitle))
        //    {
        //        ElenaError = string.Empty;
        //        ActionDetails.ActionElenaRevision.Add(new ActionElenaRevision()
        //        {
        //            ElenaLinkId = ElenaData.ElenaId,
        //            ElenaLink = new ElenaLink()
        //            { Title = ElenaData.URLTitle }
        //        });
        //        Elenalink.Add(new ElenaItem() { Title = ElenaData.URLTitle, Url = ElenaData.URL, IsDone = false });
        //        ElenaData = new ElenaURLMapping();
        //        CheckElenaLinkCanAdd();

        //        await ElenaValidations.ClearAll();
        //    }

        //    await InvokeAsync(StateHasChanged);
        //}
        //private async Task EditElena(ElenaItem detail, string activeclass)
        //{
        //    // cancel other updates
        //    Elenalink.ForEach(e => e.IsDone = false);

        //    updatingElenaLink = new()
        //    {
        //        Name = detail.Title,
        //        Url = detail.Url
        //    };

        //    detail.IsDone = !detail.IsDone;
        //    await JsRuntime.InvokeAsync<object>("addActiveElena", detail.IsDone, activeclass);
        //}

        private void DeteletProblem(ProblemSolution detail)
        {
            problemSolution.Remove(detail);
        }
        private void EditProblem(ProblemSolution detail, string activeclass)
        {
            //ElenaData.Name = detail.Name;
            //ElenaData.Url = detail.Url;
            detail.IsDone = !detail.IsDone;
            //await JsRuntime.InvokeAsync<object>("addActiveElena", detail.IsDone, activeclass);
        }

        //private void DeleteElena(ElenaItem detail)
        //{
        //    if (ActionDetails.ActionElenaRevision.Any(a => a.ElenaLinkId == detail.Id)
        //       || ActionElenaLinks.Any(a => a.ElenaLinkId == detail.Id))
        //    {
        //        var deleteItem = ActionDetails.ActionElenaRevision.Where(z => z.ElenaLinkId == detail.Id).FirstOrDefault();
        //        if (deleteItem != null)
        //        {
        //            ActionDetails.ActionElenaRevision.Remove(deleteItem);
        //        }
        //    }
        //    Elenalink.Remove(detail);
        //}

        //private void UpdateElena(ElenaItem detail, int index)
        //{
        //    detail.IsDone = false;

        //    if (updatingElenaLink != null)
        //    {
        //        Elenalink[index].Name = updatingElenaLink.Name;
        //        Elenalink[index].Title = updatingElenaLink.Name;
        //        Elenalink[index].Url = updatingElenaLink.Url;
        //        canUpdateELenaLink = true;
        //    }
        //}

        //private void CancelUpdateElena(ElenaItem detail, int index)
        //{
        //    detail.IsDone = false;
        //    canUpdateELenaLink = true;
        //    updatingElenaLink = null;
        //}

        private List<string> DeliverySelected = new List<string>();

        private void OnChangeImplementaionDeatils(string value)
        {
            ActionDetails.ImplementationDetails = value;
            value = string.Empty;
        }

        private void OnChangeImpactIndicators(string value)
        {
            ActionDetails.ImpactIndicators = value;
        }

        private void OnChangeMeSystem(string value)
        {
            ActionDetails.MeSystem = value;
            ActionDetails.ImplementationDetails = String.Empty;
            StateHasChanged();
        }

        private void OnChangeBaseLine(string value)
        {
            ActionDetails.Baseline = value;
        }

        private void OnChangePostIntervention(string value)
        {
            ActionDetails.PostIntervention = value;
        }

        private void OnChangeSocialOther(string value)
        {
            ActionDetails.SocialOther = value;
        }

        private void OnChangeDescription(string value)
        {
            solutionName = value;
            if (actrionProblem.Count == 0)
            {
                ActionDetails.Solution0 = value;
            }

            if (actrionProblem.Count == 1)
            {
                ActionDetails.Solution1 = value;
            }

            if (actrionProblem.Count == 2)
            {
                ActionDetails.Solution2 = value;
            }

            if (actrionProblem.Count == 3)
            {
                ActionDetails.Solution3 = value;
            }

            if (actrionProblem.Count == 4)
            {
                ActionDetails.Solution4 = value;
            }

            if (actrionProblem.Count == 5)
            {
                ActionDetails.Solution5 = value;
            }

            if (actrionProblem.Count == 6)
            {
                ActionDetails.Solution6 = value;
            }

            if (actrionProblem.Count == 7)
            {
                ActionDetails.Solution7 = value;
            }

            if (actrionProblem.Count == 8)
            {
                ActionDetails.Solution8 = value;
            }

            if (actrionProblem.Count == 9)
            {
                ActionDetails.Solution9 = value;
            }
        }

        private void OnChangeOtherLessons(string value)
        {
            ActionDetails.OtherLessons = value;
        }

        private void OnChangePersonalStory(string value)
        {
            ActionDetails.PersonalStory = value;
        }

        private void OnCheckArea(Area area, bool value)
        {
            if (value)
            {
                ActionDetails.ActionAreaRevision.Add(new ActionAreaRevision()
                {
                    AreaId = area.Name
                });
            }
            else
            {
                var getArea = ActionDetails.ActionAreaRevision.Where(x => x.AreaId == area.Name).First();
                ActionDetails.ActionAreaRevision.Remove(getArea);
            }
        }

        private TreeEventArgs<GTreeNode> PreviousCheckedNode = new TreeEventArgs<GTreeNode>();

        private async void TopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            TopicIds = null;
            checkedValue.Tree.UncheckAll();
            checkedValue.Node.SetChecked(true);
            PreviousCheckedNode = checkedValue;
            ActionDetails.TopicId = checkedValue.Node.DataItem.TopicId;
            //await ChangeTopic.InvokeAsync(ActionDetails.TopicId);
        }

        private void OnTargetChanged(IEnumerable<TargetGroup> target)
        {
            ActionDetails.ActionTargetGroupRevision = new List<ActionTargetGroupRevision>();
            if (target != null)
            {
                foreach (var item in target)
                {
                    ActionDetails.ActionTargetGroupRevision.Add(new ActionTargetGroupRevision() { TargetGroupId = item.Id });
                }
            }
        }

        private void OnElenaLinkChanged(IEnumerable<ElenaLink> elenaLinks)
        {
            if (elenaLinks != null)
            {
                ActionDetails.ActionElenaRevision = new List<ActionElenaRevision>();
                foreach (var item in elenaLinks)
                {
                    ActionDetails.ActionElenaRevision.Add(new ActionElenaRevision() { ElenaLinkId = item.Id });
                }
            }
        }


        private void OnDeliveryChanged(IEnumerable<Delivery> delivery)
        {
            ActionDetails.ActionDeliveryRevision = new List<ActionDeliveryRevision>();
            if (delivery != null)
            {
                foreach (var item in delivery)
                {
                    ActionDetails.ActionDeliveryRevision.Add(new ActionDeliveryRevision() { DeliveryId = item.Id });
                }
            }
        }

        private void OnELenaLinkNameChanged(string value)
        {
            ElenaData.URLTitle = value;
            CheckElenaLinkCanAdd();
        }

        private void OnELenaLinkChanged(string value)
        {
            ElenaData.URL = value;
            CheckElenaLinkCanAdd();
        }

        private void ValidateElenaLink(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
                return;
            }

            var url = Convert.ToString(e.Value);

            bool isValidUrl = !string.IsNullOrWhiteSpace(url)
                && Uri.TryCreate(url, UriKind.Absolute, out Uri uriResult)
                && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);

            e.Status = isValidUrl ? ValidationStatus.Success : ValidationStatus.Error;
        }

        private void ValidateAddingElenaName(ValidatorEventArgs e)
        {
            if (e.Value == null)
            {
                ElenaNameAddingError = "Name is required.";
                e.Status = ValidationStatus.Error;
                return;
            }

            var name = Convert.ToString(e.Value);

            //if (ActionDetails.ActionElenaLinks.Any(a => a.ElenaLinkId == name)
            //    || ActionElenaLinks.Any(a => a.ElenaLinkName == name))
            //{
            //    ElenaNameAddingError = "Name already exist.";
            //    e.Status = ValidationStatus.Error;
            //    return;
            //}

            e.Status = ValidationStatus.Success;
        }

        private void ValidateUpdatingElenaName(ElenaItem item, ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                ElenaNameUpdatingError = "Enter valid name.";
                e.Status = ValidationStatus.Error;
                return;
            }

            var name = Convert.ToString(e.Value);

            //if (item.Title != name && ActionDetails.ActionElenaLinks.Any(a => a.ElenaLinkId == name)
            //    || ActionElenaLinks.Any(a => a.ElenaLinkId == name))
            //{
            //    ElenaNameUpdatingError = "Name already exist.";
            //    e.Status = ValidationStatus.Error;
            //    return;
            //}

            e.Status = ValidationStatus.Success;
        }

        private void CheckElenaLinkCanAdd()
        {
            //bool isValidUrl = !string.IsNullOrWhiteSpace(ElenaData.Url)
            //    && Uri.TryCreate(ElenaData.Url, UriKind.Absolute, out Uri uriResult)
            //    && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);

            canAddELenaLink = !string.IsNullOrWhiteSpace(ElenaData.URLTitle);
        }

        private void UpdateElenaName(string value)
        {
            if (updatingElenaLink != null)
            {
                updatingElenaLink.Title = value;
                updatingElenaLink.Name = value;
                CheckElenaLinkCanUpdate(updatingElenaLink);
            }
        }

        private void UpdateElenaLink(string value)
        {
            if (updatingElenaLink != null)
            {
                updatingElenaLink.Url = value;
                CheckElenaLinkCanUpdate(updatingElenaLink);
            }
        }

        private void CheckElenaLinkCanUpdate(ElenaItem item)
        {
            bool isValidUrl = !string.IsNullOrWhiteSpace(item.Url)
                && Uri.TryCreate(item.Url, UriKind.Absolute, out Uri uriResult)
                && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);

            canUpdateELenaLink = !string.IsNullOrWhiteSpace(item.Name) && isValidUrl;
        }

        private bool ValidateAgeGroup = false;
        private bool ValidateTargetPopulation = false;
        private bool ValidateCoverage = false;

        private void ValidateByAgeGroup(string value)
        {
            ValidateAgeGroup = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}") ? true : false;
            ActionDetails.AgeGroup = value;
        }

        private void ValidateByTargetPopulation(string value)
        {
            ValidateTargetPopulation = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}") ? true : false;
            ActionDetails.TargetPopulation = value;
        }

        private void ValidateByCoverage(string value)
        {
            ValidateCoverage = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}") ? true : false;
            ActionDetails.CoveragePercent = value;
        }

        private void ClearAllTarget()
        {
            ActionDetails.ActionTargetGroupRevision = new List<ActionTargetGroupRevision>();
            StateHasChanged();
        }
        private async Task ClosePopup()
        {
            //problemSolution = new List<ProblemSolution>();
            //ActionDetails.ImplementationDetails = string.Empty;
            //ActionDetails.ImpactIndicators = string.Empty;
            //ActionDetails.MeSystem = string.Empty;
            //ActionDetails.Baseline = string.Empty;
            //ActionDetails.PostIntervention = string.Empty;
            //ActionDetails.OtherLessons = string.Empty;
            //ActionDetails.PersonalStory = string.Empty;
            //ActionDetails.CoveragePercent = string.Empty;
            //ActionDetails.TargetPopulation = string.Empty;
            //CoverageTypeId = string.Empty;
            //ActionDetails = new ActionRevision();
            //ActionDetails.TopicId = 0;
            //DateForStartYear = null;
            //DateForStartMonth = null;
            //DateForEndYear = null;
            //DateForEndMonth = null;
            //TreeTopicId = 0;
            //TopicIds = null;
            //ParentTopicIds = null;
            //topicTree.UncheckAll();
            //topicTree.CollapseAll();
            StateHasChanged();
            await CloseAction.InvokeAsync(false);
        }
    }
}