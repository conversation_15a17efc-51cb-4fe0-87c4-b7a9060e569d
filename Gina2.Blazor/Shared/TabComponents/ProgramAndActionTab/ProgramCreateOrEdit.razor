﻿@using Gina2.Blazor.Models.AdminModel
@using Gina2.DbModels;
@using Gina2.Blazor.Models;

@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent

<Loader IsLoading="@IsLoading" />
<Modal @ref="modalRef" Closing="@OnModalClosing" Class="modals-lg antdraggable" ShowBackdrop=false>
    <CascadingValue Value="@selectedTopic" Name="selectedTopic">
        <ActionCreateOrEdit TopicsAction="@TopicsAction"
                            ActionData="(arg => OnSaveAction(arg.Item1, arg.Item2))"
                            ActionDetails="@ActionDetails"
                            TreeTopicId="@TreeTopicId"
                            TopicIds="@TopicIds"
                            ParentTopicIds="@ParentTopicIds"
                            ActionStatusData="@ActionStatusData"
                            DateForStartMonth="@DateForStartMonth"
                            DateForStartYear="@DateForStartYear"
                            DateForEndMonth="@DateForEndMonth"
                            DateForEndYear="@DateForEndYear"
                            TargetGroupData="@TargetGroupData"
                            DeveliveryData="@DeveliveryData"
                            CoverageTypeData="@CoverageTypeData"
                            DefaultelenaLinkData="@DefaultelenaLinkData"
                            DefaultTargetData="@DefaultTargetData"
                            DefaultDeliveryData="@DefaultDeliveryData"
                            DefaultDeterminantData="@DefaultDeterminantData"
                            Areas="@Areas"
                            StartDate="@StartDate"
                            EndDate="@EndDate"
                            CoverageTypeId="@CoverageTypeId"
                            TopicList="@DuplicateTopicList"
                            ProblemTypes="@ProblemTypes"
                            Elenalink="@Elenalinks"
                            problemSolution="@ProblemSolution"
                            SocialDeterminantList="@SocialDeterminantList"
                            ChangeTopic="@ChangeTopic"
                            CloseAction="@CloseAction" />
    </CascadingValue>

</Modal>

<Modal @bind-Visible="@LocationVisible" Class="modals-lg antdraggable">
    <ModalContent Centered Class="forms">
        <ModalHeader Class="ant-header">
            <ModalTitle data-cy="MapLocationTitle">Map location</ModalTitle>
            <CloseButton Clicked="@HideLocation" />
        </ModalHeader>
        <ModalBody>
            <Fields>
                <Field>
                    <FieldLabel data-cy="LocationName">Location name</FieldLabel>
                    <MemoEdit Placeholder="" Text="@mapLocation" TextChanged="@VaidateByMapLocation" />
                    @if (ValidateMapLocation)
                    {
                        <Span Class="text-danger" data-cy="EnterValidMapLocation"> Enter valid map location</Span>
                    }
                </Field>
            </Fields>
        </ModalBody>
        <ModalFooter>
            <Button Disabled="@(string.IsNullOrEmpty(mapLocation))" data-cy="SaveBtn" Class="but-yellow pl-2 pr-2" Clicked="@SaveLocation">Save</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Div Class="newdraft _antdesign pl-2 pr-2">
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
        <Div Class="item1 flex-b" hidden="@(ProgrammeAndActionCode > 0 ? false : true)">
            <Button data-cy="BackToProgramBtn" Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/programmes-and-actions"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to program
            </Button>
        </Div>
        @*<Div Class="item2">
        <Button Clicked="@Download" Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
        </Div>*@
    </Div>
    <Div Class="pt-5 m-pt-2 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3" data-cy="FormTitle">
            @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.FormTitle))
            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.FormTitle" />
        </Heading>
        <Divider Class="divi-blue" />
    </Div>
    <Div Class="form-newd _antdesign pl-2 pr-2" id="ScrolToTop">
        <Fields>
            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel data-cy="InputTitleLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.InputTitleLabel)
                    <Span>*</Span>

                    <Tooltip data-cy="InputTitleLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.InputTitleTooltip)">
                        <Button data-cy="InputTitleLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="InputTitleLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.InputTitleGroup" />
                </FieldLabel>
                <TextEdit data-cy="InputTitlePlaceholder" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.InputTitlePlaceholder)" Text="@ProgramRevision.Title" TextChanged="@VaidateByTitle">
                </TextEdit>
                <FieldLabel data-cy="TitleRequired" Style="color:red">@(string.IsNullOrWhiteSpace(ProgramRevision.Title) && ValidationError ? "Title is required" : "")</FieldLabel>
                    @if (ValidateTitle)
                {
                    <Span Class="text-danger" data-cy="EnterValidTitle"> Enter valid title</Span>
                }
            </Field>

            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel data-cy="ProgramTitleEnglishLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramTitleEnglishLabel)
                    <Tooltip data-cy="ProgramTitleEnglishLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramTitleEnglishTooltip)">
                        <Button data-cy="ProgramTitleEnglishLabelBtn" Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button>
                    </Tooltip> <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramTitleEnglishGroup" />
                </FieldLabel>
                <TextEdit Disabled="@isEnglishTitleDisabled" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramTitleEnglishPlaceHolder)" Text="@ProgramRevision?.EnglishTranslatedTitle" TextChanged="@EnglishTitleKeyPress">

                </TextEdit>
                @if (ProgramEnglishTitle)
                {
                    <Span Class="text-danger"> Enter valid english title</Span>
                }

            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD">
                <FieldLabel data-cy="SelectProgramTypeLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramTypeLabel)
                    <Span>*</Span>
                    <Tooltip data-cy="SelectProgramTypeLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramTypeToolTip)">
                        <Button data-cy="SelectProgramTypeLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="SelectProgramTypeLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramTypeGroup" />
                </FieldLabel>
                <AntDesign.Select DataSource="@ProgramType"
                                  TItemValue="int?"
                                  Placeholder=""
                                  TItem="DbModels.ProgramType"
                                  LabelName="@nameof(DbModels.ProgramType.Name)"
                                  ValueName="@nameof(DbModels.ProgramType.Id)"
                                  @bind-Value="@ProgramRevision.ProgramTypeId"
                                  Style="width: 100%; margin-bottom: 8px;" />
                <FieldLabel Style="color:red">@(((!ProgramRevision.ProgramTypeId.HasValue || ProgramRevision.ProgramTypeId == 0) && ValidationError) ? "Program type is required" : "")</FieldLabel>
                </Field>
            </Fields>
            @if (Otherid == ProgramRevision.ProgramTypeId)
        {
            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel data-cy="SpecifyTextLabel">
                        If other, please specify
                        <TextEdit Placeholder="" @bind-Text="@ProgramRevision.TypeOther">
                        </TextEdit>
                    </FieldLabel>
                </Field>
            </Fields>
        }
        else
        {
            @(ProgramRevision.TypeOther = string.Empty)
            ;
        }
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel data-cy="SelectProgramLanguageLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramLanguageLabel)
                    <Span>*</Span>
                    <Tooltip data-cy="SelectProgramLanguageLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramLanguageToolTip)">
                        <Button data-cy="SelectProgramLanguageLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="SelectProgramLanguageLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramLanguageGroup" />
                </FieldLabel>
                <Select TValue="int?" SelectedValue="@ProgramRevision.LanguageId" SelectedValueChanged="@EnableDisableEnglishTitle">
                    <SelectItem Value="0" data-cy="SelectLanguage">Select Language</SelectItem>
                    <Repeater Items="@Language">
                        <SelectItem data-cy=@($"{context.Name.Split(" ")[0]}Name") Value="context.Id">@context.Name</SelectItem>
                    </Repeater>
                </Select>
                <FieldLabel Style="color:red">@(((!ProgramRevision.LanguageId.HasValue || ProgramRevision.LanguageId == 0) && ValidationError) ? "Program language is required" : "")</FieldLabel>

                </Field>

                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    @if (IsLoading)
                    {
                        <div class="spinner">Loading countries, please wait...</div>
                    }
                    else
                    {
                        <FieldLabel data-cy="SelectProgramCountryLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramCountryLabel)
                            <Span>*</Span>
                            <Tooltip data-cy="SelectProgramCountryTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramCountryToolTip)">
                                <Button data-cy="SelectProgramCountryBtn" Class="but-info _tooltip">
                                    <Icon data-cy="SelectProgramCountryIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramCountryGroup" />

                        </FieldLabel>
                        <AntDesign.Select DataSource="@CountryData"
                                    Mode="multiple"
                                    TItemValue="string"
                                    Placeholder=""
                                    TItem="Country"
                                    LabelName="@nameof(Country.Name)"
                                    ValueName="@nameof(Country.Iso3Code)"
                                    OnSelectedItemsChanged="OnCountriesMultiChanged"
                                    @bind-Values="@countryValues"
                                    AllowClear
                                    EnableSearch
                                    Style="width: 100%; margin-bottom: 8px;" />
                        <FieldLabel Style="color:red">@(ProgramRevision.ProgrammeCountryMapRevision.Count == 0 && ValidationError ? "Please select one or more country" : "")</FieldLabel>
                    }
                </Field>

            </Fields>

        </Div>
        <Div Class="form-newd _antdesign mt-4">

            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel data-cy="SelectProgramBriefDescriptionLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramBriefDescriptionLabel)
                            <Tooltip data-cy="SelectProgramBriefDescriptionLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramBriefDescriptionToolTip)">
                                <Button data-cy="SelectProgramBriefDescriptionLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="SelectProgramBriefDescriptionLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramBriefDescriptionGroup" />
                        </FieldLabel>
                    </Div>
                </Div>

                @*<RextEditors Changed="@OnChangeBriefDescription" Value="@ProgramRevision.BriefDescription" />*@
                <_quillEditor value="@ProgramRevision.BriefDescription" @ref="quillEditorBriefDescriptionRef"></_quillEditor>
            </Field>
        </Div>
        <Div Class="form-newd _antdesign mt-4">
            <Div Flex="Flex.JustifyContent.Between" Class="pt-0 _f-m-column">
                <Div Class="item1">
                    <Heading Class="blo-head" Size="HeadingSize.Is2" data-cy="SelectProgramMapLocationHeading">
                        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramMapLocationLabel)
                        <Tooltip data-cy="SelectProgramMapLocationTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramMapLocationToolTip)">
                            <Button data-cy="SelectProgramMapLocationBtn" Class="but-info _tooltip">
                                <Icon data-cy="SelectProgramMapLocationIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramMapLocationGroup" />
                    </Heading>
                </Div>
                <Div Class="item2 _f-m-column">
                    <Button data-cy="AddLocationBtn" Class="but-yellow pl-2 pr-2" Clicked="@ShowLocation"><Icon Class="far fa-plus" /> Add location</Button>
                </Div>
            </Div>


            <Dropzone Items="MapLocation">
                <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                    <Div Class="drag-1">

                        <Icon Class="fa-solid fa-grip-vertical" />
                        <Icon Clicked="@(() => ShowEditLocation(context))" Class="fa-solid fa-pen" />
                        @context
                    </Div>
                    <Div Class="drag-2">
                        <Icon Class="fa-solid fa-trash" Clicked="@(() => DeleteLocation(context))" />
                    </Div>

                </Div>
            </Dropzone>
            <Div Flex='Flex.JustifyContent.Center'>@(MapLocation.Count == 0 ? "No map location" : "")</Div>
        </Div>
        <Div Class="form-newd _antdesign mt-4">
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel data-cy="SelectProgramReferencesLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramReferencesLabel)
                            <Tooltip data-cy="SelectProgramReferencesLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.SelectProgramReferencesToolTip)">
                                <Button data-cy="SelectProgramReferencesLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="SelectProgramReferencesLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramReferencesGroup" />
                        </FieldLabel>
                        <FieldHelp>
                            Enter any published information, grey literature and/or weblinks related to the programme/project
                        </FieldHelp>
                    </Div>
                </Div>
                @*<RextEditors Changed="@OnChangeReferences" Value="@ProgramRevision.References" />*@
                <_quillEditor value="@ProgramRevision.References" @ref="quillEditorReferencesRef"></_quillEditor>

            </Field>

        </Div>

        <Div Class="form-newd _antdesign mt-4">
            <Field>
                <FieldLabel data-cy="ProgramPolicyLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramPolicyLabel)
                    <Tooltip data-cy="ProgramPolicyLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramPolicyToolTip)">
                        <Button data-cy="ProgramPolicyLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="ProgramPolicyLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramPolicyGroup" />
                </FieldLabel>
            </Field>
            <Divider />

            <AntDesign.Select DataSource="@SelectedCountriesPolicies"
                          Mode="multiple"
                          TItemValue="int"
                          TItem="CountryByPolicy"
                          LabelName="@nameof(CountryByPolicy.Title)"
                          ValueName="@nameof(CountryByPolicy.Id)"
                          @bind-Values="@DefaultPolicyData"
                          EnableSearch
                          AllowClear
                          Style="width: 100%; margin-bottom: 8px;" />
        </Div>

        <Div Class="form-newd _antdesign mt-4">
            <Field>
                <FieldLabel data-cy="ProgramPartnersInvolvedLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramPartnersInvolvedLabel)
                    <Tooltip data-cy="ProgramPartnersInvolvedLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramPartnersInvolvedToolTip)">
                        <Button data-cy="ProgramPartnersInvolvedLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="ProgramPartnersInvolvedLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramPartnersInvolvedGroup" />
                </FieldLabel>
            </Field>
            <Div Flex="Flex.JustifyContent.Between" Class="_tabsbox downl-flex">
                <Tabs SelectedTab="@selectedPartnerCategoryTab" Class="from-tab" SelectedTabChanged="@OnSelectedTabChanged">
                    <Items>
                        <Repeater Items="@PartnerCategoryData.OrderBy(pc => pc.DragAndDropKey)">
                            <Tab Name="@context.Name">@context.Name</Tab>
                        </Repeater>
                    </Items>
                    <Content>
                        <Repeater Context="PartnerCatogeries" Items="@PartnerCategoryData.OrderBy(pc => pc.DragAndDropKey)">
                            <TabPanel Name="@PartnerCatogeries.Name">
                                <Heading data-cy="ImplementationHeading" Size="HeadingSize.Is5" Class="pt-1 pb-1">Please indicate @PartnerCatogeries.Name involved in implementation</Heading>

                                <ListGroup Class="ulgroup">
                                    <Repeater Context="Partners" Items="@PartnerCatogeries.Partners.OrderBy(p => p.DragAndDropKey)">
                                        @{
                                        bool IsPartner = ProgramRevision.ProgrammePartnerRevision.Any(x => x.PartnerId == Partners.Id);
                                    }
                                    <ListGroupItem><Check Checked="@IsPartner" TValue="bool" CheckedChanged="@(e => OnSelectPartnerChange(PartnerCatogeries.Id, Partners.Id, false, e))">@Partners.Name</Check></ListGroupItem>
                                </Repeater>
                            </ListGroup>
                            <Field>
                                <FieldLabel>@PartnerCatogeries.Name detail(s)</FieldLabel>
                            </Field>
                        </TabPanel>
                    </Repeater>
                    @if (Details.Any())
                    {
                        <Repeater Items="@PartnerCategoryData">
                            <Div style="@(selectedPartnerCategoryTab == context.Name.ToString() ? "display : block" : "display : none")">
                                <_quillEditor value="@Details[context.Id.ToString()]" @ref="quillEditorDetailsRef[context.Name]"></_quillEditor>

                            </Div>
                        </Repeater>
                    }
                </Content>
            </Tabs>
        </Div>


        <Field Class="mt-2">
            <FieldLabel data-cy="InputTitleLabel">
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramCostLabel)

                <Tooltip data-cy="InputTitleLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramCostTooltip)">
                    <Button data-cy="InputTitleLabelBtn" Class="but-info _tooltip">
                        <Icon data-cy="InputTitleLabelIcon" Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramCostGroup" />
            </FieldLabel>
            <TextEdit data-cy="InputTitlePlaceholder" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramCostPlaceHolder)" @bind-Text="@ProgramRevision.Cost">
            </TextEdit>
        </Field>
    </Div>
    <Div>
    </Div>
    <Div Class="form-newd _antdesign mt-4">
        <Div Flex="Flex.JustifyContent.Between" Class="pt-4 _f-m-column">
            <Div Class="item1">
                <Heading Class="blo-head" Size="HeadingSize.Is3">
                    <FieldLabel data-cy="ProgramActionsLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramActionsLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="ProgramActionsLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramActionsToolTip)">
                            <Button data-cy="ProgramActionsLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="ProgramActionsLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramActionsGroup" />
                    </FieldLabel>
                </Heading>
            </Div>
            <Div Class="item2 _f-m-column">
                <Button data-cy="AddActionBtn" Class="but-yellow pl-2 pr-2" Clicked="@ShowModal"><Icon Class="far fa-plus" /> Add action</Button>
            </Div>
        </Div>
        <Dropzone Items="ActionDataList">
            <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                <Div Class="drag-1">
                    <Icon Class="fa-solid fa-grip-vertical" />
                    <Icon Clicked="@(() => ShowEditAction(context.TopicId))" Class="fa-solid fa-pen" />
                    @{
                        string TopicName = TopicsAction.Where(x => x.Id == context.TopicId)?.FirstOrDefault()?.Name;

                        foreach (var item in context.ActionTargetGroupRevision)
                        {
                            string targetname = TargetGroupData.Where(x => x.Id == item.TargetGroupId)?.FirstOrDefault()?.Name;
                            TopicName = $"{TopicName}-{targetname}";
                        }
                    }
                    @TopicName
                </Div>
                <Div Class="drag-2">
                    <Icon Class="fa-solid fa-trash" Clicked="@(() => DeleteAction(context.TopicId))" />
                </Div>

            </Div>
        </Dropzone>
        <FieldLabel Flex='Flex.JustifyContent.Center'>@(ActionDataList.Count == 0 && !ValidationError ? "No action data" : "")</FieldLabel>
            <FieldLabel Style="color:red" Flex='Flex.JustifyContent.Center' id="action-error">@(ActionDataList.Count == 0 && ValidationError ? "Action is required" : "")</FieldLabel>

        </Div>
        @*<Container Class="mt-4 _antdesign pb-6">
    <Div Class="stickybottom">
    <Button Class="but-yellow" Clicked="@SaveOrUpdateProgramAndAction">Save Draft</Button>
    <Button Class="but-by-yellow">Submit</Button>
    <Snackbar @ref="snackbar" Color="SnackbarColor.Info" Location="SnackbarLocation.Default">
    <SnackbarBody>
    Data Saved Successfully
    </SnackbarBody>
    </Snackbar>
    </Div>
    </Container>*@
        <Div Class="form-newd mt-4">
            <Div Class="row">
                <Div Class="col-lg-6 col-12">
                    <Fields>
                        <Field>
                            <FieldLabel data-cy="ProgramModerationNotesLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesLabel)
                                <Span>*</Span>
                                <Tooltip data-cy="ProgramModerationNotesLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesToolTip)">
                                    <Button data-cy="ProgramModerationNotesLabelBtn" Class="but-info _tooltip">
                                        <Icon data-cy="ProgramModerationNotesLabelTooltipIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip>
                                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramModerationNotesGroup" />
                            </FieldLabel>
                            <TextEdit Disabled Text="@UserName" TextChanged="@ValidateModerationNotes"></TextEdit>
                            <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivations.</FieldHelp>
                            @if (IsModerationNotes)
                        {
                            <Span Class="text-danger"> Enter valid moderation notes</Span>
                        }
                    </Field>
                </Fields>
            </Div>
            <Div Class="col-lg-6 col-12">
                <Fields>
                    <Field>
                        <FieldLabel data-cy="ProgramModerationNotesLabel">
                            Other notes
                            @*  @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesLabel) *@
                            @* <Span>*</Span> *@
                            <Tooltip data-cy="ProgramModerationNotesLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesToolTip)">
                                <Button data-cy="ProgramModerationNotesLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="ProgramModerationNotesLabelTooltipIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramModerationNotesGroup" />
                        </FieldLabel>
                        <TextEdit Text="@ModerationNotes" TextChanged="@ValidateModerationNotes"></TextEdit>
                        @if (IsModerationNotes)
                        {
                            <Span Class="text-danger"> Enter valid moderation notes</Span>
                        }
                    </Field>
                </Fields>
            </Div>
        </Div>
        @*  <Fields>
        <Field>
        <FieldLabel data-cy="ProgramModerationNotesLabel">
        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesLabel)
        <Span>*</Span>
        <Tooltip data-cy="ProgramModerationNotesLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ProgramModerationNotesToolTip)">
        <Button data-cy="ProgramModerationNotesLabelBtn" Class="but-info _tooltip">
        <Icon data-cy="ProgramModerationNotesLabelTooltipIcon" Name="IconName.QuestionCircle" />
        </Button>
        </Tooltip>
        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ProgramProgramModerationNotesGroup" />
        </FieldLabel>
        <MemoEdit Text="@ModerationNotes" Rows="3" TextChanged="@ValidateModerationNotes"></MemoEdit>
        @if (IsModerationNotes)
        {
        <Span Class="text-danger"> Enter valid moderation notes</Span>
        }
        </Field>
        </Fields> *@
    </Div>
    <Div Class="mt-4 pb-6">
        <Div Class="stickybottom">
            <Fields>
                <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">

                    <AntDesign.SimpleSelect TItem="string"
                                            TItemValue="string"
                                            DefaultValue="@("Draft")"
                                            @bind-Value="@nextStatusToApply"
                                            OnSelectedItemChanged="async e=>{ await OnnReveiw(e); }">
                        <SelectOptions>
                            @foreach (var item in RoleBaseWorkFlowLookUps)
                            {
                                <Tooltip ShowArrow=true Placement="TooltipPlacement.RightEnd" Title=@(item.Description)>
                                    <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Value Label=@item.Text />
                                </Tooltip>
                            }
                        </SelectOptions>
                    </AntDesign.SimpleSelect>

                </Field>
                @if (UseridVisible)
                {
                    <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                        <AntDesign.Select DataSource="@ContributorList"
                                      TItemValue="string"
                                      Placeholder="Select User Id"
                                      TItem="UserModel"
                                      LabelName="@nameof(UserModel.UserName)"
                                      ValueName="@nameof(UserModel.UserName)"
                                      @bind-Value="@selectedMailValue"
                                      AllowClear
                                      EnableSearch
                                      Style="width: 100%; margin-bottom: 0px;">
                        </AntDesign.Select>
                        <FieldHelp>Type who you want to send this content</FieldHelp>
                    </Field>
                }
                <Field>
                    <Button data-cy="SubmitBtn" Class="but-yellow" Clicked="e=> SaveOrUpdateProgramAndAction()">Save</Button>
                </Field>
            </Fields>

            <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
                <SnackbarBody>
                    New content: Your draft will be placed in moderation.
                </SnackbarBody>
            </Snackbar>
        </Div>
    </Div>

</Div>
