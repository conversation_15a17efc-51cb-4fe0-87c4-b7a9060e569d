using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Blazor.Shared.TabComponents.PolicyTab;
using Gina2.Core.Lookups;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.Services.Policy;
using Gina2.Services.Topic;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.JSInterop;
using Gina2.Services.TargetGroups;
using Gina2.Services.PartnerCategorys;
using Gina2.Services.Language;
using Gina2.Services.Country;
using Gina2.Services.Programme;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Core.Interface;
using Gina2.Blazor.Pages;
using System.Text.RegularExpressions;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.DbModels.PolicyDrafts;
using static Gina2.Core.Constants.LocalizationKeys;
using Gina2.Core.Methods;
using static Gina2.Core.Constants;
using Gina2.Core.Enums;
using Gina2.DbModels.MechanismRevisions;
using Gina2.Core.Extensions;
// using Irony.Parsing;
using Microsoft.Extensions.Caching.Memory;
using System.Diagnostics.PerformanceData;
using LinqKit;
using Blazorise;

namespace Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab
{
    public partial class ProgramCreateOrEdit : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        [Inject]
        private ILogger<ProgramCreateOrEdit> _logger { get; set; }
        [Inject]
        private IPolicyService PolicyService { get; set; }

        [Inject]
        private ITargetGroupService TargetGroupService { get; set; }

        [Inject]
        private ICurrentUserServiceExtended CurrentUserService { get; set; }

        [Inject]
        private ITopicService TopicService { get; set; }

        [Inject]
        private IPartnerCategoryService PartnerCategoryService { get; set; }

        [Inject]
        private IProgrammeService ProgramAndActionService { get; set; }

        [Inject]
        private IProgramRevisionService ProgramRevisionService { get; set; }

        [Inject]
        private ILanguageService LanguageService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IEmailServices EmailServices { get; set; }

        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        [Parameter] public string PolicyName { get; set; }

        [CascadingParameter(Name = "ProgrammeAndActionCode")]
        public int ProgrammeAndActionCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int? VersionId { get; set; }
        public int? TreeTopicId { get; set; }
        public string[] ParentTopicIds { get; set; }
        public string[] TopicIds { get; set; }
        private DateTime? DateForStartMonth { get; set; }
        private DateTime? DateForStartYear { get; set; }
        private DateTime? DateForEndYear { get; set; }
        private DateTime? DateForEndMonth { get; set; }

        public string selectedTopic = "Action";
        public ActionRevision ActionDetails { get; set; } = new ActionRevision()
        {
            ActionElenaRevision = new List<ActionElenaRevision>()
        };
        private Dictionary<string, string> Details = new Dictionary<string, string>();
        private IEnumerable<Language> Language { get; set; } = new List<Language>();
        private IEnumerable<ProgramType> ProgramType { get; set; } = new List<ProgramType>();
        private IEnumerable<Country> CountryData { get; set; } = new List<Country>();
        private IEnumerable<string> DefaultCountryData { get; set; } = new List<string>();
        private IEnumerable<int> DefaultPolicyData { get; set; } = new List<int>();
        private IEnumerable<PartnerCategory> PartnerCategoryData { get; set; } = new List<PartnerCategory>();
        private List<Country> CountryByPolicies { get; set; } = new List<Country>();

        private List<Topic> TopicsAction = new List<Topic>();
        private ProgramRevision ProgramRevision { get; set; } = new();

        private IEnumerable<string> countryValues = new List<string>();
        public List<ElenaLink> Elenalinks { get; set; } = new List<ElenaLink>();

        private string selectedPartnerCategoryTab = "";

        private Snackbar snackbar;
        public List<ProblemSolution> ProblemSolution { get; set; } = new List<ProblemSolution>();
        private IEnumerable<ActionStatus> ActionStatusData = new List<ActionStatus>();
        private IEnumerable<ElenaURLMapping> ElenaUrl = new List<ElenaURLMapping>();
        private List<Delivery> DeveliveryData = new List<Delivery>();
        private List<CoverageType> CoverageTypeData = new List<CoverageType>();
        public IEnumerable<TargetGroup> TargetGroupData = new List<TargetGroup>();
        private List<Area> Areas = new List<Area>();
        private List<ProblemType> ProblemTypes = new List<ProblemType>();
        public IEnumerable<int> DefaultTargetData { get; set; } = new List<int>();
        public IEnumerable<int> DefaultelenaLinkData { get; set; } = new List<int>();
        public IEnumerable<int> DefaultDeliveryData { get; set; } = new List<int>();
        public IEnumerable<string> DefaultDeterminantData { get; set; } = new List<string>();

        public List<ActionElenaLink> ActionElenaLinks = new List<ActionElenaLink>();
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int ActionIndex { get; set; } = -1;
        private List<string> MapLocation = new List<string>();

        private string mapLocation { get; set; }
        private string CoverageTypeId = string.Empty;

        private int MapLocationIndex { get; set; } = -1;

        public List<GTreeNode> TopicList { get; set; } = new List<GTreeNode>();
        public List<GTreeNode> DuplicateTopicList { get; set; } = new List<GTreeNode>();

        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private List<ActionRevision> ActionDataList = new();
        private IEnumerable<CountryByPolicy> SelectedCountriesPolicies { get; set; } = new List<CountryByPolicy>();
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new List<RoleBaseWorkFlowLookUp>();
        private List<UserModel> ContributorList { get; set; } = new List<UserModel>();

        private bool ValidationError { get; set; }

        private bool ValidateTitle = false;
        private bool ValidateMapLocation = false;
        public bool IsModerationNotes = false;
        public string ModerationNotes = string.Empty;
        public string UserName = string.Empty;

        public bool isEnglishTitleDisabled = false;
        public bool ProgramEnglishTitle = false;
        private int Otherid { get; set; }
        private List<SocialDeterminant> SocialDeterminantList = new List<SocialDeterminant>();
        private string nextStatusToApply = string.Empty;
        private Dictionary<string, _quillEditor> quillEditorDetailsRef = new Dictionary<string, _quillEditor>();

        private _quillEditor quillEditorBriefDescriptionRef;
        private _quillEditor quillEditorReferencesRef;
        //private _quillEditor quillEditorDetailsRef;
        private Modal modalRef;
        private void OnSelectedTabChanged(string name)
        {
            selectedPartnerCategoryTab = name;
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }
        void OnSelectPartnerChange(int categoryId, int partnerId, bool isFunding, bool value)
        {
            ProgrammePartnerRevision programCategory = new ProgrammePartnerRevision();
            programCategory.PartnerId = partnerId;
            programCategory.PartnerCategoryId = categoryId;
            if (value)
            {
                ProgramRevision.ProgrammePartnerRevision.Add(programCategory);
            }
            else
            {
                var getProgramPartnerCategory = ProgramRevision.ProgrammePartnerRevision
                        .Where(c => c.PartnerId == partnerId && c.PartnerCategoryId == categoryId)?.First();
                var getProgramPartnerMap = ProgramRevision.ProgrammePartnerRevision.Where(p => p.PartnerCategoryId == categoryId)?.First();
                ProgramRevision.ProgrammePartnerRevision.Remove(getProgramPartnerCategory);
            }

            
            
            bool IsPartnerCategory = ProgramRevision.ProgrammePartnerRevision.Any(x => x.PartnerCategoryId == categoryId);

            if (!IsPartnerCategory)
            {
                var getPatnerProgramCategories = ProgramRevision.ProgramPartnerCategoryDetailsRevision.Where(p => p.PartnerCategoryId == categoryId)?.First();
                ProgramRevision.ProgramPartnerCategoryDetailsRevision.Remove(getPatnerProgramCategories);
            }
        }
        public bool UseridVisible { get; set; } = false;
        private async Task OnnReveiw(string value)
        {
            nextStatusToApply = value;
            if (nextStatusToApply == WorkflowStatusToState.Delegated.ToString())
            {
                UseridVisible = true;
            }
            else if (nextStatusToApply == WorkflowStatusToState.SentForCorrection.ToString())
            {
                UseridVisible = true;
                if (ProgramRevision.Id != 0)
                {
                    var programRevisionHistory = await ProgramRevisionService.GetLogByProgramId(ProgramRevision.Id);

                    if (programRevisionHistory != null)
                    {
                        selectedMailValue = programRevisionHistory.OrderBy(s => s.RevisedDate).FirstOrDefault().UserName;
                    }
                }
            }
            else
            {
                UseridVisible = false;
            }
            StateHasChanged();
        }
        private bool modalVisible;
        private bool LocationVisible;
        private void ShowLocation()
        {
            mapLocation = string.Empty;
            LocationVisible = true;
        }
        private void HideLocation()
        {
            LocationVisible = false;
        }
        private async Task ShowModal()
        {
            List<string> topicParent = new List<string>();
            DuplicateTopicList = TopicList;
            DefaultTargetData = new List<int>();
            DefaultelenaLinkData = new List<int>();
            DefaultDeliveryData = new List<int>();
            DefaultDeterminantData = new List<string>();
            ProblemSolution = new List<ProblemSolution>();
            DateForStartMonth = null;
            DateForStartYear = null;
            DateForEndMonth = null;
            DateForEndYear= null;
            TreeTopicId = null;
            ParentTopicIds = topicParent.ToArray();
            ActionDetails.ImplementationDetails = string.Empty;
            ActionDetails.ImpactIndicators = string.Empty;
            ActionDetails.MeSystem = string.Empty;
            ActionDetails.Baseline = string.Empty;
            ActionDetails.PostIntervention = string.Empty;
            ActionDetails.OtherLessons = string.Empty;
            ActionDetails.PersonalStory = string.Empty;
            ActionDetails.CoveragePercent = string.Empty;
            selectedTopic = "Action";
            ActionDetails = new ActionRevision()
            {
                ActionElenaRevision = new List<ActionElenaRevision>()
            };
            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");
            StateHasChanged();
        }
        private Task OnModalClosing(ModalClosingEventArgs e)
        {
            // just set Cancel to prevent modal from closing
            e.Cancel = false || e.CloseReason != CloseReason.UserClosing;
            return Task.CompletedTask;
        }
        private async Task GetUsersAsync(string roleName)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var query = from user in _dbContext.Users.ToList()
                        join userRole in _dbContext.UserRoles
                       on user.Id equals userRole.UserId
                        join role in _dbContext.Roles
                        on userRole.RoleId equals role.Id
                        select new AppUserModel()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            UserName = user.UserName,
                            Email = user.Email,
                            Organization = user.Organization,
                            Status = user.Status,
                            UserRoles = role.Name
                        };
            var userList = query.ToList();

            foreach (var item in userList)
            {
                ContributorList.Add(new UserModel()
                {
                    Id = item.Id,
                    UserName = item.UserName,
                    DisplayName = item.UserName
                });
            }
        }

        string selectedMailValue;
        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await GetTopicsAsync();
            _ = GetUsersAsync("Contributor");
            StateHasChanged();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                 await LoadDataAsync(); // Background loading task
            }
        }

        private async Task LoadDataAsync()
        {
            await PopulateData();
            await LoadProgramDetails();
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }

        private async Task LoadProgramDetails()
        {

            if (ProgrammeAndActionCode > 0 && ProgrammeAndActionCode != 0)
            {
                var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsAsync(ProgrammeAndActionCode, VersionId);
                if (programRevision == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                ProgramRevision = programRevision;
                if (ProgramRevision != null && ProgramRevision.ProgramPartnerCategoryDetailsRevision != null)
                {
                    foreach (var item in ProgramRevision.ProgramPartnerCategoryDetailsRevision)
                    {
                        Details[item.PartnerCategoryId.ToString()] = ProgramRevision.ProgramPartnerCategoryDetailsRevision.Where(x => x.PartnerCategoryId == item.PartnerCategoryId)?.FirstOrDefault()?.Details;
                    }
                }
                countryValues = ProgramRevision.ProgrammeCountryMapRevision.Select(c => c.CountryCode).ToList();
                nextStatusToApply = programRevision.ProgramLog.FirstOrDefault().ToState;
                ActionDataList = programRevision.ActionProgramRevisionMap.Select(s => s.ActionRevision).ToList();
                MapLocation = ProgramRevision.Location != null ? ProgramRevision.Location.Split("|").ToList() : new List<string>();
                DefaultPolicyData = ProgramRevision.ProgramPolicyRevision.Select(x => x.PolicyId).ToList();
                var createOrEdit = VersionId != 0 ? "Edited" : "Created";
                UserName = $"{createOrEdit} by {CurrentUserService.UserName}";
                ModerationNotes = ProgramRevision.ProgramLog.FirstOrDefault().OtherNotes;
            }
            else
            {
                ProgramRevision.LanguageId = Language.Where(l => l.Name.Equals("English")).First().Id;
                isEnglishTitleDisabled = true;
                UserName = $"Created by {CurrentUserService.UserName}";
            }
            Otherid = ProgramType.FirstOrDefault(p => p.Name.Equals("Other")).Id;
            RoleBaseWorkFlowLookUps = RoleBaseWorkFlowLookUp.RoleBaseWorkFlowLookUps(CurrentUserService.UserRole);
        }
        private async Task PopulateData()
        {
            var ProgramTypeTask = ProgramAndActionService.GetProgrammeActionTypesAsync();
            var TopicsActionTask = ProgramAndActionService.GetTopicsAsync();
            var ActionElenaLinksTask = ProgramAndActionService.GetActionElenaAsync();
            var LanguageTask = LanguageService.GetLanguageTypesAsync();
            var CountryDataTask = CountryService.GetActiveCountries();
            var PartnerCategoryDataTask = PartnerCategoryService.GetPartnerCategoryPartnersAsync();
            var TargetGroupDataTask = TargetGroupService.GetTargetGroupsAsync();
            var ActionStatusDataTask = ProgramAndActionService.GetActionStatusAsync();
            var DeveliveryDataTask = ProgramAndActionService.GetDeliveryAsync();
            var CoverageTypeDataTask = ProgramAndActionService.GetCoverageTypeAsync();
            var AreasTask = ProgramAndActionService.GetAreasAsync();
            var ProblemTypesTask = ProgramAndActionService.GetProblemTypesAsync();
            var SocialDeterminantListTask = ProgramAndActionService.GetAllSocialDeterminantAsync();
            var ElenalinksTask = ProgramAndActionService.GetElenaAsync();

            await Task.WhenAll(ProgramTypeTask, TopicsActionTask, ActionElenaLinksTask,
                 LanguageTask, CountryDataTask, PartnerCategoryDataTask,
                 TargetGroupDataTask, ActionStatusDataTask, DeveliveryDataTask,
                 CoverageTypeDataTask, AreasTask, ProblemTypesTask, SocialDeterminantListTask,
                 ElenalinksTask);

            ProgramType = await ProgramTypeTask;
            TopicsAction = await TopicsActionTask;
            ActionElenaLinks = await ActionElenaLinksTask;
            Language = await LanguageTask;
            CountryData = await CountryDataTask;
            PartnerCategoryData = await PartnerCategoryDataTask;
            selectedPartnerCategoryTab = PartnerCategoryData.First().Name;
            PartnerCategoryData.ForEach(x => Details[x.Id.ToString()] = string.Empty);
            TargetGroupData = await TargetGroupDataTask;
            ActionStatusData = await ActionStatusDataTask;
            DeveliveryData = await DeveliveryDataTask;
            CoverageTypeData = await CoverageTypeDataTask;
            Areas = await AreasTask;
            ProblemTypes = await ProblemTypesTask;
            SocialDeterminantList = await SocialDeterminantListTask;
            Elenalinks = await ElenalinksTask;


            
            StateHasChanged();
        }

        private async Task ChangeTopic(int? topicId)
        {
            ActionDetails.TopicId = topicId;
        }
        private async Task ShowEditAction(int? topicId)
        {
            DuplicateTopicList = TopicList;
            ActionDetails = ActionDataList.Where(x => x.TopicId == topicId).FirstOrDefault();
            //Elenalink = ActionDetails?.ActionElenaRevision?.Count > 0 ? ActionDetails?.ActionElenaRevision?.FirstOrDefault()?.ElenaLink?.ElenaURLMapping.Select(x => new ElenaItem()
            //{ Title = x.URLTitle, Url = x.URL, IsDone = true }).ToList() : new List<ElenaItem>();
            ActionIndex = ActionDataList.FindIndex(x => x.TopicId == topicId);
            StartDate = MonthYearDisplayHelper.GetMonthAndYearDate(ActionDetails.StartMonth, ActionDetails.StartYear);
            EndDate = MonthYearDisplayHelper.GetMonthAndYearDate(ActionDetails.EndMonth, ActionDetails.EndYear);
            TreeTopicId = ActionDetails.TopicId;
            List<string> parentIds = new List<string>();
            parentIds.Add(AllParentTopics.FirstOrDefault(a => a.TopicId.Equals(TreeTopicId)).ParentId.ToString());
            List<string> topics = new List<string>();
            topics.Add(topicId.ToString());
            ParentTopicIds = parentIds.ToArray();
            TopicIds = topics.ToArray();
            DefaultDeliveryData = ActionDetails.ActionDeliveryRevision.Select(d => d.DeliveryId).ToList();
            DefaultTargetData = ActionDetails.ActionTargetGroupRevision.Select(t => t.TargetGroupId).ToList();
            DefaultelenaLinkData = ActionDetails.ActionElenaRevision.Select(t => t.ElenaLinkId).ToList();
            DefaultDeterminantData = ActionDetails.ActionSocialDeterminantRevisions.Select(t => t.SocialDeterminant).ToList();
            CoverageTypeId = ActionDetails.CoverageTypeId;
            //Elenalink = ActionDetails.ActionElenaLinks.Select(x => new ElenaItem()
            //{
            //    Title = String.IsNullOrEmpty(x.) ? "" : x.ElenaLinkName
            //}).ToList();
            modalVisible = true;
            var actionDetails = ActionDetails.ActionProblemRevision.ToList();
            ProblemSolution = ActionDetails.ActionProblemRevision.Select(x => new ProblemSolution()
            {
                Name = ProblemTypes.FirstOrDefault(p => p.Id == x.ProblemTypeId)?.Name,
                Solution = GetSolutionName(actionDetails.IndexOf(x))
            }).ToList();
            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");
            await InvokeAsync(StateHasChanged);

        }

        private string GetSolutionName(int index)
        {
            switch (index)
            {
                case 0: return ActionDetails.Solution0;
                case 1: return ActionDetails.Solution1;
                case 2: return ActionDetails.Solution2;
                case 3: return ActionDetails.Solution3;
                case 4: return ActionDetails.Solution4;
                case 5: return ActionDetails.Solution5;
                case 6: return ActionDetails.Solution6;
                case 7: return ActionDetails.Solution7;
                case 8: return ActionDetails.Solution8;
                case 9: return ActionDetails.Solution9;

                default: return null;
            }
        }

        private void DeleteAction(int? topicId)
        {
            ActionDataList.Remove(ActionDataList.FirstOrDefault(a => a.TopicId == topicId));
        }
        public async Task OnSaveAction(ActionRevision actionDetails, bool value)
        {
            if (ActionIndex > -1)
            {
                ActionDataList[ActionIndex] = actionDetails;
            }
            else
            {
                ActionDataList.Add(actionDetails);
            }
            ActionIndex = -1;
            //ProgrammeData.ActionRevision = ActionDataList;
            ActionDetails = new();
            ActionDetails.TopicId = 0;
            DuplicateTopicList = new List<GTreeNode>();
            selectedTopic = "Policy";
            TreeTopicId = 0;
            await modalRef.Hide();
            StateHasChanged();
        }

        private async Task SaveOrUpdateProgramAndAction()
        {
            if (!string.IsNullOrEmpty(ProgramRevision.Title)
                && ProgramRevision.ProgramTypeId != 0
                && ProgramRevision.ProgramTypeId != null
                && ProgramRevision.LanguageId != 0
                && ProgramRevision.ProgrammeCountryMapRevision.Count > 0
                && ActionDataList.Count > 0
                )
            {
                ValidationError = false;
                ProgramRevision.ProgramPartnerCategoryDetailsRevision = new List<ProgramPartnerCategoryDetailsRevision>();
                foreach (var category in PartnerCategoryData)
                {
                    string details = (await quillEditorDetailsRef[PartnerCategoryData.FirstOrDefault(p => p.Id == category.Id).Name].GetHTML()).SanitizeContent();
                    if (!string.IsNullOrEmpty(details))
                    {
                        ProgramRevision.ProgramPartnerCategoryDetailsRevision.Add(new ProgramPartnerCategoryDetailsRevision()
                        {
                            Details = details,
                            PartnerCategoryId = category.Id,
                            ProgramId = ProgramRevision.Id,
                            ProgramVId = ProgramRevision.VersionId
                        });
                    }

                }
                ProgramRevision.BriefDescription = await quillEditorBriefDescriptionRef.GetHTML();
                ProgramRevision.References = await quillEditorReferencesRef.GetHTML();
                ProgramRevision.References = await quillEditorReferencesRef.GetHTML();
                var programLog = new ProgramLog();

                programLog.RevisedDate = DateTime.UtcNow;
                programLog.UserName = CurrentUserService.UserName;
                programLog.OtherNotes = ModerationNotes;
                programLog.ToState = nextStatusToApply;
                if (nextStatusToApply == WorkflowStatusToState.Delegated || nextStatusToApply == WorkflowStatusToState.SentForCorrection)
                {
                    programLog.DelegatedDate = DateTime.UtcNow;
                    programLog.DelegatedUserName = selectedMailValue;
                }
                if (ProgramRevision.Id == 0)
                {
                    programLog.FromState = nextStatusToApply;
                }
                else
                {
                    programLog.ProgramId = ProgramRevision.Id;
                    programLog.FromState = ProgramRevision.ProgramLog.OrderByDescending(s => s.ProgramVId).First().ToState;
                }
                ProgramRevision.ProgramLog.Clear();
                ProgramRevision.ProgramLog.Add(programLog);
                foreach (var action in ActionDataList)
                {
                    action.Topic = null;
                    foreach (var target in action.ActionTargetGroupRevision)
                    {
                        target.TargetGroup = null;
                    }

                    foreach (var elena in action.ActionElenaRevision)
                    {
                        elena.ElenaLink = null;
                    }

                    foreach (var delivery in action.ActionDeliveryRevision)
                    {
                        delivery.Delivery = null;
                    }
                }
                ProgramRevision.ProgramPolicyRevision =  DefaultPolicyData.Select(e => new ProgramPolicyRevision()
                {
                    PolicyId=e
                }).ToList();
                await ProgramRevisionService.CreateProgramRevision(ProgramRevision, ActionDataList);

                string baseUrl = NavigationManager.BaseUri;
                string subject = "GIFNA data for your attention and action";
                string logedInUser = string.IsNullOrWhiteSpace(CurrentUserService.FullName)
                                                    ? CurrentUserService.Email
                                                    : CurrentUserService.FullName;
                string countryNames = string.Join(", ", CountryData.Where(c => countryValues.Contains(c.Iso3Code)).Select(c => c.Name));
                string dataType = ContentType.Actions.GetDescription();
                
                string firstCountryCode = CountryData.OrderBy(x => x.Name).FirstOrDefault()?.Iso3Code ?? string.Empty;
                string titleLink = $"{baseUrl}countries/{firstCountryCode}/programmes-and-actions/{ActionDataList.FirstOrDefault().Id}/{ProgramRevision.VersionId}";

                if (programLog.ToState == WorkflowStatusToState.Delegated)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentDelegationEmailModel delegationModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = ProgramRevision.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.DelegateEmail, delegationModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
                else if (programLog.ToState == WorkflowStatusToState.NeedsReview)
                {
                    var approverUsers = await CurrentUserService.GetApproverUsersByCountry(countryValues.ToHashSet());                    
                    var tasks = approverUsers.Select(async user =>
                    {
                        try
                        {
                            ContentReviewEmailModel reviewModel = new()
                            {
                                BaseUrl = baseUrl,
                                ReceivedBy = user.FullNameOrEmail,
                                CountryNames = countryNames,
                                ContentTitle = ProgramRevision.Title,
                                SubmitedBy = logedInUser,
                                OtherNotes = ModerationNotes,
                                DataType = dataType,
                                TitleLink = titleLink
                            };

                            await EmailServices.SendEmailByTemplateAsync(user.Email, subject, TemplateType.NeedsReview, reviewModel);                            
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Failed to send email to {user.Email}");
                        }
                    });

                    await Task.WhenAll(tasks);
                }
                else if (programLog.ToState == WorkflowStatusToState.SentForCorrection)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentCorrectionEmailModel correctionModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = ProgramRevision.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = ModerationNotes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.SendForCorrection, correctionModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }
                }
                
                string countrycode = ProgramRevision.ProgrammeCountryMapRevision?.First()?.CountryCode;
                if (nextStatusToApply == WorkflowStatusToState.Published)
                {
                    MemoryCache.Set("submit", "Program / action published successfully");

                    NavigationManager.NavigateTo($"/countries/{ProgramRevision.ProgrammeCountryMapRevision.First().CountryCode}/programmes-and-actions/{ProgramRevision.ActionProgramRevisionMap.First().ActionId}");
                }
                else if(nextStatusToApply == WorkflowStatusToState.NeedsReview)
                {
                    MemoryCache.Set("submit", "Program / action sent for review successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.SentForCorrection)
                {
                    MemoryCache.Set("submit", "Program / action sent for correction successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.Delegated)
                {
                    MemoryCache.Set("submit", "Program / action is delegated successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.Draft)
                {
                    MemoryCache.Set("submit", "Program / action draft created successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
            }
            else
            {
                ValidationError = true;
                string elementId = !string.IsNullOrEmpty(ProgramRevision.Title)
                && ProgramRevision.LanguageId != 0
                && ProgramRevision.ProgramTypeId != 0
                && ProgramRevision.ProgramTypeId != null
                && ProgramRevision.ProgrammeCountryMapRevision.Count > 0 ? "action-error" : "ScrolToTop";
                await JsRuntime.InvokeVoidAsync("ScrollToView", elementId);
            }
        }

        private void OnChangeDetail(string value, int categoryId, bool isFunding)
        {
            bool IsCategory = ProgramRevision.ProgramPartnerCategoryDetailsRevision.Any(x => x.PartnerCategoryId == categoryId);

            if (!IsCategory)
            {
                ProgramRevision.ProgramPartnerCategoryDetailsRevision.Add(new ProgramPartnerCategoryDetailsRevision()
                {
                    PartnerCategoryId = categoryId,
                    //IsFounding = isFunding,
                    Details = value
                });
            }
            else
            {
                var getPartnerCategory = ProgramRevision.ProgramPartnerCategoryDetailsRevision.Where(x => x.PartnerCategoryId == categoryId).First();
                getPartnerCategory.Details = value;
            }
        }

        private void OnChangeBriefDescription(string value)
        {
            ProgramRevision.BriefDescription = value;
        }

        private void OnChangeReferences(string value)
        {
            ProgramRevision.References = value;
        }
        private void SaveLocation()
        {
            if (string.IsNullOrEmpty(mapLocation))
            {
                LocationVisible = false;
            }
            else if (mapLocation != null)
            {
                if (MapLocationIndex > -1)
                {
                    MapLocation[MapLocationIndex] = mapLocation;
                }
                else
                {
                    MapLocation.Add(mapLocation);
                }
                ProgramRevision.Location = String.Join("|", MapLocation);
                mapLocation = string.Empty;
                MapLocationIndex = -1;
                LocationVisible = false;
            }
        }

        private void DeleteLocation(string context)
        {
            MapLocation.Remove(context);
            ProgramRevision.Location = String.Join("|", MapLocation);
        }

        private void ShowEditLocation(string value)
        {
            MapLocationIndex = MapLocation.IndexOf(value);
            mapLocation = value;
            LocationVisible = true;

        }

        private async Task GetTopicsAsync()
        {
            AllTopics = await PolicyService.GetTopicsAsync();
            AllParentTopics = await PolicyService.GetParentTopics();

            // Create dictionaries for fast lookups by Id
            var topicsDictionary = AllTopics.ToDictionary(t => t.Id);
            var parentTopicsLookup = AllParentTopics.ToLookup(p => p.ParentId);

            // Get selected topic's Id (avoiding repeated LINQ queries)
            var selectedTopicId = AllTopics.FirstOrDefault(t => t.Name.Equals(selectedTopic))?.Id;

            if (selectedTopicId != null)
            {
                // Filter and order the first set of parent topics using the dictionary
                var firstParentTopics = parentTopicsLookup[selectedTopicId.Value].OrderBy(t => t.OrderKey).ToList();
                await GetTopicTreeView(firstParentTopics, topicsDictionary, parentTopicsLookup);
            }
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> firstParentTopics, 
                                            Dictionary<int, Topic> topicsDictionary, 
                                            ILookup<int, TopicParent> parentTopicsLookup)
        {
            foreach (var item in firstParentTopics)
            {
                TopicList.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = topicsDictionary.TryGetValue(item.TopicId, out var topic) ? topic.Name : null,
                    Children = await GetChildTopicTreeView(parentTopicsLookup[item.TopicId].OrderBy(t => t.OrderKey).ToList(), topicsDictionary, parentTopicsLookup),
                    IsSelected = false
                });
            }
        }

        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<TopicParent> childTopicParents, 
                                                                Dictionary<int, Topic> topicsDictionary, 
                                                                ILookup<int, TopicParent> parentTopicsLookup)
        {
            var children = new List<GTreeNode>();
            foreach (var child in childTopicParents)
            {
                children.Add(new GTreeNode()
                {
                    TopicId = child.TopicId,
                    ParentId = child.ParentId,
                    Title = topicsDictionary.TryGetValue(child.TopicId, out var topic) ? topic.Name : null,
                    Children = await GetChildTopicTreeView(parentTopicsLookup[child.TopicId].OrderBy(t => t.OrderKey).ToList(), topicsDictionary, parentTopicsLookup),
                    IsSelected = false
                });
            }
            return children;
        }

        private void OnCountriesMultiChanged(IEnumerable<Country> Countries)
        {
            ProgramRevision.ProgrammeCountryMapRevision = new List<ProgrammeCountryMapRevision>();
            List<CountryByPolicy> listofcountry = new();

            if (Countries != null && Countries.Any())
            {
                countryValues = Countries.Select(c => c.Iso3Code).ToList();
                foreach (var country in Countries)
                {
                    CountryByPolicies = new List<Country>();
                    ProgramRevision.ProgrammeCountryMapRevision.Add(new ProgrammeCountryMapRevision()
                    {
                        CountryCode = country.Iso3Code
                    });

                    var getallCountryByPolicy = CountryData.Where(c => c.Iso3Code == country.Iso3Code).ToList();
                    CountryByPolicies.AddRange(getallCountryByPolicy);
                    foreach (var item in CountryByPolicies)
                    {
                        foreach (var item2 in item.PolicyCountryMap)
                        {
                            CountryByPolicy countryPolicy = new CountryByPolicy();
                            countryPolicy.Title = $"{country.Name}-{item2.Policy.Title}";
                            countryPolicy.Id = item2.Policy.Id;
                            listofcountry.Add(countryPolicy);
                        }
                    }
                    SelectedCountriesPolicies = listofcountry.DistinctBy(c=> c.Id).OrderBy(c => c.Id).ToList();
                    DefaultPolicyData = SelectedCountriesPolicies.Where(x => DefaultPolicyData.Contains(x.Id)).Select(l => l.Id).ToList();
                    ProgramRevision.ProgramPolicyRevision = ProgramRevision.ProgramPolicyRevision.Where(x => DefaultPolicyData.Contains(x.PolicyId)).ToList();

                }

            }
            else
            {

                countryValues = new List<string>();
                DefaultPolicyData = Enumerable.Empty<int>();
                ProgramRevision.ProgramPolicyRevision = new List<ProgramPolicyRevision>();
                SelectedCountriesPolicies = null;
            }

            StateHasChanged();
        }

        //private void OnPolicyChanged(IEnumerable<CountryByPolicy> policies)
        //{
        //    ProgramRevision.ProgramPolicyRevision = new List<ProgramPolicyRevision>();
        //    if (policies!=null)
        //    {
        //        DefaultPolicyData
        //        foreach (var item in policies)
        //        {
        //            ProgramRevision.ProgramPolicyRevision.Add(new ProgramPolicyRevision() { PolicyId = item.Id });
        //        }
        //    }
        //}

        private void CloseAction(bool value)
        {
            ActionIndex = -1;
            //ProgrammeData.ActionRevision = ActionDataList;
            ActionDetails = new();
            ActionDetails.TopicId = 0;
            DuplicateTopicList = new List<GTreeNode>();
            selectedTopic = "Policy";
            TreeTopicId = 0;
            TopicIds = null;
            ParentTopicIds = null;
            modalRef.Hide();
            StateHasChanged();
        }

        private void VaidateByTitle(string value)
        {
            ValidateTitle = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ProgramRevision.Title = value;


        }

        private void VaidateByMapLocation(string value)
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                ValidateMapLocation = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
                mapLocation = value;
            }
        }

        private void EnglishTitleKeyPress(string value)
        {
            ProgramEnglishTitle = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ProgramRevision.EnglishTranslatedTitle = value;
        }

        private async Task EnableDisableEnglishTitle(int? selectedLang)
        {
            ProgramRevision.LanguageId = selectedLang;
            if (selectedLang.HasValue && selectedLang.Value != 2481)
            {
                isEnglishTitleDisabled = false;
            }
            else
            {
                isEnglishTitleDisabled = true;
                ProgramRevision.EnglishTranslatedTitle = String.Empty;
            }
            await InvokeAsync(StateHasChanged);
        }

        public void ValidateModerationNotes(string value)
        {
            IsModerationNotes = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ModerationNotes = value;
        }
    }
}