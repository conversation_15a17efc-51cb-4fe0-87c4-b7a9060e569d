﻿@using Gina2.Core.Models;
@using Gina2.DbModels;
@using Gina2.Blazor.Models;
@using Gina2.Blazor.Helpers.PageConfigrationData

@inherits PageConfirgurationComponent

<ModalContent Centered Class="forms _antdesign datepickermargin" style="z-index:0 !important">
    <ModalHeader Class="ant-header">
        <ModalTitle>Add Action</ModalTitle> 
        <CloseButton Clicked="ClosePopup" />
    </ModalHeader>
    <ModalBody Class="_modalscroll">
        <Fields>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTopicLabel)
                <Span>*</Span>

                <Tooltip ZIndex="999999" Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTopicTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionTopicGroup" />
            </FieldLabel>
        </Fields>
        <Column Class="w-100 Topicspolicy _CheckBoxRemove m-0 p-0">
            <AntDesign.Tree @ref="topicTree"
                            ShowIcon
                            MatchedClass="site-tree-search-value"
                            DataSource="TopicList"
                            TItem="GTreeNode"
                            TitleExpression="x => x.DataItem.Title"
                            ChildrenExpression="x => {
                    if(x.DataItem.TopicId == TreeTopicId)
                    {
                    x.Checked = true;
                    }
                    return x.DataItem.Children;
                    }"
                            KeyExpression="x => x.DataItem.TopicId.ToString()"
                            DefaultExpandedKeys="@ParentTopicIds"
                            OnCheck="x => TopicTreeCheckboxClicked(x)"
                            CheckedKeys="@TopicIds"
                            CheckOnClickNode="false"
                            Checkable>
            </AntDesign.Tree>

            @if (IsTopicEmpty)
            {
                <FieldLabel Style="color:red">Action topic is required </FieldLabel>
            }
        </Column>
        <Fields>
            <Field ColumnSize="ColumnSize.Is8.OnTablet.Is12.OnMobile.Is8.OnDesktop.Is8.OnWidescreen.Is8.OnFullHD">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetGroupLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetGroupTooltip)">
                        <Button Class="but-info _tooltip">
                            <Icon Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionTargetGroupGroup" />
                </FieldLabel>
                <div class="rz-text-align-center">
                    <AntDesign.Select DataSource="@TargetGroupData"
                                      Mode="multiple"
                                      TItemValue="int"
                                      Placeholder="Select target group"
                                      TItem="TargetGroup"
                                      LabelName="@nameof(TargetGroup.Name)"
                                      ValueName="@nameof(TargetGroup.Id)"
                                      @bind-Values="@DefaultTargetData"
                                      AllowClear
                                      EnableSearch
                                      Style="width: 100%; margin-bottom: 8px;" />
                </div>
                <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetGroupPlaceHolder)</FieldHelp>

            </Field>
            <Field ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionAgeGroupLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionAgeGroupTooltip)">
                        <Button Class="but-info _tooltip">
                            <Icon Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionAgeGroupGroup" />
                </FieldLabel>
                <TextEdit TextChanged="@ValidateByAgeGroup" Text="@ActionDetails.AgeGroup" />
                <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionAgeGroupPlaceHolder)</FieldHelp>
                @if (ValidateAgeGroup)
                {
                    <Span Class="text-danger"> Enter valid age</Span>
                }
            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker p-0 m-0">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionStartDateLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionStartDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionStartDateGroup" />
                </FieldLabel>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="p-0 mb-0">
                        <FieldLabel Class="_label_textline">Month</FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@DateForStartMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingStartMonth" Picker="@AntDesign.DatePickerType.Month" />
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="pr-0 mb-0">
                        <FieldLabel Class="_label_textline">Year</FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@DateForStartYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingStartYear" />
                    </Field>
                </Fields>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker pr-0 m-0">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionEndDateLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionEndDateTooltip)"><Button Class="but-info _tooltip"><Icon Name="IconName.QuestionCircle" /></Button></Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionEndDateGroup" />
                </FieldLabel>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="p-0 mb-0">
                        <FieldLabel Class="_label_textline">Month</FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@DateForEndMonth" TValue="DateTime?" Format="MMM" OnChange="DataChangingEndMonth" Picker="@AntDesign.DatePickerType.Month" DisabledDate="DisableEndMonth" />
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="pr-0 mb-0">
                        <FieldLabel Class="_label_textline">Year</FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@DateForEndYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingEndYear"  DisabledDate="DisableEndDate" />
                    </Field>
                </Fields>
            </Field>
        </Fields>



        <Field>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPlaceLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPlaceTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionPlaceGroup" />
            </FieldLabel>
            <TextEdit Placeholder="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPlacePlaceHolder)" @bind-Text="@ActionDetails.Place">
            </TextEdit>

        </Field>
        <Field Class="mb-1" Flex="Flex.JustifyContent.End">
            @*<Button Class="but-yellow butt-act-w" > Add another item </Button>*@
        </Field>
        <Field>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionElenaLinkLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionElenaLinkTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionElenaLinkGroup" />
            </FieldLabel>
            <div class="rz-text-align-center">
                <AntDesign.Select DataSource="@Elenalink"
                                  Mode="multiple"
                                  TItemValue="int"
                                  Placeholder="Select elena link"
                                  TItem="ElenaLink"
                                  LabelName="@nameof(ElenaLink.Title)"
                                  ValueName="@nameof(ElenaLink.Id)"
                                  @bind-Values="@DefaultelenaLinkData"
                                  DefaultValues="@DefaultelenaLinkData"
                                  AllowClear
                                  EnableSearch
                                  Style="width: 100%; margin-bottom: 8px;" />
            </div>
             
            <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionElenaLinkPlaceHolder)</FieldHelp>
        </Field>
        <Fields Class="area">
            <FieldLabel Class="w-100">
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionAreaLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionAreaTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionAreaGroup" />
            </FieldLabel>
            <Repeater Items="@Areas">
                <Field Class="m-0">
                    <Check TValue="bool" Checked="@ActionDetails.ActionAreaRevision.Any(a => a.AreaId.Equals(context.Name))" CheckedChanged="@(e =>OnCheckArea(context, e))"> @context.Name</Check>
                </Field>
            </Repeater>
        </Fields>
        <Field>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionDeliveryLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionDeliveryTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionDeliveryGroup" />
            </FieldLabel>

            <div class="rz-text-align-center">
                <AntDesign.Select DataSource="@DeveliveryData"
                                  Mode="multiple"
                                  TItemValue="int"
                                  Placeholder="Select delivery(ies)"
                                  TItem="Delivery"
                                  LabelName="@nameof(Delivery.Name)"
                                  ValueName="@nameof(Delivery.Id)"
                                  @bind-Values="@DefaultDeliveryData"
                                  AllowClear
                                  EnableSearch
                                  Style="width: 100%; margin-bottom: 8px;" />
            </div>
            @* <AntDesign.Select DataSource="@DeveliveryData"
                              Mode="multiple"
                              TItemValue="int"
                              Placeholder="Select Delivery(ies)"
                              TItem="Delivery"
                              LabelName="@nameof(Delivery.Name)"
                              ValueName="@nameof(Delivery.Id)"
                              OnSelectedItemsChanged="@OnDeliveryChanged"
                              @bind-Values="@DefaultDeliveryData"
                              DefaultValues="@DefaultDeliveryData"
                              AllowClear
                              EnableSearch
                              Style="width: 100%; margin-bottom: 8px;" /> *@
            <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionDeliveryPlaceHolder)</FieldHelp>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionImplementationDetailLabel)
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionImplementationDetailTooltip)">
                            <Button Class="but-info _tooltip">
                                <Icon Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionDeliveryGroup" />
                    </FieldLabel>
                </Div>
            </Div>
            <_quillEditor value="@ActionDetails.ImplementationDetails" @ref="quillEditorImplementationDetailsRef"></_quillEditor>
            <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionImplementationDetailPlaceHolder) </FieldHelp>

        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionOutcomeIndicatorLabel)
                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionOutcomeIndicatorTooltip)">
                            <Button Class="but-info _tooltip">
                                <Icon Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionOutcomeIndicatorGroup" />
                    </FieldLabel>
                </Div>
            </Div>
            @*<RextEditors Changed="@OnChangeImpactIndicators" Value="@ActionDetails.ImpactIndicators" />*@
            <_quillEditor value="@ActionDetails.ImpactIndicators" @ref="quillEditorImpactIndicatorsRef"></_quillEditor>
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">

                    <FieldLabel>
                        @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionMAndESystemLabel)

                        <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionMAndESystemTooltip)">
                            <Button Class="but-info _tooltip">
                                <Icon Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip>
                        <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionMAndESystemGroup" />
                    </FieldLabel>
                </Div>
            </Div>
            <_quillEditor value="@ActionDetails.MeSystem" @ref="quillEditorMeSystemRef"></_quillEditor>
        </Field>
        <Field>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetPopulationLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetPopulationTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionTargetPopulationGroup" />
            </FieldLabel>
            <TextEdit TextChanged="@ValidateByTargetPopulation" Text="@ActionDetails.TargetPopulation" />
            <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTargetPopulationPlaceHolder)  </FieldHelp>
            @if (ValidateTargetPopulation)
            {
                <Span Class="text-danger"> Enter valid population</Span>
            }
        </Field>
        <Field>
            <FieldLabel>
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionConverageLevelLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionConverageLevelTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionConverageLevelGroup" />
            </FieldLabel>

            <TextEdit TextChanged="@ValidateByCoverage" Text="@ActionDetails.CoveragePercent" />
            <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionConverageLevelPlaceHolder)</FieldHelp>
            @if (ValidateCoverage)
            {
                <Span Class="text-danger"> Enter valid population</Span>
            }
        </Field>

        <Fields>
            <FieldLabel Class="w-100">
                @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionConverageTypeLabel)
                <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionConverageTypeTooltip)">
                    <Button Class="but-info _tooltip">
                        <Icon Name="IconName.QuestionCircle" />
                    </Button>
                </Tooltip>
                <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionConverageTypeGroup" />
            </FieldLabel>
            <RadioGroup Class="mt-0 radiogrp" TValue="string" @bind-CheckedValue="@CoverageTypeId">
                <Repeater Items="@CoverageTypeData">
                    <Radio Value="context.Name">@context.Name</Radio>
                </Repeater>
            </RadioGroup>
        </Fields>
        <Fields>
            <Field Class="pl-0 pt-3">
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionBaselineLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionBaselineTooltip)">
                        <Button Class="but-info _tooltip">
                            <Icon Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionBaselineGroup" />
                </FieldLabel>

                <_quillEditor value="@ActionDetails.Baseline" @ref="quillEditorBaselineRef"></_quillEditor>
            </Field>
        </Fields>
        <Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPostInterventionLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPostInterventionTooltip)">
                                <Button Class="but-info _tooltip">
                                    <Icon Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionPostInterventionGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@ActionDetails.PostIntervention" @ref="quillEditorPostInterventionsRef"></_quillEditor>
                <FieldHelp> @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPostInterventionPlaceHolder)</FieldHelp>

            </Field>
        </Fields>
        <Fields>
            <Field>
                <FieldLabel>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSocialDeterminantLabel)
                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSocialDeterminantTooltip)">
                        <Button Class="but-info _tooltip">
                            <Icon Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip>
                </FieldLabel>

                <FieldLabel>Outcome reported by social determinants</FieldLabel>
                <div class="rz-text-align-center">
                    @* <RadzenDropDown @bind-Value=@DefaultDeterminantData Data=@SocialDeterminantList TextProperty="Name" ValueProperty="Name" Name="DropDownMultipleChips"
                                    Multiple=true AllowClear=true Chips=true Style="width: 100%; margin-bottom: 8px;" /> *@
                </div>
                <AntDesign.Select DataSource="@SocialDeterminantList"
                                  Mode="multiple"
                                  TItemValue="string"
                                  Placeholder="Select social determinants"
                                  TItem="SocialDeterminant"
                                  LabelName="@nameof(SocialDeterminant.Name)"
                                  ValueName="@nameof(SocialDeterminant.Name)"
                                  @bind-Values="@DefaultDeterminantData"
                                  AllowClear
                                  EnableSearch
                                  Style="width: 100%; margin-bottom: 8px;" />
                <FieldHelp>
                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSocialDeterminantPlaceHolder)
                    .
                </FieldHelp>
            </Field>
        </Fields>

        <Fields>
            <Field Class="m-0" ColumnSize="ColumnSize.Is10.OnTablet.Is12.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                <FieldLabel>
                    @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionProblemAndSolutionLabel))
                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionSocialDeterminantGroup" />
                </FieldLabel>
            </Field>
            @*<Field Class="m-0" ColumnSize="ColumnSize.Is2.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD">
            <Button Class="but-yellow butt-act-w" Clicked="@SaveProblemAndSolution"> Add </Button>
            </Field>*@

            @*<Div Class="table-responsive pb2">
                <Table Class="table-gray">
                    <TableHeader>
                        <TableRow>
                            <TableHeaderCell>
                                <FieldLabel>
                                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionTypicalProblemLabel)
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSocialDeterminantTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionSocialDeterminantGroup" />
                                </FieldLabel>
                            </TableHeaderCell>
                            <TableHeaderCell>
                                <FieldLabel>
                                    @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSolutionLabel)
                                    <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSolutionTooltip)">
                                        <Button Class="but-info _tooltip">
                                            <Icon Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip>
                                    <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionSolutionGroup" />
                                </FieldLabel>
                            </TableHeaderCell>

                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow>
                            <TableRowCell Class="cell1">
                                <Select data-cy="Problem" TValue="int" class="pl-1 pr-3" @bind-SelectedValue="@ProblemTypeId">
                                    <SelectItem data-cy="none" Value="0">None</SelectItem>
                                    <Repeater Items="@ProblemTypes">
                                        <SelectItem data-cy="@context.Name.Replace(" ", "")" Value="context.Id">@context.Name</SelectItem>
                                    </Repeater>
                                </Select>
                                <FieldHelp Class="mt-2">Select one or more</FieldHelp>
                            </TableRowCell>
                            <TableRowCell Class="cell2">
                                <TextEdit @bind-Text="@solutionName" />
                                <FieldHelp>@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionSolutionPlaceHolder)</FieldHelp>
                            </TableRowCell>

                        </TableRow>
                    </TableBody>
                </Table>
            </Div>*@
            @if (IsTopicIdSelected)
            {
                <p class="text-danger"> Please select the topic id first </p>
            }
            @if (IsProblemIdAndSolutionHasData)
            {
                <p class="text-danger"> Please select the Problem and Solution</p>
            }

        </Fields>

        <DataGrid TItem="ProblemSolution"
                  Data="@problemSolution"
                  Editable
                  Responsive>
            <DataGridCommandColumn>
                <SaveCommandTemplate>
                    <Button ElementId="btnSave" Type="ButtonType.Submit" PreventDefaultOnSubmit Color="Color.Primary" Clicked="@(() => SaveProblemAndSolution(context))">@context.LocalizationString</Button>
                </SaveCommandTemplate>
                <CancelCommandTemplate>
                    <Button ElementId="btnCancel" Color="Color.Secondary" Clicked="@context.Clicked">@context.LocalizationString</Button>
                </CancelCommandTemplate>
            </DataGridCommandColumn>
            <DataGridColumn Field="@nameof(ProblemSolution.Name)" Caption="Name" DisplayFormat="{0:C}" DisplayFormatProvider="@System.Globalization.CultureInfo.GetCultureInfo("fr-FR")" Editable>
                <EditTemplate>
                    @{
                        int id = context.Item.Id;
                    }
                    <Select data-cy="Problem" TValue="int" class="pl-1 pr-3" @bind-SelectedValue="@context.Item.Id">
                        <SelectItem data-cy="none" Value="0">None</SelectItem>
                        <Repeater Items="@ProblemTypes" Context="problemType">
                            <SelectItem data-cy="@problemType.Name.Replace(" ", "")" Value="problemType.Id">@problemType.Name</SelectItem>
                        </Repeater>
                    </Select>
                </EditTemplate>
            </DataGridColumn>
            <DataGridColumn Field="@nameof(ProblemSolution.Solution)" Caption="Solution" DisplayFormat="{0:C}" DisplayFormatProvider="@System.Globalization.CultureInfo.GetCultureInfo("fr-FR")" Editable >
                <EditTemplate>
                    @{
                        solutionName = context.Item.Solution;
                    }
                    <TextEdit @bind-Text="@context.Item.Solution" />
                </EditTemplate>
            </DataGridColumn>

        </DataGrid>

        <Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionotherLessanLearntLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionotherLessanLearntTooltip)">
                                <Button Class="but-info _tooltip">
                                    <Icon Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionotherLessanLearntGroup" />
                        </FieldLabel>
                    </Div>
                </Div>
                <_quillEditor value="@ActionDetails.OtherLessons" @ref="quillEditorOtherLessonsRef"></_quillEditor>
            </Field>
        </Fields>
        <Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>
                            @PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPersonalStoryLabel)
                            <Tooltip Text="@PageConfigurations.GetPageConfigrationValueByName(ProgramCreatePageConfigurationKey.ActionPersonalStoryTooltip)">
                                <Button Class="but-info _tooltip">
                                    <Icon Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip>
                            <AdminEditbut Key="@ProgramCreatePageConfigurationKey.ActionPersonalStoryGroup" />
                        </FieldLabel>

                    </Div>
                </Div>
                <_quillEditor value="@ActionDetails.PersonalStory" @ref="quillEditorPersonalStoryRef"></_quillEditor>
            </Field>
        </Fields>
    </ModalBody>
    <ModalFooter>
        <Button Class="but-yellow pl-2 pr-2 butt-act-w" Clicked="@SaveAction">Save</Button>
    </ModalFooter>
</ModalContent>