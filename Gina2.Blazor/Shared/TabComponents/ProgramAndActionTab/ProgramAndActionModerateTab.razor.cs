﻿using AntDesign;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Gina2.DbModels.PolicyDrafts;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.Services.Dashboard;
using Gina2.Services.Policy;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab
{
    public partial class ProgramAndActionModerateTab
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private ModalService ModalService { get; set; }
        [CascadingParameter(Name = "ProgrammeAndActionCode")] public int ProgrammeAndActionCode { get; set; }
        [CascadingParameter(Name = "CountryCode")] public string CountryCode { get; set; }
        private string containType = "Actions";
        [Inject]
        private IProgramRevisionService ProgramRevisionService { get; set; }
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private IDashboardService DashboardService { get; set; }
        [Inject]
        private IProgrammeService ProgrammeService { get; set; }
        private List<ModerationRevisionLog> ModerationLog { get; set; } = new();
        private int RevisionId { get; set; }
        private int lastRevisionId;
        List<ModerationRevisionLog> ModerateActions { get; set; } = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if(firstRender) {
                ModerationLog = await DashboardService.GetModerationLogByEntityId(ProgrammeAndActionCode, containType);
                lastRevisionId = ModerationLog.First().EntityRevisionId;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task OpenConfirm(RenderFragment renderFragment, int entityId, int versionId, string contentType, int logId)
        {
            bool unPublish = false;
            if (renderFragment == UnpublishContent)
            {
                unPublish = true;
            }
            var options = new ConfirmOptions()
            {
                Title = "Confirm your action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await ApplyActions(contentType, entityId, versionId, logId);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertConfirm(ModerationRevisionLog log, RenderFragment renderFragment)
        {
            var options = new ConfirmOptions()
            {
                Title = "Confirm revert action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await RevertAction(log);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertAction(ModerationRevisionLog log)
        {
            await ProgramRevisionService.Revert(log.EditId, log.EntityAnotherRevisionId, log.ToState, CurrentUserService.UserName);
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
        }
        private async Task ApplyActions(string contentType, int entityId, int versionId, int logId)
        {
            var programRevision = await ProgramRevisionService.GetProgramRevisionDetailsToCreateAdraftAsync(entityId, versionId);
            programRevision.ProgramLog.Clear();
            programRevision.ProgramLog.Add(new ProgramLog
            {
                ProgramId = programRevision.Id,
                ProgramVId = programRevision.VersionId,
                FromState = WorkflowStatusToState.Published,
                ToState = WorkflowStatusToState.Draft,
                RevisedDate = DateTimeOffset.UtcNow,
                UserName = CurrentUserService.UserName,
            });
            var actionRevision = programRevision.ActionProgramRevisionMap.Select(s => s.ActionRevision).ToList();
            await ProgramRevisionService.CreateProgramRevision(programRevision, actionRevision, true);
            await DashboardService.UnpublishModerationLogByLogId(logId, containType);
            await ProgrammeService.RemoveByIdAsync(entityId);
            _ = OpenSuccessToaster("Unpublished successfully");
            NavigationManager.NavigateTo("admin/dashboard");
        }

        public async void NavigateToDraftEdit(int entityId, int revisionId)
        {
            NavigationManager.NavigateTo($"countries/{CountryCode}/programmes-and-actions/{entityId}/{revisionId}/Edit");
        }
        public async void NavigateToDraft(int entityId, int revisionId)
        {
            NavigationManager.NavigateTo($"countries/{CountryCode}/programmes-and-actions/{ProgrammeAndActionCode}/{revisionId}");
        }

    }
}
