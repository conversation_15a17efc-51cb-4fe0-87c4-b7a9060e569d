﻿@using Gina2.Core.Methods
@using Gina2.DbModels;
@using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions
@using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions
@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent;

<Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
    <Div Class="item1">
        <Button hidden="@(CountryCode != null ? false : true)" Class="back-but" data-cy="BackToProgramme" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/programmes-and-actions"))">
            <Icon Class="fas fa-chevron-left"></Icon> Back to Programmes / actions
        </Button>


    </Div>
    <Div Class="item2">
        @*<Button Loading="@ExportPdfLoading" Clicked="@DownloadPdf" data-cy="PDFBtn" Class="but-yellow mr-1"><Icon Class="arrow-bottom" data-cy="PDFBtnIcon" /> PDF</Button>*@
        <Button Loading="@ExportCsvLoading" Clicked="Download" data-cy="CSVBtn" Class="but-yellow"><Icon class="arrow-bottom" data-cy="CSVBtnIcon" /> CSV</Button>
        @if (EnablePhase2Features)
        {
            <AuthorizeView>
                <Authorized>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem href="@(VersionId != 0 ? $"/countries/{CountryCode}/programmes-and-actions/{ProgrammeRevisionDetail.Id}/{ProgrammeRevisionDetail.VersionId}/edit" : $"/countries/{CountryCode}/programmes-and-actions/{ProgrammeRevisionDetail.Id}/edit")">
                                Edit
                            </DropdownItem>
                            @if (EnablePhase2Features)
                            {
                                <DropdownItem href="@($"/countries/{CountryCode}/programmes-and-actions/{ProgrammeAndActionCode}/moderate")">
                                    Moderate
                                </DropdownItem>
                            }
                        </DropdownMenu>
                    </Dropdown>
                </Authorized>
            </AuthorizeView>
        }
    </Div>
</Div>

<Heading Class="Headingtab alltab_D_h3" Size="HeadingSize.Is3" data-cy="Programmes&ActionsHeading"> Programme / action @ProgramAndActionTitle</Heading>
<Divider />
<Div>
    <Layout Sider Class="search-box pt-2 pb-5 mob-layout">

        <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel extractshed">

                @{
                    string Location = string.IsNullOrEmpty(ProgrammeRevisionDetail?.Location) ? "-" : ProgrammeRevisionDetail?.Location;
                    string Description = string.IsNullOrEmpty(ProgrammeRevisionDetail?.BriefDescription) ? "-" : ProgrammeRevisionDetail?.BriefDescription;
                    string Reference = string.IsNullOrEmpty(ProgrammeRevisionDetail?.References) ? "-" : ProgrammeRevisionDetail?.References;
                }
                <div class="_BGDetail _ProgrammeAndActionDetail" id="programmebgdetailhidden">
                    @if (!string.IsNullOrEmpty(ProgrammeRevisionDetail?.BriefDescription))
                    {

                        <Heading data-cy="ProgrammeDescription" Size="HeadingSize.Is3" Class="p-0">Programme description </Heading>
                        <Paragraph data-cy="ParagraphDescription" Class="_pl-4" Margin="Margin.Is1.FromBottom">@((MarkupString)Description.GetLink())</Paragraph>

                    }
                    @if (!string.IsNullOrEmpty(ProgrammeRevisionDetail?.ProgramType?.Name))
                    {
                        <Heading data-cy="ProgrammeTypeHeading" Size="HeadingSize.Is3" Class="p-0">Programme type </Heading>
                        <Paragraph data-cy="ProgrammeTypeParagraph" Class="_pl-4" Margin="Margin.Is1.FromBottom">@(!string.IsNullOrEmpty(ProgrammeRevisionDetail?.ProgramType?.Name) ? @ProgrammeRevisionDetail?.ProgramType?.Name : "-")</Paragraph>
                    }
                    @if (!string.IsNullOrEmpty(ProgrammeRevisionDetail?.Cost))
                    {
                        <Heading data-cy="ProgrammeDescription" Size="HeadingSize.Is3" Class="p-0">Cost</Heading>
                        <Paragraph data-cy="ParagraphDescription" Class="_pl-4" Margin="Margin.Is1.FromBottom">@ProgrammeRevisionDetail?.Cost</Paragraph>
                    }
                    @if (!string.IsNullOrEmpty(ProgrammeRevisionDetail?.References))
                    {
                        <Heading data-cy="ProgrammeDescription" Size="HeadingSize.Is3" Class="p-0">References</Heading>
                        <Paragraph data-cy="ParagraphDescription" Class="_pl-4" Margin="Margin.Is1.FromBottom">@((MarkupString)Reference.GetLink())</Paragraph>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.NewTopic))
                    {
                        <Heading data-cy="Topic" Size="HeadingSize.Is3" Class="p-0">New topic </Heading>
                        <Paragraph data-cy="ActionDetailPargraph3" Class="_pl-4" Margin="Margin.Is1.FromBottom">@ActionDetail.NewTopic</Paragraph>
                    }


                </div>
                <ListGroup Class="list-ta _programdet pb-4 w-100">
                    @if (!string.IsNullOrEmpty(ActionDetail.StatusId))
                    {
                        <ListGroupItem>
                            <span data-cy="StartDate" class="titelspan">Status:</span>
                            <span data-cy="ActionDetailPargrapgh" class="paragr">@ActionDetail.StatusId</span>
                        </ListGroupItem>
                    }
                    @if (ActionDetail.StartYear.HasValue || ActionDetail.StartMonth.HasValue)
                    {
                        <ListGroupItem>
                            <span data-cy="StartDate" class="titelspan">Start date: </span>
                            <span data-cy="ActionDetailPargrapgh" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(ActionDetail.StartMonth, ActionDetail.StartYear)</span>
                        </ListGroupItem>
                    }

                    @if (ActionDetail.EndMonth.HasValue || ActionDetail.EndYear.HasValue)
                    {
                        <ListGroupItem>
                            <span data-cy="EndDate" class="titelspan">End date: </span>
                            <span data-cy="ActionDetailPargraph2" class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(ActionDetail.EndMonth, ActionDetail.EndYear)</span>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(ActionDetail.Place))
                    {
                        <ListGroupItem>
                            <span data-cy="Place" class="titelspan">Place: </span>
                            <span data-cy="PlacePara" class="paragr">@ActionDetail.Place</span>
                        </ListGroupItem>
                    }
                    @if (ProgrammeAndActionDetail.ActionAreaRevision != null && ProgrammeAndActionDetail.ActionAreaRevision.Count > 0)
                    {
                        <ListGroupItem>
                            <span data-cy="Area" class="titelspan">Area: </span>
                            <span data-cy="AreaPara" class="paragr">
                                <Repeater Items="@ProgrammeAndActionDetail.ActionAreaRevision">
                                    <Div Class="_program_title">
                                        <Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Area.Name</Span>
                                    </Div>
                                </Repeater>
                            </span>
                        </ListGroupItem>
                    }
                    @if (ProgrammeAndActionDetail.ActionTargetGroupRevision != null && ProgrammeAndActionDetail.ActionTargetGroupRevision.Count > 0)
                    {
                        <ListGroupItem>
                            <span data-cy="GTarget" class="titelspan">Target group: </span>
                            <span data-cy="GTargetPlace" class="paragr">
                                <Repeater Items="@ProgrammeAndActionDetail.ActionTargetGroupRevision.OrderBy(t => t.TargetGroup.DragAndDropKey)">
                                    <Div Class="_program_title">
                                        <Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.TargetGroup.Name</Span>
                                    </Div>
                                </Repeater>
                            </span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.AgeGroup))
                    {
                        <ListGroupItem>
                            <span data-cy="AgeGroup" class="titelspan">Age group: </span>
                            <span data-cy="AgeGroupD" class="paragr">@ActionDetail.AgeGroup</span>
                        </ListGroupItem>
                    }
                    @if (ProgrammeAndActionDetail.ActionDeliveryRevision != null && ProgrammeAndActionDetail.ActionDeliveryRevision.Count > 0)
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Delivery: </span>
                            <span data-cy="PlaceDelivery" class="paragr">
                                <Repeater Items="@ProgrammeAndActionDetail.ActionDeliveryRevision.OrderBy(d => d.Delivery.DragAndDropKey)">
                                    <Div Class="_program_title">
                                        <Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Delivery.Name</Span>
                                    </Div>
                                </Repeater>
                            </span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.OtherDelivery))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Other delivery: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@(!string.IsNullOrEmpty(ActionDetail.OtherDelivery) ? @ActionDetail.OtherDelivery : "-")</span>
                        </ListGroupItem>
                    }

                    @{
                        string ImplementationDetails = string.IsNullOrEmpty(ActionDetail.ImplementationDetails) ? "-" : ActionDetail.ImplementationDetails;
                        string Indicatior = string.IsNullOrEmpty(ActionDetail.ImpactIndicators) ? "-" : ActionDetail.ImpactIndicators;
                        string System = string.IsNullOrEmpty(ActionDetail.MeSystem) ? "-" : ActionDetail.MeSystem;
                    }
                    @{
                        string Baseline = string.IsNullOrEmpty(ActionDetail.Baseline) ? "-" : ActionDetail.Baseline;
                        string PostIntervention = string.IsNullOrEmpty(ActionDetail.PostIntervention) ? "-" : ActionDetail.PostIntervention;
                        string SocialOther = string.IsNullOrEmpty(ActionDetail.SocialOther) ? "-" : ActionDetail.SocialOther;
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.ImplementationDetails))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Implementation details: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)ImplementationDetails.GetLink())</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.TargetPopulation))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Target population size: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@ActionDetail.TargetPopulation</span>
                        </ListGroupItem>
                    }

                    @if (!string.IsNullOrEmpty(ActionDetail.CoveragePercent))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Coverage level (%): </span>
                            <span data-cy="PlaceDelivery" class="paragr">@ActionDetail.CoveragePercent</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.CoverageTypeId))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Converage type: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@ActionDetail.CoverageTypeId</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.ImpactIndicators))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Outcome indicator(s): </span>
                            <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)Indicatior.GetLink())</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.MeSystem))
                    {

                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">M&E system </span>
                            <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)System.GetLink())</span>
                        </ListGroupItem>

                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.Baseline))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Baseline: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)Baseline.GetLink())</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(ActionDetail.PostIntervention))
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Post intervention: </span>
                            <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)PostIntervention.GetLink())</span>
                        </ListGroupItem>
                    }
                    @if (actionSocialDeterminants != null && actionSocialDeterminants.Count > 0)
                    {
                        <ListGroupItem>
                            <span data-cy="Delivery" class="titelspan">Outcome reported by social determinants: </span>
                            <span data-cy="PlaceDelivery" class="paragr">
                                <Repeater Items="@actionSocialDeterminants">
                                    <Div Class="_program_title">
                                        <a><Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.SocialDeterminant</Span> </a>
                                    </Div>

                                </Repeater>
                            </span>
                        </ListGroupItem>

                        @if (!string.IsNullOrEmpty(ActionDetail.SocialOther))
                        {
                            <ListGroupItem>
                                <span data-cy="Delivery" class="titelspan">Please specify: </span>
                                <span data-cy="PlaceDelivery" class="paragr">@((MarkupString)SocialOther)</span>
                            </ListGroupItem>
                        }
                    }
                </ListGroup>
                @{
                    string OtherLesson = string.IsNullOrEmpty(ActionDetail.OtherLessons) ? "-" : ActionDetail.OtherLessons;
                    string PersonalStory = string.IsNullOrEmpty(ActionDetail.PersonalStory) ? "-" : ActionDetail.PersonalStory;
                }
                @if (!string.IsNullOrEmpty(ActionDetail.OtherLessons))
                {
                    <Heading data-cy="OtherLessons" Size="HeadingSize.Is3">Other lessons learnt </Heading>
                    <Paragraph data-cy="OtherLessonsPara" Margin="Margin.Is1.FromBottom">@((MarkupString)OtherLesson.GetLink())</Paragraph>
                }

                @if (!string.IsNullOrEmpty(ActionDetail.PersonalStory))
                {
                    <Heading data-cy="PersonalStory" Size="HeadingSize.Is3">Personal story </Heading>
                    <Paragraph data-cy="PersonalStoryPara" Margin="Margin.Is1.FromBottom">@((MarkupString)PersonalStory.GetLink())</Paragraph>
                }

                @*@if (!string.IsNullOrEmpty(ActionDetail.MicronutrientCompound))
                {
                <Heading data-cy="MicroNutrient" Size="HeadingSize.Is3">Micro Nutrient </Heading>
                <Paragraph Margin="Margin.Is1.FromBottom">@(!string.IsNullOrEmpty(ActionDetail.MicronutrientCompound) ? @ActionDetail.MicronutrientCompound : "-")</Paragraph>
                }*@


                @if (!string.IsNullOrEmpty(ActionDetail.OtherProblems))
                {
                    <Heading data-cy="OtherProblems" Size="HeadingSize.Is3">Other problems </Heading>
                    <Paragraph data-cy="OtherProblemsPara" Margin="Margin.Is1.FromBottom">@ActionDetail.OtherProblems</Paragraph>
                }
                @if (ProgrammeAndActionDetail.ActionProblemRevision != null && ProgrammeAndActionDetail.ActionProblemRevision.Any())
                {
                    <Layout Class="search-box pt-3 pb-3 mob-layout">
                        <Layout Class="left-layout DataGrids ">
                            <LayoutContent>
                                <DataGrid FixedHeaderDataGridMaxHeight="500px"
                                          FixedHeaderDataGridHeight="450px"
                                          TItem="@ActionProblemRevision"
                                          Data="@ProgrammeAndActionDetail.ActionProblemRevision">
                                    <EmptyTemplate>
                                        <Div> No data found </Div>
                                    </EmptyTemplate>
                                    <DataGridColumns>
                                        <DataGridColumn Caption="Typical problems" Field="@nameof(ActionProblemRevision.ProblemType)" Width="130px">
                                            <DisplayTemplate>
                                                @context.ProblemType.Name
                                            </DisplayTemplate>
                                        </DataGridColumn>
                                        <DataGridColumn Caption="Solutions" Width="130px" Field="@nameof(ActionProblemRevision.ProblemNumber)" Context="Solution">
                                            <DisplayTemplate>
                                                @{
                                                    int value = Solution.ProblemNumber;
                                                }
                                                @switch (value)
                                                {
                                                    case 0:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution0)</Paragraph>
                                                        break;
                                                    case 1:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution1)</Paragraph>
                                                        break;
                                                    case 2:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution2)</Paragraph>
                                                        break;
                                                    case 3:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution3)</Paragraph>
                                                        break;
                                                    case 4:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution4)</Paragraph>
                                                        break;
                                                    case 5:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution5)</Paragraph>
                                                        break;
                                                    case 6:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution6)</Paragraph>
                                                        break;
                                                    case 7:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution7)</Paragraph>
                                                        break;
                                                    case 8:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution8)</Paragraph>
                                                        break;
                                                    case 9:
                                                        <Paragraph Margin="Margin.Is1.FromBottom">@((MarkupString)ActionDetail.Solution9)</Paragraph>
                                                        break;
                                                }
                                            </DisplayTemplate>
                                        </DataGridColumn>
                                    </DataGridColumns>
                                </DataGrid>
                            </LayoutContent>
                        </Layout>
                    </Layout>
                }
                <AuthorizeView Roles="Admin">
                    <Div>
                        <Accordion Class="accor-fbox">
                            <Collapse Visible="@revision">
                                <CollapseHeader>
                                    <Heading Class="head-but mt-4 m-0 d-flex align-items-center" Size="HeadingSize.Is4">
                                        <Span Class="revisiontitle">Revision log</Span> 
                                        <Button Clicked="@(()=>revision = !revision)"></Button>
                                    </Heading>
                                </CollapseHeader>
                                <CollapseBody Class="tab-0">
                                    <DataGrid Class="table-nth _actions"
                                              TItem="@ProgramLog"
                                              Data="@programLogInfo"
                                              PageSize="5"
                                              ShowPageSizes
                                              ShowPager
                                              Responsive="false"
                                              SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <Div>No data found.</Div>
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            <DataGridColumn Caption="Date" Field="@nameof(ProgramLog.RevisedDate)" TextAlignment="TextAlignment.Start"
                                                            Width="22%">
                                                <DisplayTemplate Context="displayContext">
                                                    @displayContext.RevisedDate.GetDayWithFormatedDate()
                                                </DisplayTemplate>
                                            </DataGridColumn>

                                            <DataGridColumn Field="@nameof(ProgramLog.UserName)" Caption="User" Width="15%" />
                                            <DataGridColumn Field="@nameof(ProgramLog.OtherNotes)" Caption="Log" Width="20%" />
                                            <DataGridColumn Field="@nameof(ProgramLog.ToState)" Caption="State" Width="15%" />
                                        </DataGridColumns>
                                    </DataGrid>
                                </CollapseBody>
                            </Collapse>
                        </Accordion>
                    </Div>
                </AuthorizeView>
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>
                <Div Class="accordion-Search" id="accordionExample">
                    <div class="accordion">
                        <button data-cy="CountryBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            Country(ies):
                        </button>
                        <div id="collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body _siderdetail_tab">
                                <Div Class="downl-flex cunt-box _flex_countr">
                                    <Repeater Items="@ProgrammeRevisionDetail?.ProgrammeCountryMapRevision.OrderBy(c => c.Country.Name)">
                                        <a target="_blank" href="@($"countries/{context.CountryCode}/programmes-and-actions")">@context.Country.Name</a>
                                    </Repeater>
                                </Div>
                            </div>
                        </div>
                        @if (PartnerItems.Any())
                        {
                            <button data-cy="PartnerInPolicyImplementationBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                Implementing organizations/groups
                            </button>
                            <div id="collapse2" class="accordion-collapse collapse show">
                                <div class="accordion-body p-0 _padding_t-b8">
                                    <Bar Class="Verticalbar _itemalinebar" Mode="BarMode.VerticalInline"
                                         CollapseMode="BarCollapseMode.Small">
                                        <BarMenu>
                                            <BarStart>
                                                <Repeater Items="@PartnerItems" Context="partner">
                                                    <BarItem>
                                                        <BarDropdown>
                                                            <BarDropdownToggle>
                                                                @partner.PartnerName
                                                            </BarDropdownToggle>
                                                            <BarDropdownMenu>
                                                                <Repeater Items="partner.PartnerData.OrderBy(p => p.DragAndDropKey)" Context="partnerdetail">
                                                                    <BarDropdownItem>@partnerdetail.Name</BarDropdownItem>
                                                                </Repeater>

                                                                @if (!string.IsNullOrEmpty(partner.PartnerDetails))
                                                                {
                                                                    <BarDropdown>
                                                                        <BarItem>
                                                                            <BarDropdown Class="_PartnerDetails">
                                                                                <BarDropdownToggle>More details</BarDropdownToggle>
                                                                                <BarDropdownMenu>
                                                                                    <BarDropdownItem>@((MarkupString)partner.PartnerDetails)</BarDropdownItem>
                                                                                </BarDropdownMenu>
                                                                            </BarDropdown>
                                                                        </BarItem>
                                                                    </BarDropdown>
                                                                }

                                                                <BarDropdown>
                                                                </BarDropdown>
                                                            </BarDropdownMenu>
                                                        </BarDropdown>
                                                    </BarItem>
                                                </Repeater>


                                            </BarStart>
                                        </BarMenu>
                                    </Bar>
                                </div>
                            </div>
                        }

                        @if (programFundingResourceRevisions.Any())
                        {
                            <button data-cy="PartnerInPolicyImplementationBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                Funding sources
                            </button>
                            <div id="collapse2" class="accordion-collapse collapse show">
                                <div class="accordion-body p-0 _padding_t-b8">
                                    <Bar Class="Verticalbar _itemalinebar" Mode="BarMode.VerticalInline"
                                         CollapseMode="BarCollapseMode.Small">
                                        <BarMenu>
                                            <BarStart>
                                                <Repeater Items="@programFundingResourceRevisions" Context="Funding">
                                                    <BarItem>
                                                        <BarDropdown>
                                                            <BarDropdownToggle>
                                                                @Funding.FundingResourcePartnerCategory.Name
                                                            </BarDropdownToggle>
                                                            <BarDropdownMenu>
                                                                <BarDropdownItem>@Funding.FundingResourcePartner.Name</BarDropdownItem>

                                                                @*<Repeater Items="@Funding.FundingResourcePartner Context="partnerdetail">
                                                                    <BarDropdownItem>@partnerdetail.Name</BarDropdownItem>
                                                                </Repeater>*@
                                                                @if (Funding.FundingResourcePartnerCategory.ProgramFundingResourceDetailRevisions.Any(p => p.ProgramVId.Equals(Funding.ProgramVId)))
                                                                {
                                                                    string detail = Funding.FundingResourcePartnerCategory.ProgramFundingResourceDetailRevisions.FirstOrDefault(p => p.ProgramVId.Equals(Funding.ProgramVId)).Details;
                                                                    <BarDropdown>
                                                                        <BarItem>
                                                                            <BarDropdown Class="_PartnerDetails">
                                                                                <BarDropdownToggle>More details</BarDropdownToggle>
                                                                                <BarDropdownMenu>
                                                                                    <BarDropdownItem>@((MarkupString)detail)</BarDropdownItem>
                                                                                </BarDropdownMenu>
                                                                            </BarDropdown>
                                                                        </BarItem>
                                                                    </BarDropdown>
                                                                }

                                                                <BarDropdown>
                                                                </BarDropdown>
                                                            </BarDropdownMenu>
                                                        </BarDropdown>
                                                    </BarItem>
                                                </Repeater>
                                            </BarStart>
                                        </BarMenu>
                                    </Bar>
                                </div>
                            </div>
                        }

                        @if (otherActions != null && otherActions.Count > 0)
                        {
                            <button class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                Other actions from same programme
                            </button>
                            <div id="collapse3" class="accordion-collapse collapse show">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="@otherActions">
                                        <Div Class="_program_title">
                                            <a href="@(VersionId == 0 ? $"/countries/{CountryCode}/programmes-and-actions/{context.ActionId}" : $"/countries/{CountryCode}/programmes-and-actions/{context.ActionId}/{context.VersionId}")" target="_blank">
                                                <span>&#x2022;</span>&nbsp;&nbsp;&nbsp;<span>@context.Title</span>
                                            </a>
                                        </Div>
                                    </Repeater>
                                </div>
                            </div>
                        }
                        @if (ProgrammeRevisionDetail != null && ProgrammeRevisionDetail.ProgramPolicyRevision.Count > 0)
                        {
                            <button data-cy="LinkToPoliciesBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                Policy links
                            </button>
                            <div id="collapse3" class="accordion-collapse collapse show">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="@ProgrammeRevisionDetail.ProgramPolicyRevision">
                                        <Div Class="_program_title">
                                            <a target="_blank" href="@($"/policies/{context.Policy.Id}")"><Span>&#x2022;</Span>&nbsp;&nbsp;&nbsp;<Span>@context.Policy.CombinedTitle</Span> </a>
                                        </Div>
                                    </Repeater>
                                </div>
                            </div>
                        }


                        @if (ProgrammeAndActionDetail.ActionElenaRevision != null && ProgrammeAndActionDetail.ActionElenaRevision.Count > 0)
                        {
                            <button data-cy="ElenaLinks" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                eLENA Link
                            </button>
                            <div id="collapse4" class="accordion-collapse collapse show">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="@ProgrammeAndActionDetail.ActionElenaRevision" Context="elenaLink">
                                        <Repeater Items="@elenaLink.ElenaLink.ElenaURLMapping" Context="elenaURlMap">
                                            <Div Class="_program_title">
                                                <a target="_blank" href="@elenaURlMap.URL">
                                                    <Span>&#x2022;</Span>
                                                    &nbsp;&nbsp;&nbsp;<Span>@elenaURlMap.URLTitle</Span>
                                                </a>
                                            </Div>
                                        </Repeater>
                                    </Repeater>
                                </div>
                            </div>
                        }
                    </div>
                </Div>
            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Div>
