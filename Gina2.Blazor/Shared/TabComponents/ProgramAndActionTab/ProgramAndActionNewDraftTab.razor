﻿<Loader IsLoading="@IsLoading" />

<Modal @bind-Visible="@modalVisible" Class="modals-lg">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Add Action</ModalTitle>
            <CloseButton/>
        </ModalHeader>
        <ModalBody>
            <Field>
                <FieldLabel>Theme</FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@TypeOfPolicyData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                    <FieldHelp>Read more about Programs and actions document types used in GIFNA.</FieldHelp>
                </Select>
            </Field>
            <Fields>
                <FieldLabel>Topics <Span>*</Span></FieldLabel>
                <Field Class="m-0" ColumnSize="ColumnSize.Is10.OnTablet.Is12.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@TypeOfPolicyData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                    <FieldHelp>Read more about Programs and actions document types used in GIFNA.</FieldHelp>
                </Select>
            </Field>
            <Field Class="m-0" ColumnSize="ColumnSize.Is2.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD"> 
                <Button Class="but-yellow w-100" > Add </Button>
            </Field>
            </Fields>
            <Column Class="w-100 Topicspolicy m-0 p-0">
             <GinaTreeView />
             </Column>
             <Fields>
                <Field Class="pt-3" ColumnSize="ColumnSize.Is8.OnTablet.Is12.OnMobile.Is8.OnDesktop.Is8.OnWidescreen.Is8.OnFullHD">
                    <FieldLabel>Target group</FieldLabel>
                    <Select TValue="int" Class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                    </Select>
                    <FieldHelp>Select one or more</FieldHelp>
                </Field>
                <Field Class="pt-3" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                    <FieldLabel>Age group</FieldLabel>
                    <TextEdit/>
                    <FieldHelp>Please specify if months or years</FieldHelp>
                </Field>
            </Fields>
            <Field >
                <FieldLabel>Place</FieldLabel>
                <TextEdit/>
                <FieldHelp>Province, district, village</FieldHelp>
            </Field>

            <Fields>
                <FieldLabel>eLENA link(s)</FieldLabel>
                <Field Class="m-0" ColumnSize="ColumnSize.Is10.OnTablet.Is12.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                <TextEdit Placeholder="Add new eLENA link"/>
            </Field>
            <Field Class="m-0" ColumnSize="ColumnSize.Is2.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD"> 
                <Button Class="but-yellow w-100" > Add </Button>
            </Field>
            </Fields>
        <Field>
                @{
                int Index = -1;
                }
                    <Accordion Class="Elenalink">
                        <Dropzone Items="Elenalink">
    <Collapse Class="draggabls" draggable="true" Visible="@context.IsDone">
                            @{
                                Index++;
                                string activeclass = $"ElenaActive{Index}";
                            }
        <CollapseHeader id="@activeclass" Flex="Flex.JustifyContent.Between">
            <Div Class="drag-1">
                <Icon Class="fa-solid fa-grip-vertical" />
                
                <Icon Clicked="@(() => AcvectElenalink(context, activeclass))" Class="fa-solid fa-pen" />
                @context.Title
            </Div>
            <Div Class="drag-2">
                <Icon Class="fa-solid fa-trash" />
            </Div>
        </CollapseHeader>
        <CollapseBody>
            <Fields>
                 <Field>
                    <FieldLabel>Link name <Span>*</Span></FieldLabel>
                    <TextEdit Placeholder="Add name"/>
                </Field>
                <Field>
                    <FieldLabel>Link title <Span>*</Span></FieldLabel>
                    <TextEdit Placeholder="Add title"/>
                </Field>
            </Fields>
            <Field>
                <FieldLabel>Link URL <Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Add new link"/>
            </Field>
        </CollapseBody>
    </Collapse>
    </Dropzone>
</Accordion>
    
        </Field>
            <Fields Class="pt-3">
                <FieldLabel>Area</FieldLabel>
                <Field Class="m-0">
                    <Check TValue="bool"> Urban</Check>
                </Field>
                <Field Class="m-0">
                    <Check TValue="bool"> Rural</Check>
                </Field>
                <Field Class="m-0">
                    <Check TValue="bool"> Peri-urban</Check>
                </Field>
            </Fields>
            <Field Class="pt-3">
                <FieldLabel>Status</FieldLabel>
                <Select TValue="int" Class="pl-1 pr-3">
                    <Repeater Items="@Countries">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            <Fields>
                <Field>
                    <FieldLabel>Start date</FieldLabel>
                    <GinaDate />
                </Field>
                <Field>
                    <FieldLabel>End date</FieldLabel>
                    <GinaDate />
                </Field>
            </Fields>
            <Field>
                <FieldLabel>Delivery</FieldLabel>
                    <Select TValue="int" Class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                    </Select>
                    <FieldHelp>Select one or more</FieldHelp>
            </Field>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Implementation details</FieldLabel>
                    <FieldHelp>(dose & frequency, number of staff needed)</FieldHelp>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
                    <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Outcome indicator(s)</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
                    <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>M&E system</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
             <Field>
                <FieldLabel>Target population size</FieldLabel>
                <TextEdit></TextEdit>
                <FieldHelp>Planned coverage in numbers and %</FieldHelp>
            </Field>
            <Field>
                <FieldLabel>Coverage level (%)</FieldLabel>
                <Select TValue="int" Class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                    </Select>
                    <FieldHelp>Please fill in the actual coverage</FieldHelp>
            </Field>

            <Fields >
                <FieldLabel>Coverage Type</FieldLabel>
                <Field Class="m-0">
                    <Check TValue="bool"> N/A</Check>
                </Field>
                <Field Class="m-0">
                    <Check TValue="bool"> Point</Check>
                </Field>
                <Field Class="m-0">
                    <Check TValue="bool"> Period</Check>
                </Field>
            </Fields>
            
            <Fields>
            <Field Class="pt-3">
                <FieldLabel>Baseline</FieldLabel>
                        <RextEditors />
                    </Field>
                    </Fields>
                    <Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Post-intervention</FieldLabel>
                    <FieldHelp>of impact indicator(s)</FieldHelp>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
                    </Fields>
                    <Fields>
            <Field>
                <FieldLabel>Outcome reported by social determinants</FieldLabel>
                        <RextEditors />
                        <FieldHelp>To make multiple selections, press "CTRL" and click on items to choose.</FieldHelp>
                    </Field>
                    </Fields>

             <Fields>
                <Field Class="m-0" ColumnSize="ColumnSize.Is10.OnTablet.Is12.OnMobile.Is10.OnDesktop.Is10.OnWidescreen.Is10.OnFullHD">
                <FieldLabel>Problems and solutions</FieldLabel>
            </Field>
            <Field Class="m-0" ColumnSize="ColumnSize.Is2.OnTablet.Is12.OnMobile.Is2.OnDesktop.Is2.OnWidescreen.Is2.OnFullHD"> 
                <Button Class="but-yellow w-100" > Add </Button>
            </Field>
            </Fields>
             @{
                int Index = -1;
                }
                    <Accordion Class="Elenalink">
                        <Dropzone Items="Elenalink">
    <Collapse Class="draggabls" draggable="true" Visible="@context.IsDone">
                            @{
                                Index++;
                                string activeclass = $"ElenaActive{Index}";
                            }
        <CollapseHeader id="@activeclass" Flex="Flex.JustifyContent.Between">
            <Div Class="drag-1">
                <Icon Class="fa-solid fa-grip-vertical" />
                
                <Icon Clicked="@(() => AcvectElenalink(context, activeclass))" Class="fa-solid fa-pen" />
                @context.Title
            </Div>
            <Div Class="drag-2">
                <Icon Class="fa-solid fa-trash" />
            </Div>
        </CollapseHeader>
        <CollapseBody>
                <Field>
                    <FieldLabel>Typical problems</FieldLabel>
                    <TextEdit Placeholder="Add name"/>
                    <FieldHelp>Select one or more</FieldHelp>
                </Field>
            <Field>
                <FieldLabel>Solutions</FieldLabel>
                <RextEditors />
                <FieldHelp>Idea to solve each problem - did it work or why not</FieldHelp>
            </Field>
        </CollapseBody>
    </Collapse>
    </Dropzone>
</Accordion>


            <Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Other lessons learnt</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
                    </Fields>
<Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Personal story</FieldLabel>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
                        <RextEditors />
                    </Field>
                    </Fields>
        </ModalBody>
        <ModalFooter>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@HideModal">Save</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @bind-Visible="@LocationVisible" Class="modals-lg">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Map Location</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            <Fields>
            <Field>
                <FieldLabel>Location name</FieldLabel>
                <TextEdit Placeholder="Enter Location..." />
            </Field>
            <Field>
                <FieldLabel>Last Title</FieldLabel>
                <TextEdit Placeholder="Enter name..." />
            </Field>
            </Fields>
            <Field>
                <FieldLabel>URL</FieldLabel>
                <TextEdit Placeholder="Please provide the redirection link" />
            </Field>
        </ModalBody>
        <ModalFooter>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@HideLocation">Save</Button>
        </ModalFooter>
    </ModalContent>
</Modal>


<Container>
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
        <Div Class="item1 flex-b">
            <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/programmes-and-actions"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to Programs and actions
            </Button>
            @*<Div><Button Class="back"><Icon Class="fas fa-chevron-left" Clicked=@(() => OnChangingTab.InvokeAsync(("Policy", PolicyCode)))></Icon></Button></Div> <Heading Size="HeadingSize.Is3">New Draft Policy</Heading>*@

        </Div>
        <Div Class="item2">
            <Button Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
            <AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot">
            <DropdownToggle Color="Color.Primary" Split />
            <DropdownMenu>
                <DropdownItem href="@($"/programmes-and-actions/{ProgrammeAndActionCode}")">View published</DropdownItem>
                            <DropdownItem href="@($"/programmes-and-actions/{ProgrammeAndActionCode}/moderate")">Moderate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
        </Div>
    </Div>
    @*<Breadcrumb Class="bread-crumb pl-2">
        <BreadcrumbItem>
            <BreadcrumbLink To="#">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
            <BreadcrumbLink To="policies">New Policy</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem Active>
            <BreadcrumbLink To="#">New Policy</BreadcrumbLink>
        </BreadcrumbItem>
    </Breadcrumb>*@
     </Container>  
<Container Class="newdraft pl-2 pr-2">
    <Container Padding="Padding.Is0" Class="pt-4 mobi-heing">
        <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new programme / actions</Heading>
        <Divider Class="divi-blue" />
    </Container>

    <Container Class="form-newd">
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Title<Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Enter Title Here"></TextEdit>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Programme type <Span>*</Span></FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@TypeOfPolicyData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                    <FieldHelp>Read more about Programs and actions document types used in GIFNA.</FieldHelp>
                </Select>
            </Field>
        </Fields>
        <Fields>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Language</FieldLabel>
                <Select TValue="int">
                    <Repeater Items="@LanguageData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                <FieldLabel>Country(ies) <Span>*</Span></FieldLabel>
            <Select TValue="int">
                        <Repeater Items="@Countries">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                    </Select>
                    </Field>
        </Fields>

        <Div Flex="Flex.JustifyContent.Between" Class="pt-4">
                    <Div Class="item1">
                        <Heading Class="blo-head" Size="HeadingSize.Is3">Map Location</Heading>
                    </Div>
                    <Div Class="item2">
         <Button Class="but-yellow pl-2 pr-2"><Icon Class="far fa-plus" /> Add Country</Button>
         @*<Button Class="but-info"><Tooltip Text="Add Country"><Icon Name="IconName.QuestionCircle" /></Tooltip></Button>*@
                    </Div>
                </Div>

         <Dropzone Items="MyFirstList">
        <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
            <Div Class="drag-1">
                <Icon Class="fa-solid fa-grip-vertical" />
                 <Icon Clicked="@Location" Class="fa-solid fa-pen" />
                @context.Title
            </Div>
            <Div Class="drag-2">
                <Icon Class="fa-solid fa-trash" />
            </Div>
            
        </Div>
    </Dropzone>
    </Container>

    <Container Class="form-newd mt-4">

        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>Brief description</FieldLabel>
                    <FieldHelp>Describe programme/project</FieldHelp>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>

            <RextEditors />
        </Field>
        <Field>
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <FieldLabel>References</FieldLabel>
                    <FieldHelp>Enter any published information, grey literature and/or weblinks related to the programme/project</FieldHelp>
                </Div>
                <Div Class="item2">
                    <Select TValue="int">
                        <SelectItem Value="0">Filtered HTML</SelectItem>
                        <SelectItem Value="1">Filtered 1</SelectItem>
                        <SelectItem Value="2">Filtered 2</SelectItem>
                    </Select>
                    <Button Class="but-info"><Icon Name="IconName.QuestionCircle" /></Button>
                </Div>
            </Div>
            <RextEditors />
        </Field>
        
    </Container>

    <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>Programme related to existing policy</FieldLabel>
        </Field>
        <Divider />
        
<Addons Class="pb-3">
                        <Addon AddonType="AddonType.Start">
                            <Button Class="but-blue pl-2 pr-2">
                                <Icon Class="far fa-plus" /> Add
                            </Button>
                        </Addon>
                        <Addon AddonType="AddonType.Body">
                            <TextEdit Placeholder="Provide title of new policy not yet listed..." />
                        </Addon>
                    </Addons>
                    <ListGroup>
                            <Repeater Items="@ItemData">
                                <ListGroupItem>@context</ListGroupItem>
                            </Repeater>
                     </ListGroup>
          </Container>

    <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>Partners Involved</FieldLabel>
        </Field>
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Government</Tab>
                <Tab Name="Tab2">Bilateral and d...</Tab>
                <Tab Name="Tab3">UN agencies</Tab>
                <Tab Name="Tab4">International N...</Tab>
                <Tab Name="Tab5">Intergovernm...</Tab>
                <Tab Name="Tab6">National NG...</Tab>
                <Tab Name="Tab7">Research / A...</Tab>
                <Tab Name="Tab8">Private Sector</Tab>
                <Tab Name="Tab9">Other</Tab>
            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the government sector(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab2">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate bilateral agencies and donors involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab3">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the UN agencies involved in implementation of the policy</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab4">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab5">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab6">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab7">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate Research / Academia involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab8">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the private sectors involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab9">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate any other information necessary in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <Field>
                        <FieldLabel>Cost</FieldLabel>
                        <RextEditors/>
                        <FieldHelp>Specify currency, purpose (e.g. human resources, logistics) and for which action</FieldHelp>
                    </Field>
            </Content>
        </Tabs>
    </Container>

    <Container Class="form-newd mt-4">
        <Field>
            <FieldLabel>Funding sources</FieldLabel>
        </Field>
        <Tabs SelectedTab="@selectedTab" Class="gina-tab" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab Name="Tab1">Government</Tab>
                <Tab Name="Tab2">Bilateral and d...</Tab>
                <Tab Name="Tab3">UN agencies</Tab>
                <Tab Name="Tab4">International N...</Tab>
                <Tab Name="Tab6">National NG...</Tab>
                <Tab Name="Tab7">Research / A...</Tab>
                <Tab Name="Tab8">Private Sector</Tab>
                <Tab Name="Tab9">Other</Tab>
            </Items>
            <Content>
                <TabPanel Name="Tab1">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the government sector(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab2">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate bilateral agencies and donors involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab3">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the UN agencies involved in implementation of the policy</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab4">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab6">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate international NGO(s) involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab7">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate Research / Academia involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab8">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate the private sectors involved in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
                <TabPanel Name="Tab9">
                    <Heading Size="HeadingSize.Is5" Class="pt-4 pb-1">Please indicate any other information necessary in implementation</Heading>
                    <ListGroup Class="ulgroup">
                        <Repeater Items="@ItemData">
                            <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                        </Repeater>
                    </ListGroup>
                    <Field>
                        <FieldLabel>Government detail(s)</FieldLabel>
                        <RextEditors />
                    </Field>

                </TabPanel>
            </Content>
        </Tabs>

         <Div Flex="Flex.JustifyContent.Between" Class="pt-4">
                    <Div Class="item1">
                        <Heading Class="blo-head" Size="HeadingSize.Is3">Actions</Heading>
                    </Div>
                    <Div Class="item2">
         <Button Class="but-yellow pl-2 pr-2" Clicked="@ShowModal"><Icon Class="far fa-plus" /> Add Action</Button>
                    </Div>
                </Div>
       <Dropzone Items="MyFirstList">
        <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
            <Div Class="drag-1">
                <Icon Class="fa-solid fa-grip-vertical" />
                 <Icon Clicked="@ShowModal" Class="fa-solid fa-pen" />
                @context.Title
            </Div>
            <Div Class="drag-2">
                <Icon Class="fa-solid fa-trash" />
            </Div>
            
        </Div>
    </Dropzone>

        <Field>
            <FieldLabel>Programme id</FieldLabel>
            <TextEdit Placeholder="Enter title here..."></TextEdit>
        </Field>
   
    </Container>
    <Container Class="mt-4 pb-6">
        <Button Class="but-blues" Clicked="@(()=>snackbar.Show())">Save</Button>
        <br />
        <Heading Class="alert-warn mt-3">New content: Your draft will be placed in moderation.</Heading>
        <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
            <SnackbarBody>
                New content: Your draft will be placed in moderation.
            </SnackbarBody>
        </Snackbar>
    </Container>
</Container>
