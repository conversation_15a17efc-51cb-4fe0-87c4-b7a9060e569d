﻿using AutoMapper;
using Domain.Actions;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.Pdf;
using Gina2.Blazor.Pages;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.DbModels.ProgramANDActionRevisions.ActionRevisions;
using Gina2.DbModels.ProgramANDActionRevisions.ProgramRevisions;
using Gina2.MySqlRepository.Models;
using Gina2.Services.Country;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Gina2.Services.Programme;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Utilities;
using System.IO;
using System.Text;

namespace Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab
{
    public partial class ProgramAndActionDetailTab : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }

        [Inject]
        public IMapper _mapper { get; set; }
        [Inject]
        private IProgrammeService ProgrammeAndActionService { get; set; }
        [Inject]
        private IProgramRevisionService ProgramRevisionService { get; set; }
        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }

        [CascadingParameter(Name = "ProgrammeAndActionCode")]
        public int ProgrammeAndActionCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int VersionId { get; set; }
        private string ProgrammeActionTypeName { get; set; }
        private string ProgramAndActionTitle { get; set; }

        private List<OtherActions> otherActions = new List<OtherActions>();

        private ActionRevision ProgrammeAndActionDetail { get; set; } = new ActionRevision();
        private ProgramRevision ProgrammeRevisionDetail { get; set; } = new ProgramRevision();
        private ActionRevision ActionDetail { get; set; } = new ActionRevision();
        private List<PartnerDetail> PartnerItems { get; set; } = new List<PartnerDetail>();
        private IEnumerable<ProgramLog> programLogInfo = Enumerable.Empty<ProgramLog>();
        private List<ActionSocialDeterminant> actionSocialDeterminants = new List<ActionSocialDeterminant>();
        private List<ProgramCategoryPartnerMapItem> ProgramPartnerList { get; set; } = new();
        private bool EnablePhase2Features = false;
        private bool ExportPdfLoading { get; set; }
        private bool ExportCsvLoading { get; set; }
        bool revision = false;

        public class ElenaData
        {
            public string title { get; set; }
            public string url { get; set; }
        }

        List<ElenaData> elenaDatas = new List<ElenaData>();

        protected override void OnInitialized()
        {
            object data = MemoryCache.Get("submit");
            MemoryCache.Remove("submit");
            if (data != null)
            {
                _ = OpenToaster(data.ToString(), "", AntDesign.NotificationType.Success);

            }
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                EnablePhase2Features = Convert.ToBoolean(Configuration["EnablePhase2Features"]);

                
                ProgrammeAndActionDetail = await ProgramRevisionService.GetActionProgramRevisionDetailsAsync(ProgrammeAndActionCode, VersionId);
                if (ProgrammeAndActionDetail == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                if(!string.IsNullOrEmpty(CountryCode))
                {
                    var IsActionelongsToCountryInURL = ProgrammeAndActionDetail.ActionProgramRevisionMap.SelectMany(e=>e.ProgramRevision.ProgrammeCountryMapRevision).FirstOrDefault(c=>c.CountryCode == CountryCode) != null? true: false;
                    if ( !IsActionelongsToCountryInURL)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }
                }
                actionSocialDeterminants = await ProgrammeAndActionService.GetSocialDeterminantAsync(ProgrammeAndActionCode);

                ProgrammeRevisionDetail = ProgrammeAndActionDetail.ActionProgramRevisionMap.FirstOrDefault().ProgramRevision;

                programLogInfo = await ProgramRevisionService.GetLogByProgramId(ProgrammeRevisionDetail.Id);

                ProgramAndActionTitle = await GetProgramAndActionTitle(ProgrammeAndActionDetail);
                ProgrammeRevisionDetail.BriefDescription = ProgrammeRevisionDetail.BriefDescription.GetLink();
                ProgrammeRevisionDetail.References = ProgrammeRevisionDetail.References.GetLink();
                
                var programId = await ProgramRevisionService.GetProgramIdByAction(ProgrammeAndActionCode);
                if (programId.Item1 > 0)
                {
                    var programsActions = await ProgramRevisionService.GetProgramRevisionDetailsAsync(programId.Item1, programId.Item2);
                    if (programsActions != null && programsActions.ActionProgramRevisionMap.Any())
                    {
                        var programsActionsList = programsActions.ActionProgramRevisionMap.Where(a=> !a.ActionId.Equals(ProgrammeAndActionCode));
                        foreach (var item in programsActionsList)
                        {
                            if (item.Id != ProgrammeAndActionCode && item.ActionRevision != null)
                            {
                                OtherActions other = new OtherActions();
                                other.ActionId = item.ActionId;
                                other.VersionId = item.ActionRevision.VersionId;
                                other.Title = await GetOtherActionTitle(item.ActionRevision);
                                otherActions.Add(other);
                            }
                        }
                    }
                }

                ActionDetail = ProgrammeAndActionDetail;
                await GetPartnerAsync();
                await GetFundingResourceAsync();
                await InvokeAsync(StateHasChanged);
                _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "programmebgdetailhidden");
            }
        }

        private async Task<string> GetOtherActionTitle(ActionRevision actionItem)
        {
            StringBuilder title = new StringBuilder();
            
            string actionTopic = actionItem != null && actionItem.Topic != null ? $"{actionItem.Topic.Name}" : string.Empty;
            title.Append($"{actionTopic}");
            foreach (var item in actionItem.ActionTargetGroupRevision)
            {
                if(item.TargetGroup != null)
                    title.Append($" / {item.TargetGroup.Name}");
            }

            return title.ToString();
        }

        private async Task<string> GetProgramAndActionTitle(ActionRevision actionItem)
        {
            StringBuilder title = new StringBuilder();
            string program = actionItem.ActionProgramRevisionMap != null ? $"- {actionItem.ActionProgramRevisionMap.FirstOrDefault().ProgramRevision.Title}" : string.Empty;
            string actionTopic = actionItem.Topic != null ? $"{actionItem.Topic.Name}" : string.Empty;
            title.Append($"{program} / {actionTopic}");
            foreach (var item in actionItem.ActionTargetGroupRevision)
            {
                if(item.TargetGroup != null)
                    title.Append($" - {item.TargetGroup.Name}");
            }

            return title.ToString();
        }

        private async Task GetPartnerAsync()
        {
            ProgramPartnerList = await ProgrammeAndActionService.GetPartnerCategoriesAsync(ProgrammeRevisionDetail.Id);

            foreach (var item in ProgramPartnerList.OrderBy(p => p.PartnerCategory.DragAndDropKey))
            {
                var parent = PartnerItems.Where(x => x.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault();
                if (parent != null)
                {
                    parent.PartnerData.Add(new() { Name = item.Partner.Name, DragAndDropKey = item.Partner.DragAndDropKey });
                }
                else
                {
                    List<PartnerOrder> partner = new List<PartnerOrder>();
                    partner.Add(new() { Name = item.Partner.Name, DragAndDropKey = item.Partner.DragAndDropKey });
                    PartnerItems.Add(new PartnerDetail()
                    {
                        PartnerName = item.PartnerCategory.Name,
                        PartnerCategoryId = item.PartnerCategoryId,
                        PartnerData = partner,
                        PartnerDetails = item.PartnerCategory.ProgramPartnerCategories.Where(w => w.ProgramId == ProgrammeRevisionDetail.Id && w.PartnerCategoryId == item.PartnerCategoryId).FirstOrDefault()?.Details ?? string.Empty
                    });
                }
            }
        }

        List<ProgramFundingResourceRevision> programFundingResourceRevisions = new List<ProgramFundingResourceRevision>();

        private async Task GetFundingResourceAsync()
        {
            programFundingResourceRevisions = await ProgrammeAndActionService.GetFundingCategoriesAsync(ProgrammeAndActionDetail.ProgramId);
            //var details = funding.FirstOrDefault().FundingResourcePartnerCategory.ProgramFundingResourceDetailRevisions.FirstOrDefault(x =>
            //x.ProgramVId == funding.FirstOrDefault().ProgramVId).Details;
            //foreach (var item in ProgramPartnerList)
            //{
            //    var partnerList = item.PartnerCategory.Partners.FirstOrDefault(a => a.PartnerCategoryId == item.PartnerCategoryId);
            //    List<PartnerOrder> partner = new List<PartnerOrder>();
            //    partner.Add(new() { Name = partnerList.Name, DragAndDropKey = partnerList.DragAndDropKey });
            //    PartnerItems.Add(new PartnerDetail()
            //    {
            //        PartnerName = item.PartnerCategory?.Name,
            //        PartnerCategoryId = item.PartnerCategoryId,
            //        PartnerData = partner,
            //        PartnerDetails = item.Details ?? string.Empty
            //    });
            //}
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }
        private async Task Download()
        {
            ExportCsvLoading = true;

            List<int> ActionId = new List<int>
            {
                ProgrammeAndActionCode
            };
            var search = new GlobalSearchRequest() { DownloadByDataItem = true };
            var data = await FileDownloadService.GetActionDataForCsv(search, ActionId);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());
                await JsRuntime.InvokeVoidAsync("saveAsFile", "Programmes-and-actions.csv", fileData);
            }
            ExportCsvLoading = false;
        }

        private async Task DownloadPdf()
        {
            ExportPdfLoading = true;
            var actionDetail = _mapper.Map<ActionPdf>(ProgrammeAndActionDetail);
            var ProgramDetail = _mapper.Map<ProgramPdf>(ProgrammeAndActionDetail.ActionProblemRevision);
            var ActionDatadictionary = JObject.FromObject(actionDetail).ToObject<Dictionary<string, string>>();
            var ProgramDatadictionary = JObject.FromObject(ProgramDetail).ToObject<Dictionary<string, string>>();
            StringBuilder htmlDetails = new StringBuilder();
            int index = 0;
            if (ActionDatadictionary != null || ProgramDatadictionary != null)
            {
                foreach (var item in ProgramDatadictionary)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                    {
                        index++;
                        htmlDetails.Append($@"<tr>
                  <td>
                      <table id='colorheader{index}'>
                          <tr>
                              <td align='left'>
                                  {item.Key}
                              </td>
                          <tr>
                      </table>
                  </td>
              </tr>
               <tr>
                  <td><table id='value{index}'>
                          <tr>
                              <td align='left'>
                         {item.Value}
                              </td>
                          <tr>
                      </table>
                  </td>

              </tr>");
                    }

                }
                foreach (var item in ActionDatadictionary)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                    {
                        index++;
                        htmlDetails.Append($@"<tr>
                  <td>
                      <table id='colorheader{index}'>
                          <tr>
                              <td align='left'>
                                  {item.Key}
                              </td>
                          <tr>
                      </table>
                  </td>
              </tr>
               <tr>
                  <td><table id='value{index}'>
                          <tr>
                              <td align='left'>
                         {item.Value}
                              </td>
                          <tr>
                      </table>
                  </td>

              </tr>");
                    }

                }
            }
            string html = @$"<table id='table'>
              <tr>
                  <td>
                      <table id='header'>
                          <tr>
                              <td align='center'>Global database on the Implementation<br/>of Nutrition Action (GIFNA)</td>
                              <td></td>
                          </tr>
                      </table>
                  </td>
              </tr>
              <tr>
                  <td>
                      <table id='subheader'>
                          <tr>
                              <td>Programme / action {ProgramAndActionTitle} </td>
                          </tr>
                      </table>
                  </td>
              </tr>
                            {htmlDetails}

                       </table>
                  </td>
              </tr>
            <tr>
                  <td>
                      <table id='table6'>
                          <tr>
                              <td align='left'><b>Policy topics:</b></td>
                              <td align='left'><b>Partners in policy implementation</b></td>
                          </tr>
                          <tr>
                              <td align='left'>
            &bull; Stunting in children 0-5 yrs<br />
            &bull; Wasting in children 0-5 years<br />
            &bull; Underweight in children 0-5 years<br />
            &bull; Breastfeeding - Exclusive 6 months<br />
            &bull; Minimum dietary diversity of women<br />
            &bull; Provision of school meals / School feeding programme<br />
            &bull; Food security and agriculture<br />
            &bull; Water and sanitation
            </td>
                              <td align='left'>
            &bull; Stunting in children 0-5 yrs<br />
            &bull; Wasting in children 0-5 years<br />
            &bull; Underweight in children 0-5 years<br />
            &bull; Breastfeeding - Exclusive 6 months<br />
            &bull; Minimum dietary diversity of women<br />
            &bull; Provision of school meals / School feeding programme<br />
            &bull; Food security and agriculture<br />
            &bull; Water and sanitation</td>
                          </tr>
                      </table>
                  </td>

              </tr>
            <tr>
                  <td>
                      <table id='footer'>
                          <tr>
                              <td>
                              </td>
                              <td>
                                  © World Health Organization <br/>
                                  2012. All rights reserved.
                              </td>
                              <td>
                                  5 October 2022
                              </td>
                              <td>
                                  {NavigationManager.Uri}
                              </td>
                          </tr>
            </table>
            </td>
              </tr>
            </table>";
            ExportPdfLoading = false;
            await JsRuntime.InvokeVoidAsync("CommitmentsPdf", $"Programme / action {ProgramAndActionTitle}.pdf", html, index);
        }

    }
}