﻿using Gina2.Blazor.Models;
using Gina2.Core.Methods;
using Gina2.DbModels;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared.TabComponents.CommitmentTab
{
    public partial class CommitmentListTab
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }
        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "DetailUrlContext")]
        public string DetailUrlContext { get; set; }

        [CascadingParameter(Name = "Data")]
        public List<Models.PublishedListItem> Data { get; set; }

        [CascadingParameter(Name = "SmartData")]
        public List<SmartCommitment> SmartData { get; set; }

        [CascadingParameter(Name = "FileName")]
        public string FileName { get; set; }
        private GlobalSearchRequest SearchRequest = new();
        private int DataSize { get; set; }

        protected override void OnInitialized()
        {
            DataSize = Data.Count;
        }
        private async Task Download()
        {
            var data = await FileDownloadService.GetCommitmentDataforCSV(CountryCode);
            await DownloadFile<Gina2.MySqlRepository.Models.CommitmentCSV>(data, FileName);

        }
        private async Task DownloadFile<T>(List<T> data, string fileName) where T : class
        {
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV<T>(data);
                //var fileData = FileData<PolicyCSV>(data);
                await JsRuntime.InvokeVoidAsync("saveAsFile", $"{fileName}.csv", fileData);
            }
        }
        private async Task TabGridhideshow(string name)
        {
            await JsRuntime.InvokeVoidAsync("griddataHide", name);
            StateHasChanged();

        }
    }
}
