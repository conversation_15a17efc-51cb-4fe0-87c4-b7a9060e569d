﻿using AutoMapper;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.Pdf;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.Services.Commitments;
using Gina2.Services.Country;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage;
using Newtonsoft.Json.Linq;
using System.Text;
using Gina2.Core;
using Gina2.Blazor.Pages;
using Microsoft.Extensions.Caching.Memory;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Services.FileDownload;
using Gina2.Core.Methods;
using Gina2.Services.Models;
using Gina2.Services.Policy;

namespace Gina2.Blazor.Shared.TabComponents.CommitmentTab
{
    public partial class CommitmentDetailTab : PageConfirgurationComponent
    {
        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }

        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        [Inject]
        public IMapper Mapper { get; set; }
        [Inject]
        private ICommitmentService CommitmentService { get; set; }
        [Inject]
        private ICommitmentRevisionService CommitmentRevisionService { get; set; }
        [Inject]
        private ICountryService CountryService { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }

        [CascadingParameter(Name = "CommitmentCode")]
        public int CommitmentCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int VersionId { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }
        private CommitmentRevision CommitmentDetail { get; set; } = new();
        private IEnumerable<Commitment> CommitmentList { get; set; }
        private SmartCommitment SmartCommitmentDetail { get; set; } = new();
        private List<Ic2n> CommitmentICN2S { get; set; } = new();
        private List<Sdg> CommitmentSDG { get; set; } = new();
        private IEnumerable<Country> CountryName { get; set; }
        public List<SmartCommitment> SmartCommitmentList = new();
        public SmartCommitmentRevision SmartCommitment = new();
        private IEnumerable<CommitmentLog> commitmentLogInfo = Enumerable.Empty<CommitmentLog>();
        private bool ExportPdfLoading { get; set; }
        private bool ExportCsvLoading { get; set; }
        public CloudBlobContainer CloudBlobContainer
        {
            get
            {
                string blobStorageConnectionString = new AppSettingsHelper(Configuration).GetBlobStorageConnectionString();
                if (string.IsNullOrWhiteSpace(blobStorageConnectionString))
                {
                    return null;
                }

                CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
                CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
                return cloudBlobClient.GetContainerReference(Constants.BlobStorage.DataTypeContainerName);
            }
        }

        private bool isLoading = true;

        bool revision = false;
        private bool EnablePhase2Features = false;

        protected override void OnInitialized()
        {
            object data = MemoryCache.Get("submit");
            MemoryCache.Remove("submit");
            if (data != null)
            {
                _ = OpenToaster(data.ToString(), "", AntDesign.NotificationType.Success);

            }
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                EnablePhase2Features = Convert.ToBoolean(Configuration["EnablePhase2Features"]);
                SmartCommitment = await CommitmentRevisionService.GetSmartCommitmentRevisionAsync(CommitmentCode, VersionId);
                if (SmartCommitment == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                if(!string.IsNullOrEmpty(CountryCode))
                {
                    var IsCommitmentBelongsToCountryInURL = SmartCommitment.SmartCommitmentCommitmentRevisionMap.SelectMany(c=>c.CommitmentRevision.CommitmentCountryMapRevision).FirstOrDefault(e=>e.CountryCode == CountryCode) != null? true: false;
                    if ( !IsCommitmentBelongsToCountryInURL)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }
                }
                //SmartCommitment.SmartCommitmentCommitmentRevisionMap.Remove(SmartCommitment);
                var commitmentCode = SmartCommitment.SmartCommitmentCommitmentRevisionMap.FirstOrDefault();
                CommitmentDetail = await CommitmentRevisionService.GetCommitmentRevisionAsync(commitmentCode.CommitmentId, commitmentCode.CommitmentVId);

                if (CommitmentDetail == null)
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
                commitmentLogInfo = await CommitmentService.GetAllCommitmentLogByCommitmentId(CommitmentDetail.Id);
                isLoading = false;
                await InvokeAsync(StateHasChanged);

                _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "mechanismhiddenbg");
                _ = JsRuntime.InvokeVoidAsync("detailhiddenbg", "bgdetailhidden");
            }
        }

        private async Task DownLoadPdf(string uri)
        {
            isLoading = true;
            string fileName = uri.Replace("public://", "");
            if (!string.IsNullOrEmpty(fileName))
            {
                try
                {
                    CloudBlockBlob cloudBlockBlob = CloudBlobContainer.GetBlockBlobReference(fileName);
                    if (await cloudBlockBlob.ExistsAsync())
                    {
                        await cloudBlockBlob.FetchAttributesAsync();
                        byte[] arr = new byte[cloudBlockBlob.Properties.Length];
                        await cloudBlockBlob.DownloadToByteArrayAsync(arr, 0);
                        await JsRuntime.InvokeVoidAsync("BlazorDownloadFile", fileName, "application/pdf", arr);
                    }
                    else
                    {
                        await OpenErrorToaster("File not found");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("file not found {ex}", ex);
                }
            }
            isLoading = false;
        }

        private void OnChangingTab(string url)
        {
            NavigationManager.NavigateTo(url);
        }
        private async Task Download()
        {
            ExportCsvLoading = true;

            List<int> commitmentCode = new List<int>
            {
                CommitmentCode
            };
            var search = new GlobalSearchRequest() { DownloadByDataItem = true };
            var data = await FileDownloadService.GetCommitmentDataforCSV(search, commitmentCode);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());
                await JsRuntime.InvokeVoidAsync("saveAsFile", "Commitments.csv", fileData);
            }
            ExportCsvLoading = false;
        }

        private async Task DownloadPdf()
        {
            ExportPdfLoading = true;
            var SmartCommitmentDetails = Mapper.Map<SmartCommitmentPdf>(SmartCommitment);
            var CommitmentDetails = Mapper.Map<CommitmentPdf>(CommitmentDetail);
            var SmartCommitmentDatadictionary = JObject.FromObject(SmartCommitmentDetails).ToObject<Dictionary<string, string>>();
            var CommitmentDatadictionary = JObject.FromObject(CommitmentDetails).ToObject<Dictionary<string, string>>();
            StringBuilder htmlDetails = new();
            int index = 0;
            if (SmartCommitmentDatadictionary != null || CommitmentDatadictionary != null)
            {
                foreach (var item in SmartCommitmentDatadictionary)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                    {
                        index++;
                        htmlDetails.Append($@"<tr>
                  <td>
                      <table id='colorheader{index}'>
                          <tr>
                              <td align='left'>
                                  {item.Key}
                              </td>
                          <tr>
                      </table>
                  </td>
              </tr>
               <tr>
                  <td><table id='value{index}'>
                          <tr>
                              <td align='left'>
                         {item.Value}
                              </td>
                          <tr>
                      </table>
                  </td>

              </tr>");
                    }

                }
                foreach (var item in CommitmentDatadictionary)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                    {
                        index++;
                        htmlDetails.Append($@"<tr>
                  <td>
                      <table id='colorheader{index}'>
                          <tr>
                              <td align='left'>
                                  {item.Key}
                              </td>
                          <tr>
                      </table>
                  </td>
              </tr>
               <tr>
                  <td><table id='value{index}'>
                          <tr>
                              <td align='left'>
                         {item.Value}
                              </td>
                          <tr>
                      </table>
                  </td>

              </tr>");
                    }

                }
            }
            string html = @$"<table id='table'>
              <tr>
                  <td>
                      <table id='header'>
                          <tr>
                              <td align='center'>Global database on the Implementation<br/>of Nutrition Action (GIFNA)</td>
                              <td></td>
                          </tr>
                      </table>
                  </td>
              </tr>
              <tr>
                  <td>
                      <table id='subheader'>
                          <tr>
                              <td>Commitment - {CommitmentDetail.Title} - Commitment {SmartCommitmentDetail.CommitmentId} - {SmartCommitmentDetail.Title} </td>
                          </tr>
                      </table>
                  </td>
              </tr>
                            {htmlDetails}

                       </table>
                  </td>
              </tr>
            <tr>
                  <td>
                      <table id='table6'>
                          <tr>
                              <td align='left'><b>Policy topics:</b></td>
                              <td align='left'><b>Partners in policy implementation</b></td>
                          </tr>
                          <tr>
                              <td align='left'>
            &bull; Stunting in children 0-5 yrs<br />
            &bull; Wasting in children 0-5 years<br />
            &bull; Underweight in children 0-5 years<br />
            &bull; Breastfeeding - Exclusive 6 months<br />
            &bull; Minimum dietary diversity of women<br />
            &bull; Provision of school meals / School feeding programme<br />
            &bull; Food security and agriculture<br />
            &bull; Water and sanitation
            </td>
                              <td align='left'>
            &bull; Stunting in children 0-5 yrs<br />
            &bull; Wasting in children 0-5 years<br />
            &bull; Underweight in children 0-5 years<br />
            &bull; Breastfeeding - Exclusive 6 months<br />
            &bull; Minimum dietary diversity of women<br />
            &bull; Provision of school meals / School feeding programme<br />
            &bull; Food security and agriculture<br />
            &bull; Water and sanitation</td>
                          </tr>
                      </table>
                  </td>

              </tr>
            <tr>
                  <td>
                      <table id='footer'>
                          <tr>
                              <td>
                              </td>
                              <td>
                                  © World Health Organization <br/>
                                  2012. All rights reserved.
                              </td>
                              <td>
                                  5 October 2022
                              </td>
                              <td>
                                  {NavigationManager.Uri}
                              </td>
                          </tr>
            </table>
            </td>
              </tr>
            </table>";
            ExportPdfLoading = false;
            await JsRuntime.InvokeVoidAsync("CommitmentsPdf", $"Commitment - {CommitmentDetail.Title} - Commitment {SmartCommitmentDetail.CommitmentId} - {SmartCommitmentDetail.Title}.pdf", html, index);

        }
    }
}
