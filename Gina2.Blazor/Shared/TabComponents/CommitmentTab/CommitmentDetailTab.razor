﻿@using Gina2.DbModels.CommitmentRevisions
@using Gina2.DbModels.PolicyDrafts
@using Gina2.Core.Methods;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent;
<Loader IsLoading="@isLoading" />

<Div Flex="Flex.JustifyContent.Between" Class="downl-flex">
    <Div Class="item1">

        <Button hidden="@(CountryCode != null ? false : true)" Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/commitments"))">
            <Icon Class="fas fa-chevron-left"></Icon> Back to Commitments
        </Button>

    </Div>
    <Div Class="item2">
        @*<Button Loading="@ExportPdfLoading" Clicked="@DownloadPdf" data-cy="PDFBtn" Class="but-yellow mr-1"><Icon class="arrow-bottom" /> PDF</Button>*@
        <Button Loading="@ExportCsvLoading" Clicked="Download" data-cy="CSVBtn" Class="but-yellow"><Icon class="arrow-bottom" /> CSV</Button>
        @if (EnablePhase2Features)
        {
            <AuthorizeView>
                <Authorized>
                    <Dropdown Class="menu-dot">
                        <DropdownToggle Color="Color.Primary" Split />
                        <DropdownMenu>
                            <DropdownItem href="@(VersionId != 0 ? $"/countries/{CountryCode}/commitments/{CommitmentDetail.Id}/{CommitmentDetail.VersionId}/edit" : $"/countries/{CountryCode}/commitments/{CommitmentDetail.Id}/edit")">Edit</DropdownItem>
                            @if (EnablePhase2Features)
                            {
                                <DropdownItem href="@($"/countries/{CountryCode}/commitments/{CommitmentCode}/moderate")">Moderate</DropdownItem>

                            }
                        </DropdownMenu>
                    </Dropdown>
                </Authorized>
            </AuthorizeView>
        }
    </Div>
</Div>

<Heading Class="Headingtab alltab_D_h3" Size="HeadingSize.Is3">Commitment - @CommitmentDetail?.Title -  @SmartCommitment?.CommitmentNumber - @SmartCommitment?.Title</Heading>
<Divider />

<Div>

    <Layout Sider Class="search-box pt-2 pb-5 mob-layout">

        <Layout Class="left-layout pr-3">
            <LayoutContent Class="tabsel extractshed">
                <div class="_BGDetail _commiylist" id="bgdetailhidden">
                    <ListGroup Class="list-ta">
                        @if (CommitmentDetail.StartYear.HasValue || CommitmentDetail.StartMonth.HasValue)
                        {
                            <ListGroupItem>
                                <span class="titelspan">Date of commitment(s) made: </span>
                                <span class="paragr">@MonthYearDisplayHelper.GetMonthAndYearString(CommitmentDetail.StartMonth, CommitmentDetail.StartYear)</span>
                            </ListGroupItem>
                        }
                        @if (SmartCommitment != null && SmartCommitment.SmartCommitmentCommitmentRevisionMap.Any() && SmartCommitment.SmartCommitmentCommitmentRevisionMap.FirstOrDefault().CommitmentRevision.CommitmentPartnerRevision.Any())
                        {
                            string sectorList = string.Join(",", SmartCommitment.SmartCommitmentCommitmentRevisionMap.FirstOrDefault().CommitmentRevision.CommitmentPartnerRevision.Select(p => p.Partner.Name).ToList());
                            <ListGroupItem>

                                <span class="paragr"><span class="titelspan">Sector: </span> @sectorList</span>
                            </ListGroupItem>
                        }
                        @if (!string.IsNullOrEmpty(CommitmentDetail.MinistryDepartment))
                        {
                            <ListGroupItem>

                                <span class="paragr"><span class="titelspan">Ministry, department or national agency: </span> @CommitmentDetail?.MinistryDepartment.GetLink()</span>
                            </ListGroupItem>
                        }
                        @if (!string.IsNullOrEmpty(CommitmentDetail.EndosedBy))
                        {
                            <ListGroupItem>

                                <span class="paragr"><span class="titelspan">Endorsed by: </span> @CommitmentDetail?.EndosedBy</span>
                            </ListGroupItem>
                        }
                        @if (!string.IsNullOrEmpty(CommitmentDetail.Event))
                        {
                            <ListGroupItem>

                                <span class="paragr"><span class="titelspan">Event: </span> @CommitmentDetail?.Event</span>
                            </ListGroupItem>
                        }


                        @if (!string.IsNullOrEmpty(CommitmentDetail.Description))
                        {
                            <ListGroupItem>
                                <span class="paragr"><span class="titelspan">Background information: </span> @((MarkupString)CommitmentDetail.Description.GetLink())</span>
                            </ListGroupItem>
                        }
                    </ListGroup>
                </div>
                <ListGroup Class="list-ta _MechanismDetail _CommitDetail">
                    @if (!string.IsNullOrEmpty(SmartCommitment.Title) && !string.IsNullOrEmpty(SmartCommitment.CommitmentNumber))
                    {
                        <ListGroupItem>
                            <span class="titelspan">SMART commitment: </span>
                            <span class="paragr"> Commitment @SmartCommitment?.CommitmentNumber - @SmartCommitment?.Title</span>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(SmartCommitment.Extract))
                    {
                        <ListGroupItem>
                            <span class="titelspan">SMART commitment details: </span>
                            <span class="paragr"> @((MarkupString)SmartCommitment.Extract)</span>
                        </ListGroupItem>

                    }
                    @if (SmartCommitment != null && SmartCommitment.SmartCommitmentICN2Revision.Any())
                    {
                        <ListGroupItem>
                            <span class="titelspan">Links to the ICN2 Framework for Action: </span>
                            <span class="paragr">
                                <Repeater Items="@SmartCommitment.SmartCommitmentICN2Revision">
                                    @*<Paragraph Margin="Margin.Is1.FromBottom">@context.Icn2.Name</Paragraph>*@
                                    <ReadMoreOrLessComponent Text="@context.Icn2.Name" TruncateLength="100" />
                                </Repeater>
                            </span>
                        </ListGroupItem>

                    }
                    @if (SmartCommitment != null && SmartCommitment.SmartCommitSDGRevision.Any())
                    {
                        <ListGroupItem>
                            <span class="titelspan">Links to the Sustainable Development Goals (SDGs):</span>
                            <span class="paragr">
                                <Repeater Items="@SmartCommitment.SmartCommitSDGRevision">
                                    @*<Paragraph Margin="Margin.Is1.FromBottom">@context.Sdg.Name</Paragraph>*@
                                    <ReadMoreOrLessComponent Text="@context.Sdg.Name" TruncateLength="100" />
                                </Repeater>
                            </span>
                        </ListGroupItem>
                    }
                </ListGroup>

                <ListGroup Class="list-view mt-3" id="mechanismhiddenbg">

                    @if (!string.IsNullOrEmpty(CommitmentDetail.ResourceAllocation))
                    {

                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">Resource allocation: </Heading>
                            <Paragraph>@CommitmentDetail?.ResourceAllocation.GetLink()</Paragraph>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(CommitmentDetail.PlannedProgressMonitoring))
                    {

                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">Planned progress monitoring: </Heading>
                            <Paragraph>@CommitmentDetail?.PlannedProgressMonitoring.GetLink()</Paragraph>
                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(CommitmentDetail.Links))
                    {
                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">URL for further information: </Heading>
                            <NavLink href="@CommitmentDetail?.Links" target="_blank">@CommitmentDetail?.Links</NavLink>

                        </ListGroupItem>
                    }
                    @if (!string.IsNullOrEmpty(CommitmentDetail.Notes))
                    {
                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">Further notes:  </Heading>
                            <Paragraph>@CommitmentDetail?.Notes.GetLink()</Paragraph>
                        </ListGroupItem>

                    }


                    @if (CommitmentDetail != null && CommitmentDetail.CommitmentAttachmentRevision != null && CommitmentDetail.CommitmentAttachmentRevision.Any())
                    {
                        <ListGroupItem>
                            <Heading Size="HeadingSize.Is5">Document upload: </Heading>
                            @foreach (var item in CommitmentDetail.CommitmentAttachmentRevision)
                            {
                                <a href="@($"pdf-preview/{Uri.EscapeDataString(item.FileDisplayName)}")" target="_blank">
                                    <img src="img/picture_as_pdf.png" class="pr-1" />
                                    @item.FileDisplayName
                                </a>
                                <br />
                            }
                        </ListGroupItem>
                    }
                </ListGroup>
                <AuthorizeView Roles="Admin">
                    <Div Class="table-responsive">
                        <Accordion Class="accor-fbox">
                            <Collapse Visible="@revision">
                                <CollapseHeader>
                                    <Heading Class="head-but mt-4 m-0 d-flex align-items-center" Size="HeadingSize.Is4">
                                        <Span Class="revisiontitle">Revision log</Span> 
                                        <Button Clicked="@(()=>revision = !revision)"></Button>
                                    </Heading>
                                </CollapseHeader>
                                <CollapseBody Class="tab-0">
                                    <DataGrid Class="table-nth _actions"
                                    TItem="@CommitmentLog"
                                    Data="@commitmentLogInfo"
                                    PageSize="5"
                                    ShowPageSizes
                                    ShowPager
                                    Responsive
                                    SortMode="DataGridSortMode.Single">
                                        <EmptyTemplate>
                                            <Div>No data found.</Div>
                                        </EmptyTemplate>
                                        <DataGridColumns>
                                            <DataGridColumn Caption="Date" Field="@nameof(CommitmentLog.RevisedDate)" TextAlignment="TextAlignment.Start"
                                            Width="22%">
                                                <DisplayTemplate Context="displayContext">
                                                    @displayContext.RevisedDate.GetDayWithFormatedDate()
                                                </DisplayTemplate>
                                            </DataGridColumn>
                                            <DataGridColumn Field="@nameof(CommitmentLog.UserName)" Caption="User" Width="15%" />
                                            <DataGridColumn Field="@nameof(CommitmentLog.OtherNotes)" Caption="Log" Width="22%" />
                                            <DataGridColumn Field="@nameof(CommitmentLog.ToState)" Caption="State" Width="19%" />
                                        </DataGridColumns>
                                    </DataGrid>
                                </CollapseBody>
                            </Collapse>
                        </Accordion>
                    </Div>
                </AuthorizeView>
            </LayoutContent>
        </Layout>
        <LayoutSider Class="Search-sider right-layout pl-1">
            <LayoutSiderContent>

                <Div Class="accordion-Search" id="accordionExample">
                    <div class="accordion">
                        <button type="button" class="_padding_l-r20" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            Country(ies):
                        </button>
                        <div id="collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body _siderdetail_tab">
                                <Div Class="downl-flex cunt-box _flex_countr">
                                    <Repeater Items="@CommitmentDetail?.CommitmentCountryMapRevision.OrderBy(c => c.Country.Name)">
                                        <a target="_blank" href='/countries/@context.CountryCode/commitments'>@context.Country.Name</a>
                                    </Repeater>
                                </Div>
                            </div>
                        </div>

                        @if (SmartCommitment != null && CommitmentDetail.SmartCommitmentCommitmentRevisionMap.Any())
                        {
                            <button type="button" class="_padding_l-r20" data-bs-toggle="collapse" data-bs-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo">
                                Other SMART Commitments
                            </button>
                            <div id="collapsetwo" class="accordion-collapse collapse show">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="@CommitmentDetail.SmartCommitmentCommitmentRevisionMap.Where(s => s.SmartCommitmentId != CommitmentCode).ToList()" Context="SmartCommitment">
                                        <Div Class="_other">
                                            <a target="_blank" href="@(VersionId == 0 ? $"countries/{CountryCode}/commitments/{SmartCommitment.SmartCommitmentId}" : $"countries/{CountryCode}/commitments/{SmartCommitment.SmartCommitmentId}/{SmartCommitment.SmartCommitmentVId}")"> Commitment @SmartCommitment.SmartCommitmentRevision.CommitmentNumber - @SmartCommitment.SmartCommitmentRevision.Title</a>
                                        </Div>
                                    </Repeater>
                                </div>
                            </div>
                        }
                        @if (CommitmentDetail != null && CommitmentDetail.CommitmentPolicyRevision.Any())
                        {
                            <button data-cy="LinkToActionsBtn" class="_padding_l-r20" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                Link to policy(ies):
                            </button>
                            <div id="collapse4" class="accordion-collapse collapse show" aria-labelledby="headingOne">
                                <div class="accordion-body _siderdetail_tab">
                                    <Repeater Items="CommitmentDetail.CommitmentPolicyRevision">
                                        <Div Class="_other">
                                            <a target="_blank" href="@($"countries/{CountryCode}/policies/{context.Policy.Id}")" class="_allpad-17">@context.Policy.Title</a>
                                        </Div>
                                    </Repeater>
                                </div>
                            </div>
                        }
                    </div>
                </Div>

            </LayoutSiderContent>
        </LayoutSider>
    </Layout>
</Div>

