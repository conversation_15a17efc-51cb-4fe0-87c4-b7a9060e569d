using AntDesign;
using Blazorise;
using Blazorise.Snackbar;
using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Blazor.Models;
using Gina2.Blazor.Models.AdminModel;
using Gina2.Core.Enums;
using Gina2.Core.Extensions;
using Gina2.Core.Interface;
using Gina2.Core.Lookups;
using Gina2.Core.Methods;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.Services.Commitments;
using Gina2.Services.Country;
using Gina2.Services.ICN2;
using Gina2.Services.PartnerCategorys;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using static Gina2.Core.Constants;
using static Gina2.Core.Constants.LocalizationKeys;
using static Pipelines.Sockets.Unofficial.SocketConnection;

namespace Gina2.Blazor.Shared.TabComponents.CommitmentTab
{
    public partial class CommitmentCreateOrEditTab : PageConfirgurationComponent
    {
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        [Inject]
        private ILogger<CommitmentCreateOrEditTab> _logger { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private ICurrentUserServiceExtended CurrentUserService { get; set; }

        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private ICommitmentService CommitmentSerivce { get; set; }

        [Inject]
        private ICommitmentRevisionService CommitmentRevisionService { get; set; }

        [Inject]
        private IPartnerCategoryService PartnerCategoryService { get; set; }

        [Inject]
        private IIcn2Service Icn2Service { get; set; }

        [Inject]
        private IEmailServices EmailServices { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IDbContextFactory<GenaAppIdentityContext> DbFactory { get; set; }

        [CascadingParameter(Name = "CommitmentCode")]
        public int CommitmentCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "VersionId")]
        public int? VersionId { get; set; }
        public bool IsModerationNotes = false;
        private long ImageSize { get; set; } = 25 * (1024 * 1024); // 25 mb(kb to mb)
        public string ModerationNotes = string.Empty;
        public string UserName = string.Empty;
        string nextStatusToApply = string.Empty;
        private string ImageError { get; set; }
        private IEnumerable<Country> CountryData { get; set; } = new List<Country>();
        private IEnumerable<string> DefaultCountryData { get; set; } = new List<string>();
        private IEnumerable<Partner> PartnerData { get; set; } = new List<Partner>();
        private IEnumerable<Icn2Category> Icn2CategoryData { get; set; } = new List<Icn2Category>();
        private List<SmartCommitmentRevision> SmartCommitmentDetailList { get; set; } = new();

        private CommitmentRevision CommitmentData = new() { CommitmentAttachmentRevision = new List<CommitmentAttachmentRevision>() };
        private DateTime? CommitmentStartYear { get; set; }
        private DateTime? CommitmentStartMonth { get; set; }
        public DateTime? SmartCommitmentStartDate { get; set; }
        public DateTime? SmartCommitmentStartYear { get; set; }
        public DateTime? SmartCommitmentStartMonth { get; set; }

        private bool SmartCommitmentModal;

        private Snackbar snackbar;
        private bool ValidationError { get; set; }
        private SmartCommitmentRevision SmartCommitmentDetails { get; set; } = new();
        private IEnumerable<int> DefaultPolicyData { get; set; } = new List<int>();
        public List<RoleBaseWorkFlowLookUp> RoleBaseWorkFlowLookUps { get; set; } = new List<RoleBaseWorkFlowLookUp>();
        private List<UserModel> ContributorList { get; set; } = new List<UserModel>();
        private int SmartCommitmentIndex { get; set; } = -1;
        private Dictionary<string, byte[]> ImageBytes { get; set; } = new Dictionary<string, byte[]>();
        private string ImageType { get; set; }
        private string FileDisplayName { get; set; }
        private string BlobFileName { get; set; } = string.Empty;
        string selectedMailValue;
        private Blazorise.Modal modalRef;
        private _quillEditor quillEditorDescriptionRef;
        private async Task ShowCreateSmartCommitmentModal()
        {
            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antsmartdraggable", "ant-header");
        }
        private async Task<Task> ClosePopup()
        {
            SmartCommitmentModal = false;

            return Task.CompletedTask;
        }
        private Task OnModalClosing(Blazorise.ModalClosingEventArgs e)
        {
            // just set Cancel to prevent modal from closing
            e.Cancel = SmartCommitmentModal || e.CloseReason != CloseReason.UserClosing;

            return Task.CompletedTask;
        }
        private async Task ShowEditSmartCommitmentModal(SmartCommitmentRevision context)
        {
            SmartCommitmentIndex = SmartCommitmentDetailList.FindIndex(x => x == context);
            SmartCommitmentDetails = context;
            SdgIds = SmartCommitmentDetails.SmartCommitSDGRevision.Select(s => s.SdgId.ToString()).ToArray();
            if (SmartCommitmentDetails.Year != null)
            {
                SmartCommitmentStartYear = new DateTime(SmartCommitmentDetails.Year.Value, 1, 1);
            }
            if (SmartCommitmentDetails.Month != null)
            {
                SmartCommitmentStartMonth = new DateTime(1, SmartCommitmentDetails.Month.Value, 1);
            }
            SmartCommitmentStartDate = MonthYearDisplayHelper.GetMonthAndYearDate(SmartCommitmentDetails.Month, SmartCommitmentDetails.Year);
            await modalRef.Show();
            await JsRuntime.InvokeVoidAsync("dragPopup", "antsmartdraggable", "ant-header");
        }

        public void ValidateModerationNotes(string value)
        {
            IsModerationNotes = RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}");
            ModerationNotes = value;
        }

        private void DeleteSmartCommitmentModal(SmartCommitmentRevision context)
        {
            SmartCommitmentDetailList.Remove(context);
        }

        private void DataChangingStartYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            CommitmentStartYear = dateTimeChangedEventArgs.Date;
            CommitmentData.StartYear = dateTimeChangedEventArgs.Date.Value.Year;
        }
        private void DataChangingStartMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            CommitmentStartMonth = dateTimeChangedEventArgs.Date;
            CommitmentData.StartMonth = dateTimeChangedEventArgs.Date.Value.Month;
        }
        private async Task OnChanged(FileChangedEventArgs args)
        {
            try
            {
                ImageError = string.Empty;
                IsAccordianLoading = true;
                var files = args.Files;
                if (files != null && files.Any(e => e.Type != "application/pdf"))
                {
                    ImageError = "Only pdf is allowed";
                    IsAccordianLoading = false;
                    return;
                }
                foreach (var file in files)
                {
                    if (file.Size <= ImageSize)
                    {
                        await using MemoryStream fs = new();
                        await file.OpenReadStream(maxAllowedSize: ImageSize).CopyToAsync(fs);
                        var shortFileName = RegexHelper.Replace(file.Name, @"[^0-9a-zA-Z\._]", string.Empty);
                        BlobFileName = $"{Guid.NewGuid():N}-{shortFileName}";
                        ImageBytes.Add(BlobFileName, GetBytes(fs));
                        FileDisplayName = file.Name;
                        FileInfo fi = new(shortFileName);
                        CreateFileDbData(BlobFileName, fi.Extension, FileDisplayName);
                        ImageError = string.Empty;
                    }
                    else
                    {
                        ImageError = "File size is too big";
                    }
                }
                IsAccordianLoading = false;
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Print("ERROR: " + e.Message + Environment.NewLine);
            }
        }

        public void RemoveFile(CommitmentAttachmentRevision file)
        {
            CommitmentData.CommitmentAttachmentRevision.Remove(file);
            ImageBytes.Remove(file.Url);
        }
        private void CreateFileDbData(string bolbFileName, string fileType, string fileDisplayName)
        {
            try
            {
                CommitmentData.CommitmentAttachmentRevision.Add(new CommitmentAttachmentRevision()
                {
                    FileName = bolbFileName,
                    FileType = fileType,
                    FileDisplayName = fileDisplayName,
                    Url = BlobFileName
                });
            }
            catch (Exception ex)
            {

                throw;
            }

        }
        private void GetUsersAsync(string roleName)
        {
            using var _dbContext = DbFactory.CreateDbContext();
            var query = from user in _dbContext.Users.ToList()
                        join userRole in _dbContext.UserRoles
                       on user.Id equals userRole.UserId
                        join role in _dbContext.Roles
                        on userRole.RoleId equals role.Id
                        select new AppUserModel()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            UserName = user.UserName,
                            Email = user.Email,
                            Organization = user.Organization,
                            Status = user.Status,
                            UserRoles = role.Name
                        };
            var userList = query.ToList();

            foreach (var item in userList)
            {
                ContributorList.Add(new UserModel()
                {
                    Id = item.Id,
                    UserName = item.UserName,
                    DisplayName = item.UserName,
                });
            }
        }

        private async Task SaveImageToAzureStorage()
        {
            string blobStorageConnectionString = new Helpers.AppSettingsHelper(Configuration).GetBlobStorageConnectionString();

            if (string.IsNullOrWhiteSpace(blobStorageConnectionString)
                || string.IsNullOrWhiteSpace(BlobFileName)
                || !ImageBytes.Any())
            {
                // TODO: show error
                return;
            }
            CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
            CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
            CloudBlobContainer cloudBlobContainer = cloudBlobClient.GetContainerReference(Gina2.Core.Constants.BlobStorage.DataTypeContainerName);
            foreach (var item in ImageBytes)
            {
                CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(item.Key);
                await cloudBlockBlob.DeleteIfExistsAsync();
                cloudBlockBlob.Properties.ContentType = "application/pdf";
                await cloudBlockBlob.UploadFromByteArrayAsync(item.Value, 0, item.Value.Length);
            }
            ImageBytes = null;
            ImageType = string.Empty;
        }

        public static byte[] GetBytes(Stream stream)
        {
            var bytes = new byte[stream.Length];
            stream.Seek(0, SeekOrigin.Begin);
            stream.ReadAsync(bytes, 0, bytes.Length);
            stream.Dispose();
            return bytes;
        }
        public List<CommitmentNumber> CommitmentNumber { get; set; } = new List<CommitmentNumber>() {
            new(){Id = 1, Name = "Commitment 1" },
            new(){Id = 2, Name = "Commitment 2" },
            new(){Id = 3, Name = "Commitment 3" },
            new(){Id = 4, Name = "Commitment 4" },
            new(){Id = 5, Name = "Commitment 5" },
            new(){Id = 6, Name = "Commitment 6" },
            new(){Id = 7, Name = "Commitment 7" },
            new(){Id = 8, Name = "Commitment 8" },
            new(){Id = 9, Name = "Commitment 9" },
            new(){Id = 10, Name = "Commitment 10" },
        };

        public bool UseridVisible { get; set; } = false;
        private async Task OnnReveiw(string value)
        {
            nextStatusToApply = value;
            if (nextStatusToApply == WorkflowStatusToState.Delegated.ToString())
            {
                UseridVisible = true;
            }
            else if (nextStatusToApply == WorkflowStatusToState.SentForCorrection.ToString())
            {
                UseridVisible = true;
                if (CommitmentData.Id != 0)
                {
                    var commitmentRevisionHistory = await CommitmentRevisionService.GetLogByCommitmentId(CommitmentData.Id);

                    if (commitmentRevisionHistory != null)
                    {
                        selectedMailValue = commitmentRevisionHistory.OrderBy(s => s.RevisedDate).FirstOrDefault().UserName;
                    }
                }
            }
            else
            {
                UseridVisible = false;
            }
            StateHasChanged();
        }
        private List<Sdg> SdgData = new List<Sdg>();

        public List<SdgTree> sdgTrees = new List<SdgTree>();
        public string[] SdgIds { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                IsLoading = true;
                StateHasChanged();
                CountryData = await CountryService.GetCountriesAsync();
                PartnerData = await PartnerCategoryService.GetPartnerForCommitmentAsync();
                Icn2CategoryData = await Icn2Service.GetIc2nCategoriesAsync();
                SdgData = await CommitmentSerivce.GetAllSdgAsync();
                var getParentId = SdgData.Where(x => x.ParentId == null).ToList();
                await GetSdgTreeView(getParentId);
                GetUsersAsync("Contributor");

                if (CommitmentCode != 0)
                {
                    CommitmentData = await CommitmentRevisionService.GetCommitmentRevisionAsync(CommitmentCode, VersionId);
                    if (CommitmentData == null)
                    {
                        NavigationManager.NavigateTo("NotFound");
                        return;
                    }

                    SmartCommitmentDetailList = CommitmentData.SmartCommitmentCommitmentRevisionMap
                                                .Select(s => s.SmartCommitmentRevision).ToList();
                    FileDisplayName = CommitmentData.CommitmentAttachmentRevision?.FirstOrDefault(c => c.CommitmentId == CommitmentCode)?.FileDisplayName;
                    DefaultCountryData = CommitmentData.CommitmentCountryMapRevision.Select(x => x.CountryCode).ToList();
                    DefaultPolicyData = CommitmentData.CommitmentPolicyRevision.Select(x => x.PolicyId).ToList();
                    var selectedCountry = CountryData.Where(c => DefaultCountryData.Contains(c.Iso3Code)).ToList();
                    List<CountryByPolicy> listofcountry = new List<CountryByPolicy>();

                    foreach (var country in selectedCountry)
                    {
                        foreach (var policy in country.PolicyCountryMap)
                        {
                            CountryByPolicy countryPolicy = new CountryByPolicy();
                            countryPolicy.Title = $"{country.Name}-{policy.Policy.Title}";
                            countryPolicy.Id = policy.Policy.Id;
                            listofcountry.Add(countryPolicy);
                        }
                    }
                    SelectedCountriesPolicies = listofcountry;
                    if (CommitmentData.StartYear != null)
                    {
                        CommitmentStartYear = new DateTime(CommitmentData.StartYear.Value, 1, 1);
                    }
                    if (CommitmentData.StartMonth != null)
                    {
                        CommitmentStartMonth = new DateTime(1, CommitmentData.StartMonth.Value, 1);
                    }


                    var createOrEdit = VersionId != 0 ? "Edited" : "Created";
                    UserName = $"{createOrEdit} by {CurrentUserService.UserName}";
                    ModerationNotes = CommitmentData.CommitmentLog.FirstOrDefault().OtherNotes;
                }
                else
                {
                    UserName = $"Created by {CurrentUserService.UserName}";
                }

                RoleBaseWorkFlowLookUps = RoleBaseWorkFlowLookUp.RoleBaseWorkFlowLookUps(CurrentUserService.UserRole);
                IsLoading = false;
                StateHasChanged();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task GetSdgTreeView(List<Sdg> parentData)
        {
            foreach (var item in parentData)
            {
                var getListOfSdf = SdgData.Where(x => x.ParentId == item.Id).ToList();
                if (getListOfSdf != null && getListOfSdf.Count > 0)
                {
                    sdgTrees.Add(new()
                    {
                        Id = item.Id,
                        ParentName = item.Name,
                        ChildName = await GetChildSdgTreeView(getListOfSdf)
                    });
                }
            }
        }

        private async Task<List<SdgTree>> GetChildSdgTreeView(List<Sdg> Sdg)
        {
            List<SdgTree> child = new List<SdgTree>();
            foreach (var item in Sdg)
            {
                var getChildListOfSdf = SdgData.Where(x => x.ParentId == item.Id).ToList();
                //if (getChildListOfSdf != null && getChildListOfSdf.Count > 0)
                //{
                child.Add(new()
                {
                    Id = item.Id,
                    ParentName = item.Name,
                    ChildName = await GetChildSdgTreeView(getChildListOfSdf)
                });
                //}

            }
            return child;
        }

        private void OnChangeDescription(string value)
        {
            CommitmentData.Description = value;
        }
        private List<Country> CountryByPolicies { get; set; } = new List<Country>();
        private IEnumerable<CountryByPolicy> SelectedCountriesPolicies { get; set; } = new List<CountryByPolicy>();

        private void OnCountriesMultiChanged(IEnumerable<Country> Countries)
        {
            if (Countries != null && Countries.Any())
            {
                List<CountryByPolicy> listofcountry = new List<CountryByPolicy>();
                foreach (var country in Countries)
                {
                    CountryByPolicies = new List<Country>();
                    CommitmentData.CommitmentCountryMapRevision.Add(new CommitmentCountryMapRevision()
                    {
                        CountryCode = country.Iso3Code
                    });

                    var getallCountryByPolicy = CountryData.Where(c => c.Iso3Code == country.Iso3Code).ToList();
                    CountryByPolicies.AddRange(getallCountryByPolicy);
                    foreach (var item in CountryByPolicies)
                    {
                        foreach (var item2 in item.PolicyCountryMap)
                        {
                            CountryByPolicy countryPolicy = new CountryByPolicy();
                            countryPolicy.Title = $"{country.Name}-{item2.Policy.Title}";
                            countryPolicy.Id = item2.Policy.Id;
                            listofcountry.Add(countryPolicy);
                        }
                    }
                }
                
                SelectedCountriesPolicies = listofcountry.DistinctBy(c => c.Id).OrderBy(c => c.Id).ToList();
            }
            StateHasChanged();
        }

        private void OnChangeSector(bool value, int partnerId)
        {
            if (value)
            {
                CommitmentData.CommitmentPartnerRevision.Add(new CommitmentPartnerRevision()
                {
                    PartnerId = partnerId
                });
            }
            else
            {
                var getPartner = CommitmentData.CommitmentPartnerRevision.Where(p => p.PartnerId == partnerId).First();
                CommitmentData.CommitmentPartnerRevision.Remove(getPartner);
            }

        }

        private void DataChangingDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            CommitmentData.StartYear = Int32.Parse(Year);
            CommitmentData.StartMonth = Int32.Parse(month);
        }

        private void SaveSmartCommitment(SmartCommitmentRevision smartCommitment, bool value)
        {
            if (SmartCommitmentIndex != -1)
            {
                SmartCommitmentDetailList[SmartCommitmentIndex] = smartCommitment;
            }
            else
            {
                SmartCommitmentDetailList.Add(smartCommitment);
            }

            //CommitmentData.SmartCommitmentRevision = SmartCommitmentDetailList;
            SmartCommitmentDetails = new SmartCommitmentRevision();
            modalRef.Hide();
            SmartCommitmentIndex = -1;
        }

        private async Task SaveCommitment()
        {
            CommitmentData.Description = await quillEditorDescriptionRef.GetHTML();
            if (string.IsNullOrEmpty(CommitmentData.Title)
                || CommitmentData.CommitmentCountryMapRevision.Count == 0
                || SmartCommitmentDetailList.Count == 0
                )
            {
                ValidationError = true;
                string elementId = String.Empty;

                if (string.IsNullOrEmpty(CommitmentData.Title)
                        || CommitmentData.CommitmentCountryMapRevision.Count == 0)
                {
                    elementId = "smart-title-error";
                }
                else if (SmartCommitmentDetailList.Count == 0)
                {
                    elementId = "smart-commitment-error";
                }

                if (!string.IsNullOrEmpty(elementId) && JsRuntime != null)
                {
                    await JsRuntime.InvokeVoidAsync("ScrollToView", elementId);
                }
                return;
            }

            if (CommitmentSerivce != null && NavigationManager != null && snackbar != null)
            {
                ValidationError = false;
                await SaveImageToAzureStorage();
                var commitmentLog = new CommitmentLog();

                commitmentLog.RevisedDate = DateTime.UtcNow;
                commitmentLog.UserName = CurrentUserService.UserName;
                commitmentLog.OtherNotes = ModerationNotes;
                commitmentLog.ToState = nextStatusToApply;
                if (nextStatusToApply == WorkflowStatusToState.Delegated || nextStatusToApply == WorkflowStatusToState.SentForCorrection)
                {
                    commitmentLog.DelegatedDate = DateTime.UtcNow;
                    commitmentLog.DelegatedUserName = selectedMailValue;
                }
                if (CommitmentData.Id == 0)
                {
                    commitmentLog.FromState = nextStatusToApply;
                }
                else
                {
                    commitmentLog.CommitmentId = CommitmentData.Id;
                    commitmentLog.FromState = CommitmentData.CommitmentLog.OrderByDescending(s => s.CommitmentVId).First().ToState;
                }
                List<CommitmentPartnerRevision> commitmentPartnerRevisions = new List<CommitmentPartnerRevision>();
                List<CommitmentPolicyRevision> CommitmentPolicyRevision = new List<CommitmentPolicyRevision>();
                commitmentPartnerRevisions = CommitmentData.CommitmentPartnerRevision.ToList();
                CommitmentPolicyRevision = CommitmentData.CommitmentPolicyRevision.ToList();
                CommitmentData.CommitmentCountryMapRevision.Clear();
                CommitmentData.CommitmentPartnerRevision.Clear();
                CommitmentData.CommitmentPolicyRevision.Clear();
                CommitmentData.CommitmentLog.Clear();
                CommitmentData.CommitmentLog.Add(commitmentLog);
                foreach (var item in DefaultCountryData)
                {
                    CommitmentData.CommitmentCountryMapRevision.Add(new CommitmentCountryMapRevision()
                    {
                        CommitmentId = CommitmentData.Id,
                        CountryCode = item,
                        CommitmentVId = CommitmentData.VersionId
                    });
                }

                foreach (var item in commitmentPartnerRevisions)
                {
                    CommitmentData.CommitmentPartnerRevision.Add(new CommitmentPartnerRevision()
                    {
                        PartnerId = item.PartnerId,
                        CommitmentId = item.CommitmentId,
                    });
                }

                foreach (var item in CommitmentPolicyRevision)
                {
                    CommitmentData.CommitmentPolicyRevision.Add(new CommitmentPolicyRevision()
                    {
                        PolicyId = item.PolicyId,
                        CommitmentId = item.CommitmentId,
                    });
                }

                int commitmentcode = await CommitmentRevisionService.SaveCommitmentRevision(CommitmentData, SmartCommitmentDetailList);

                string baseUrl = NavigationManager.BaseUri;
                string subject = "GIFNA data for your attention and action";
                string logedInUser = string.IsNullOrWhiteSpace(CurrentUserService.FullName)
                                                    ? CurrentUserService.Email
                                                    : CurrentUserService.FullName;
                string countryNames = string.Join(", ", CountryData.Where(c => DefaultCountryData.Contains(c.Iso3Code)).Select(c => c.Name));
                string dataType = ContentType.Commitment.GetDescription();

                string firstCountryCode = CountryData.OrderBy(x => x.Name).FirstOrDefault()?.Iso3Code ?? string.Empty;
                string titleLink = $"{baseUrl}countries/{firstCountryCode}/commitments/{SmartCommitmentDetailList.FirstOrDefault().Id}/{CommitmentData.VersionId}";

                if (commitmentLog.ToState == WorkflowStatusToState.Delegated)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentDelegationEmailModel delegationModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = CommitmentData.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = CommitmentData.Notes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.DelegateEmail, delegationModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }                   
                }
                else if (commitmentLog.ToState == WorkflowStatusToState.NeedsReview)
                {
                    List<ApplicationUser> approverUsers = await CurrentUserService.GetApproverUsersByCountry(DefaultCountryData.ToHashSet());                    
                    var tasks = approverUsers.Select(async user =>
                    {
                        try
                        {
                            ContentReviewEmailModel reviewModel = new()
                            {
                                BaseUrl = baseUrl,
                                ReceivedBy = user.FullNameOrEmail,
                                CountryNames = countryNames,
                                ContentTitle = CommitmentData.Title,
                                SubmitedBy = logedInUser,
                                OtherNotes = CommitmentData.Notes,
                                DataType = dataType,
                                TitleLink = titleLink
                            };

                            await EmailServices.SendEmailByTemplateAsync(user.Email, subject, TemplateType.NeedsReview, reviewModel);                            
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Failed to send email to {user.Email}");
                        }
                    });

                    await Task.WhenAll(tasks);
                }
                else if (commitmentLog.ToState == WorkflowStatusToState.SentForCorrection)
                {
                    try
                    {
                        string receivedUserName = await CurrentUserService.GetUserNameOrEmailAsync(selectedMailValue);
                        ContentCorrectionEmailModel correctionModel = new()
                        {
                            BaseUrl = baseUrl,
                            ReceivedBy = receivedUserName,
                            CountryNames = countryNames,
                            ContentTitle = CommitmentData.Title,
                            SubmitedBy = logedInUser,
                            OtherNotes = CommitmentData.Notes,
                            DataType = dataType,
                            TitleLink = titleLink
                        };

                        await EmailServices.SendEmailByTemplateAsync(selectedMailValue, subject, TemplateType.SendForCorrection, correctionModel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email to {selectedMailValue}");
                        throw;
                    }                   
                }

                string countrycode = CommitmentData.CommitmentCountryMapRevision.First().CountryCode;
                if (nextStatusToApply == WorkflowStatusToState.Published)
                {
                    MemoryCache.Set("submit", "Commitment published successfully");
                    NavigationManager.NavigateTo($"/countries/{countrycode}/commitments/{CommitmentData.SmartCommitmentCommitmentRevisionMap.First().SmartCommitmentId}");
                }
                else if (nextStatusToApply == WorkflowStatusToState.NeedsReview)
                {
                    MemoryCache.Set("submit", "Commitment sent for review successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.SentForCorrection)
                {
                    MemoryCache.Set("submit", "Commitment sent for correction successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.Delegated)
                {
                    MemoryCache.Set("submit", "Commitment is delegated successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
                else if (nextStatusToApply == WorkflowStatusToState.Draft)
                {
                    MemoryCache.Set("submit", "Commitment draft created successfully");
                    NavigationManager.NavigateTo("admin/dashboard");
                }
            }
        }
        private void OnChangingTab(string url)
        {
            NavigationManager?.NavigateTo(url);
        }

        private void OnPolicyChanged(IEnumerable<CountryByPolicy> policies)
        {
            CommitmentData.CommitmentPolicyRevision = new List<CommitmentPolicyRevision>();

            foreach (var item in policies)
            {
                CommitmentData.CommitmentPolicyRevision.Add(new CommitmentPolicyRevision() { PolicyId = item.Id });
            }

        }

        private void ValidateText(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }
    }
}