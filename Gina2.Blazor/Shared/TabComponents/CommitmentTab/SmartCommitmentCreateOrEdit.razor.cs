using Blazorise;
using Blazorise.Snackbar;
using System.Collections.ObjectModel;
using Gina2.Blazor.Models;
using Microsoft.AspNetCore.Components;
using AntDesign;
using Gina2.DbModels;
using System.Text.RegularExpressions;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.Core.Methods;
using Gina2.Blazor.Helpers.PageConfigrationData;

namespace Gina2.Blazor.Shared.TabComponents.CommitmentTab
{
    public partial class SmartCommitmentCreateOrEdit
    {

        [Parameter]
        public List<CommitmentNumber> CommitmentNumber { get; set; } = new List<CommitmentNumber>();

        [Parameter]
        public IEnumerable<Icn2Category> Icn2CategoryData { get; set; } = new List<Icn2Category>();

        [Parameter]
        public EventCallback<(SmartCommitmentRevision, bool)> SmartCommitmentData { get; set; }

        [Parameter]
        public SmartCommitmentRevision SmartCommitmentDetails { get; set; } = new();
        
        [Parameter]
        public DateTime? SmartCommitmentStartDate { get; set; }

        [Parameter]
        public List<SdgTree> SdgTrees { get; set; } = new List<SdgTree>();
        private _quillEditor quillEditorDescriptionRef;

        [Parameter]
        public DateTime? SmartCommitmentStartYear { get; set; }

        [Parameter]
        public DateTime? SmartCommitmentStartMonth { get; set; }

        [Parameter]
        public string[] SdgIds { get; set; }

        public bool SmartCommitmentModal { get; private set; }
        private Validations validations;
        private void OnChangeDetails(string value)
        {
            SmartCommitmentDetails.Extract = value;
        }

        private void DataChangingStartYear(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            SmartCommitmentStartYear = dateTimeChangedEventArgs.Date;
            SmartCommitmentDetails.Year = dateTimeChangedEventArgs.Date.Value.Year;
        }
        private void DataChangingStartMonth(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            SmartCommitmentStartMonth = dateTimeChangedEventArgs.Date;
            SmartCommitmentDetails.Month = dateTimeChangedEventArgs.Date.Value.Month;
        }

        private void DataChangingDate(DateTimeChangedEventArgs<DateTime?> dateTimeChangedEventArgs)
        {
            string[] yearMonth = dateTimeChangedEventArgs.DateString.Split('-');
            string Year = yearMonth[0];
            string month = yearMonth[1];
            SmartCommitmentDetails.Year = Int32.Parse(Year);
            SmartCommitmentDetails.Month = Int32.Parse(month);
        }

        private void OnClickIcn2(bool value, int icn2Id)
        {
            if (value)
            {
                SmartCommitmentDetails.SmartCommitmentICN2Revision.Add(new SmartCommitmentICN2Revision()
                {
                    Icn2Id = icn2Id
                });
            }
            else
            {
                var getIcn2 = SmartCommitmentDetails.SmartCommitmentICN2Revision.Where(i => i.Icn2Id == icn2Id).First();
                SmartCommitmentDetails.SmartCommitmentICN2Revision.Remove(getIcn2);
            }

        }

        private async Task SaveSmarCommitment()
        {
            if (await validations.ValidateAll())
            {
                SmartCommitmentDetails.Extract = await quillEditorDescriptionRef.GetHTML();
                SmartCommitmentData.InvokeAsync((SmartCommitmentDetails, false));
            }
        }
        
        private void SdgTreeCheckboxClicked(TreeEventArgs<SdgTree> checkedValue)
        {
            if (checkedValue.Node.Checked)
            {
                SmartCommitmentDetails.SmartCommitSDGRevision.Add(new()
                {
                    SdgId = checkedValue.Node.DataItem.Id
                });
            }
            else {
                var getSdg = SmartCommitmentDetails.SmartCommitSDGRevision
                        .Where(s => s.SdgId == checkedValue.Node.DataItem.Id).First();

                SmartCommitmentDetails.SmartCommitSDGRevision.Remove(getSdg);
            }
        }

        private void ValidateText(ValidatorEventArgs e)
        {
            if (e.Value == null || RegexHelper.IsRegexMatch(e.Value.ToString(), @"<[^>]+>|.* {.*}"))
            {
                e.Status = ValidationStatus.Error;
            }
        }
    }
}