﻿@using Gina2.Blazor.Models
@using Blazorise;

<CountryTabItem />
<Div Class="mt-4">
    <Div Flex="Flex.JustifyContent.End.AlignItems.End">
        <Button Clicked="@Download" Class="but-yellow mob-t-hide"><Icon class="fa-solid fa-upload" /> Export commitments</Button>
    </Div>
</Div>
<Div Class="pol-list">
    <Div Class="accor-fbox pt-4 pb-6 search-box paginationareaa commitmentpage">

        <Div id="DataGridshows1" Class="DataGrids">
            <DataGrid FixedHeaderDataGridMaxHeight="500px"
                FixedHeaderDataGridHeight="450px"
                TItem="@PublishedListItem"  
                Data="@Data"
                TotalItems="@DataSize"
                PageSize="@SearchRequest.PageSize"
                ShowPageSizes
                PageSizes="new[] { 50,100,250,500,1000 }"
                ShowPager
                Responsive
                CurrentPage="@SearchRequest.PageNo"
                SortMode="DataGridSortMode.Single"
                Sortable="true">
                <EmptyTemplate>
                    <Div> No data found </Div>
                </EmptyTemplate>

                <DataGridColumns>
                    <DataGridColumn Caption="SMART commitment" Sortable="true" Field="@nameof(PublishedListItem.Title)" Width="60%">
                        <DisplayTemplate>
                            <NavLink href="@($"/countries/{CountryCode}/{DetailUrlContext}/{context.Key}")">@context.Title</NavLink>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn Field="@nameof(PublishedListItem.StartYear)" Caption="Date of commitment made" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="true" Width="20%"/>
                    <DataGridColumn Field="@nameof(PublishedListItem.EndYear)" Caption="Planned date of fulfillment" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="true" Width="19%" />
                    <DataGridColumn Width="1%" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center">
                        <CaptionTemplate>
                            <Button Class="_sort-down" Clicked="@(() => TabGridhideshow("DataGridshows1"))"><i Class="fas fa-sort-down"></i></Button>
                        </CaptionTemplate>
                    </DataGridColumn>
                </DataGridColumns>
                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                <TotalItemsTemplate>
                    <Badge TextColor="TextColor.Dark">
                        @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                    </Badge>
                </TotalItemsTemplate>
            </DataGrid>
        </Div>
    </Div>
</Div>