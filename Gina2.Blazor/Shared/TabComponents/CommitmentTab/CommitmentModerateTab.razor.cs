﻿using AntDesign;
using Gina2.Core.Interface;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.DbModels.CommitmentRevisions;
using Gina2.DbModels.PolicyDrafts;
using Gina2.Services.Commitments;
using Gina2.Services.Dashboard;
using Microsoft.AspNetCore.Components;
using static Gina2.Core.Constants;

namespace Gina2.Blazor.Shared.TabComponents.CommitmentTab
{
    public partial class CommitmentModerateTab
    {
        [Inject]
        private ICurrentUserService CurrentUserService { get; set; }
        [Inject]
        private IDashboardService DashboardService { get; set; }
        [Inject]
        private ModalService ModalService { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Inject]
        private ICommitmentRevisionService CommitmentRevisionService { get; set; } 
        [Inject]
        private ICommitmentService CommitmentService { get; set; }

        [CascadingParameter(Name = "CommitmentCode")]
        public int CommitmentCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }
        private string containType = "SMART commitments";
        private List<ModerationRevisionLog> ModerationLog { get; set; } = new();
        private int lastRevisionId;
        private int RevisionId { get; set; }
        List<ModerationRevisionLog> ModerateActions { get; set; } = new();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                ModerationLog = await DashboardService.GetModerationLogByEntityId(CommitmentCode, containType);
                lastRevisionId = ModerationLog.First().EntityRevisionId;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task OpenConfirm(RenderFragment renderFragment, int entityId, int versionId, string contentType, int logId)
        {
            bool unPublish = false;
            if (renderFragment == UnpublishContent)
            {
                unPublish = true;
            }
            var options = new ConfirmOptions()
            {
                Title = "Confirm your action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await ApplyActions(contentType, entityId, versionId, logId);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }

        private async Task RevertConfirm(ModerationRevisionLog log, RenderFragment renderFragment)
        {
            var options = new ConfirmOptions()
            {
                Title = "Confirm revert action",
                Width = 350,
                Content = renderFragment,
                OnOk = async e =>
                {
                    await RevertAction(log);
                },
                OnCancel = e => { return Task.CompletedTask; }
            };

            var confirmRef = await ModalService.CreateConfirmAsync(options);

            confirmRef.OnOpen = () =>
            {
                return Task.CompletedTask;
            };

            confirmRef.OnClose = () =>
            {
                return Task.CompletedTask;
            };
        }
        private async Task RevertAction(ModerationRevisionLog log)
        { 
            await CommitmentRevisionService.Revert(log.EditId, log.EntityAnotherRevisionId, log.ToState, CurrentUserService.UserName);
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
        }
        private async Task ApplyActions(string contentType, int entityId, int versionId, int logId)
        {
            var commitmentRevision = await CommitmentRevisionService.GetCommitmentRevisionToCreateAdraftAsync(entityId, versionId);
            commitmentRevision.CommitmentLog.Clear();
            commitmentRevision.CommitmentLog.Add(new CommitmentLog
            {
                CommitmentId = commitmentRevision.Id,
                CommitmentVId = commitmentRevision.VersionId,
                FromState = WorkflowStatusToState.Published,
                ToState = WorkflowStatusToState.Draft,
                RevisedDate = DateTimeOffset.UtcNow,
                UserName = CurrentUserService.UserName,
            });
            var smartCommitmentRevision = commitmentRevision.SmartCommitmentCommitmentRevisionMap.Select(s => s.SmartCommitmentRevision).ToList();
            await CommitmentRevisionService.SaveCommitmentRevision(commitmentRevision, smartCommitmentRevision, true);
            await DashboardService.UnpublishModerationLogByLogId(logId, containType);
            await CommitmentService.RemoveByIdAsync(entityId);
            _ = OpenSuccessToaster("Unpublished successfully");
            NavigationManager.NavigateTo("admin/dashboard");
        }

        public async void NavigateToDraftEdit(int entityId, int revisionId)
        {
            NavigationManager.NavigateTo($"countries/{CountryCode}/commitments/{entityId}/{revisionId}/Edit");
        }
        public async void NavigateToDraft(int entityId, int revisionId)
        {
            NavigationManager.NavigateTo($"countries/{CountryCode}/commitments/{entityId}/{revisionId}");
        }
    }
}
