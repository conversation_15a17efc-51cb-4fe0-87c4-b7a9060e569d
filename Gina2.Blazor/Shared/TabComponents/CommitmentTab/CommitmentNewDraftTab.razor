﻿<Modal @bind-Visible="@modalVisible" Class="modals-lg createcommitment">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Add smart commitments</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            <Fields>
                <Field>
                    <FieldLabel>First Title</FieldLabel>
                    <Select TValue="int" class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                        <FieldHelp>Read more about Programs and actions document types used in GIFNA.</FieldHelp>
                    </Select>
                </Field>
                <Field>
                    <FieldLabel>SMART Commitment Title*</FieldLabel>
                    <Select TValue="int" class="pl-1 pr-3">
                        <Repeater Items="@TypeOfPolicyData">
                            <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                        </Repeater>
                        <FieldHelp>Read more about Programs and actions document types used in GIFNA.</FieldHelp>
                    </Select>
                </Field>
            </Fields>
            <Field>
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>SMART commitment details</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Filtered 1</SelectItem>
                            <SelectItem Value="2">Filtered 2</SelectItem>
                        </Select>
                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <Field>
                <FieldLabel>Start date</FieldLabel>
                <GinaDate />
            </Field>
            <Fields>
                <FieldLabel>Questions</FieldLabel>
                <Field>
                    <Check TValue="bool"> Is the commitment Specific?</Check>
                </Field>
                <Field>
                    <Check TValue="bool"> Is the commitment Measurable?</Check>
                </Field>
            </Fields>
            <Fields>
                <Field>
                    <Check TValue="bool"> Is the commitment Achievable?</Check>
                </Field>
                <Field>
                    <Check TValue="bool"> Is the commitment Relevant?</Check>
                </Field>
            </Fields>
            <Fields>
                <Field>
                    <Check TValue="bool"> Is the commitment Time-bound?</Check>
                </Field>
            </Fields>
            <Fields>
                <FieldLabel>Links to the ICN2 Framework for Action</FieldLabel>
            </Fields>
            <Bar Class="TreeMenu Verticalleft" Mode="BarMode.VerticalInline"
                 CollapseMode="BarCollapseMode.Small">
                <BarMenu>
                    <BarStart>
                        <BarItem>
                            <BarDropdown>
                                <BarDropdownToggle>
                                    Links to the ICN2 Framework for Action
                                </BarDropdownToggle>
                                <BarDropdownMenu>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Recommended actions to create an enabling environment for effective action
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">1: Enhance political commitment and social participation for improving nutrition at the country level through political dialogue and advocacy.</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">2: Develop – or revise, as appropriate – and cost National Nutrition Plans, align policies that impact nutrition across different ministries and agencies, and strengthen legal frameworks and strategic capacities for nutrition.</Check></BarDropdownItem>
                                                <BarDropdownItem>
                                                    <Check TValue="bool">
                                                        3: Strengthen and establish, as appropriate, national cross-government, inter-sector, multi-stakeholder mechanisms for food security and nutrition to oversee implementation of policies, strategies, programmes and other investments in nutrition. Such platforms may be needed at various levels, with robust safeguards against abuse and conflicts of interest.
                                                    </Check>
                                                </BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">4: Increase responsible and sustainable investment in nutrition, especially at country level with domestic finance; generate additional resources through innovative financing tools; engage development partners to increase Official Development Assistance in nutrition and foster private investments as appropriate.</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">5: Improve the availability, quality, quantity, coverage and management of multisectoral information systems related to food and nutrition for improved policy development and accountability.</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Recommended actions for sustainable food systems promoting healthy diets
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Overweight, obesity and diet-related NCDs
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Wasting in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in women</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in adolescent girls</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>

                                </BarDropdownMenu>
                            </BarDropdown>
                        </BarItem>
                        <BarItem>
                            <BarDropdown>
                                <BarDropdownToggle>
                                    Links to the Sustainable Development Goals (SDGs)
                                </BarDropdownToggle>
                                <BarDropdownMenu>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Goal 1. End poverty in all its forms everywhere
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Maternal, infant and young child nutrition</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Growth monitoring and promotion</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Breastfeeding promotion/counselling</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Promotion of exclusive breastfeeding for 6 months</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Baby-friendly Hospital Initiative (BFHI)</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Health professional training on breastfeeding</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Breastfeeding in difficult circumstances
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Counselling on feeding and care of LBW infants</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool"> Infant feeding in emergencies</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool"> Counselling on infant feeding in the context HIV</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>
                                                <BarDropdownItem><Check TValue="bool">International Code of Marketing of Breast-milk Substitutes</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Monitoring of the Code</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Capacity building for the Code</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Maternity protection</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Complementary feeding promotion/counselling</Check></BarDropdownItem>
                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Infant and young child feeding
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Wasting in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in women</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in adolescent girls</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>

                                </BarDropdownMenu>
                            </BarDropdown>
                        </BarItem>
                    </BarStart>
                </BarMenu>
            </Bar>

            <Fields>
                <FieldLabel>Links to the Sustainable Development Goals (SDGs)</FieldLabel>
            </Fields>
            <Bar Class="TreeMenu Verticalleft" Mode="BarMode.VerticalInline"
                 CollapseMode="BarCollapseMode.Small">
                <BarMenu>
                    <BarStart>
                        <BarItem>
                            <BarDropdown>
                                <BarDropdownToggle>
                                    Links to the ICN2 Framework for Action
                                </BarDropdownToggle>
                                <BarDropdownMenu>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Recommended actions to create an enabling environment for effective action
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">1: Enhance political commitment and social participation for improving nutrition at the country level through political dialogue and advocacy.</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">2: Develop – or revise, as appropriate – and cost National Nutrition Plans, align policies that impact nutrition across different ministries and agencies, and strengthen legal frameworks and strategic capacities for nutrition.</Check></BarDropdownItem>
                                                <BarDropdownItem>
                                                    <Check TValue="bool">
                                                        3: Strengthen and establish, as appropriate, national cross-government, inter-sector, multi-stakeholder mechanisms for food security and nutrition to oversee implementation of policies, strategies, programmes and other investments in nutrition. Such platforms may be needed at various levels, with robust safeguards against abuse and conflicts of interest.
                                                    </Check>
                                                </BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">4: Increase responsible and sustainable investment in nutrition, especially at country level with domestic finance; generate additional resources through innovative financing tools; engage development partners to increase Official Development Assistance in nutrition and foster private investments as appropriate.</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">5: Improve the availability, quality, quantity, coverage and management of multisectoral information systems related to food and nutrition for improved policy development and accountability.</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Recommended actions for sustainable food systems promoting healthy diets
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Overweight, obesity and diet-related NCDs
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Wasting in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in women</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in adolescent girls</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>

                                </BarDropdownMenu>
                            </BarDropdown>
                        </BarItem>
                        <BarItem>
                            <BarDropdown>
                                <BarDropdownToggle>
                                    Links to the Sustainable Development Goals (SDGs)
                                </BarDropdownToggle>
                                <BarDropdownMenu>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Goal 1. End poverty in all its forms everywhere
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Maternal, infant and young child nutrition</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Growth monitoring and promotion</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Breastfeeding promotion/counselling</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Promotion of exclusive breastfeeding for 6 months</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Baby-friendly Hospital Initiative (BFHI)</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Health professional training on breastfeeding</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Breastfeeding in difficult circumstances
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Counselling on feeding and care of LBW infants</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool"> Infant feeding in emergencies</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool"> Counselling on infant feeding in the context HIV</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>
                                                <BarDropdownItem><Check TValue="bool">International Code of Marketing of Breast-milk Substitutes</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Monitoring of the Code</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Capacity building for the Code</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Maternity protection</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Complementary feeding promotion/counselling</Check></BarDropdownItem>
                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>
                                    <BarItem>
                                        <BarDropdown>
                                            <BarDropdownToggle>
                                                Infant and young child feeding
                                            </BarDropdownToggle>
                                            <BarDropdownMenu>
                                                <BarDropdownItem><Check TValue="bool">Low birth weight</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Stunting in children 0-5 yrs</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Wasting in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in children 0-5 years</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in women</Check></BarDropdownItem>
                                                <BarDropdownItem><Check TValue="bool">Underweight in adolescent girls</Check></BarDropdownItem>
                                                <BarItem>
                                                    <BarDropdown>
                                                        <BarDropdownToggle>
                                                            Anaemia
                                                        </BarDropdownToggle>
                                                        <BarDropdownMenu>
                                                            <BarDropdownItem><Check TValue="bool">Iodine deficiency disorders</Check></BarDropdownItem>
                                                            <BarDropdownItem><Check TValue="bool">Vitamin A deficiency</Check></BarDropdownItem>
                                                        </BarDropdownMenu>
                                                    </BarDropdown>
                                                </BarItem>

                                            </BarDropdownMenu>
                                        </BarDropdown>
                                    </BarItem>

                                </BarDropdownMenu>
                            </BarDropdown>
                        </BarItem>
                    </BarStart>
                </BarMenu>
            </Bar>
        </ModalBody>
        <ModalFooter>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@HideModal">Save</Button>
        </ModalFooter>
    </ModalContent>
</Modal>
<AuthorizeView>
    <Authorized>
        <Dropdown Class="menu-dot homeedit tabedit">
            <DropdownToggle Class="aboutmenu" Color="Color.Primary" Split />
            <DropdownMenu>
                <DropdownItem href="@($"/commitments/{CommitmentCode}")">View published</DropdownItem>
                <DropdownItem  href="@($"/commitments/{CommitmentCode}/moderate")">Moderate</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>
<Container>
    <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
        <Div Class="item1 flex-b">
            <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/commitments"))">
                <Icon Class="fas fa-chevron-left"></Icon> Back to Commitments
            </Button>

        </Div>
        <Div Class="item2">
            <Button Class="but-yellow mr-1"><Icon class="arrow-bottom" /> CSV</Button>
        </Div>
    </Div>
    <Container Class="newdraft" Padding="Padding.Is0">
        <Container Padding="Padding.Is0" Class="pt-3 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3">Fill the information for new commitments</Heading>
            <Divider Class="divi-blue" />
        </Container>

        <Container Class="form-newd">
            <Field>
                <FieldLabel>Title of commitments*<Span>*</Span></FieldLabel>
                <TextEdit Placeholder="Brazil’s commitments within the Decade of Action on Nutrition"></TextEdit>
            </Field>
            <Field Class="pt-2">
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <FieldLabel>Background information</FieldLabel>
                    </Div>
                    <Div Class="item2">
                        <Select TValue="int">
                            <SelectItem Value="0">Filtered HTML</SelectItem>
                            <SelectItem Value="1">Full HTML</SelectItem>
                            <SelectItem Value="2">Plain text</SelectItem>
                            <SelectItem Value="3">PHP code</SelectItem>
                        </Select>

                        <Button Class="but-info"><Tooltip Text="Disable rich-text More information about text formats"><Icon Name="IconName.QuestionCircle" /></Tooltip></Button>

                    </Div>
                </Div>
                <RextEditors />
            </Field>
            <Field Class="pt-2">
                <FieldLabel>Country(ies)</FieldLabel>
                <Select TValue="int" class="pl-1 pr-3">
                    <Repeater Items="@LanguageData">
                        <SelectItem Value="context.GetTypeCode()">@context</SelectItem>
                    </Repeater>
                </Select>
            </Field>
            <Field>
                <FieldLabel>Ministry, department or national agency</FieldLabel>
                <RextEditors></RextEditors>
            </Field>

            <Heading Size="HeadingSize.Is5" Class="pt-3 pb-1">Sector</Heading>
            <ListGroup Class="ulgroup">
                <Repeater Items="@ItemData">
                    <ListGroupItem><Check TValue="bool">@context</Check></ListGroupItem>
                </Repeater>
            </ListGroup>


            <Fields Class="pt-3">
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Endorsed by</FieldLabel>
                    <TextEdit Placeholder="Government of Brazil"></TextEdit>
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Date of commitment made</FieldLabel>
                    <GinaDate />
                </Field>
            </Fields>
            <Field>
                <FieldLabel>Event</FieldLabel>
                <TextEdit Placeholder="Government of Brazil"></TextEdit>
            </Field>
        </Container>

        <Container Class="form-newd mt-4">
            <Div Flex="Flex.JustifyContent.Between">
                <Div Class="item1">
                    <Heading Class="blo-head" Size="HeadingSize.Is3">SMART Commitment</Heading>
                </Div>
                <Div Class="item2">
                    <Button Class="but-yellow pl-2 pr-2"><Icon Class="far fa-plus" /> Add Commitment</Button>
                    <Button Class="but-info"><Tooltip Text="Hello tooltip"><Icon Name="IconName.QuestionCircle" /></Tooltip></Button>
                </Div>
            </Div>

            <Dropzone Items="MyFirstList">
                <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                    <Div Class="drag-1">
                        <Icon Class="fa-solid fa-grip-vertical" />
                        <Icon Clicked="@ShowModal" Class="fa-solid fa-pen" />
                        @context.Title
                    </Div>
                    <Div Class="drag-2">
                        <Icon Class="fa-solid fa-trash" />
                    </Div>

                </Div>
            </Dropzone>

            <Field>
                <FieldLabel>File upload</FieldLabel>
                <FileEdit Changed="@OnChanged" Placeholder="Select or drag and drop multiple files" Multiple />
                <FieldLabel>Files must be less than 2 MB | Allowed file types: .pdf</FieldLabel>
            </Field>
        </Container>

        <Container Class=" form-newd gina-new mt-4">

            <FieldLabel Class="gina-bold">Link to action(s)</FieldLabel>
            <Divider />
            <Addons Class="pb-3">
                <Addon AddonType="AddonType.Start">
                    <Button Color="Color.Light">
                        <Icon Name="IconName.Search" />
                    </Button>
                </Addon>
                <Addon AddonType="AddonType.Body">
                    <TextEdit Placeholder="Some text value..." />
                </Addon>
            </Addons>
            <Repeater Items="@ListOfActionRecords">
                <Check TValue="bool">@context</Check>
            </Repeater>

        </Container>

        <Container Class="form-newd mt-4">
            <Field>
                <FieldLabel>Resource allocation</FieldLabel>
                <RextEditors></RextEditors>
            </Field>
            <Field>
                <FieldLabel>Resource allocation</FieldLabel>
                <RextEditors></RextEditors>
            </Field>
            <Field>
                <FieldLabel>URL link</FieldLabel>
                <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
                <TextEdit Placeholder="Enter policy title here..."></TextEdit>
            </Field>
            <Field>
                <FieldLabel>Further notes</FieldLabel>
                <RextEditors></RextEditors>
            </Field>

        </Container>
        <Container Class="mt-4 pb-6">
            <Button Class="but-blues" Clicked="@(()=>snackbar.Show())">Save</Button>
            <br />
            <Heading Class="alert-warn mt-3">New content: Your draft will be placed in moderation.</Heading>
            <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
                <SnackbarBody>
                    New content: Your draft will be placed in moderation.
                </SnackbarBody>
            </Snackbar>
        </Container>
    </Container>
</Container>

