﻿@using Gina2.Blazor.Models.AdminModel
@using Gina2.DbModels;
@using Gina2.Blazor.Models;
@using Gina2.Blazor.Helpers.PageConfigrationData;
@using static Gina2.Core.Constants;
@inherits PageConfirgurationComponent
<Loader IsLoading="@IsLoading" />
@if (CommitmentData != null)
{
    <Modal @ref="modalRef" Closing="@OnModalClosing" Class="modals-lg createcommitment antsmartdraggable datepickermargin" ShowBackdrop=false>
        <SmartCommitmentCreateOrEdit CommitmentNumber="@CommitmentNumber"
                                     Icn2CategoryData="@Icn2CategoryData"
                                     SmartCommitmentData="@(e => SaveSmartCommitment(e.Item1, e.Item2))"
                                     SmartCommitmentDetails="@SmartCommitmentDetails"
                                     SmartCommitmentStartYear="@SmartCommitmentStartYear"
                                     SmartCommitmentStartMonth="@SmartCommitmentStartMonth"
                                     SmartCommitmentStartDate="@SmartCommitmentStartDate"
                                     SdgTrees="@sdgTrees"
                                     SdgIds="@SdgIds" />
    </Modal>
    @*<AuthorizeView>
    <Authorized>No Smart Commitment Data
        <Dropdown Class="menu-dot homeedit tabedit">
            <DropdownToggle Class="aboutmenu" Color="Color.Primary" Split />
            <DropdownMenu>
                <DropdownItem><Link To="@($"/countries/{CountryCode}/commitments/{CommitmentCode}")" Title="View published"/></DropdownItem>
                <DropdownItem><Link To="@($"/countries/{CountryCode}/commitments/{CommitmentCode}/moderate")" Title="Moderate"/></DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
</AuthorizeView>*@
    <Div Class="newdraft _antdesign pl-2 pr-2">
        @if (CommitmentCode != 0 && CountryCode != null)
        {
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex mobile-col">
                <Div Class="item1 flex-b" hidden="@(CommitmentCode > 0 ? false : true)">
                    <Button Class="back-but" Clicked="@(() => OnChangingTab($"/countries/{CountryCode}/commitments"))">
                        <Icon data-cy="BackToCommitmentsIcon" Class="fas fa-chevron-left"></Icon> Back to Commitments
                    </Button>
                </Div>
                <Div Class="item2">
                    @*<Button data-cy="PDFBtn" Class="but-yellow mr-1"><Icon class="arrow-bottom" /> PDF</Button>*@
                </Div>
            </Div>
        }

        <Div Class="pt-5 m-pt-2 mobi-heing">
            <Heading Class="new-heading" Size="HeadingSize.Is3" data-cy="CommitmentHeadingCreate">
                @if (CommitmentCode == 0)
                {
                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentHeadingCreate))
                    <AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentHeadingCreate" />
                }
                else
                {
                    @((MarkupString)PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentHeadingEdit))
                    <AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentHeadingEdit" />
                }
            </Heading>
            <Divider Class="divi-blue" />
        </Div>
        <Validations ValidateOnLoad="false">

            <Div Class="form-newd" id="smart-title-error">
                <Field>
                    <FieldLabel data-cy="CommitmentTitleLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentTitleLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="CommitmentTitleLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentTitleTooltip)">
                            <Button data-cy="CommitmentTitleLabelTooltipBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentTitleLabelTooltipIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentTitleGroup" />
                    </FieldLabel>

                    <Validation Validator="@ValidateText">
                        <TextEdit Placeholder="" @bind-Text="@CommitmentData.Title">
                            <Feedback>
                                <ValidationError>Enter valid title</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Validation>
                </Field>
                <FieldLabel Style="color:red">@(string.IsNullOrEmpty(CommitmentData.Title) && ValidationError ? "Enter valid title" : "")</FieldLabel>

                <Field Class="pt-2">
                    <Div Flex="Flex.JustifyContent.Between">
                        <Div Class="item1">
                            <FieldLabel data-cy="CommitmentBackgroundInfoLabel">
                                @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentBackgroundInfoLabel)
                                <Tooltip data-cy="CommitmentBackgroundInfoLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentBackgroundInfoTooltip)">
                                    <Button data-cy="CommitmentBackgroundInfoLabelBtn" Class="but-info _tooltip">
                                        <Icon data-cy="CommitmentBackgroundInfoLabelIcon" Name="IconName.QuestionCircle" />
                                    </Button>
                                </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentBackgroundInfoGroup" />
                            </FieldLabel>
                        </Div>
                    </Div>
                    @*<RextEditors Changed="@OnChangeDescription" Value="@CommitmentData.Description" />*@
                    <_quillEditor value="@CommitmentData.Description" @ref="quillEditorDescriptionRef"></_quillEditor>
                </Field>
                <Field Class="pt-2">
                    <FieldLabel data-cy="CommitmentCountryLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentCountryLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="CommitmentCountryLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentCountryTooltip)">
                            <Button data-cy="CommitmentCountryLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentCountryLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentCountryGroup" />
                    </FieldLabel>
                    <AntDesign.Select DataSource="@CountryData"
                                      Mode="multiple"
                                      TItemValue="string"
                                      TItem="Country"
                                      LabelName="@nameof(Country.Name)"
                                      ValueName="@nameof(Country.Iso3Code)"
                                      OnSelectedItemsChanged="@OnCountriesMultiChanged"
                                      @bind-Values="@DefaultCountryData"
                                      AllowClear=true
                                      EnableSearch=true
                                      AutoClearSearchValue=true
                                      Style="width: 100%; margin-bottom: 8px;" />
                    <FieldLabel Style="color:red">@(CommitmentData.CommitmentCountryMapRevision.Count == 0 && ValidationError ? "Please select one or more country" : "")</FieldLabel>

                </Field>
                <Field>
                    <FieldLabel data-cy="CommitmentMinistryLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentMinistryLabel)
                        <Tooltip data-cy="CommitmentMinistryLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentMinistryTooltip)">
                            <Button data-cy="CommitmentMinistryLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentMinistryLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentMinistryGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <MemoEdit Rows="5" @bind-Text="@CommitmentData.MinistryDepartment">
                            <Feedback>
                                <ValidationError>Enter valid title</ValidationError>
                            </Feedback>
                        </MemoEdit>
                    </Validation>

                </Field>

                <Heading Size="HeadingSize.Is5" Class="pt-3 pb-1" data-cy="CommitmentSectorLabel" Style="font-weight:700;">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentSectorLabel)
                    <Tooltip data-cy="CommitmentSectorLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentSectorTooltip)">
                        <Button data-cy="CommitmentSectorLabelBtn" Class="but-info _tooltip">
                            <Icon data-cy="CommitmentSectorLabelIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentSectorGroup" />
                </Heading>
                <ListGroup Class="ulgroup">
                    <Repeater Items="@PartnerData">
                        @{
                            bool IsPartner = CommitmentData.CommitmentPartnerRevision.Any(p => p.PartnerId == context.Id);
                        }
                        <ListGroupItem><Check TValue="bool" Checked="@IsPartner" CheckedChanged="@(e => OnChangeSector(e, context.Id))">@context.Name</Check></ListGroupItem>
                    </Repeater>
                </ListGroup>


                <Fields Class="pt-3 align-items-center">
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="CommitmentEndorsedByLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEndorsedByLabel)
                            <Tooltip data-cy="CommitmentEndorsedByLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEndorsedByTooltip)">
                                <Button data-cy="CommitmentEndorsedByLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="CommitmentEndorsedByLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentEndorsedByGroup" />
                        </FieldLabel>
                        <Validation Validator="@ValidateText">
                            <TextEdit @bind-Text="@CommitmentData.EndosedBy" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEndorsedByPlaceholder)">
                                <Feedback>
                                    <ValidationError>Enter valid endosed by</ValidationError>
                                </Feedback>
                            </TextEdit>
                        </Validation>
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD" Class="_datepicker datepickermargin">
                        <FieldLabel Class="commitementlabel" data-cy="CommitmentDateLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentDateLabel)
                            <Tooltip data-cy="CommitmentDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentDateTooltip)">
                                <Button data-cy="CommitmentDateLabelTooltipBtn" Class="but-info _tooltip">
                                    <Icon data-cy="CommitmentDateLabelTooltipIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentDateGroup" />
                        </FieldLabel>
                        <Fields>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyStartMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentStartMonthLabel) </FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@CommitmentStartMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingStartMonth" Picker="@AntDesign.DatePickerType.Month" />
                            </Field>
                            <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                                <FieldLabel data-cy="PolicyStartYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentStartYearLabel) </FieldLabel>
                                <AntDesign.DatePicker @bind-Value="@CommitmentStartYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingStartYear" />
                            </Field>
                        </Fields>
                        @*<AntDesign.DatePicker @bind-Value="@CommitmentStartDate" TValue="DateTime?" Picker="month" OnChange="DataChangingDate" />*@
                    </Field>
                </Fields>







                <Field>
                    <FieldLabel data-cy="CommitmentEventLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEventLabel)
                        <Tooltip data-cy="CommitmentEventLabel" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEventTooltip)">
                            <Button data-cy="CommitmentEventLabel" Class="but-info _tooltip">
                                <Icon Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentEventGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <TextEdit @bind-Text="@CommitmentData.Event" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentEventPlaceholder)">
                            <Feedback>
                                <ValidationError>Enter valid event</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Validation>
                </Field>
            </Div>

            <Div Class="form-newd mt-4" id="smart-commitment-error">
                <Div Flex="Flex.JustifyContent.Between">
                    <Div Class="item1">
                        <Heading Class="blo-head" Size="HeadingSize.Is2" data-cy="SmartCommitmentLabelHeading">
                            @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentLabel)
                            <Span Style="color:red">*</Span>
                            <Tooltip data-cy="SmartCommitmentLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentTooltip)">
                                <Button data-cy="SmartCommitmentLabelBtn" Class="but-info _tooltip">
                                    <Icon data-cy="SmartCommitmentLabelIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentGroup" />
                        </Heading>
                    </Div>
                    <Div Class="item2">
                        <Button Class="but-yellow pl-2 pr-2" Clicked="@ShowCreateSmartCommitmentModal"><Icon Class="far fa-plus" />Add SMART commitment</Button>
                        @*<Button Class="but-info"><Tooltip Text="Hello tooltip"><Icon Name="IconName.QuestionCircle" /></Tooltip></Button>*@
                    </Div>
                </Div>
                <Dropzone Items="SmartCommitmentDetailList">
                    <Div Class="draggabls" Flex="Flex.JustifyContent.Between" draggable="true">
                        <Div Class="drag-1">
                            <Icon Class="fa-solid fa-grip-vertical" />
                            <Icon Clicked="@(() => ShowEditSmartCommitmentModal(context))" Class="fa-solid fa-pen" />
                            @context.Title
                        </Div>
                        <Div Class="drag-2">
                            <Icon Class="fa-solid fa-trash" Clicked="@(() => DeleteSmartCommitmentModal(context))" />
                        </Div>
                    </Div>
                </Dropzone>
                <FieldLabel Flex='Flex.JustifyContent.Center'>@(SmartCommitmentDetailList.Count == 0 && !ValidationError ? "No SMART commitment data" : "")</FieldLabel>
                <FieldLabel Style="color:red" Flex='Flex.JustifyContent.Center'>@(CommitmentData.SmartCommitmentCommitmentRevisionMap.Count == 0 && ValidationError ? "At least one Smart Commitment is Required" : "")</FieldLabel>

                <Field Class="choosefile">
                    <FieldLabel data-cy="CommitmentFileUploadLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentFileUploadLabel)
                        <Tooltip data-cy="CommitmentFileUploadLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentFileUploadTooltip)">
                            <Button data-cy="CommitmentFileUploadLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentFileUploadLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentFileUploadGroup" />
                    </FieldLabel>
                    <FileEdit Changed="@OnChanged" Filter=".pdf"  Placeholder="Choose File" Multiple />
                    <FieldLabel hidden="@(string.IsNullOrEmpty(FileDisplayName) ? true : false)">@FileDisplayName</FieldLabel><br />
                    <FieldLabel>Allowed file types: .pdf</FieldLabel>
                    <Repeater Items="@CommitmentData.CommitmentAttachmentRevision">
                        <div>
                            <Span>@context.FileDisplayName </Span> <Span>
                                <Tooltip Text="Remove">
                                    <Button Clicked="e => RemoveFile(context)" Class="but-info _tooltip">
                                        <Icon Name="IconName.Delete" />
                                    </Button>
                                </Tooltip>
                            </Span>
                        </div>

                    </Repeater>
                </Field>
            </Div>

            <Div Class="form-newd mt-4">
                <Field>
                    <FieldLabel data-cy="CommitmentLinkToPolicyLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentLinkToPolicyLabel)
                        <Tooltip data-cy="CommitmentLinkToPolicyLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentLinkToPolicyTooltip)">
                            <Button data-cy="CommitmentLinkToPolicyLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentLinkToPolicyLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentLinkToPolicyGroup" />
                    </FieldLabel>
                </Field>
                <AntDesign.Select DataSource="@SelectedCountriesPolicies"
                                  Mode="multiple"
                                  TItemValue="int"
                                  TItem="CountryByPolicy"
                                  LabelName="@nameof(CountryByPolicy.Title)"
                                  ValueName="@nameof(CountryByPolicy.Id)"
                                  OnSelectedItemsChanged="@OnPolicyChanged"
                                  @bind-Values="@DefaultPolicyData"
                                  EnableSearch
                                  Style="width: 100%; margin-bottom: 8px;" />
            </Div>

            <Div Class="form-newd mt-4">
                <Field>
                    <FieldLabel data-cy="CommitmentResourceAllocLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentResourceAllocLabel)
                        <Tooltip data-cy="CommitmentResourceAllocLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentResourceAllocTooltip)">
                            <Button data-cy="CommitmentResourceAllocLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentResourceAllocLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentResourceAllocGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <MemoEdit Rows="5" @bind-Text="@CommitmentData.ResourceAllocation">
                            <Feedback>
                                <ValidationError>Enter valid resource allocation</ValidationError>
                            </Feedback>
                        </MemoEdit>
                    </Validation>
                </Field>
                <Field>
                    <FieldLabel data-cy="CommitmentPlannedProgressLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentPlannedProgressLabel)
                        <Tooltip data-cy="CommitmentPlannedProgressLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentPlannedProgressTooltip)">
                            <Button data-cy="CommitmentPlannedProgressLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentPlannedProgressLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentPlannedProgressGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <MemoEdit Rows="5" @bind-Text="@CommitmentData.PlannedProgressMonitoring">
                            <Feedback>
                                <ValidationError>Enter valid progress monitoring</ValidationError>
                            </Feedback>
                        </MemoEdit>
                    </Validation>
                </Field>
                <Field>
                    <FieldLabel data-cy="CommitmentURLLinkLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentURLLinkLabel)
                        <Tooltip data-cy="CommitmentURLLinkLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentURLLinkTooltip)">
                            <Button data-cy="CommitmentURLLinkLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentURLLinkLabelIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentURLLinkGroup" />
                    </FieldLabel>
                    <FieldHelp>Please select a country to see policies to be linked</FieldHelp>
                    <Validation Validator="@ValidateText">
                        <TextEdit @bind-Text="@CommitmentData.Links" Placeholder="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentURLLinkPlaceholder)">
                            <Feedback>
                                <ValidationError>Enter valid url</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Validation>
                </Field>
                <Field>
                    <FieldLabel data-cy="CommitmentFurtherNoteLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentFurtherNoteLabel)
                        <Tooltip data-cy="CommitmentFurtherNoteLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentFurtherNoteTooltip)">
                            <Button data-cy="CommitmentFurtherNoteLabelBtn" Class="but-info _tooltip">
                                <Icon data-cy="CommitmentFurtherNoteIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentFurtherNoteGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <MemoEdit Rows="5" @bind-Text="@CommitmentData.Notes">
                            <Feedback>
                                <ValidationError>Enter valid further note</ValidationError>
                            </Feedback>
                        </MemoEdit>
                    </Validation>
                </Field>

            </Div>

            <Div Class="form-newd mt-4">
                <Div Class="row">
                    <Div Class="col-lg-6 col-12">
                        <Fields>
                            <Field>
                                <FieldLabel data-cy="CommitmentModerationNoteLabel">
                                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteLabel)
                                    <Span>*</Span><Tooltip data-cy="CommitmentModerationNoteTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteTooltip)">
                                        <Button data-cy="CommitmentModerationNoteBtn" Class="but-info _tooltip">
                                            <Icon data-cy="CommitmentModerationNoteIcon" Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentModerationNoteGroup" />
                                </FieldLabel>
                                    <TextEdit Disabled Text="@UserName" TextChanged="@ValidateModerationNotes"></TextEdit>
                                    <FieldHelp>Provide an explanation of the changes you are making. This will help other authors understand your motivations.</FieldHelp>
                                    @if (IsModerationNotes)
                                {
                                    <Span Class="text-danger"> Enter valid moderation notes</Span>
                                }
                            </Field>
                        </Fields>
                    </Div>
                    <Div Class="col-lg-6 col-12">
                        <Fields>
                            <Field>
                                <FieldLabel data-cy="CommitmentModerationNoteLabel">
                                    Other notes
                                    @* @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteLabel) *@
                                    @* <Span>*</Span> *@<Tooltip data-cy="CommitmentModerationNoteTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteTooltip)">
                                        <Button data-cy="CommitmentModerationNoteBtn" Class="but-info _tooltip">
                                            <Icon data-cy="CommitmentModerationNoteIcon" Name="IconName.QuestionCircle" />
                                        </Button>
                                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentModerationNoteGroup" />
                                </FieldLabel>
                                <TextEdit Text="@ModerationNotes" TextChanged="@ValidateModerationNotes"></TextEdit>
                            </Field>
                        </Fields>
                    </Div>
                </Div>
                @* <Fields>
                    <Field>
                        <FieldLabel data-cy="CommitmentModerationNoteLabel">
                            @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteLabel)
                            <Span>*</Span><Tooltip data-cy="CommitmentModerationNoteTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.CommitmentModerationNoteTooltip)">
                                <Button data-cy="CommitmentModerationNoteBtn" Class="but-info _tooltip">
                                    <Icon data-cy="CommitmentModerationNoteIcon" Name="IconName.QuestionCircle" />
                                </Button>
                            </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.CommitmentModerationNoteGroup" />
                        </FieldLabel>
                        <MemoEdit Disabled Text="@ModerationNotes" Rows="3" TextChanged="@ValidateModerationNotes"></MemoEdit>
                        @if (IsModerationNotes)
                        {
                            <Span Class="text-danger"> Enter valid moderation notes</Span>
                        }
                    </Field>
                </Fields> *@
            </Div>
            <Div Class="mt-4 pb-6">
                <Div Class="stickybottom">
                    <Fields>
                        <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">

                            <AntDesign.SimpleSelect TItem="string"
                                                    TItemValue="string"
                                                    DefaultValue="@(WorkflowStatusToState.Draft.ToString())"
                                                    @bind-Value="@nextStatusToApply"
                                                    OnSelectedItemChanged="async e=> await OnnReveiw(e)">
                                <SelectOptions>
                                    @foreach (var item in RoleBaseWorkFlowLookUps)
                                    {
                                        <Tooltip ShowArrow=true Placement="TooltipPlacement.RightEnd" Title=@(item.Description)>
                                            <AntDesign.SelectOption TItemValue="string" TItem="string" Value=@item.Value Label=@item.Text />
                                        </Tooltip>
                                    }
                                </SelectOptions>
                            </AntDesign.SimpleSelect>

                        </Field>
                        @if (UseridVisible)
                        {
                            <Field Class="_antdesign-select" ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD">
                                @* <FieldLabel>Select User id</FieldLabel>*@
                                <AntDesign.Select DataSource="@ContributorList"
                                                  TItemValue="string"
                                                  TItem="UserModel"
                                                  LabelName="@nameof(UserModel.UserName)"
                                                  ValueName="@nameof(UserModel.UserName)"
                                                  @bind-Value="@selectedMailValue"
                                                  AllowClear
                                                  EnableSearch
                                                  Style="width: 100%; margin-bottom: 0px;">
                                </AntDesign.Select>
                                <FieldHelp>Type who you want to send this content</FieldHelp>
                            </Field>
                        }
                        <Field>
                            <Button Class="but-yellow" Clicked="e=> SaveCommitment()" data-cy="SubmitBtn">Save</Button>
                        </Field>
                    </Fields>

                    <Snackbar @ref="snackbar" Color="SnackbarColor.Primary">
                        <SnackbarBody>
                            New content: Your draft will be placed in moderation.
                        </SnackbarBody>
                    </Snackbar>
                </Div>
            </Div>
        </Validations>
    </Div>
}