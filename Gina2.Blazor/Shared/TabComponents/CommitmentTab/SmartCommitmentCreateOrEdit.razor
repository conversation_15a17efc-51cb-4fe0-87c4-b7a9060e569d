﻿@using Gina2.Blazor.Models;
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent

<ModalContent Centered Class="forms">
    <ModalHeader Class="ant-header">
        <ModalTitle>Add SMART commitments</ModalTitle>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
    </ModalHeader>
    <ModalBody Class="_modalscroll">
        <Validations @ref="@validations" ValidateOnLoad="false">
            <Fields>
                <Field>
                    <FieldLabel data-cy="SmartCommitmentNumberLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentNumberLabel)
                        
                        <Tooltip data-cy="SmartCommitmentNumberLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentNumberTooltip)">
                            <Button data-cy="CommitmentNumberLabelTooltipBtn" Class="but-info _tooltip">
                                <Icon data-cy="SmartCommitmentNumberLabelTooltipIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentNumberGroup" />
                    </FieldLabel>
                    <Select TValue="string" class="pl-1 pr-3" @bind-SelectedValue="SmartCommitmentDetails.CommitmentNumber">
                        <Repeater Items="@CommitmentNumber">
                            <SelectItem Value="context.Name">@context.Name</SelectItem>
                        </Repeater>
                    </Select>
                </Field>
                <Field>
                    <FieldLabel data-cy="SmartCommitmentTitleLabel">
                        @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentTitleLabel)
                        <Span>*</Span>
                        <Tooltip data-cy="SmartCommitmentTitleLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentTitleTooltip)">
                            <Button data-cy="CommitmentTitleLabelTooltipBtn" Class="but-info _tooltip">
                                <Icon data-cy="SmartCommitmentTitleLabelTooltipIcon" Name="IconName.QuestionCircle" />
                            </Button>
                        </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentTitleGroup" />
                    </FieldLabel>
                    <Validation Validator="@ValidateText">
                        <TextEdit Placeholder="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentTitlePlaceholder)" @bind-Text="@SmartCommitmentDetails.Title">
                            <Feedback>
                                <ValidationError>Enter valid title</ValidationError>
                            </Feedback>
                        </TextEdit>
                    </Validation>
                </Field>
            </Fields>
            <Field>
                <FieldLabel data-cy="SmartCommitmentDetailsLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentDetailsLabel)
                    
                    <Tooltip data-cy="SmartCommitmentDetailsLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentDetailsTooltip)">
                        <Button data-cy="CommitmentTDetailsLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="SmartCommitmentDetailsLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentDetailsGroup" />
                </FieldLabel>
                <_quillEditor value="@SmartCommitmentDetails.Extract" @ref="quillEditorDescriptionRef"></_quillEditor>

                @*<RextEditors Changed="@OnChangeDetails" Value="@SmartCommitmentDetails.Extract" />*@
            </Field>
            <Field Class="_datepicker">
                <FieldLabel data-cy="SmartCommitmentDateLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentStartDateLabel)
                    
                    <Tooltip data-cy="SmartCommitmentDateLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentStartDateTooltip)">
                        <Button data-cy="CommitmentDateLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="SmartCommitmentDateLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentStartDateGroup" />
                </FieldLabel>
                <Fields>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="PolicyStartMonthLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentStartMonthLabel) </FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@SmartCommitmentStartMonth" TValue="DateTime?" Format="MMM" DisabledDate="@(date => date > new DateTime(DateTime.Now.Year,12,31))" OnChange="DataChangingStartMonth" Picker="@AntDesign.DatePickerType.Month" />
                    </Field>
                    <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                        <FieldLabel data-cy="PolicyStartYearLabel" Class="_label_textline">@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentStartYearLabel) </FieldLabel>
                        <AntDesign.DatePicker @bind-Value="@SmartCommitmentStartYear" TValue="DateTime?" Picker="@AntDesign.DatePickerType.Year" OnChange="DataChangingStartYear" />
                    </Field>
                </Fields>
                @*<AntDesign.DatePicker @bind-Value="@SmartCommitmentStartDate" TValue="DateTime?" Picker="month" OnChange="DataChangingDate" />*@

            </Field>
            <Fields Class="area">
                <FieldLabel Class="w-100" data-cy="SmartCommitmentQuestionsLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentQuestionsLabel)
                    
                    <Tooltip data-cy="SmartCommitmentQuestionsLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentQuestionsTooltip)">
                        <Button data-cy="CommitmentQuestionsLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="SmartCommitmentQuestionsLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentQuestionsGroup" />
                </FieldLabel>
                <Field Class="questionchkbox">
                    <Check TValue="bool"> Is the commitment Specific?</Check>
                    <Check TValue="bool"> Is the commitment Measurable?</Check>
                    <Check TValue="bool"> Is the commitment Achievable?</Check>
                    <Check TValue="bool"> Is the commitment Relevant?</Check>
                    <Check TValue="bool"> Is the commitment Time-bound?</Check>
                </Field>
            </Fields>
            <Fields>
                <FieldLabel data-cy="SmartCommitmentIcnLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentLinksIcn2Label)
                    
                    <Tooltip data-cy="SmartCommitmentIcnLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentLinksIcn2Tooltip)">
                        <Button data-cy="CommitmentIcnLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="SmartCommitmentIcnLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentLinksIcn2Group" />
                </FieldLabel>
            </Fields>
            <Bar Class="Verticalbar _itemalinebar" Mode="BarMode.VerticalInline"
                 CollapseMode="BarCollapseMode.Small">
                <BarMenu>
                    <BarStart>
                        <Repeater Items="@Icn2CategoryData" Context="Icn2Category">
                            <BarItem>
                                <BarDropdown>
                                    <BarDropdownToggle >
                                        @Icn2Category.Name
                                    </BarDropdownToggle>
                                    <BarDropdownMenu>
                                        <Repeater Items="Icn2Category.Icn2s.OrderBy(i => i.DragAndDropKey)" Context="Icn2">
                                            <BarDropdownItem>
                                                @{
                                                    bool IsIcn2 = SmartCommitmentDetails.SmartCommitmentICN2Revision.Any(p => p.Icn2Id == Icn2.Id);
                                                }
                                                <Check TValue="bool" Checked="@IsIcn2" CheckedChanged="@(e => OnClickIcn2(e, Icn2.Id))">@Icn2.Name</Check>
                                            </BarDropdownItem>
                                        </Repeater>
                                        <BarDropdown>
                                        </BarDropdown>
                                    </BarDropdownMenu>
                                </BarDropdown>
                            </BarItem>
                        </Repeater>
                    </BarStart>

                </BarMenu>
            </Bar>
            <br />
            <Fields>
                <FieldLabel data-cy="SmartCommitmentSdgLabel">
                    @PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentLinksSdgLabel)
                    
                    <Tooltip data-cy="SmartCommitmentSdgLabelTooltip" Text="@PageConfigurations.GetPageConfigrationValueByName(CommitmentPageConfigurationKey.SmartCommitmentLinksSdgTooltip)">
                        <Button data-cy="CommitmentSdgLabelTooltipBtn" Class="but-info _tooltip">
                            <Icon data-cy="SmartCommitmentSdgLabelTooltipIcon" Name="IconName.QuestionCircle" />
                        </Button>
                    </Tooltip><AdminEditbut Key="@CommitmentPageConfigurationKey.SmartCommitmentLinksSdgGroup" />
                </FieldLabel>
            </Fields>

            <Column Class="w-100 Topicspolicy _CheckBoxRemove m-0 p-0">
                <AntDesign.Tree ShowIcon
                                DefaultExpandAll=true
                                MatchedClass="site-tree-search-value"
                                DataSource="SdgTrees"
                                TItem="SdgTree"
                                Class="antDesignggg"
                                KeyExpression="x => x.DataItem.Id.ToString()"
                                TitleExpression="x => x.DataItem.ParentName"
                                ChildrenExpression="x => {
                                if(SmartCommitmentDetails.SmartCommitSDGRevision
                                    .Any(s => s.SdgId == x.DataItem.Id))
                                {
                                x.Checked = true;
                                }
                                return x.DataItem.ChildName;
                                }"
                                CheckedKeys="@SdgIds"
                                OnCheck="x => SdgTreeCheckboxClicked(x)"
                                Checkable>
                </AntDesign.Tree>
            </Column>
        </Validations>
    </ModalBody>
    <ModalFooter>
        <Button Class="but-yellow pl-2 pr-2" Clicked="@SaveSmarCommitment">Save</Button>
    </ModalFooter>
</ModalContent>