﻿using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Gina2.Blazor.Models;
using Gina2.Services.Country;
using Gina2.DbModels.Views;
using DocumentFormat.OpenXml.Presentation;
using Gina2.Services.Models;
using Domain.Search;

namespace Gina2.Blazor.Shared
{
    public class RegionView
    {
        public string RegionCode { get; set; }
        public string RegionName { get; set; }
        public string RegionImageURl { get; set; }
        public int OrderBy { get; set; }
    }
    public partial class RegionsComponent
    {
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }
        [Parameter]
        public IEnumerable<ViewCountryDetails> Countries { get; set; } = new List<ViewCountryDetails>();
        [Parameter]
        public IEnumerable<Region> Regions { get; set; } = new List<Region>();
        [Parameter]
        public GlobalSearchRequest searchRequest { get; set; }

        [Parameter]
        public FileDownload FileDownloadChild { get; set; }
        [Parameter]
        public string SelectedDatatype { get; set; }
        private string RegionsName { get; set; }
        private string insertClass { get; set; }
        private bool ThreeRegionsEnabled { get; set; } = false;
        private bool TwoRegionsEnabled { get; set; } = false;
        public IEnumerable<ViewCountryDetails> CountryNames { get; set; } = new List<ViewCountryDetails>();
        private bool isSearchDisabled = true;
        public string RegionRow { get; set; }
        private bool IsSelected { get; set; } = false;
        public string selectedCountryCode { get; set; }
        private string searchedText = string.Empty;
        CountryList threeRegionCountryList =new();
        CountryList twoRegionCountryList = new();
        public List<RegionView> regionViews { get; set; } = new List<RegionView>();

        protected override async Task OnParametersSetAsync()
        {
            SearchResultCounts searchResultCount = new SearchResultCounts();
            FileDownloadChild.RefreshDataofFileDownload(searchResultCount, searchRequest);
            if (
                !string.IsNullOrEmpty(insertClass)&&
                !string.IsNullOrEmpty(RegionsName) &&
                !string.IsNullOrEmpty(RegionRow)
                )
            { 
            await ShowCountryList(insertClass, RegionsName, IsSelected, RegionRow);
            }
            Regions = Regions.OrderBy(e => e.Name).ToList();
            regionViews = new List<RegionView>();
            foreach (var item in Regions)
            {
                regionViews.Add(new RegionView()
                {
                    RegionCode = item.Code,
                    RegionName = GetRegionName(item.Code),
                    RegionImageURl = GetRegionImageUrl(item.Code),
                    OrderBy = item.DragAndDropKey
                });
            }
            regionViews = regionViews.OrderBy(e => e.OrderBy).ThenBy(e => e.RegionCode).ToList();
        }
        private string GetRegionImageUrl(string regionCode)
        {
            string url = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    url = "img/region/Africa.png";
                    break;
                case "AMR":
                    url = "img/region/America.png";
                    break;
                case "EMR":
                    url = "img/region/Easter-med.png";
                    break;
                case "EUR":
                    url = "img/region/Europe.png";
                    break;
                case "SEAR":
                    url = "img/region/South-easy-asia.png";
                    break;
                case "WPR":
                    url = "img/region/Western-pacific.png";
                    break;
                default:
                    break;
            }
            return url;
        }
        private string GetRegionName(string regionCode)
        {
            string name = string.Empty;
            switch (regionCode)
            {
                case "AFR":
                    name = "African Region";
                    break;
                case "AMR":
                    name = "Region of the Americas";
                    break;
                case "EMR":
                    name = "Eastern Mediterranean Region";
                    break;
                case "EUR":
                    name = "European Region";
                    break;
                case "SEAR":
                    name = "South-East Asia Region";
                    break;
                case "WPR":
                    name = "Western Pacific Region";
                    break;
                default:
                    break;
            }
            return name;
        }
        private async Task ShowCountryList(string regionvalue, string value, bool isRegion, string region)
        {
            ThreeRegionsEnabled = (value != RegionsName && region == "ThreeRegionsEnabled") || (isRegion && region == "ThreeRegionsEnabled") ? true : false;
            TwoRegionsEnabled = (value != RegionsName && region == "TwoRegionsEnabled") || (isRegion && region == "TwoRegionsEnabled") ? true : false;
            CountryNames = Countries?.Where(x => x.RegionCode == regionvalue);
            //CountryNames = CountryNames.OrderBy(e => e.OrderBY).ThenBy(e => e.CountryName);
            RegionsName = value;
            insertClass = regionvalue;
            RegionRow = region;
            IsSelected = isRegion;
            await JSRuntime.InvokeVoidAsync("addActiveClassName", insertClass);
            await FileDownloadChild.GetCountByCountryOrRegion(searchRequest, SelectedDatatype, regionvalue, "");

            if (ThreeRegionsEnabled && threeRegionCountryList != null)
            {
                threeRegionCountryList.Refresh();
            }

            if (TwoRegionsEnabled && twoRegionCountryList != null)
            {
                twoRegionCountryList.Refresh();
            }
            StateHasChanged();
        }

        private async Task MobileHideCountryList()
        {
            ThreeRegionsEnabled = false;
            TwoRegionsEnabled = false;
            await JSRuntime.InvokeVoidAsync("addActiveClassName", insertClass);
        }

        private void Navigate()
        {
            string countryCode = GetSelectedCountryCode();
            if (!string.IsNullOrWhiteSpace(countryCode))
            {
                NavigationManager.NavigateTo($"countries/{countryCode}/policies");
            }
        }

        private void OnCountrySearchTextChangedAsync(string value)
        {
            searchedText = value;
            isSearchDisabled = !IsValidCountry(value);
            Navigate();
        }

        private bool IsValidCountry(string value)
        {
            if (string.IsNullOrWhiteSpace(value) || Countries == null)
            {
                return false;
            }

            return Countries.Select(acn => acn.CountryName).Contains(value, StringComparer.InvariantCultureIgnoreCase);
        }

        private string GetSelectedCountryCode()
        {
            if (string.IsNullOrWhiteSpace(searchedText) || Countries == null)
            {
                return string.Empty;
            }

            ViewCountryDetails selectedCountry = Countries.FirstOrDefault(acn => acn.CountryName.Equals(searchedText, StringComparison.CurrentCultureIgnoreCase));

            return selectedCountry == null ? string.Empty : selectedCountry.CountryCode;
        }
    }
}