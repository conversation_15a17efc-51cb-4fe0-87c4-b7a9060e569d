﻿<div>
    @if (showFullText)
    {
        <Paragraph Margin="Margin.Is1.FromBottom">@Text
            <a hidden="@(Text.Length <= TruncateLength)" class="ReadMore" @onclick="ToggleShowFullText">Read less</a>
        </Paragraph>
    }
    else
    {
        <Paragraph Margin="Margin.Is1.FromBottom">@TruncatedText
            <a hidden="@(Text.Length <= TruncateLength)" class="ReadMore" @onclick="ToggleShowFullText">Read more</a>
        </Paragraph>
    }
</div>

@code {
    [Parameter]
    public string Text { get; set; }

    [Parameter]
    public int TruncateLength { get; set; } = 100;

    private bool showFullText = false;

    private string TruncatedText => Text.Length > TruncateLength ? $"{Text.Substring(0, TruncateLength)}..." : Text;

    private void ToggleShowFullText()
    {
        showFullText = !showFullText;
    }
}
