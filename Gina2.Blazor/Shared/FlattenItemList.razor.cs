﻿using Gina2.Services.Models;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared
{
    public partial class FlattenItemList
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "Data")]
        public List<Models.PublishedListItem> Data { get; set; }

        [CascadingParameter(Name = "Title")]
        public string Title { get; set; }

        [CascadingParameter(Name = "TabTitle")]
        public string TabTitle { get; set; }

        [CascadingParameter(Name = "TypeofTitle")]
        public string TypeofTitle { get; set; }

        [CascadingParameter(Name = "TableTitle")]
        public string TableTitle { get; set; }

        [CascadingParameter(Name = "AdminTitle")]
        public string AdminTitle { get; set; }
        [CascadingParameter(Name = "DetailUrlContext")]
        public string DetailUrlContext { get; set; }

        private GlobalSearchRequest SearchRequest = new();
        public bool IsLoading { get; set; } = false;

        private int DataSize { get; set; }

        protected override void OnInitialized()
        {
            DataSize = Data.Count;
        }

       
    }
}