﻿
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.JSInterop;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace Gina2.Blazor.Shared
{
    public partial class NavMenu
    {
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public NavigationManager _NavigationManager { get; set; }
        string activeUrl = string.Empty;
        private string ActiveClass { get; set; }
		
		public void NavigateToMap(string link)
        {
            
            _NavigationManager.NavigateTo(link, true);
            CloseNavMenu();

		}

        protected override void OnInitialized()
        {
            _NavigationManager.LocationChanged += OnLocationChanged;
            CheckUrl();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await ScrollToFragment();
                
            }
        }
        public void CheckUrl()
        {
            string baseUrl = _NavigationManager.BaseUri;
            if (_NavigationManager.Uri.ToLower().StartsWith(string.Format("{0}map", baseUrl)))
            {
                ActiveClass = "active";
            }
            else
            {
                ActiveClass = "";
            }
        }
        public void Dispose()
        {
            _NavigationManager.LocationChanged -= OnLocationChanged;
        }

        private void OnLocationChanged(object sender, LocationChangedEventArgs e)
        {
            CheckUrl();
            Task.Run(async () => await ScrollToFragment());
            StateHasChanged();
        }

        private async Task ScrollToFragment()
        {
            var baseRemoved = _NavigationManager.Uri.Replace(_NavigationManager.BaseUri, string.Empty);
            activeUrl = baseRemoved.Split('/')[0];
            var uri = new Uri(_NavigationManager.Uri, UriKind.Absolute);
            var fragment = uri.Fragment;
            if (fragment.StartsWith('#'))
            {

                var elementId = fragment.Substring(1);
                var index = elementId.IndexOf(":~:", StringComparison.Ordinal);
                if (index > 0)
                {
                    elementId = elementId.Substring(0, index);
                }

                if (!string.IsNullOrEmpty(elementId))
                {
                    await JSRuntime.InvokeVoidAsync("BlazorScrollToId", elementId);
                }
            }
        }
        private async Task CloseNavMenu()
        {
            await JSRuntime.InvokeVoidAsync("closeNav", null);
        }
    }

}
