using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class UnAuthorizedView
    {
        private string Url { get; set; }
        private bool IsOfficeJs { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            { 
            IsOfficeJs = await JsRuntime.InvokeAsync<bool>("CheckOfficeJs");
                if (IsOfficeJs)
                {
                    Url = "/login?ReturnUrl=/admin/plug-in/home";
                }
                else { 
                    Url = "/login";
                }
                StateHasChanged();
            }
        }

        private async Task NavigateToLogin()
        {
            NavigationManager.NavigateTo(Url, true);
        }
    }
}