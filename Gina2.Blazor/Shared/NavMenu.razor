﻿@implements IDisposable

<ul class="nav nav-pills menu-bar">
    <li class="nav-item">
        <NavLink class="nav-link" Match="NavLinkMatch.All" @onclick="CloseNavMenu" href="" data-cy="HomeLink">Home</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="map" @onclick=@(() => NavigateToMap("map")) Match="@NavLinkMatch.Prefix" data-cy="HomeMapLink">Map</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="countries" @onclick="CloseNavMenu" Match="@NavLinkMatch.Prefix" data-cy="HomeCountriesLink">Countries</NavLink>
    </li>
    @* <li class="nav-item">
            <NavLink class="nav-link" @onclick=@(() => NavigateToMap("#Nutritional")) href="#Nutritional" Match="@NavLinkMatch.All">Themes</NavLink>
        </li>*@

    <li class="nav-item">
        <NavLink class="nav-link" href="summaries" @onclick="CloseNavMenu" Match="@NavLinkMatch.Prefix" data-cy="HomeScorecardsLink">Scorecards and data summaries</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="search" @onclick="CloseNavMenu" Match="@NavLinkMatch.Prefix" data-cy="HomeSearchLink">Search</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="about-us" @onclick="CloseNavMenu" Match="@NavLinkMatch.Prefix" data-cy="HomeAboutGIFNALink">About GIFNA</NavLink>
    </li>
</ul>
