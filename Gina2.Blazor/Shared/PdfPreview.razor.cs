using Gina2.Blazor.Helpers;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Core;
using Gina2.Core.Methods;
using Gina2.Core.Constant;
using Microsoft.AspNetCore.Components;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System.Web;
using Microsoft.JSInterop;
using Gina2.Services.Policy;


namespace Gina2.Blazor.Shared
{
    public partial class PdfPreview : PageConfirgurationComponent
    {
        [Inject]
        private IConfiguration Configuration { get; set; }
        [Inject]
        private ILogger<PdfPreview> _logget{get; set;}
        [Inject]
        private FileStateService _fileStateService{get;set;}
        [Parameter]
        public string FileDisplayName { get; set; }
        [Inject]
        private IPolicyDraftService PolicyDraftService { get; set; }
        [Inject]
        private IJSRuntime JS { get; set; }

        private CloudBlobContainer cloudBlobContainer;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                string fileBlobName = _fileStateService.FileBlobName;
                if(string.IsNullOrEmpty(fileBlobName))
                {
                    var url = (await PolicyDraftService.GetAttachmentDetailsByFileName(FileDisplayName))?.Url;
                    fileBlobName = RemovePublicPrefix(url);
                }

                fileBlobName = HttpUtility.UrlDecode(fileBlobName);
                cloudBlobContainer = InitializeCloudBlobContainer();
                await LoadAndDisplayPdf(fileBlobName, FileDisplayName);
                
            }
            catch (Exception ex)
            {
                _logget.LogError($"Error initializing PDF preview: {ex.Message}");
                await OpenErrorToaster("Unable to load the PDF file.");
            }
        }
        private static string RemovePublicPrefix(string input)
        {
            const string prefix = "public://";
            return input.StartsWith(prefix) ? input.Substring(prefix.Length) : input;
        }


        private CloudBlobContainer InitializeCloudBlobContainer()
        {
            string blobStorageConnectionString = new AppSettingsHelper(Configuration).GetBlobStorageConnectionString();
            if (string.IsNullOrWhiteSpace(blobStorageConnectionString))
            {
                return null;
            }

            CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(blobStorageConnectionString);
            CloudBlobClient cloudBlobClient = cloudStorageAccount.CreateCloudBlobClient();
            return cloudBlobClient.GetContainerReference(Constants.BlobStorage.DataTypeContainerName);
        }

        private async Task LoadAndDisplayPdf(string fileBlobName, string fileDisplayName)
        {
            try
            {
                // Get the blob reference from the container
                CloudBlockBlob cloudBlockBlobbyFileBlobName = cloudBlobContainer.GetBlockBlobReference(fileBlobName);
                CloudBlockBlob cloudBlockBlobByDisplayName = cloudBlobContainer.GetBlockBlobReference(fileDisplayName);
                CloudBlockBlob cloudBlockBlob ;

                if( await cloudBlockBlobbyFileBlobName.ExistsAsync())
                    cloudBlockBlob = cloudBlockBlobbyFileBlobName;
                else
                    cloudBlockBlob = cloudBlockBlobByDisplayName;

                if (await cloudBlockBlob.ExistsAsync())
                {
                    // Fetch the blob attributes to get its length
                    await cloudBlockBlob.FetchAttributesAsync();

                    // Download the blob content as a byte array
                    byte[] fileBytes = new byte[cloudBlockBlob.Properties.Length];
                    await cloudBlockBlob.DownloadToByteArrayAsync(fileBytes, 0);

                    // Use JSInterop to load the PDF into the iframe
                    await JS.InvokeVoidAsync("loadPdfInIframe", fileBytes);
                }
                else
                {
                    _logget.LogError($"file {fileBlobName} not found");
                    await OpenErrorToaster("File not found");
                }
            }
            catch (Exception ex)
            {
                _logget.LogError($"Error while downloading the file: {ex.Message}");
                await OpenErrorToaster("Unable to download the file.");
            }
        }
    }
}
