using Blazored.Modal;
using Blazored.Modal.Services;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class AdminEditbut
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private IModalService ModalService { get; set; } // Inject IModalService

        [Parameter]
        public int Id { get; set; }

        [Parameter]
        public string Key { get; set; }

        [CascadingParameter]
        private BlazoredModalInstance BlazoredModal { get; set; } = default!;

        public bool IsLoading { get; set; } = false;

        private async Task AdminBar()
        {
            // Check if a modal is already open
            string isModal = MemoryCache.Get("modal")?.ToString();
            if (string.IsNullOrEmpty(isModal))
            {
                MemoryCache.Set("modal", "true");

                await ShowLoader(true);

                // Configure modal options
                var options = new ModalOptions()
                {
                    Position = ModalPosition.Middle,
                    DisableBackgroundCancel = true,
                    OverlayCustomClass = "_customscroll draggable",
                    HideCloseButton = true,
                };

                // Pass parameters to the modal
                var modelData = new ModalParameters
                {
                    { "Key", Key }
                };

                // Show the modal
                ModalService.Show<AdminEdit>("Update", modelData, options);

                await ShowLoader(false);
            }
        }

        private async Task ShowLoader(bool value)
        {
            // Update the loading state without blocking the UI thread
            IsLoading = value;
            await InvokeAsync(StateHasChanged); // Ensure UI updates
        }
    }
}