﻿
<CountryTabItem />
<Div>
    <Tabs Class="policies-list" SelectedTab="@SelectedListTab" SelectedTabChanged="@OnSelectedTabChanged">
        <Items>
            <Tab Name="flattenItemTab" data-cy=@($"ViewAll{TabTitle.Split(" ")[0]}")>View all @TabTitle</Tab>
            <Tab Name="categoryItemTab" data-cy=@($"{TypeofTitle.Split(" ")[0]}")>@TypeofTitle</Tab>
            <li>
                <Button Loading="@ExportLoading" data-cy=@($"{TabTitle.Split(" ")[0]}ExportBtn") Clicked="@Download" Class="but-yellow mob-t-hide"><Icon data-cy=@($"{TabTitle.Replace(" ","")}UpLoadIcon") class="fa-solid fa-upload" /> Export @TabTitle</Button>
            </li>
        </Items>
        <Content>
            <TabPanel Name="flattenItemTab">
                <FlattenItemList />
            </TabPanel>
            <TabPanel Name="categoryItemTab">
                <CategoryItemList />
            </TabPanel>
        </Content>
    </Tabs>
</Div>