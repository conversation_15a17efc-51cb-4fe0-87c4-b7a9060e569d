﻿@using Gina2.DbModels.Views
@using Microsoft.JSInterop;
@using System.Collections.ObjectModel
@using Gina2.Blazor.Models;

<Column>
    <Container Class="p-0">
        <Div Flex="Flex.JustifyContent.Between" Class="policies-fr2">
            <Div Class="item1">
                <Autocomplete TItem="ViewCountryDetails"
                              TValue="string"
                              Data="@Countries"
                              TextField="@(( item ) => item.CountryName)"
                              ValueField="@(( item ) => item.CountryCode)"
                              data-cy="RegionTypePlaceHolder"
                              Placeholder="Type a country"
                              Filter="AutocompleteFilter.StartsWith"
                              FreeTyping
                              @bind-SelectedValue="@selectedCountryCode"
                              SelectedTextChanged="OnCountrySearchTextChangedAsync"
                              CustomFilter="@(( item, searchValue ) => item.CountryName.IndexOf( searchValue, 0, StringComparison.CurrentCultureIgnoreCase ) >= 0 )">
                    <NotFoundContent> Sorry... @context was not found! </NotFoundContent>
                </Autocomplete>
            </Div>
            <Div Class="item2 mob-item">
                <Button Disabled="@isSearchDisabled" @onclick=@Navigate Class="but-blue pl-5 pr-5 _Navhight" data-cy="NavigationBtn">Navigate</Button>
            </Div>
        </Div>
    </Container>

    @if (Regions.Count() > 0)
    {
        <Container Class="pt-5 pb-5 pl-0 pr-0 mo-p-0">
        <Row>
            @foreach(var item in regionViews.Take(3))           
            {
                <Column Class="mapbox-item" >
                <Button id="@item.RegionCode" Clicked="@(() => ShowCountryList(item.RegionCode, item.RegionName, !ThreeRegionsEnabled, "ThreeRegionsEnabled"))">
                    <img  src="@item.RegionImageURl" data-cy=@($"{item.RegionName.Split(" ")[0]}Image") />
                    <Heading Size="HeadingSize.Is3" data-cy=@($"{item.RegionName.Split(" ")[0]}Btn") >@item.RegionName</Heading>
                </Button>
            </Column>
            }
        </Row>
        @if (ThreeRegionsEnabled)
        {
            <Div Class="list-cont">
                <Button Clicked="@MobileHideCountryList" Class="mobile-hide">x</Button>
                <CountryList @ref="threeRegionCountryList" RegionsName="@RegionsName" CategoryName="policies" CountryNames="@CountryNames" />
            </Div>
        }
        <Row>
            @foreach(var item in regionViews.TakeLast(3))           
            {
                <Column Class="mapbox-item" >
                <Button  id="@item.RegionCode" Clicked="@(() => ShowCountryList(item.RegionCode, item.RegionName, !TwoRegionsEnabled, "TwoRegionsEnabled"))">
                    <img src="@item.RegionImageURl" data-cy=@($"{item.RegionName.Split(" ")[0]}Image")/>
                    <Heading Size="HeadingSize.Is3"  data-cy=@($"{item.RegionName.Split(" ")[0]}Btn")>@item.RegionName</Heading>
                </Button>
            </Column>
            }
        </Row>
        @if (TwoRegionsEnabled)
        {
            <Div Class="list-cont">
                <Button Clicked="@MobileHideCountryList" Class="mobile-hide">x</Button>
                <CountryList @ref="twoRegionCountryList" RegionsName="@RegionsName" CategoryName="policies" CountryNames="@CountryNames" />
            </Div>
        }
    </Container>
    }
</Column>

