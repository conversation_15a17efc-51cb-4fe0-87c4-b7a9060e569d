﻿using DocumentFormat.OpenXml.Presentation;
using Gina2.Blazor.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class Header
    {
        [CascadingParameter]
        public SiteCustomizationDto layoutSetting { get; set; }

        [Parameter]
        public string Hesderst { get; set; } = "position:fixed;";
        private bool IsOfficeJs { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        protected override async Task OnInitializedAsync()
        {
            IsOfficeJs = await JsRuntime.InvokeAsync<bool>("CheckOfficeJs");

        }
        private async Task ToggleNavMenu()
        {
            await JsRuntime.InvokeVoidAsync("openNav", null);
        }
        void handleChange(string value)
        {
            Console.WriteLine(value);
        }
        void handleItemsChange(IEnumerable<string> value)
        {
            Console.WriteLine(value);
        }
    }
}
