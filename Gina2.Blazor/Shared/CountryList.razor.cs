﻿using Domain.Search;
using Gina2.Blazor.Models;
using Gina2.DbModels.Views;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Shared
{
    public partial class CountryList
    {
        [Parameter] public string RegionsName { get; set; }
        [Parameter] public IEnumerable<ViewCountryDetails> CountryNames { get; set; }
        [Parameter] public string CategoryName { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [CascadingParameter(Name = "Url")]
        public string SelectedDataUrl { get; set; }

        public IEnumerable<ViewCountryDetails> CountriesByMember { get; set; }
        public IEnumerable<ViewCountryDetails> FirstColumnCountries { get; set; }
        public IEnumerable<ViewCountryDetails> SecondColumnCountries { get; set; }
        public IEnumerable<ViewCountryDetails> ThirdColumnCountries { get; set; }
        private ObservableCollection<string> AlphabeticLists { get; set; } = new ObservableCollection<string>() {
        "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"
        };
        private ObservableCollection<AlhpabeticListsData> AlphabeticListsFilter { get; set; }
        public bool IsLoading { get; set; } = false;
        public class AlhpabeticListsData
        {
            public ObservableCollection<string> AlphabeticListsLetters { get; set; }
        }
        private int SelectedValue = 0;

        protected override async Task OnInitializedAsync()
        {
            GetCountriesByMember();
        }

        public void Refresh()
        {
            GetCountriesByMember();
        }

        private void GetCountriesByMember()
        {
            if (CountryNames != null)
            {
                CountriesByMember = SelectedValue == 0 ? CountryNames.Where(x => x.CountryStatus == "Member State") :
                    CountryNames.Where(x => x.CountryStatus != "Member State");
                CalcalulateCloumnCountry();
                StateHasChanged();

            }

        }

        private void CalcalulateCloumnCountry()
        {
            var total = CountriesByMember.Count();
            var perColumnCountry = total / 3.0;
            var perrow = (perColumnCountry % 1 == 0) ? Convert.ToInt16(perColumnCountry) : Convert.ToInt16(perColumnCountry) + 1;
            var allConutryList = CountriesByMember.ToList();
            FirstColumnCountries = allConutryList.Take(perrow).Distinct().ToList();
            SecondColumnCountries = allConutryList.Skip(perrow).Take(perrow).Distinct().ToList();
            ThirdColumnCountries = allConutryList.Skip(perrow + perrow).Take(perrow).Distinct().ToList();

        }

        private void OnSelectedValueChanged(int value)
        {
            SelectedValue = value;
            GetCountriesByMember();
        }

        private void NavigateToPolicies(string countryCode)
        {
            this.IsLoading = true;
            StateHasChanged();

            NavigationManager.NavigateTo($"countries/{countryCode}/{SelectedDataUrl}");
        }
    }
}