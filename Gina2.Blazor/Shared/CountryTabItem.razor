﻿
<Div Class="pol-hepar pb-3 flex-b">
    <Heading Size="HeadingSize.Is2"><AuthorizeView><Authorized>@AdminTitle</Authorized><NotAuthorized>@Title</NotAuthorized></AuthorizeView>  in @CountryName</Heading>
</Div>
<Div Class="pol-list">
    <Div Flex="Flex.JustifyContent.Between" Class="poli-but-flex">
        <Button data-cy="NutritionCountryProfile(NLiS)Btn" Class="but-blue ml-05 mr-05" Clicked="@(() => NutritionCountry($"https://apps.who.int/nutrition/landscape/report.aspx?iso={CountryCode}"))"><Span class="arow-img"><img src="img/arow-right.png" /></Span> Nutrition country profile (NLiS)</Button>
        
        @*once url will be corrected then i will set these*@
        @*<Button data-cy="CountryStatistics(GHO)Btn" Clicked="@(() => NutritionCountry($"https://www.who.int/data/gho"))" Class="but-blue ml-05 mr-05"><Span class="arow-img"><img src="img/arow-right.png" /></Span> Country statistics (GHO)</Button>*@
        
        <Button data-cy="WHOcountryPageBtn" Class="but-blue" Clicked="@(() => NutritionCountry($"https://www.who.int/countries/{CountryCode.ToLower()}"))">WHO country page <Span class="arow-img"><img src="img/arow-right.png" /></Span> </Button>
    </Div>   
</Div>