﻿<Container Class="_lastest-bg">
    <Div Flex="Flex.JustifyContent.End.AlignItems.End">
        <Column ColumnSize="ColumnSize.Is4.OnTablet.Is12.OnMobile.Is4.OnDesktop.Is4.OnWidescreen.Is4.OnFullHD" Class="_lastest_addition">
            <button Class="but closehome" @onclick="ToggleVisibility">
                Latest addition
            </button>
            <div Class="_lastest" style="display: @((isVisible ? "block" : "none"))">@((MarkupString)@Value)</div>
        </Column>
    </Div>
</Container>