using Domain.Terms;
using Blazorise.Snackbar;
using Microsoft.AspNetCore.Components;
using Gina2.Services.Vocabulary;
using AutoMapper;

namespace Gina2.Blazor.Shared.GTreeView
{
    public partial class CustomTreeViewComponent
    {
        [Inject]
        private IMapper _mapper { get; set; }
        [Inject]
        public IVocabularyService vocabularyService { get; set; }
        [Parameter]
        public bool IsDoubleTreeCheck { get; set; }
        [Parameter]
        public string SelectLeftOrRight { get; set; }

        [Parameter]
        public List<Term> TopicList { get; set; } = new List<Term>();
        [Parameter]
        public Term LeftOrRightTerm { get; set; } = new Term();
        [Parameter]
        public Term ParentTopic { get; set; } = new Term();
        [Parameter]
        public string ClassTreeView { get; set; }
        [Parameter]
        public string SearchTopic { get; set; }
        [Parameter]
        public int SearchBindTopicId { get; set; }

        [Parameter]
        public string Vocabulary { get; set; }

        [Parameter]
        public bool ShowTopicChild { get; set; }

        [Parameter]
        public bool EnableDoubleTree { get; set; }

        [Parameter]
        public bool ShowPartnerOricn2Child { get; set; }
        [Parameter]
        public bool IsAllChildCheck { get; set; }

        [Parameter]
        public bool HideChildAddButton { get; set; }
        [Parameter]
        public bool DeleteSingleData { get; set; }

        [Parameter]
        public bool SearchParentCheck { get; set; }

        [Parameter]
        public bool ChildParentCheck { get; set; }

        [Parameter]
        public IEnumerable<Gina2.DbModels.TopicParent> AllParentTopics { get; set; }

        [Parameter]
        public IEnumerable<Gina2.DbModels.Sdg> SdgData { get; set; }

        [Parameter]
        public EventCallback SaveUpdateOrderTerms { get; set; }
        [Parameter]
        public EventCallback<Term> Addchild { get; set; }

        [Parameter]
        public EventCallback<Term> Editchild { get; set; }

        [Parameter]
        public EventCallback<Term> Dropchild { get; set; }

        [Parameter]
        public EventCallback<Term> OpenChild { get; set; }
        [Parameter]
        public EventCallback<Term> ParentChanging { get; set; }
        [Parameter]
        public EventCallback<(bool, Term, string, Term)> OnChangeTreeChild { get; set; }

        [Parameter]
        public bool ShowTreeCheckBox { get; set; }
        public bool isLoadingTerms { get; set; }
        public bool ChildVisible { get; set; }

        private List<int> DoubleTree { get; set; } = new List<int>()
        {1};
        public SnackbarStack SnackbarStack { get; set; } = new();

        public void AddNewChildTerm(Term term)
        {
            Addchild.InvokeAsync(term);
        }

        public void EditChildTermPopupAsync(Term term)
        {
            Editchild.InvokeAsync(term);
        }

        private async Task AddChildInTerm(Term term, List<Term> TreeList)
        {
            var childTopic = await vocabularyService.GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
            foreach (var item in childTopic)
            {
                TreeList.Add(item);
                await AddChildInTerm(item, TreeList);
            }
        }

        private async Task RemoveChildInTerm(Term term, List<Term> TreeList)
        {
            var childTopic = await vocabularyService.GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
            foreach (var item in childTopic)
            {
                TreeList.Remove(item);
                await AddChildInTerm(item, TreeList);
            }
        }
        private async Task OnSearchTreeChild(bool check, Term term, string leftOrRight, Term parent)
        {
            if (!check && term.ParentId == null)
            {
                term.IsAllChildCheck = false;
            }
            isLoadingTerms = true;
            term.IsSearchCheck = check;
            term.IsAllChildCheck = check;
            term.Child = new();
            term.IsPlusIcon = false;
            term.IsParentAllChildCheck = !parent.Child.Any(p => p.IsAllChildCheck == false);
            term.IsParentSearchCheck = parent.Child.Any(p => p.IsSearchCheck == true);
            parent.IsAllChildCheck = !parent.Child.Any(p => p.IsAllChildCheck == false);
            parent.IsSearchCheck = parent.Child.Any(p => p.IsSearchCheck == true);
            //await SetTermChildrenSelection(term, check);
            //var childTopic = await vocabularyService.GetTermsByIdAsync(Vocabulary, parent.Id, parent.Code);
            //parent.Child = childTopic;
            //parent.IsAllChildCheck = childTopic.Any(p => p.IsSearchCheck == false);
            _=ParentChanging.InvokeAsync(parent);

            isLoadingTerms = false;
            _ = Task.Run(async () => await InvokeAsync(StateHasChanged));

            //await SnackbarStack.PushAsync($"Updated the term: '{term.Name}'", SnackbarColor.Success, options => options.IntervalBeforeClose = 2000);
        }


        //private async Task SetTermChildrenSelection(Term term, bool check)
        //{
        //    foreach (var item in term.Child)
        //    {
        //        item.IsCheck = check;

        //        if (item.Child?.Any() == true)
        //        {
        //            _ = SetTermChildrenSelection(item, check);
        //        }
        //    }

        //    await vocabularyService.SetTermChildrenSelection(term, check);
        //}
        public void OnSelectTree(bool check, Term term, string value)
        {
            IsDoubleTreeCheck = check;
            var childterm = TopicList.FirstOrDefault(t => t.Id == term.Id);
            if (childterm != null)
            {
                childterm.IsSelectedCheck = check;
            }
            //var IsParentAllChildCheck = ParentTopic.Child.Any(p => p.IsAllChildCheck == true);
            //var IsParentSearchCheck = ParentTopic.Child.Any(p => p.IsSearchCheck == true);
            OnChangeTreeChild.InvokeAsync((check, term, value, ParentTopic));
        }

        private void ParentChildChanging(Term parent)
        {
            var parentTerm = TopicList.FirstOrDefault(t => t.Id == parent.Id);
            parentTerm.IsAllChildCheck = parent.IsAllChildCheck;
            parentTerm.IsSearchCheck = parent.IsSearchCheck;
            ParentTopic.IsAllChildCheck = !ParentTopic.Child.Any(p => p.IsAllChildCheck == false);
            ParentTopic.IsSearchCheck = parent.Child.Any(p => p.IsSearchCheck == true);
            //ParentTopic.IsSearchCheck = parent.IsSearchCheck;
            ParentChanging.InvokeAsync(ParentTopic);
        }
        public async Task<List<Term>> OpenParent(Term term)
        {
            if (EnableDoubleTree)
            {
                if (!term.IsPlusIcon)
                {
                    term.IsPlusIcon = true;
                    term.Child = await vocabularyService.GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
                    foreach (var item in term.Child)
                    {
                        item.AllParentId.Add(term.Id);
                        item.AllParentId.AddRange(term.AllParentId);
                        var childTopic = await vocabularyService.GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                        //if (item.IsSearchCheck)
                        //{ 
                        //item.IsAllChildCheck = childTopic.Any(t => t.IsSearchCheck == false);
                        //}
                        item.IsCheck = term.IsSelectedCheck;
                        item.IsTopicParent = childTopic.Count > 0 ? false : true;
                    }
                    return term.Child;
                }
                else
                {
                    term.IsPlusIcon = false;
                    return term.Child = new List<Term>();
                }
            }
            else {

                if (!term.IsPlusIcon)
                {
                    term.IsPlusIcon = true;
                    term.Child = await vocabularyService.GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
                    foreach (var item in term.Child)
                    {
                        item.AllParentId.Add(term.Id);
                        item.AllParentId.AddRange(term.AllParentId);
                        var childTopic = await vocabularyService.GetTermsByIdAsync(Vocabulary, item.Id, item.Code);
                        //if (item.IsSearchCheck)
                        //{ 
                        //item.IsAllChildCheck = childTopic.Any(t => t.IsSearchCheck == false);
                        //}
                        item.IsTopicParent = childTopic.Count > 0 ? false : true;
                    }
                    return term.Child;
                }
                else
                {
                    term.IsPlusIcon = false;
                    return term.Child = new List<Term>();
                }
            }
        }

        private async Task OnDropItem(Term term)
        {
            await Dropchild.InvokeAsync(term);
        }

        private async Task UpOrDown(Term parent, Term term, int movementKey, List<Term> terms, Term LeftOrRightTerm)
        {
            int oldIndex = terms.IndexOf(term);
            int newIndex = oldIndex + movementKey;
            if (newIndex >= 0 && newIndex < terms.Count)
            {
                terms.Remove(term);
                terms.Insert(newIndex, term);
                if (LeftOrRightTerm.IsPlusIcon)
                { 
                LeftOrRightTerm.Child = terms;
                }
                await SaveUpdateOrderTerms.InvokeAsync();
            }
               
        }

        private Term DeleteTopic { get; set; } = new();
        private bool DeleteVisible = false;
        private void ShowDeleteModal(Term term)
        {
            DeleteTopic = term;
            DeleteVisible = true;
        }
        private async Task DeleteTopics()
        {
            TopicList.Remove(DeleteTopic);
            DeleteVisible = false;
            var topic = _mapper.Map<DbModels.Topic>(DeleteTopic);
            await vocabularyService.DeleteTopics(DeleteTopic, Vocabulary);
        }
    }
}