﻿<Repeater Items="@SelectTreeTerms" Context="Tree">
    <Div Class="_selectchlid-tree" Style="@ChildPadding">
        <Div Class="drag-1">
            <Icon Style="cursor: pointer;" Class="@( Tree.IsPlusIcon ? "fas fa-chevron-down" : "fas fa-chevron-right")" Clicked="@(async () => await ExpandChild(Tree))" />
              <Radio CheckedChanged="@(value => SelectExistingChildTopic(Tree))"  TValue="int" Group="colors" Value="@Tree.Id">@Tree.Name</Radio>
        </Div>
            
            @if (Tree.Child.Count > 0)
            {
            string leftpadding= $"padding-left:25px";
            <Gina2.Blazor.Shared.GTreeView.CustomDropdownTreeView
                ChildPadding="@leftpadding"
                ExistingChild="@SelectExistingChildTopic"
                Vocabulary="@Vocabulary"
                SelectTreeTerms="@Tree.Child" />

            }
    </Div>
</Repeater>