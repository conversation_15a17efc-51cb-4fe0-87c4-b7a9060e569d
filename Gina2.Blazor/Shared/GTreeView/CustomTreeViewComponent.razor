﻿@using Domain.Terms;

<Modal @bind-Visible="@DeleteVisible" Class="modals-lg _modalcenter">
    <ModalContent Centered Class="forms">
        <ModalHeader>
            <ModalTitle>Are you sure want to Delete @DeleteTopic.Name ?</ModalTitle>
        </ModalHeader>
        <ModalFooter>
            <Button Class="_but-delete pl-2 pr-2" Clicked="@DeleteTopics">Delete</Button>
            <Button Class="but-yellow pl-2 pr-2" Clicked="@(() => DeleteVisible = false)">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Loader IsLoading="@isLoadingTerms" />

<Div Class="@ClassTreeView">
    <Repeater Items="@TopicList">
        <Div Class="_taxonomies" Style="@(DeleteSingleData ? "display: none;" : "")" Flex="Flex.JustifyContent.Between" draggable="true">
            <Div Class="drag-1">
                @if (!ShowTopicChild)
                {
                    <Tooltip hidden="@context.IsTopicParent" Text="Open Child">
                        <Icon Style="cursor: pointer; padding-right:5px;" Class="@( context.IsPlusIcon ? "fas fa-chevron-down" : "fas fa-chevron-right")" Clicked="@(async () => await OpenParent(context))" />
                    </Tooltip>
                }

                @if (ShowTreeCheckBox)
                {
                    <Check TValue="bool" Checked="@context.IsAllChildCheck"
                    Class="@(context.IsSearchCheck == true && context.IsAllChildCheck == false ? "_Checked" : "")" CheckedChanged="@(value => OnSearchTreeChild(value, context, SelectLeftOrRight, ParentTopic))" />
                }
                    <Check hidden = "@(EnableDoubleTree ? false : true)" TValue="bool" Checked="@context.IsCheck" CheckedChanged="@(value => OnSelectTree(value, context, SelectLeftOrRight))" />


                <Tooltip Text="@context.Description">
                    <NavLink href="@($"/vocabularies/{Vocabulary}/terms/{context.Id.ToString()}/references")">
                        <Div id="@(!string.IsNullOrEmpty(SearchTopic) && SearchTopic == context.Name ? $"_searchscroll{context.Id}" : "")"
                        Style="@(!string.IsNullOrEmpty(SearchTopic) && SearchTopic == context.Name ? "color:orange" : "")">@context.Name</Div>
                    </NavLink>
                </Tooltip>
            </Div>
            @if (!ShowTreeCheckBox)
            {
                <Div Class="drag-2 _dropbutton">
                    <Tooltip Text="Up">
                        <Icon Name="IconName.ArrowUp" Clicked="@(async () => await UpOrDown(ParentTopic, context, -1, TopicList, LeftOrRightTerm))" />
                    </Tooltip>
                    <Tooltip Text="Down">
                        <Icon Name="IconName.ArrowDown" Clicked="@(async () => await UpOrDown(ParentTopic, context, 1, TopicList, LeftOrRightTerm))" />
                    </Tooltip>
                    <Tooltip Text="Edit the Term">
                        <Icon Class="fa-solid fa-pen" Clicked="@(async () => EditChildTermPopupAsync(context))" />
                    </Tooltip>
                    <Tooltip Text="Delete the Term">
                                        <Icon Class="fa-solid fa-trash" Clicked="@( () =>  ShowDeleteModal(context))"/>
                                    </Tooltip>
                    @if (!ShowTopicChild)
                    {
                        <Tooltip Text="Add the Child">
                            <Icon Class="fa-solid fa-plus" Clicked="@(async () => AddNewChildTerm(context))" />
                        </Tooltip>
                    }

                </Div>
            }

        </Div>
        @if (context.Child.Count > 0)
        {
            <Gina2.Blazor.Shared.GTreeView.CustomTreeViewComponent HideChildAddButton="@HideChildAddButton"
                                EnableDoubleTree="@EnableDoubleTree"
                                SearchBindTopicId="@SearchBindTopicId"
                                SearchParentCheck="@SearchParentCheck"
                                ChildParentCheck = "@ChildParentCheck"
                                ParentChanging="@ParentChildChanging"
                                IsAllChildCheck = "@IsAllChildCheck"
                                                               DeleteSingleData="@DeleteSingleData"
                                                               OnChangeTreeChild="@(arg => OnSelectTree(arg.Item1, arg.Item2, arg.Item3))"
                                                               SelectLeftOrRight="@SelectLeftOrRight"
                                                               ShowTopicChild="@ShowTopicChild"
                                                               ShowPartnerOricn2Child="@ShowPartnerOricn2Child"
                                                               Vocabulary="@Vocabulary"
                                                               Dropchild="@OnDropItem"
                                                               SearchTopic="@SearchTopic"
                                                               OpenChild="OpenParent"
                                                               Editchild="EditChildTermPopupAsync"
                                                               Addchild="AddNewChildTerm"
                                                               TopicList="@context.Child"
                                                               AllParentTopics="@AllParentTopics"
                                                               SdgData="@SdgData"
                                                               ParentTopic="@context"
                                                               LeftOrRightTerm="@TopicList.Where(t => t.Id == context.Id).FirstOrDefault()"
                                                               SaveUpdateOrderTerms="@SaveUpdateOrderTerms"
                                                               ClassTreeView="_taxon content _customChild"
                                                               ShowTreeCheckBox="ShowTreeCheckBox" />

        }
    </Repeater>
</Div>




