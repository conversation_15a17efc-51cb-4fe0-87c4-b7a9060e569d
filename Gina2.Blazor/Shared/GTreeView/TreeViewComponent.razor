﻿@using Gina2.Core.Models;
<AntDesign.Tree ShowIcon
                MatchedClass="site-tree-search-value"
                DataSource="TopicList"
                TItem="GTreeNode"
                TitleExpression="x => x.DataItem.Title"
                ChildrenExpression="x => {
if(x.DataItem.TopicId == TreeTopicId)
{
x.Checked = true;
}
return x.DataItem.Children;
}"
                OnCheck="x => TopicTreeCheckboxClicked(x)"
                Checkable>
</AntDesign.Tree>