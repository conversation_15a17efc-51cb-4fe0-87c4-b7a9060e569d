using Domain.Terms;
using Gina2.Services.Vocabulary;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared.GTreeView
{
    public partial class CustomDropdownTreeView
    {
        [Inject]
        public IVocabularyService VocabularyService { get; set; }
        [Parameter]
        public List<Term> SelectTreeTerms { get; set; } = new List<Term>();
        [Parameter]
        public string Vocabulary { get; set; }
        [Parameter]
        public string ChildPadding { get; set; }
        [Parameter]
        public int LeftPadding { get; set; }
        [Parameter]
        public EventCallback<Term> ExistingChild { get; set; }

        private async Task SelectExistingChildTopic(Term term)
        {
            _= ExistingChild.InvokeAsync(term);
        }
        public async Task<List<Term>> ExpandChild(Term term)
        {
            if (!term.IsPlusIcon)
            {
                term.IsPlusIcon = true;
                term.Child = await VocabularyService.GetTermsByIdAsync(Vocabulary, term.Id, term.Code);
                return term.Child;
            }
            else
            {
                term.IsPlusIcon = false;
                return term.Child = new List<Term>();
            }

        }
    }
}