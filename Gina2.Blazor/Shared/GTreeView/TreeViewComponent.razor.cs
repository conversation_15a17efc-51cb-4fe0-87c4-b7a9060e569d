using AntDesign;
using Gina2.Core.Models;
using Gina2.DbModels;
using Gina2.Services.Policy;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared.GTreeView
{
    public partial class TreeViewComponent
    {
        [Inject]
        private IPolicyService PolicySQLService { get; set; }

        [Parameter]
        public EventCallback<TreeEventArgs<GTreeNode>> Changed { get; set; }

        [CascadingParameter(Name = "selectedTopic")]
        public string SelectedTopic { get; set; }

        [CascadingParameter(Name = "TreeTopicId")]
        public int TreeTopicId { get; set; }
        public class ListTopics
        {
            public string ParentName { get; set; }
            public List<ListTopics> ChildName { get; set; }
            public bool isSelected { get; set; }
        }

        [Parameter]
        public List<GTreeNode> TopicList { get; set; } = new List<GTreeNode>();

        private List<Topic> AllTopics = new();
        private List<TopicParent> AllParentTopics = new();
        private List<int> topicparent { get; set; } = new List<int>();

        private int PreTopicId { get; set; }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender || TreeTopicId != 0)
            {
                await GetTopicsAsync();
                StateHasChanged();
            }
        }

        private async Task GetTopicsAsync()
        {
            AllTopics = await PolicySQLService.GetTopicsAsync();
            AllParentTopics = await PolicySQLService.GetParentTopics();
            var First = AllParentTopics.Where(x => x.ParentId.Equals(AllTopics.Where(t => t.Name.Equals(SelectedTopic)).FirstOrDefault().Id));
            await GetTopicTreeView(First);
        }

        private async Task GetTopicTreeView(IEnumerable<TopicParent> First)
        {
            foreach (var item in First)
            {
                TopicList.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId && t.AllowedInAction == true).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList()),
                    IsSelected = false
                });
            }
        }
        private async Task<List<GTreeNode>> GetChildTopicTreeView(IEnumerable<TopicParent> First)
        {
            List<GTreeNode> child = new List<GTreeNode>();
            foreach (var item in First)
            {
                GTreeNode itemchild = new GTreeNode();
                child.Add(new GTreeNode()
                {
                    TopicId = item.TopicId,
                    ParentId = item.ParentId,
                    Title = AllTopics.Where(t => t.Id == item.TopicId && t.AllowedInAction == true).FirstOrDefault().Name,
                    Children = await GetChildTopicTreeView(AllParentTopics.Where(x => x.ParentId.Equals(item.TopicId)).ToList()),
                    IsSelected = false
                });
            }
            return child;
        }

        private TreeEventArgs<GTreeNode> PreviousCheckedNode = new TreeEventArgs<GTreeNode>();

        private async Task TopicTreeCheckboxClicked(TreeEventArgs<GTreeNode> checkedValue)
        {
            if (PreviousCheckedNode.Node != null)
            {
                PreviousCheckedNode.Node.Checked = false;
            }
            //if (PreTopicId != null)
            //{ 
            //var a = TopicList.t
            //}
            //checkedValue.TargetNode.Checked = true;
            checkedValue.Node.Checked = true;
            PreviousCheckedNode = checkedValue;
            //checkedValue.Node.pre
            await Changed.InvokeAsync(checkedValue);
            
        }
    }
}