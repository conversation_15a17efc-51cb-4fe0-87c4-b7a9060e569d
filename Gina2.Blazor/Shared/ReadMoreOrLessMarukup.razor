﻿<div>
    @if (showFullText)
    {
        <Paragraph Margin="Margin.Is1.FromBottom">
            @Content
            <a hidden="@(Content.Value.Length <= TruncateLength)" class="ReadMore" @onclick="ToggleShowFullText">Read less</a>
        </Paragraph>
    }
    else
    {
        <Paragraph Margin="Margin.Is1.FromBottom">
            @TruncatedContent
            <a hidden="@(TruncatedContent.Value.Length <= TruncateLength)" class="ReadMore" @onclick="ToggleShowFullText">Read more</a>
        </Paragraph>
    }
</div>

@code {
    [Parameter]
    public MarkupString Content { get; set; }

    [Parameter]
    public int TruncateLength { get; set; } = 100;

    private bool showFullText = false;

    private MarkupString TruncatedContent
    {
        get
        {
            var text = Content.ToString();
            return new MarkupString(text.Length > TruncateLength ? $"{text.Substring(0, TruncateLength)}..." : text);
        }
    }

    private void ToggleShowFullText()
    {
        showFullText = !showFullText;
    }
}
