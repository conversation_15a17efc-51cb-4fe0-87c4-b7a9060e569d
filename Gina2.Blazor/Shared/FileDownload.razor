﻿@using Domain.Search
@using Gina2.Blazor.Helpers
<Divider Class="divi-blue" />
<Div Flex="Flex.JustifyContent.Between" Class="poli-flex" data-cy="FilterDataItemsText">Download:</Div>
<Div Flex="Flex.JustifyContent.Between" Class="_loading-bg poli-flex cssvmaps mob-f-column">

    <Blazorise.Button Class="csvdiv _cuser-no" Loading="@IsPolicyDownload" Disabled="@(!SearchRequest.IsPolicyDataTypeSelected || NoOfPolices == 0 || IsPolicyDownload)">
        <Div><h3><span class="p-0">Policies</span><span class="p-0"> (@NoOfPolices) @NoOfCountriesInPolicy</span> </h3></Div>
        <div class="dropdown but-csv-dow">
            <button class="dropbtn"><Icon class="arrow-bottom" data-cy="CSVBtn1" /> Export</button>
            <div class="dropdown-content">
                <a class="dropdown-item" @onclick="@(e => PolicyDownloadClicked(true))" data-cy="DownloadDataItemBtn1">Download by data items</a>
                <a class="dropdown-item" @onclick="@(e => PolicyDownloadClicked(false))" data-cy="DownloadCountriesBtn1">Download by countries</a>
            </div>
        </div>
    </Blazorise.Button>
    <Blazorise.Button Class="csvdiv _cuser-no" Loading="@IsActionDownloading" Disabled="@(!SearchRequest.IsPragrammesAndActionsDataTypeSelected || NoOfActions == 0 || IsActionDownloading)">
        <Div><h3><span class="p-0">Programmes and actions</span><span class="p-0"> (@NoOfActions) @NoOfCountriesInActions</span> </h3></Div>
        <div class="dropdown but-csv-dow">
            <button class="dropbtn"><Icon class="arrow-bottom" data-cy="CSVBtn2" /> Export</button>
            <div class="dropdown-content">
                <a class="dropdown-item" @onclick="@(e=>ActionDownloadClicked(true))" data-cy="DownloadDataItemBtn2">Download by data items</a>
                <a class="dropdown-item" @onclick="@(e=>ActionDownloadClicked(false))" data-cy="DownloadCountriesBtn2">Download by countries</a>
            </div>
        </div>
    </Blazorise.Button>
    <Blazorise.Button Class="csvdiv _cuser-no" Loading="@IsMechanismDownload" Disabled="@(!SearchRequest.IsMechanicalDataTypeSelected || NoOfMechanisms == 0 || IsMechanismDownload)">
        <Div><h3><span class="p-0">Mechanisms</span><span class="p-0"> (@NoOfMechanisms) @NoOfCountriesInMechanism</span></h3></Div>
        <div class="dropdown but-csv-dow">
            <button class="dropbtn"><Icon class="arrow-bottom" data-cy="CSVBtn3" /> Export</button>
            <div class="dropdown-content">
                <a class="dropdown-item" @onclick="@(e => MechanismDownloadClicked(true))" data-cy="DownloadDataItemBtn3">Download by data items</a>
                <a class="dropdown-item" @onclick="@(e => MechanismDownloadClicked(false))" data-cy="DownloadCountriesBtn3">Download by countries</a>
            </div>
        </div>
    </Blazorise.Button>
    <Blazorise.Button Class="csvdiv _cuser-no" Loading="@IsCommitmentDownload" Disabled="@(!SearchRequest.IsCommitmentsDataTypeSelected || NoOfSmartComitments == 0 || IsCommitmentDownload)">
        <Div><h3><span class="p-0">SMART commitments</span><span class="p-0"> (@NoOfSmartComitments) @NoOfCountriesInSmartComitments</span> </h3></Div>
        <div class="dropdown but-csv-dow">
            <button class="dropbtn"><Icon class="arrow-bottom" data-cy="CSVBtn4" /> Export</button>
            <div class="dropdown-content">
                <a class="dropdown-item" @onclick="@(e=> CommitmentDownloadClicked(true))" data-cy="DownloadDataItemBtn4">Download by data items</a>
                <a class="dropdown-item" @onclick="@(e=> CommitmentDownloadClicked(false))" data-cy="DownloadCountriesBtn4">Download by countries</a>
            </div>
        </div>
    </Blazorise.Button>
</Div>
<style>
    ._loading-bg button {
        color: #212529
    }
</style>