﻿using AntDesign;
using Blazorise;
using Blazorise.DataGrid;
using Domain.Search;
using Gina2.MySqlRepository.Models;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Gina2.Services.Policy;
using Gina2.Services.Search;
using Gina2.SqlRepository.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Microsoft.JSInterop;
using System.Diagnostics;

namespace Gina2.Blazor.Shared
{
    public partial class DataGridforSearch
    {
        [Inject]
        private ISearchService SearchService { get; set; }

        [Parameter]
        public string FileType { get; set; } = "map";

        [Parameter]
        public EventCallback<Domain.Search.SearchResultCounts> SendCountInforomSpToParentEvent { get; set; }

        [Parameter]
        public EventCallback<Dictionary<string, object>> SendDataForFileDownloadEvent { get; set; }
        public GlobalSearchRequest SearchRequest { get; set; } = new GlobalSearchRequest();
        private Domain.Search.SearchResponse searchResponse = new();
        public string TotalRecordMessage { get; set; }

        private bool initiatedfromSearchButton = false;
        private DataGrid<Domain.Search.SearchResult> SearchDataGrid;
        private bool isSorting = false;
        public int TotalFilteredCount { get; set; }
        public bool IsSearchedClicked { get; set; } = false;
        private bool isSearchInitiated = false;
        private readonly List<Domain.Search.SearchResult> selectedEntries = new();
        private IEnumerable<Domain.Search.SearchResult> SearchResults = new List<Domain.Search.SearchResult>();
        public SearchResultCounts SearchResultCounts = new();
        public int TotalItems;
        private Visibility loaderVisibility;

        public bool IsAccordianLoading { get; set; } = false;

        protected override async Task OnInitializedAsync()
        {
           
           _= RefreshDataGrid();
        }
        public async Task RefreshDataGrid()
        {
            _ = ToggleLoader(true);
            
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var data = await SearchService.SearchAsync(SearchRequest);
            SearchResults = data.SearchResults;
            stopwatch.Stop();
            Console.WriteLine($"search grid calling time: {stopwatch.Elapsed}");
            TotalItems = data.TotalFilteredCount;
            _ = ToggleLoader(false);
            
            await InvokeAsync(StateHasChanged);
        }
        
        private async Task OnSortChanged(DataGridSortChangedEventArgs e)
        {
            _ = ToggleLoader(true);
            
            isSorting = true;
            SearchRequest.SortingColumn = GetSortingColumn(e.FieldName);
            SearchRequest.IsDescendingOrder = e.SortDirection == Blazorise.SortDirection.Descending;
            _ = ToggleLoader(false);
           
        }
       public async Task ToggleLoader(bool value)
        {
            IsAccordianLoading = value;
            loaderVisibility = value?Visibility.Visible:Visibility.Invisible;
            await InvokeAsync(StateHasChanged);
            if(value)
            {
                await Task.Delay(500);   
            }
        }


        private async Task OnReadData(DataGridReadDataEventArgs<SearchResult> e)
        {
            if (!isSearchInitiated)
            {
                isSearchInitiated = true;
                return;
            }

            if (!e.CancellationToken.IsCancellationRequested)
            {
                
                
                if (e.ReadDataMode is DataGridReadDataMode.Paging)
                {
                    SearchRequest.PageNo = initiatedfromSearchButton || isSorting || e.PageSize != SearchRequest.PageSize ? GlobalSearchRequest.DefaultPageNo : e.Page;
                    SearchRequest.PageSize = e.PageSize;
                }

                isSorting = false;
                
                _ = RefreshDataGrid();
                
            }
           
        }

        private static Services.Models.SearchSortingColumn GetSortingColumn(string fieldName) => fieldName switch
        {
            "Title" => Services.Models.SearchSortingColumn.Title,
            "Type" => Services.Models.SearchSortingColumn.Type,
            "StartYear" => Services.Models.SearchSortingColumn.StartYear,
            "EndYear" => Services.Models.SearchSortingColumn.EndYear,
            "CountryList" => Services.Models.SearchSortingColumn.CountryList,
            "RegionCode" => Services.Models.SearchSortingColumn.RegionCode,
            _ => Services.Models.SearchSortingColumn.Title
        };

        public void SelectDeselectCheckBox(GlobalSearchRequest searchRequest)
        {
            searchResponse.SearchResults.ForEach(r => r.IsSelected = searchRequest.IsAllSelected);
            if (!searchRequest.IsAllSelected)
            {
                selectedEntries.Clear();
            }
            else
            {
                searchResponse.SearchResults.ForEach(r =>
                {
                    selectedEntries.AddIf(!selectedEntries.Exists(i => i.UniqueKey.Equals(r.UniqueKey)), r);
                });
            }
            StateHasChanged();
        }
    }
}