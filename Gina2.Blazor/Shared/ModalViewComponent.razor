﻿@using Ra<PERSON>zen
@typeparam T
<Modal @ref="SaveModal">
    <ModalContent Centered>
        <ModalHeader>
            <ModalTitle>@Title</ModalTitle>
            <CloseButton Clicked="@HideModal" />
        </ModalHeader>
        <ModalBody>
            <EditForm Model="@SelectedData" id="FrmAddEdit">
                <DataAnnotationsValidator />
                <FormComponent T="T" Columns="Columns" SelectedData="SelectedData" />
            </EditForm>
        </ModalBody>
        <ModalFooter>
            <button type="submit" class="btn btn-primary" form="FrmAddEdit" @onclick="OnSave">Save</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal" @onclick="() => HideModal()">Close</button>
        </ModalFooter>
    </ModalContent>
</Modal>