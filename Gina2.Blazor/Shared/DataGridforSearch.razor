﻿@using Domain.Search
@using Gina2.Blazor.Helpers
@using Gina2.SqlRepository.Models
<Divider Class="divi-blue" />
<Div Class="DataGrids paginationareaa">
    <AccordianLoader IsAccordianLoading="@IsAccordianLoading" loaderVisibilitys="@loaderVisibility" />
    <DataGrid PageSizes="new[] { 50,100,250,500,1000 }" @ref="@SearchDataGrid"
              Class="_checkgrid"
              FixedHeaderDataGridMaxHeight="500px"
              FixedHeaderDataGridHeight="450px"
              TItem="@SearchResult"
              Data="@SearchResults"
              ReadData="@OnReadData"
              TotalItems="@TotalItems"
              PageSize="SearchRequest.PageSize"
              ShowPageSizes
              ShowPager
              Responsive
              CurrentPage="SearchRequest.PageNo"
              SortChanged="@OnSortChanged"
              SortMode="DataGridSortMode.Single">
        <EmptyTemplate>
            <Div>No data found.</Div>
        </EmptyTemplate>
        <DataGridColumns>
            <DataGridColumn Field="@nameof(MapandSearchTableViewModelFromSp.RegionCode)" Caption="WHO region" Sortable="true" Width="16%" />
            <DataGridColumn Field="@nameof(MapandSearchTableViewModelFromSp.CountryList)" Caption="Country" Sortable="true" Width="15%" />
            <DataGridColumn Caption="Title" Sortable="true" Field="@nameof(MapandSearchTableViewModelFromSp.Title)" Width="40%">
                <DisplayTemplate>
                    <NavLink href="@context?.DetailsUrl" target="_blank">@TextHighlighter.Highlight(@context?.Title, @SearchRequest.SearchText)</NavLink>
                </DisplayTemplate>
            </DataGridColumn>
            <DataGridColumn Field="@nameof(MapandSearchTableViewModelFromSp.Type)" Caption="Type" Width="12%" Sortable="true" />
            <DataGridColumn Field="@nameof(MapandSearchTableViewModelFromSp.StartYear)" Caption="Start year" Sortable="true" Width="14%" />
        </DataGridColumns>
        <ItemsPerPageTemplate></ItemsPerPageTemplate>
        <TotalItemsTemplate>
            <Badge TextColor="TextColor.Dark">
                @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
            </Badge>
        </TotalItemsTemplate>
    </DataGrid>
</Div>
