﻿<BarMenu>
    <BarStart>
        @if (Convert.ToBoolean(Configuration["EnablePhase2Features"]))
        {
            <BarItem data-cy="dashboard">
                <BarLink To="admin/dashboard" @onclick="CloseNavMenu">
                    <BarIcon Class="fas fa-th-large" />
                    Dashboard
                </BarLink>
            </BarItem>
        }

        <BarItem data-cy="myProfile">
            <BarLink To="admin/profile" @onclick="CloseNavMenu">
                <BarIcon IconName="IconName.User" />
                My profile
            </BarLink>
        </BarItem>
        <AuthorizeView Roles="Admin">
            <BarItem >
                <BarDropdown id="indicator">
                    <BarDropdownToggle data-cy="indicator" @onclick="@(()=> ToggleMenu("indicator"))">
                        <BarIcon Class="far fa-map" />
                        Indicator
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem data-cy="indicatorManagement" To="admin/indicatortype" @onclick="CloseNavMenu">
                            Indicator management
                        </BarDropdownItem>
                        <BarDropdownItem data-cy="indicatorValue" To="/admin/mapindicator" @onclick="CloseNavMenu">
                            Indicator value
                        </BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>
            <BarItem data-cy="taxanomies">
                <BarLink To="admin/taxonomies" @onclick="CloseNavMenu">
                    <BarIcon Class="far fa-file-alt" />
                    Taxonomies
                </BarLink>
            </BarItem>
            @if (Convert.ToBoolean(Configuration["EnablePhase2Features"]))
            {
                <BarItem data-cy="bulkImportAndExport">
                    <BarLink To="admin/bulkimportexport" @onclick="CloseNavMenu">
                        <BarIcon Class="far fa-file-alt" />
                        Bulk import & export
                    </BarLink>
                </BarItem>

                <BarItem data-cy="composeMail">
                    <BarLink To="admin/composeemail" @onclick="CloseNavMenu">
                        <BarIcon Class="far fa-file-alt" />
                        Compose email
                    </BarLink>
                </BarItem>

                <BarItem data-cy="static">
                    <BarLink To="/admin/static" @onclick="CloseNavMenu">
                        <BarIcon Class="far fa-file-alt" />
                        Static
                    </BarLink>
                </BarItem>
            }
        </AuthorizeView>
        @if (Convert.ToBoolean(Configuration["EnablePhase2Features"]))
        {
            <BarItem data-cy="content">
                <BarDropdown id="content">
                    <BarDropdownToggle @onclick="@(()=> ToggleMenu("content"))">
                        <BarIcon Class="far fa-clipboard" @onclick="CloseNavMenu" />
                        Content
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem To="admin/policies/create" @onclick="CloseNavMenu">
                            Policy
                        </BarDropdownItem>
                        <BarDropdownItem To="admin/programme/create" @onclick="CloseNavMenu">
                            Programme
                        </BarDropdownItem>
                        <BarDropdownItem To="admin/mechanisms/create" @onclick="CloseNavMenu">
                            Mechanisms
                        </BarDropdownItem>
                        <BarDropdownItem To="admin/commitment/create" @onclick="CloseNavMenu">
                            Commitment
                        </BarDropdownItem>

                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>
        }
        <AuthorizeView Roles="Admin">
            <BarItem data-cy="userManagment">
                <BarDropdown id="userManagment">
                    <BarDropdownToggle @onclick="@(()=> ToggleMenu("userManagment"))">
                        <BarIcon Class="fas fa-tasks" />
                        User management
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem To="admin/users" @onclick="CloseNavMenu">Users</BarDropdownItem>
                        <BarDropdownItem To="admin/roles" @onclick="CloseNavMenu">Roles</BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>
        </AuthorizeView>
        <AuthorizeView Roles="Admin">
            <BarItem data-cy="Layout">
                <BarDropdown id="Layout">
                    <BarDropdownToggle @onclick="@(()=> ToggleMenu("Layout"))">
                        <BarIcon Class="far fa-clipboard" />
                        Layout
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem To="/admin/site-customization" @onclick="CloseNavMenu">
                            Header & Footer
                        </BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>

            <BarItem data-cy="scorecard">
                <BarDropdown id="Scorecard">
                    <BarDropdownToggle @onclick="@(()=> ToggleMenu("Scorecard"))">
                        <BarIcon Class="far fa-map" />
                        Scorecard
                    </BarDropdownToggle>
                    <BarDropdownMenu>
                        <BarDropdownItem To="admin/scorecards/create" @onclick="CloseNavMenu">
                            Create scorecard
                        </BarDropdownItem>
                        <BarDropdownItem To="/admin/scorecards/scoretopic" @onclick="CloseNavMenu">
                            Score topics
                        </BarDropdownItem>
                        <BarDropdownItem To="/admin/scorecards" @onclick="CloseNavMenu">
                            Scorecard list
                        </BarDropdownItem>
                    </BarDropdownMenu>
                </BarDropdown>
            </BarItem>
        </AuthorizeView>
    </BarStart>
</BarMenu>
