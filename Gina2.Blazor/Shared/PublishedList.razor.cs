﻿using Gina2.Core.Methods;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;

namespace Gina2.Blazor.Shared
{
    public partial class PublishedList
    {
        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }
        [Inject]
        private IJSRuntime _JsRuntime { get; set; }

        [CascadingParameter(Name = "Title")]
        public string Title { get; set; }

        [CascadingParameter(Name = "TypeofTitle")]
        public string TypeofTitle { get; set; }

        [CascadingParameter(Name = "TabTitle")]
        public string TabTitle { get; set; }

        [CascadingParameter(Name = "AdminTitle")]
        public string AdminTitle { get; set; }

        [CascadingParameter(Name = "CsvData")]
        public List<string> CsvData { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "CsvFields")]
        public string CsvFields { get; set; }

        [CascadingParameter(Name = "CsvKeys")]
        public string CsvKeys { get; set; }

        [CascadingParameter(Name = "FileName")]
        public string FileName { get; set; }
        [CascadingParameter(Name = "CountryName")]
        private string CountryName { get; set; }
        [Parameter]
        public string SelectedListTab { get; set; }
        private bool ExportLoading { get; set; }

        private GlobalSearchRequest searchRequest = new();

        private void OnSelectedTabChanged(string name)
        {
            SelectedListTab = name;
        }

        private async Task Download()
        {
            ExportLoading = true;
            string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
            List<string> list = new List<string>();
            list.Add(CountryName);
            searchRequest.SelectedCountries = list;
            if (Title == "Policies")
            {
               var data = await FileDownloadService.GetPolicyDataforCSV(searchRequest, new List<int>());
                await DownloadFile(data, $"gifna_policies_{CountryCode}_{currentDate}.csv");
            }
            else if (Title == "Programmes and actions")
            {
               var data = await FileDownloadService.GetActionDataForCsv(searchRequest, new List<int>());
               await DownloadFile(data, $"gifna_programmes and actions_{CountryCode}_{currentDate}.csv");
            }
            else if (Title == "Mechanisms")
            {
               var data = await FileDownloadService.GetMechanismsforCSV(searchRequest, new List<int>());
               await DownloadFile(data, $"gifna_mechanisms_{CountryCode}_{currentDate}.csv");
            }
            ExportLoading = false;
            StateHasChanged();
        }

        private async Task DownloadFile<T>(List<T> data, string fileName) where T : class
        {
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV<T>(data);
                //var fileData = FileData<PolicyCSV>(data);
                await _JsRuntime.InvokeVoidAsync("saveAsFile", fileName, fileData);
            }
        }
    }
}
