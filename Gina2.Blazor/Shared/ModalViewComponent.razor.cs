﻿using Blazorise;
using Gina2.Blazor.ClassValues;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Gina2.Blazor.Shared
{
    public partial class ModalViewComponent<T> where T : class
    {
        #region Properties

        [Parameter]
        public string Title { get; set; }

        [Parameter]
        public T SelectedData { get; set; }

        private Modal SaveModal { get; set; }

        [Parameter]
        public List<ClassValue> Columns { get; set; }

        #endregion Properties

        #region EventCallback

        [Parameter] public EventCallback<bool> PopupState { get; set; }

        [Parameter] public EventCallback<bool> SaveData { get; set; }

        #endregion EventCallback

        #region Methods

        #region Methods - Inherited Members

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
               ShowModalAsync();
            }
        }

        #endregion Methods - Inherited Members

        #region Methods - Helpers
        private async Task OnSave()
        {
            await SaveData.InvokeAsync();
            await HideModal();
        }

        private async Task HideModal()
        {
            await PopupState.InvokeAsync(false);
            await SaveModal.Hide();
        }

        public void ShowModalAsync()
        {
            SaveModal.Show();
        }
        
        #endregion Methods - Helpers

        #endregion Methods
    }
}

