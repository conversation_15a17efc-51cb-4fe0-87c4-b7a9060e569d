﻿using Gina2.Blazor.ClassValues;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;

namespace Gina2.Blazor.Shared
{
    public partial class PageViewComponent<T> where T : class
    {
       

        [Parameter]
        public string Title { get; set; }

        [Parameter]
        public T SelectedData { get; set; }

        [Parameter]
        public List<ClassValue> Columns { get; set; }

        private EditContext EditContext;


        [Parameter] public EventCallback<bool> PopupState { get; set; }

        [Parameter] public EventCallback<bool> SaveData { get; set; }

        
        protected override Task OnInitializedAsync()
        {
            EditContext = new(SelectedData);
            return base.OnInitializedAsync();
        }

        private async Task OnSave()
        {
            await SaveData.InvokeAsync();
        }

        private async Task BackToMain()
        {
            await PopupState.InvokeAsync(false);
        }
    }
}
