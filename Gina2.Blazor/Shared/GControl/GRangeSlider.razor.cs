﻿using AntDesign;
using Microsoft.AspNetCore.Components;
using Gina2.DbModels;
using Gina2.Blazor.CacheService;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.Core.Constant;

namespace Gina2.Blazor.Shared.GControl
{
    public partial class GRangeSlider : PageConfirgurationComponent
    {
        [Parameter]
        public EventCallback<(double, double)> Changed { get; set; }
        public int StartYear { get; set; }
        public int EndYear { get; set; }
        [Parameter]
        public int SelectedStartYear { get; set; }
        [Parameter]
        public int SelectedEndYear { get; set; }
        public (double, double) SelectedValues { get; set; }
        IEnumerable<int> values { get; set; } = new List<int>();
        private (double, double) SliderDefaultValue { get; set; }
        private SliderMark[] SliderValue { get; set; }
       
        protected override async Task OnInitializedAsync()
        {
           await base.OnInitializedAsync();
            SliderDefaultValue = (Convert.ToDouble(SelectedStartYear), Convert.ToDouble(SelectedEndYear));
            SetSliderValue();
            StateHasChanged();
        }
       
        public int GetSteperValue(int totalvalue)
        {
            int step = 1;
            if (totalvalue <= 30)
            {
                step = 1;
            }
            else if (totalvalue > 30 && totalvalue <= 60)
            {
                step =2;
            }
            else if (totalvalue > 60 && totalvalue <= 90)
            {
                step = 3;
            }
            else if (totalvalue > 90 && totalvalue <= 120)
            {
                step = 4;
            }
            else if (totalvalue > 90 && totalvalue <= 120)
            {
                step = 5;
            }
            else
            {
                step = 10;
            }
            return step;
        }
        public void SetSliderValue()
        {
             StartYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeStartYear) ?? DateTime.Now.Year - 32;
             EndYear = GetPageConfigrationValuebyKey(MapPageConfigurationKey.RangeEndYear) ?? DateTime.Now.Year;
            var totalValue = GetSteperValue(EndYear - StartYear);
            var sliderValue = new List<SliderMark>();

            for (int i = StartYear; i <= EndYear; i+= totalValue)
            {
                sliderValue.Add(new SliderMark(i, i.ToString()));
            }

            SliderValue = sliderValue.ToArray();
        }
        private int? GetPageConfigrationValuebyKey(string key)
        {
            string value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(key))?.Value;
            if (!string.IsNullOrEmpty(value))
            {
                return Convert.ToInt32(value);
            }
            return null;
        }
        private void OnChange((double, double) e)
        {
            SliderDefaultValue = e;
            Changed.InvokeAsync(e);
        }
    }
}
