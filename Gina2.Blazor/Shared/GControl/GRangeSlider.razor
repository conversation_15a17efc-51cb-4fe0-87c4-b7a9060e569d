﻿@using Gina2.Blazor.Helpers
@using Gina2.Blazor.Helpers.PageConfigrationData
@inherits PageConfirgurationComponent
<Div Class="YersFilter">
    <Heading Class="year pb-1">
        <AdminEditbut Key="@MapPageConfigurationKey.RangeTitle" />
        @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(MapPageConfigurationKey.RangeTitle)) - @SliderDefaultValue
        <AdminEditbut Key="@MapPageConfigurationKey.RangeTitleGroup" />
    </Heading>
    <AntDesign.Slider TValue="(double, double)"
                      Value="SliderDefaultValue"
                      Min="@StartYear"
                      Max="@EndYear"
                      DefaultValue="SliderDefaultValue"
                      OnChange="OnChange"
                      Marks=@SliderValue />
</Div>