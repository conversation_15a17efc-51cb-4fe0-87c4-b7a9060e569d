﻿using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared.GControl
{
    public partial class ColorsPicker
    {
        [Parameter]
        public string Color { get; set; } 

        [Parameter]
        public EventCallback<string> ColorChanged { get; set; }
        private string[] ColorPalette { get; set; } = new string[] { "#009ade", "#F26829", "#F4a8id", "#A622BC", "#5b2c86", "#80bc00", "#ef3842", "#00205c", "#FFFFFF", "#A8ffff", "#89ecff", "#43b5fb", "#68Doff", "#009ade", "#004f8c", "#004f8c", "#0067A6", "#003972", "#000941" };


        private async Task OnColorChanging(string color)
        {
            Color = color;
            await ColorChanged.InvokeAsync(Color);
        }
    }
}
