﻿@using Gina2.Blazor.Models
@using Gina2.DbModels.Views
@inject IJSRuntime JS
<Loader IsLoading="@IsLoading" />

<Container>
    <Div Flex="Flex.JustifyContent.Between" Class="policies-fr2">
        <Div Class="item1">
            <Autocomplete TItem="ViewCountryDetails"
                          TValue="string"
                          Data="@CountryItems"
                          TextField="@(( item ) => item.CountryName)"
                          ValueField="@(( item ) => item.CountryCode)"
                          data-cy="CountryTypePlaceHolder"
                          Placeholder="Type a country"
                          Filter="AutocompleteFilter.StartsWith"
                          FreeTyping
                          SelectedValue="@selectedCountryCode"
                          SelectedTextChanged="OnCountrySearchTextChangedAsync"
                          SelectedValueChanged="OnCountrySearchTextChanged1Async"
                          CustomFilter="@(( item, searchValue ) => item.CountryName.IndexOf( searchValue, 0, StringComparison.CurrentCultureIgnoreCase ) >= 0 )">
                <NotFoundContent> Sorry... @context was not found! </NotFoundContent>
            </Autocomplete>
        </Div>
        <Div Class="item2 mob-item">
            <Button Disabled="@isSearchDisabled" @onclick=@Navigate Class="but-blue pl-5 pr-5 _Navhight" data-cy="NavigateBtn">Navigate</Button>
        </Div>
    </Div>
</Container>

<Container Class="pt-3 pb-5">
    <Div Flex="Flex.JustifyContent.Between" Class="mob-f-column">
        <Div Class="atozlink">
            <Repeater Items="@AlphabeticLists">
                @if (HasCountryRecordsForLetter(context))
                {
                    <Span>
                        @*<Blazorise.Link Clicked="ScrollToFragment(context)" href="@GetNavigationTag(context)">@context</Blazorise.Link>*@
                        <Blazorise.Link Clicked="@(()=>ScrollToFragment(context))" href="@GetNavigationTag(context)">@context</Blazorise.Link>
                    </Span>
                }
                else
                {
                    <Span Class="country-letter-disabled">
                        <Blazorise.Link Style="cursor: default">@context</Blazorise.Link>
                    </Span>
                }
            </Repeater>
        </Div>
        <Div Class="m-pt-2 boder-left">
            <Select TValue="int" Class="select-blue pl-2 pr-4" SelectedValue="@SelectedValue" SelectedValueChanged="@OnSelectedValueChanged">
                <SelectItem Value="0" data-cy="CountyDropDown1">WHO Member States</SelectItem>
                <SelectItem Value="1" data-cy="CountyDropDown2">Areas and territories</SelectItem>
            </Select>
        </Div>
    </Div>

    <Div Class="contryslist mob-f-column">
        <Repeater Items="@AlphabeticListsFilter" Context="contextOne">
            <Div Class="contryitem">
                <Repeater Items="contextOne.AlphabeticListsLetters" Context="contextTwo">
                    @{
                        var Filters = CountriesByMember.Where(x => x.CountryName.StartsWith(contextTwo, StringComparison.OrdinalIgnoreCase));
                    }
                    @if (Filters.Any())
                    {
                        <ListGroup>
                            <Heading id="@contextTwo">@contextTwo.ToUpper()</Heading>
                            <Repeater Items="@Filters.OrderBy(c => c.CountryName)" Context="contextThree">
                                <ListGroupItem>
                                    <NavLink href="@($"countries/{contextThree.CountryCode}/{SelectedDataUrl}")">@contextThree.CountryName</NavLink>
                                </ListGroupItem>
                            </Repeater>
                        </ListGroup>
                    }
                </Repeater>
            </Div>
        </Repeater>
    </Div>
</Container>
