﻿using Blazorise.Snackbar;
using Gina2.Blazor.ClassValues;
using Gina2.Blazor.Resources;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Shared
{
    public partial class MasterComponent<T> where T : class
    {
        [Inject]
        private IStringLocalizer<Resource> Loc { get; set; }

        [Parameter]
        public List<ClassValue> Columns { get; set; }

        [Parameter]
        public ObservableCollection<T> Data { get; set; }

        [Parameter]
        public T SelectedData { get; set; }

        [Parameter]
        public string Title { get; set; }

        [Parameter]
        public string PageName { get; set; }

        #region Properties

        private bool IsShowPopup { get; set; } = false;


        private Snackbar SuccessMessagePrompt { get; set; }

        public string Message { get; set; }

        #endregion Properties

        #region EventCallback

        [Parameter]
        public EventCallback<Guid> SelectionChanged { get; set; }

        [Parameter]
        public EventCallback<T> SaveData { get; set; }

        #endregion EventCallback

        #region Methods - Helpers

        private void Add()
        {
            Title = string.Format(Loc[Core.Constants.LocalizationKeys.Master.Add]?.Value, PageName);
            IsShowPopup = true;
        }

        private void ResetPopup(bool status)
        {
            IsShowPopup = status;
        }

        private void DataDeleted()
        {
            Message = string.Format(Loc[Core.Constants.LocalizationKeys.Master.DeleteSuccess]?.Value, PageName);
            SuccessMessagePrompt.Show();
        }

        private void Save()
        {
            SaveData.InvokeAsync();
            Message = string.Format(Loc[Core.Constants.LocalizationKeys.Master.SaveSuccess]?.Value, PageName);
            IsShowPopup = false;
            SuccessMessagePrompt.Show();
        }

        #endregion Methods - Helpers
    }
}