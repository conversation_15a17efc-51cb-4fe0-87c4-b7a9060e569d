﻿@inherits LayoutComponentBase
@inject NavigationManager Navigation

<PageTitle>The Global database on the Implementation of Nutrition Action (GIFNA)</PageTitle>

<div id="myNav" class="overlay">
    <!-- Button to close the overlay navigation -->
    <a href="javascript:void(0)" class="closebtn" @onclick="CloseNavMenu">&times;</a>
    <div class="overlay-content">
        <Div class="mob-brand" Match="NavLinkMatch.All">
            <span><img class="img-responsive" width="136" src="img/@(layoutSetting.HeaderImage)" /></span>
            <span class="logotext">
                The Global database on the Implementation<br />of
                Nutrition Action (GIFNA)
            </span>
        </Div>
        <NavMenu />
         <AuthorizeView Context="authorizedContext">
                <Authorized>
       @if (!IsOfficeJs)
                {
                    <Div Class="_admin-menu">
                        <h3>Admin menu</h3>
            <hr />
                        <AdminVertical />
        </Div>}
            </Authorized>
        </AuthorizeView>
        <LoginDisplay />
    </div>
</div>
<Clarity></Clarity>
<CascadingValue Value="@layoutSetting">
    @if (!IsLoginURl.Contains("login"))
    {
        if (IsOfficeJs)
        {
            <HeaderOffice />
        }
        else
        {
            <Header />
        }
       @* <button onclick="topFunction()" id="myBtn" title="Go to top"><Icon Name="IconName.ArrowUp" /></button>*@
    }

    <Layout Class="admin-bar">
        <Layout Sider>
            <AuthorizeView Context="authorizedContext">
                <Authorized>
                    <LayoutSider>
                        @if (!IsOfficeJs)
                                {
                        <Bar Class="adminbar" id="Adminmenu" Mode="BarMode.VerticalInline"
                             CollapseMode="BarCollapseMode.Small"
                             Breakpoint="Breakpoint.Desktop"
                             NavigationBreakpoint="Breakpoint.Tablet"
                             ThemeContrast="ThemeContrast.Dark">
                                <AdminVertical />
                            </Bar>
                                }
                    </LayoutSider>
                </Authorized>
            </AuthorizeView>
            <Layout>
                <div class="page @PageType">
                    <main>
                        <AuthorizeView Context="authorizedContext">
                            <Authorized>
                                @if (!IsOfficeJs)
                                {
                                  <Bar Class="leftbar" Mode="BarMode.Horizontal"
                                     Background="Background.Dark"
                                     ThemeContrast="ThemeContrast.Dark">
                                    <BarToggler Clicked="@(() => AdminBar("Adminmenu"))" />
                                </Bar>  
                                }
                                
                            </Authorized>
                        </AuthorizeView>
                        <ErrorBoundary>
                            <ChildContent>
                                 @Body
                            </ChildContent>
                            <ErrorContent Context="Exception">
                                <GlobalExceptionHandling ErrorException="@Exception"/>
                            </ErrorContent>
                        </ErrorBoundary>
                       
                    </main>
                </div>
                @if (!IsLoginURl.Contains("login"))
                {
                    <Footer />
                }
            </Layout>
        </Layout>
    </Layout>
   
</CascadingValue>
<RadzenDialog />
