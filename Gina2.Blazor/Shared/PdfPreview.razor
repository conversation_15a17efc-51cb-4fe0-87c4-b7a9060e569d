
@using Gina2.Blazor.Helpers.PageConfigrationData;


@inherits PageConfirgurationComponent;
@page "/pdf-preview/{FileDisplayName}"
<div class="pdf-container">
    @if (!string.IsNullOrEmpty(FileDisplayName))
    {
        <iframe id="pdfIframe" width="100%" height="600px"></iframe>
    }
    else
    {
        <p>No PDF to display.</p>
    }
</div>
<PageTitle>GIFNA @FileDisplayName</PageTitle>
<style>
    body, html {
        height: 100%;
        margin: 0;
    }

    .pdf-container {
        height: 100vh; /* Full viewport height */
        display: flex;
        flex-direction: column;
    }

    iframe {
        flex: 1; /* Ensures iframe takes up remaining space */
        border: none; /* Removes the iframe border */
    }
</style>


