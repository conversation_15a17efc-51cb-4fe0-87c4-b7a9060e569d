﻿@using Microsoft.Extensions.Configuration;
@using Serilog;
@inject IConfiguration configuration
@inject IJSRuntime JSRuntime

@code {
	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		try
		{
			if (firstRender)
			{
				bool clarityEnable = Convert.ToBoolean(configuration["Clarity:IsClarityEnable"]);
				if (clarityEnable)
				{
					string clarityCode = configuration["Clarity:ClarityCode"];
					await JSRuntime.InvokeVoidAsync("clarityLoad", clarityCode);
				}
			}

		}
		catch (JSDisconnectedException ex)
		{
			Log.Error(ex.Message);
		}
		catch (Exception ex)
		{
			Log.Error("clarity", ex.Message);
		}
		await base.OnAfterRenderAsync(firstRender);
	}
}
