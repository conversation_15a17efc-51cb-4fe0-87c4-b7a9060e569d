﻿@inject IJSRuntime JsRuntime
@using ClassValues;
@using Microsoft.AspNetCore.Components.Forms
@typeparam T
<table border="1" class="table table-striped table-bordered">
    <thead>
        <tr>
            @foreach (ClassValue item in Columns)
            {
                @if (item.PropertyName.Equals(Core.Constants.Common.Properties.Id,StringComparison.OrdinalIgnoreCase))
                {
                    <th style="display:none;"></th>
                }
                else
                {
                    <th scope="col">@item.DisplayName</th>
                }
            }
            <th colspan="3" scope="col">Action</th>
        </tr>
    </thead>
    <tbody>
        @foreach (dynamic data in Data)
        {
            <tr>
                @foreach (ClassValue col in Columns)
                {
                    @if (col.PropertyName.Equals(Core.Constants.Common.Properties.Id,StringComparison.OrdinalIgnoreCase))
                    {
                        <td style="display:none;"></td>
                    }
                    else
                    {
                        @if (Nullable.GetUnderlyingType(data.GetType().GetProperty(col.PropertyName).PropertyType) != null)
                        {
                            dynamic propertyInfo = data.GetType().GetProperty(col.PropertyName);
                            propertyInfo.SetValue(data, Activator.CreateInstance(Nullable.GetUnderlyingType(data.GetType().GetProperty(col.PropertyName).PropertyType)));                            
                        }
                        dynamic value = data.GetType().GetProperty(col.PropertyName).GetValue(data, null);
                        if(value == null)
                        {
                            dynamic propertyInfo = data.GetType().GetProperty(col.PropertyName);
                            propertyInfo.SetValue(data, Activator.CreateInstance(data.GetType().GetProperty(col.PropertyName).PropertyType));
                        }
                        <td>@data.GetType().GetProperty(col.PropertyName).GetValue(data, null)</td>
                    }
                }
                <td>
                    <button class="btn btn-primary" @onclick="@(()=>Edit(data.Id))">Edit</button>
                    <button class="btn btn-danger" @onclick="@(()=>Delete(data.Id))">Delete</button>
                </td>

            </tr>
        }
    </tbody>
</table>