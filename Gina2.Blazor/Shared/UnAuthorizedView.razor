﻿<PageTitle>Unauthorized</PageTitle>
<Container>
    <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="notfount">
        <h2>Unauthorized</h2>
        <p>If you have questions please contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <Div Flex="Flex.JustifyContent.Center.AlignItems.Center">
            <a @onclick="@NavigateToLogin" class="but-yellow">Login</a>
        </Div>
    </Div>
</Container>

