﻿using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.DbModels;
using Gina2.Services.Country;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared
{
    public partial class CountryDetails : PageConfirgurationComponent
    {
        [Inject]
        private ICountryService CountryService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Parameter]
        public string SelectedTab { get; set; }

        [Parameter]
        public string ContentName { get; set; }

        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [CascadingParameter(Name = "PolicyCode")]
        public int PolicyCode { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "CountryName")]
        private string CountryName { get; set; }

        public string SelectedPolicy { get; set; }

        private bool Hightlight { get; set; }
        protected override async Task OnInitializedAsync()
        {
            // TOOD: lets write a service method to retrieve only the country only.
            // TODO: later we are going to take this from the cache.
            var allCountries = await CountryService?.GetCountriesAsync();
            CountryName = allCountries?.Where(x => x.Iso3Code == CountryCode)?.FirstOrDefault()?.Name;

            if (!NavigationManager.Uri.ToLower().StartsWith(string.Format("{0}policies", NavigationManager.BaseUri))
                && !NavigationManager.Uri.ToLower().StartsWith(string.Format("{0}mechanisms", NavigationManager.BaseUri))
                && !NavigationManager.Uri.ToLower().StartsWith(string.Format("{0}programmes-and-actions", NavigationManager.BaseUri))
                && !NavigationManager.Uri.ToLower().StartsWith(string.Format("{0}commitments", NavigationManager.BaseUri))
                )
            {
                if (string.IsNullOrEmpty(CountryName))
                {
                    NavigationManager.NavigateTo("NotFound");
                    return;
                }
            }
            base.OnInitializedAsync();
        }
        private void NavigateTohighlights()
        {
            NavigationManager.NavigateTo("commitment/highlights");
            Hightlight = true;
        }
        private void OnSelectedTabChanged(string name)
        {
            if (!Hightlight)
            {
                SelectedTab = name;
                NavigationManager.NavigateTo($"countries/{CountryCode}/{SelectedTab}");
            }            
        }

    }
}
