﻿@using Gina2.Blazor.Models
@using Blazorise;

<Loader IsLoading="@IsLoading" />
<Div Class="paginationareaa">
<Layout Class="search-box pt-3 pb-3 mob-layout">
    <Layout Class="left-layout DataGrids">
        <LayoutContent>
            <DataGrid 
                FixedHeaderDataGridMaxHeight="500px"
                FixedHeaderDataGridHeight="450px"
                TItem="@PublishedListItem"
                Data="@Data"
                TotalItems="@DataSize"
                PageSize="@SearchRequest.PageSize"
                ShowPageSizes
                PageSizes="new[] { 50,100,250,500,1000 }"
                ShowPager
                Responsive
                CurrentPage="@SearchRequest.PageNo"
                SortMode="DataGridSortMode.Single"
                Sortable="true">
                <EmptyTemplate>
                    <Div> No data found </Div>
                </EmptyTemplate>
                <DataGridColumns>
                    <DataGridColumn Caption="@TableTitle" Sortable="true" Field="@nameof(PublishedListItem.CombinedTitle)">
                        <DisplayTemplate>
                            <NavLink Class="_linkhover" href="@($"/countries/{CountryCode}/{DetailUrlContext}/{context.Key}")">@context.CombinedTitle</NavLink>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn Field="@nameof(PublishedListItem.StartYear)" Caption=" Start year" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="true" Width="130px"/>
                    <DataGridColumn Displayable="@(TableTitle == "Mechanisms"? false : true)" Field="@nameof(PublishedListItem.EndYear)" Caption=" End year" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="true" Width="130px" />
                </DataGridColumns>
                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                <TotalItemsTemplate>
                    <Badge TextColor="TextColor.Dark">
                        @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                    </Badge>
                </TotalItemsTemplate>
            </DataGrid>

        </LayoutContent>
    </Layout>
</Layout>
</Div>