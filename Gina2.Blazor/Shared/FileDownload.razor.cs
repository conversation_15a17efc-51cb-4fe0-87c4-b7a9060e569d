﻿using System.Diagnostics;
using Blazorise.DataGrid;
using Domain.Search;
using Gina2.Blazor.Models;
using Gina2.Core.Methods;
using Gina2.MySqlRepository.Models;
using Gina2.Services.FileDownload;
using Gina2.Services.Models;
using Gina2.Services.Policy;
using Gina2.Services.Search;
using Gina2.SqlRepository.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class FileDownload
    {
        [Inject]
        private IPolicyService _policyService { get; set; }
        [Parameter]
        public string FileType { get; set; }
        [Parameter]
        public List<SearchHistory> SearchHistories { get; set; }
        [Parameter]
        public bool ShowCSVPanel { get; set; }
        [Parameter]
        public GlobalSearchRequest SearchRequest { get; set; } = new GlobalSearchRequest();
        public bool ShowDownloadPane { get; set; } = true;
        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private ILogger<FileDownload> Logger { get; set; }
        [Inject]
        private IFileDownloadService FileDownloadService { get; set; }
        public bool IsActionDownloading { get; set; } = false;
        public bool IsPolicyDownload { get; set; } = false;
        public bool IsMechanismDownload { get; set; } = false;
        public bool IsCommitmentDownload { get; set; } = false;
        public int NoOfCountriesInActions { get; set; }
        public int NoOfActions { get; set; }
        public int NoOfPolices { get; set; }
        public int NoOfCountriesInPolicy { get; set; }
        public int NoOfMechanisms { get; set; }
        public int NoOfCountriesInMechanism { get; set; }
        public int NoOfSmartComitments { get; set; }
        public int NoOfCountriesInSmartComitments { get; set; }
        private List<Domain.Search.SearchResult> selectedEntries = new();

        public void RefreshDataofFileDownload(SearchResultCounts searchResultCount, GlobalSearchRequest searchRequest)
        {
            SearchRequest = searchRequest;
            NoOfPolices =  searchRequest.IsPolicyDataTypeSelected && searchResultCount.DataTypeCount.ContainsKey(Gina2.Core.Constants.Policy) ?
                            searchResultCount.DataTypeCount[Gina2.Core.Constants.Policy]  : 0;
            NoOfMechanisms = searchRequest.IsMechanicalDataTypeSelected && searchResultCount.DataTypeCount.ContainsKey(Gina2.Core.Constants.Mechanism) ?
                            searchResultCount.DataTypeCount[Gina2.Core.Constants.Mechanism] : 0;
            NoOfActions = searchRequest.IsPragrammesAndActionsDataTypeSelected && searchResultCount.DataTypeCount.ContainsKey(Gina2.Core.Constants.Action) ?
                            searchResultCount.DataTypeCount[Gina2.Core.Constants.Action] : 0;
            NoOfSmartComitments = searchRequest.IsCommitmentsDataTypeSelected && searchResultCount.DataTypeCount.ContainsKey(Gina2.Core.Constants.Commitment) ?
                            searchResultCount.DataTypeCount[Gina2.Core.Constants.Commitment] : 0;

            NoOfCountriesInPolicy = searchRequest.IsPolicyDataTypeSelected && searchResultCount.DataTypeWiseCountryCount.ContainsKey(Gina2.Core.Constants.Policy) ?
                            searchResultCount.DataTypeWiseCountryCount[Gina2.Core.Constants.Policy] : 0;
            NoOfCountriesInMechanism = searchRequest.IsMechanicalDataTypeSelected && searchResultCount.DataTypeWiseCountryCount.ContainsKey(Gina2.Core.Constants.Mechanism) ?
                            searchResultCount.DataTypeWiseCountryCount[Gina2.Core.Constants.Mechanism] : 0;
            NoOfCountriesInActions = searchRequest.IsPragrammesAndActionsDataTypeSelected && searchResultCount.DataTypeWiseCountryCount.ContainsKey(Gina2.Core.Constants.Action) ?
                            searchResultCount.DataTypeWiseCountryCount[Gina2.Core.Constants.Action] : 0;
            NoOfCountriesInSmartComitments = searchRequest.IsCommitmentsDataTypeSelected && searchResultCount.DataTypeWiseCountryCount.ContainsKey(Gina2.Core.Constants.Commitment) ?
                            searchResultCount.DataTypeWiseCountryCount[Gina2.Core.Constants.Commitment] : 0;
            StateHasChanged();
        }

        public void RefreshCount(SearchResultCounts searchResultCount, GlobalSearchRequest searchRequest)
        {
            RefreshDataofFileDownload(searchResultCount, searchRequest);
        }

        public void RefreshDataFromSpOfFileDownload(SearchResponseXmlModel searchResultCount, GlobalSearchRequest searchRequest)
        {
            SearchRequest = searchRequest;
            NoOfPolices = searchResultCount.DataTypeCountList.DataTypeCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Policy)?.Count??0;
            NoOfMechanisms = searchResultCount.DataTypeCountList.DataTypeCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Mechanism)?.Count ?? 0;
            NoOfActions = searchResultCount.DataTypeCountList.DataTypeCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Action)?.Count ?? 0;
            NoOfSmartComitments = searchResultCount.DataTypeCountList.DataTypeCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Commitment)?.Count ?? 0;

            NoOfCountriesInPolicy = searchResultCount.DataTypeWiseCountryCountList.DataTypeWiseCountryCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Policy)?.DataTypeCount ?? 0;
            NoOfCountriesInMechanism = searchResultCount.DataTypeWiseCountryCountList.DataTypeWiseCountryCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Mechanism)?.DataTypeCount ?? 0;
            NoOfCountriesInActions = searchResultCount.DataTypeWiseCountryCountList.DataTypeWiseCountryCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Action)?.DataTypeCount ?? 0;
            NoOfCountriesInSmartComitments = searchResultCount.DataTypeWiseCountryCountList.DataTypeWiseCountryCount.FirstOrDefault(d => d.Type == Gina2.Core.Constants.Commitment)?.DataTypeCount ?? 0;
        }

        public void RefreshDataofSelectList(Dictionary<string, object> ChildRecords)
        {
            if (ChildRecords.Count > 0)
            {
                SearchRequest.IsAllSelected = Convert.ToBoolean(ChildRecords["IsAllSelected"]);
                selectedEntries = ChildRecords["SelectedItem"] as List<SearchResult>;
            }
        }
        private async Task ActionDownloadClicked(bool downloadByItem)
        {
            if (IsActionDownloading) return;
            SearchRequest.DownloadByDataItem = downloadByItem;
            await DownloadActionsAsync();
        }
        private async Task PolicyDownloadClicked(bool downloadByItem)
        {
            if (IsPolicyDownload) return;
            SearchRequest.DownloadByDataItem = downloadByItem;
            try
            {
                _ = DownloadPoliciesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError("File download error:: ", ex);
                throw;
            }

        }
        private async Task MechanismDownloadClicked(bool downloadByItem)
        {
            if (IsMechanismDownload) return;
            SearchRequest.DownloadByDataItem = downloadByItem;
            await DownloadMechanismsAsync();
        }
        private async Task CommitmentDownloadClicked(bool downloadByItem)
        {
            if (IsCommitmentDownload) return;
            SearchRequest.DownloadByDataItem = downloadByItem;
            _ = DownloadCommitmentAsync();
        }
        private async Task DownloadActionsAsync()
        {
            IsActionDownloading = true;
            StateHasChanged();

            // Stopwatch for measuring data population time
            var RetrievingIdsStopwatch = Stopwatch.StartNew();

            var selectedAcyions = selectedEntries.Where(e => e.Type == "P/A" && e.IsSelected == true).Select(e => e.Id).ToList();
            RetrievingIdsStopwatch.Stop();
            var RetrievingIdsElapsedTime = RetrievingIdsStopwatch.Elapsed;

            // Stopwatch for measuring data population time
            var dataStopwatch = Stopwatch.StartNew();
            var data = await FileDownloadService.GetActionDataForCsv(SearchRequest, selectedAcyions);
            dataStopwatch.Stop();
            var dataElapsedTime = dataStopwatch.Elapsed;

            if (data.Any())
            {
                // Stopwatch for measuring download time
                var downloadStopwatch = Stopwatch.StartNew();

                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data);
                string fileNamePrefix = SearchRequest.DownloadByDataItem ? "data-items" : "countries";
                string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
                await JSRuntime.InvokeVoidAsync("saveAsFile", $"GIFNA-{FileType}-programmes-and-actions-{fileNamePrefix}-{currentDate}.csv", fileData);

                downloadStopwatch.Stop();
                var downloadElapsedTime = downloadStopwatch.Elapsed;

                // Log the execution times
                Logger.LogInformation($"Retrieving Programs Ids time for Programs download file: {RetrievingIdsElapsedTime}");
                Logger.LogInformation($"Data population time for Programs download file: {dataElapsedTime}");
                Logger.LogInformation($"Download time for Programs download file after populating data: {downloadElapsedTime}");
            }
            else
            {
                Logger.LogWarning("No file available there are no data");
            }
            IsActionDownloading = false;
            StateHasChanged();
        }
        private async Task DownloadPoliciesAsync()
        {
            IsPolicyDownload = true;

            var selectedPolices = selectedEntries.Where(e => e.Type == "P" && e.IsSelected == true).Select(e => e.Id).ToList();

            var data = await FileDownloadService.GetPolicyDataforCSV(SearchRequest, selectedPolices);


            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data.ToList());

                try
                {

                    string fileNamePrefix = SearchRequest.DownloadByDataItem ? "data-items" : "countries";
                    string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";

                    await JSRuntime.InvokeVoidAsync("saveAsFile", $"GIFNA-{FileType}-policies-{fileNamePrefix}-{currentDate}.csv", fileData);
                    fileData.Dispose();

                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            else
            {
                Logger.LogWarning("No file available there are no data");
            }

            IsPolicyDownload = false;
            await InvokeAsync(StateHasChanged);
        }

        private async Task DownloadMechanismsAsync()
        {
            IsMechanismDownload = true;
            var selectedMecanisms = selectedEntries.Where(e => e.Type == "M" && e.IsSelected == true).Select(e => e.Id).ToList();
            var data = await FileDownloadService.GetMechanismsforCSV(SearchRequest, selectedMecanisms);
            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data);
                string fileNamePrefix = SearchRequest.DownloadByDataItem ? "data-items" : "countries";
                string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
                await JSRuntime.InvokeVoidAsync("saveAsFile", $"GIFNA-{FileType}-mechanisms-{fileNamePrefix}-{currentDate}.csv", fileData);
            }
            else
            {
                Logger.LogWarning("No file available there are no data");
            }
            IsMechanismDownload = false;
            StateHasChanged();

        }
        private async Task DownloadCommitmentAsync()
        {
            IsCommitmentDownload = true;
            StateHasChanged();
            var selectedCommitmentes = selectedEntries.Where(e => e.Type == "C" && e.IsSelected == true).Select(e => e.Id).ToList();
            var data = await FileDownloadService.GetCommitmentDataforCSV(SearchRequest, selectedCommitmentes);

            if (data.Any())
            {
                var writer = new FileDownloading();
                var fileData = writer.CreateCSV(data);
                string fileNamePrefix = SearchRequest.DownloadByDataItem ? "data-items" : "countries";
                string currentDate = $"{DateTime.Now.ToString("yyyy")}-{DateTime.Now.ToString("MM")}-{DateTime.Now.ToString("dd")}";
                await JSRuntime.InvokeVoidAsync("saveAsFile", $"GIFNA-{FileType}-commitments-{fileNamePrefix}-{currentDate}.csv", fileData);
            }
            else
            {
                Logger.LogWarning("No file available there are no data");
            }
            IsCommitmentDownload = false;
            StateHasChanged();
        }

        public async Task GetCountByCountryOrRegion(GlobalSearchRequest searchRequest, string SelectedDatatype, string regionCode, string countryStatus)
        {
            searchRequest.SelectedRegionCode= regionCode;
            Domain.Search.SearchResultCounts searchResultCounts = new Domain.Search.SearchResultCounts();
            IEnumerable<SearchCount> searchCounts  = new List<SearchCount>();
            searchCounts = await _policyService.GetContentCount(regionCode,countryStatus);
            searchResultCounts.DataTypeCount = searchCounts.ToDictionary(x => x.Type, x => x.Count);
            searchResultCounts.DataTypeWiseCountryCount = searchCounts.ToDictionary(x => x.Type, x => x.DistinctIsoCount);
            ToggleSearchRequestSelectedDataType(searchRequest, SelectedDatatype);
            RefreshDataofFileDownload(searchResultCounts, searchRequest);
        }

        private GlobalSearchRequest ToggleSearchRequestSelectedDataType(GlobalSearchRequest searchRequest, string selectedDataType)
        {
            if (selectedDataType == null)
            {
                searchRequest.IsPolicyDataTypeSelected = true;
                searchRequest.IsPragrammesAndActionsDataTypeSelected = true;
                searchRequest.IsMechanicalDataTypeSelected = true;
                searchRequest.IsCommitmentsDataTypeSelected = true;
            }
            else if (selectedDataType == "Policies")
            {
                searchRequest.IsPolicyDataTypeSelected = true;
                searchRequest.IsPragrammesAndActionsDataTypeSelected = false;
                searchRequest.IsMechanicalDataTypeSelected = false;
                searchRequest.IsCommitmentsDataTypeSelected = false;
            }
            else if (selectedDataType == "Programmes and actions")
            {
                searchRequest.IsPolicyDataTypeSelected = false;
                searchRequest.IsPragrammesAndActionsDataTypeSelected = true;
                searchRequest.IsMechanicalDataTypeSelected = false;
                searchRequest.IsCommitmentsDataTypeSelected = false;
            }
            else if (selectedDataType == "Mechanisms")
            {
                searchRequest.IsPolicyDataTypeSelected = false;
                searchRequest.IsPragrammesAndActionsDataTypeSelected = false;
                searchRequest.IsMechanicalDataTypeSelected = true;
                searchRequest.IsCommitmentsDataTypeSelected = false;
            }
            else if (selectedDataType == "SMART commitments")
            {
                searchRequest.IsPolicyDataTypeSelected = false;
                searchRequest.IsPragrammesAndActionsDataTypeSelected = false;
                searchRequest.IsMechanicalDataTypeSelected = false;
                searchRequest.IsCommitmentsDataTypeSelected = true;
            }

            return searchRequest;
        }
    }
}
