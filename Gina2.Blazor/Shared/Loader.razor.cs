﻿using Microsoft.AspNetCore.Components;
using Blazorise;
using Microsoft.JSInterop;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Components.Authorization;
using Azure.Storage.Blobs.Models;
using System.Security.Claims;

namespace Gina2.Blazor.Shared
{
   
    public partial class Loader
    {
        [Parameter]
        public bool IsLoading { get; set; }

        private Visibility loaderVisibility = Visibility.Visible;

        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public NavigationManager _NavigationManager { get; set; }
        [Inject]
        private AuthenticationStateProvider _AuthenticationStateProvider { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Loaderpages();
            }
        }
        protected override async Task OnParametersSetAsync()
        {
            await Loaderpages();

        }

        private async Task Loaderpages()
        {
            var authState = await _AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            if (user.Identity is not null && user.Identity.IsAuthenticated)
            {
                string baseUrl = _NavigationManager.Uri;
                string value = @"\/countries\/.*";
                Regex TestPages = new Regex(value);

                if (TestPages.IsMatch(baseUrl))
                {
                    await JSRuntime.InvokeVoidAsync("IsLoadingcon", IsLoading);
                }
            }
        }
    }
}
