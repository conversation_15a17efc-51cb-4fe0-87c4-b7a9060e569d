﻿using Gina2.Blazor.Models;
using Gina2.DbModels.Views;
using Gina2.Services.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System;
using System.Collections.ObjectModel;
using static Gina2.Core.Constants;


namespace Gina2.Blazor.Shared
{
    public partial class CountriesComponent
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Parameter]
        public IEnumerable<ViewCountryDetails> CountryItems { get; set; } = new List<ViewCountryDetails>();
        [Parameter]
        public string SelectedDataUrl { get; set; }

        [Parameter]
        public GlobalSearchRequest searchRequest { get; set; }

        [Parameter]
        public FileDownload FileDownloadChild { get; set; }
        [Parameter]
        public string SelectedDatatype { get; set; }

        [Parameter]
        public EventCallback<int> OnCountryStatus { get; set; }


        public IEnumerable<ViewCountryDetails> CountriesByMember { get; set; }

        private ObservableCollection<string> AlphabeticLists { get; set; } = new ObservableCollection<string>() {
        "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"
        };

        public bool IsLoading { get; set; } = false;
        public string selectedCountryCode { get; set; }
        private string searchedText = string.Empty;
        private bool isSearchDisabled = true;

        private ObservableCollection<AlhpabeticListsData> AlphabeticListsFilter { get; set; }


        public class AlhpabeticListsData
        {
            public ObservableCollection<string> AlphabeticListsLetters { get; set; }
        }
        protected override async Task OnParametersSetAsync()
        {
            CountryItems = CountryItems;
            GetCountriesByMember();
            FileDownloadChild.GetCountByCountryOrRegion(searchRequest, SelectedDatatype, "",searchRequest.SelectedStatus);
            StateHasChanged();
        }
        //protected override async Task OnInitializedAsync()
        //{
        //    GetCountriesByMember();
        //}

        private string GetNavigationTag(string value)
        {
            return $"#{value}";
        }

        private int SelectedValue = 0;

        private void GetCountriesByMember()
        {
            CountriesByMember = SelectedValue == 0 ? CountryItems.Where(x => x.CountryStatus == "Member State") :
                CountryItems.Where(x => x.CountryStatus != "Member State");
            CalcalulateCloumnCountry();
        }
        private void CalcalulateCloumnCountry()
        {
            var total = CountriesByMember.Count();
            var perColumnCountry = total / 3.0;
            var perrow = (perColumnCountry % 1 == 0) ? Convert.ToInt16(perColumnCountry) : Convert.ToInt16(perColumnCountry) + 1;
            var allConutryList = CountriesByMember.OrderBy(e => e.CountryName).Select(e => e.CountryName.ToCharArray().ElementAt(0).ToString()).ToList();
            var firstColumn = allConutryList.Take(perrow).Distinct().ToList();
            var secondColumn = allConutryList.Skip(perrow).Take(perrow).Distinct().ToList();
            AlphabeticListsFilter = new ObservableCollection<AlhpabeticListsData>()
            {
               new AlhpabeticListsData()   { AlphabeticListsLetters = new ObservableCollection<string>(firstColumn)},
               new AlhpabeticListsData()   { AlphabeticListsLetters =new ObservableCollection<string>(secondColumn.Except(firstColumn)) },
             };
            var combined = AlphabeticListsFilter.SelectMany(w => w.AlphabeticListsLetters.ToList());
            AlphabeticListsFilter.Add(new AlhpabeticListsData() { AlphabeticListsLetters = new ObservableCollection<string>(allConutryList.Except(combined).Distinct().ToList()) });

        }
        private void OnSelectedValueChanged(int value)
        {
            searchRequest.SelectedStatus = value==0 ? CountryStaus.MemberState : CountryStaus.Territorry; 
            SelectedValue = value;
            GetCountriesByMember();
            OnCountryStatus.InvokeAsync(SelectedValue);
            FileDownloadChild.GetCountByCountryOrRegion(searchRequest, SelectedDatatype, "",searchRequest.SelectedStatus);
        }

        private void Navigate()
        {
            string countryCode = GetSelectedCountryCode();
            if (!string.IsNullOrWhiteSpace(countryCode))
            {
                NavigationManager.NavigateTo($"countries/{countryCode}/policies");
            }
        }

        private async Task OnCountrySearchTextChangedAsync(string value)
        {
            searchedText = value;
            isSearchDisabled = !IsValidCountry(value);
        }

        private async Task OnCountrySearchTextChanged1Async(string value)
        {
            searchedText = value;
            isSearchDisabled = !IsValidCountry(value);

            if (!string.IsNullOrWhiteSpace(value))
            {
                NavigationManager.NavigateTo($"countries/{value.Trim()}/policies");
            }
        }

        private bool IsValidCountry(string value)
        {
            if (string.IsNullOrWhiteSpace(value) || CountryItems == null)
            {
                return false;
            }

            return CountryItems.Select(acn => acn.CountryName).Contains(value, StringComparer.InvariantCultureIgnoreCase);
        }

        private string GetSelectedCountryCode()
        {
            if (string.IsNullOrWhiteSpace(searchedText) || CountryItems == null)
            {
                return string.Empty;
            }

            ViewCountryDetails selectedCountry = CountryItems.FirstOrDefault(acn => acn.CountryName.Equals(searchedText, StringComparison.CurrentCultureIgnoreCase));

            return selectedCountry == null ? string.Empty : selectedCountry.CountryCode;
        }

        private bool HasCountryRecordsForLetter(string value)
        {
            return CountriesByMember.Any(acn => acn.CountryName.StartsWith(value, StringComparison.CurrentCultureIgnoreCase));
        }

        private void NavigateToPolicies(string countryCode)
        {
            this.IsLoading = true;
            StateHasChanged();

            NavigationManager.NavigateTo($"countries/{countryCode}/{SelectedDataUrl}");
        }
        private async Task ScrollToFragment(string elementId)
        {
			await JS.InvokeVoidAsync("MenuScrollToId", elementId);
		}
		}
}