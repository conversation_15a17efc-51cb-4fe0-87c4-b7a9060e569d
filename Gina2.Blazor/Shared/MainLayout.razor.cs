﻿using AutoMapper;
using Blazorise;
using Gina2.Blazor.Models;
using Gina2.Core;
using Gina2.DbModels;
using Gina2.Services.LayoutCustomization;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
namespace Gina2.Blazor.Shared
{
    public partial class MainLayout
    {
        private string IsLoginURl { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }

        [Inject]
        private ILayoutCustomizationService LayoutCustomizationService { get; set; }

        [Inject]
        private IMapper Mapper { get; set; }

        private SiteCustomizationDto layoutSetting = new SiteCustomizationDto();
        private bool IsOfficeJs { get; set; }

        private async Task AdminBar(string name)
        {
            await JsRuntime.InvokeVoidAsync("toggleAdminbar", name);
            StateHasChanged();
        }
        public string PageType { get; set; }

        protected override void OnParametersSet()
        {
            PageType = $"g-{(this.Body.Target as RouteView)?.RouteData?.PageType?.Name.ToLower()}";
			OnParameters();
		}
		private async Task OnParameters()
		{
			await JsRuntime.InvokeVoidAsync("OnParametersSet", PageType);
            await JsRuntime.InvokeVoidAsync("dragPopup", "antdraggable", "ant-header");

        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            { 
            
            }
        }

        protected override async Task OnInitializedAsync()
        {

            IsLoginURl = Navigation.Uri;
            IsOfficeJs = await JsRuntime.InvokeAsync<bool>("CheckOfficeJs");

            var layoutCache = MemoryCache.Get<SiteCustomizationDto>(Constants.Cachekeys.CustomLayout);

            if (layoutCache == null)
            {
                var siteLayoutSettingFromDB = await LayoutCustomizationService.GetPublishedSetting();

                MemoryCache.Set(Core.Constants.Cachekeys.CustomLayout, Mapper.Map<LayoutCustomization, SiteCustomizationDto>(siteLayoutSettingFromDB));
                layoutCache = MemoryCache.Get<SiteCustomizationDto>(Constants.Cachekeys.CustomLayout);
            }

            layoutSetting = layoutCache;
            StateHasChanged();

        }
        private async Task CloseNavMenu()
        {
            await JsRuntime.InvokeVoidAsync("closeNav", null);
        }

    }
}
