﻿using Gina2.Blazor.Areas.Identity.IdentityServices;
using Gina2.Core.Interface;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class LoginDisplay
    {
        string sohtname { get; set; } = string.Empty;
        [Inject]
        public ProtectedSessionStorage SessionStorage { get; set; }
        [Inject]
        NavigationManager _Navigation { get; set; }
        [Inject]
        public ICurrentUserService _CurrentUserService { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        private string LoginUrl { get; set; }
        private string LogoutUrl { get; set; }
        private string ExcelClass { get; set; }
        private bool IsOfficeJs { get; set; }
        protected override async Task OnInitializedAsync()
        {
            sohtname = _CurrentUserService.DisplayUserName;

            IsOfficeJs = await JsRuntime.InvokeAsync<bool>("CheckOfficeJs");
            if (IsOfficeJs)
            {
                LoginUrl = "/login?ReturnUrl=/admin/plug-in/home";
                LogoutUrl = "/logout?ReturnUrl=true";
                ExcelClass = "dropdown-menu _menu-excell";
            }
            else
            {
                LoginUrl = "/login";
                LogoutUrl = "/logout";
                ExcelClass = "dropdown-menu";

            }
        }

        void loginPage()
        {
            var uri = _Navigation.ToBaseRelativePath(_Navigation.Uri);
            MemoryCache.Set("url", $"~/{uri}");
            _Navigation.NavigateTo(LoginUrl, forceLoad: true);
        }
        void logout()
        {
            _Navigation.NavigateTo(LogoutUrl, forceLoad: true);
        }
    }
}
