﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class AdminVertical
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        public NavigationManager NavigationManager { get; set; }
        [Inject]
        private IConfiguration Configuration { get; set; }
        private bool isMenuOpen = false;
        private async Task CloseNavMenu()
        {
            await JsRuntime.InvokeVoidAsync("closeNav", null);
        }
        private   Task ToggleMenu(string menuId)
        {
            isMenuOpen = !isMenuOpen;
            // Call JavaScript function to toggle menu state
            JsRuntime.InvokeVoidAsync("toggleMenu", menuId);
            return Task.CompletedTask;
        }
    }
}
