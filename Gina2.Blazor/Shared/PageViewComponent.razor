﻿@using Ra<PERSON>zen
@typeparam T

<EditForm EditContext="@EditContext" id="FrmAddEdit">
    <DataAnnotationsValidator />
    <FormComponent T="T" Columns="Columns" SelectedData="SelectedData"/>
    <Microsoft.AspNetCore.Components.Forms.ValidationSummary></Microsoft.AspNetCore.Components.Forms.ValidationSummary>
</EditForm>
<div class="float-end end-0 content">
    <button type="button" class="btn btn-primary me-1" @onclick="OnSave">Save</button>
    <button type="button" class="btn btn-secondary me-1" @onclick="() => BackToMain()">Close</button>
</div>