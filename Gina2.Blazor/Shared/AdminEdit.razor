﻿@using Gina2.Blazor.Helpers
@implements IDisposable

<Loader IsLoading="@IsLoading" />
<Container Class="forms">
    @switch (Type)
    {
        case "richtextEditior":
            @*            <RadzenHtmlEditor class="_editor" style="min-height: 380px; margin-bottom: 1rem;" @bind-Value="@inputValue">
    <HtmlEditor />
    </RadzenHtmlEditor>*@
            <_quillEditor value="@inputValue" @ref="quillEditorRef"></_quillEditor>
            @if (RichtextEditiorError)
            {
                <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                    <FieldLabel>
                        <Span Class="text-danger"> Required or invalid data </Span>
                    </FieldLabel>
                </Field>
            }
            break;

        case "rangeSlider":

            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Start year of timeline displayed</FieldLabel>
                    <AntDesign.DatePicker Class="w-100" TValue="DateTime?" Picker="year" Value="@RangeStartYear" ValueChanged="e=>ChangeRangeStartYear(e)" />
                    @if (RangeStartYearError)
                    {
                        <Span Class="text-danger"> Required</Span>
                    }
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>End year of timeline displayed</FieldLabel>
                    <AntDesign.DatePicker Class="w-100" TValue="DateTime?" Picker="year" Value="@RangeEndYear" ValueChanged="e=>ChangeRangeEndYear(e)" />
                    @if (RangeEndYearError)
                    {
                        <Span Class="text-danger"> Required</Span>
                    }
                </Field>
            </Fields>

            <Fields>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Default start year of data pulled</FieldLabel>
                    <AntDesign.DatePicker Class="w-100" TValue="DateTime?" Picker="year" Value="@RangeSelectedStartYear" ValueChanged="e=>ChangeRangeSelectedStartYear(e)" />
                    @if (RangeSelectedStartYearError)
                    {
                        <Span Class="text-danger"> Required</Span>
                    }
                </Field>
                <Field ColumnSize="ColumnSize.Is6.OnTablet.Is12.OnMobile.Is6.OnDesktop.Is6.OnWidescreen.Is6.OnFullHD">
                    <FieldLabel>Default end year of data pulled</FieldLabel>
                    <AntDesign.DatePicker Class="w-100" TValue="DateTime?" Picker="year" Value="@RangeSelectedEndYear" ValueChanged="e=>ChangeRangeSelectedEndYear(e)" />
                    @if (RangeSelectedEndYearError)
                    {
                        <Span Class="text-danger"> Required</Span>
                    }
                </Field>
            </Fields>
            break;
        case "input":
            @if (PageConfigurations.Any(e => e.Name.Contains("Label")))
            {
                <Field>
                    <FieldLabel>Title  <Span>*</Span></FieldLabel>
                    <TextEdit Text="@labelValue" TextChanged="@TitleKeyPress">
                    </TextEdit>
                    @if (@TitleFiledError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Required </Span>
                            </FieldLabel>
                        </Field>
                    }
                    @if (@TitleHtmlError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Please remove html or css </Span>
                            </FieldLabel>
                        </Field>
                    }
                </Field>
            }
            @if (PageConfigurations.Any(e => e.Name.ToLower().Contains("tooltip")))
            {
                <Field>
                    <FieldLabel>Tooltip  <Span>*</Span></FieldLabel>
                    <TextEdit Text="@tooltipValue" TextChanged="@TooltipKeyPress">
                    </TextEdit>
                    @if (@TooltipFiledError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Required </Span>
                            </FieldLabel>
                        </Field>
                    }
                    @if (TooltipHtmlError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Please remove html or css </Span>
                            </FieldLabel>
                        </Field>
                    }
                </Field>
            }
            @if (PageConfigurations.Any(e => e.Name.Contains("Placeholder")))
            {
                <Field>
                    <FieldLabel>Placeholder</FieldLabel>
                    <TextEdit @bind-Text="@placeHolderValue">
                    </TextEdit>
                    @if (@PlaceHolderFiledError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Required </Span>
                            </FieldLabel>
                        </Field>
                    }
                    @if (PlaceHolderHTmlError)
                    {
                        <Field ColumnSize="ColumnSize.Is12.OnTablet.Is12.OnMobile.Is12.OnDesktop.Is12.OnWidescreen.Is12.OnFullHD" Class="p-0">
                            <FieldLabel>
                                <Span Class="text-danger"> Please remove html or css </Span>
                            </FieldLabel>
                        </Field>
                    }
                </Field>
            }
            break;
    }

    <Button Class="but-blue" Clicked="@OnCancelClicked"> Cancel </Button>
    <Button Class="but-yellow" Clicked="@OnUpdateClicked" Disabled="@CanSave()"> Save </Button>

</Container>