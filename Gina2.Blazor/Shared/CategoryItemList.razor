﻿@using Gina2.Blazor.Models
@using Blazorise;

<Loader IsLoading="@IsLoading" />
<Div Flex="Flex.JustifyContent.Between" Class="policies-fr2 mob-flex-colm">
    <Div Class="item1">
        <Select  data-cy="SelectionOptions"  TValue="string" class="pl-3 pr-3" SelectedValue="@selectedCategory" SelectedValueChanged="@OnSelectedValueChanged">
            <SelectItem Value="@DefaultCategoryValue"  data-cy="AnyText" >-Any-</SelectItem>
            <Repeater Items="@Categories">
                <SelectItem Value="context.Id.ToString()" data-cy=@($"{context.Name.Split(" ")[0]}Name")>@context.Name</SelectItem>
            </Repeater>
        </Select>
    </Div>
    <Div Class="item2">
        <Button data-cy="ApplyBtn" Class="but-yello pl-5 pr-5" Clicked="@Filter">Apply</Button>
        <Button data-cy="Reset" Class="but-by-wite" Clicked="@Reset">Reset</Button>
    </Div>
</Div>
<Accordion Class="accor-fbox pb-6 search-box">

    <Repeater Items="@Data.GroupBy(s=>s.CategoryName)" Context="categoryItemContext">
        @if (categoryItemContext.Key != null)
        {
            <Heading class="pt-5" Size="HeadingSize.Is3" data-cy=@($"{categoryItemContext.Key.Replace(" ","")}Key")>@categoryItemContext.Key</Heading>
        }

        @{
            string gridshideshow = string.Empty;
            if (categoryItemContext.Key != null)
            {
                
                gridshideshow = $"DataGridshows{categoryItemContext.Key}";
            }
             
            List<PublishedListItem> groupedData = new List<PublishedListItem>();
            if (!string.IsNullOrEmpty(gridshideshow))
            {
                groupedData = Data.Where(s => s.CategoryName == @categoryItemContext.Key).ToList();
                Task.Delay(100);
            }
        }
        <Div id="@gridshideshow" Class="DataGrids paginationareaa">
            <DataGrid FixedHeaderDataGridMaxHeight="500px"
                      FixedHeaderDataGridHeight="450px"
                      TItem="@PublishedListItem"
                      Data="@groupedData"
                      Responsive
                      ShowPager
                      ShowPageSizes
                      SortMode="DataGridSortMode.Single"
                      Sortable="true">
                <EmptyTemplate>
                    <Div data-cy="NoDataFound" > No data found </Div>
                </EmptyTemplate>
                <DataGridColumns>
                    <DataGridColumn Caption="@TableTitle" Sortable="true" Field="@nameof(PublishedListItem.Title)" Width="72%">
                                <DisplayTemplate>
                            <NavLink Class="_linkhover" href="@($"/countries/{CountryCode}/{DetailUrlContext}/{context.Key}")">@context.Title</NavLink>
                                </DisplayTemplate>
                            </DataGridColumn>
                    <DataGridColumn Field="@nameof(PublishedListItem.StartYear)" Caption=" Start year" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="false" Width="14%" SortDirection="@Blazorise.SortDirection.Descending" />
                            <DataGridColumn Field="@nameof(PublishedListItem.EndYear)" Caption=" End year" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center" Sortable="false" Width="13%" />
                            <DataGridColumn Width="1%" TextAlignment="TextAlignment.Center" HeaderTextAlignment="TextAlignment.Center">
                                <CaptionTemplate>
                                    <Button Class="_sort-down" Clicked="@(() => TabGridhideshow(gridshideshow))"><i Class="fas fa-sort-down"></i></Button>
                                </CaptionTemplate>
                            </DataGridColumn>
                  </DataGridColumns>
                <ItemsPerPageTemplate></ItemsPerPageTemplate>
                <TotalItemsTemplate>
                    <Badge TextColor="TextColor.Dark">
                        @((context.CurrentPageSize * (@context.CurrentPage - 1) + 1)) - @(Math.Min(((@context.CurrentPage - 1) * context.CurrentPageSize) + context.CurrentPageSize, context.TotalItems ?? 0))  of @context.TotalItems data items
                    </Badge>
                </TotalItemsTemplate>
            </DataGrid>
        </Div>
    </Repeater>
    <Div Flex='Flex.JustifyContent.Center'>@(Data.Count == 0 ? "No Data Found" : "")</Div>
</Accordion>