﻿@using Gina2.Blazor.Areas.Identity.IdentityServices
<AuthorizeView>
    <Authorized>
        <Div Class="btn-group logout">

            <button type="button" class="btn butt-w dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <Div Class="userround">@sohtname</Div>
            </button>
            <ul class="@ExcelClass">
                <li><a class="dropdown-item" @onclick="logout" >Log out</a></li>
            </ul>

        </Div>
    </Authorized>
    <NotAuthorized>
        <Button class="login-link" @onclick=@loginPage>Log in</Button>
    </NotAuthorized>
</AuthorizeView>
