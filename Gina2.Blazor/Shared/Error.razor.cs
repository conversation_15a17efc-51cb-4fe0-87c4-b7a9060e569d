﻿using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared
{
    public partial class Error
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }
        [Inject]
        public ILogger<Error> logger { get; set; }
        public void ProcessError(Exception ex)
        {
            logger.LogError("Error:ProcessError - Type: {Type} Message: {Message}",
                ex.GetType(), ex.Message);
        }
    }
}
