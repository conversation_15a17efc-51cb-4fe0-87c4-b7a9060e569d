﻿@using System.Text.Json;
@using Gina2.Core.Extensions;
@inject IJSRuntime JSRuntime

<div class="_EditorElement">
    @if (EditorEnabled)
    {
        <div id="toolbar">
        </div>
    }
    <div @ref="@DivEditorElement" />
</div>

@code {
    [Parameter]
    public ElementReference DivEditorElement { get; set; }
    private string EditorHTMLContent;
    private bool EditorEnabled = true;

    [Parameter]
    public string value { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeAsync<string>("createQuill", DivEditorElement, value);
        }
    }

    public async Task<string> GetHTML()
    {
        try
        {
            EditorHTMLContent = await JSRuntime.InvokeAsync<string>("getQuillHTML", DivEditorElement);
            if (EditorHTMLContent == "<p><br></p>")
            {
                EditorHTMLContent = string.Empty;
            }
            return EditorHTMLContent;
        }
        catch (Exception)
        {
            throw;
        }
    }


    protected override async Task OnParametersSetAsync()
    {
        try
        {
            await base.OnParametersSetAsync();
            await Task.Delay(500);
            await JSRuntime.InvokeAsync<object>("loadContent", DivEditorElement, value);
        }
        catch (Exception)
        {
            throw;
        }
    }
}
