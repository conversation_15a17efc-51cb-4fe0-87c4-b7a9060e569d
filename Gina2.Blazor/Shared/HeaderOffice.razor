﻿<div class="container-fluid logo-menu _excleheader" style="@Hesderst">
    <nav class="navbar navbar-expand-lg navbar-dark">
        <Div class="navbar-brand" Match="NavLinkMatch.All">
            <a href="https://www.who.int"> <img class="img-responsive app-headerimage" id="img-app-header" width="136" src="img/@(layoutSetting.HeaderImage)" /></a>
            <span class="logotext header-logotext" id="header-title" data-cy="HeaderTitle">
                @((MarkupString)@layoutSetting.Title) new
            </span>
        </Div>

        @if (!IsOfficeJs)
        {
            <button class="navbar-toggler float-r" type="button" data-cy="HomeToggleMenuButton" @onclick="ToggleNavMenu">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse text-r" id="navbarNavDropdown" data-cy="HomeNavBarDropDown">
                <LoginDisplay />
            </div>
        }
        else
        {
            <LoginDisplay />

        }

    </nav>

</div>
