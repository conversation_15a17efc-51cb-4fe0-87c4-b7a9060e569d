using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AntDesign;
using Microsoft.AspNetCore.Components;
using OneOf.Types;

namespace Gina2.Blazor.Shared
{
    public partial class GlobalExceptionHandling
    {
        [Parameter]
        public string ErrorMessage { get; set; }

        [Parameter]
        public string ErrorDetail { get; set; }

        [Parameter]
        public Exception ErrorException { get; set; }
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        public AntDesign.NotificationService _notice { get; set; }
        public string Home { get; set; }
        public string CurrentPage { get; set; }
        protected override async Task OnInitializedAsync()
        {
            Home = NavigationManager.BaseUri;
            CurrentPage = NavigationManager.Uri;
            Logger.LogError("Error:ProcessError - Type: {Type}", ErrorException);
            await OpenToaster("500", "Something went wrong", AntDesign.NotificationType.Error);
         //   NavigationManager.NavigateTo(NavigationManager.Uri, forceLoad: true);

        }
        public void RefreshPage(string url)
        {
            NavigationManager.NavigateTo(url, forceLoad: true);
        }

        public async Task OpenToaster(string title, string description, AntDesign.NotificationType type)
        {
            await _notice.Open(new NotificationConfig()
            {
                Message = title,
                Key = Guid.NewGuid().ToString(),
                Description = description,
                NotificationType = type
            });
            StateHasChanged();
        }
    }
}