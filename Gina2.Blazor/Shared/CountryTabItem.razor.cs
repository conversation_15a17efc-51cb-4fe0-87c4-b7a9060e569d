﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class CountryTabItem
    {

        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        [CascadingParameter(Name = "Title")]
        public string Title { get; set; }

        [CascadingParameter(Name = "AdminTitle")]
        public string AdminTitle { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "CountryName")]
        public string CountryName { get; set; }

        private async Task NutritionCountry(string url)
        {
            try
            {

                await JSRuntime.InvokeVoidAsync("open", CancellationToken.None, url, "_blank");
            }
            catch (Exception)
            {
                throw;
            }

        }
    }
}
