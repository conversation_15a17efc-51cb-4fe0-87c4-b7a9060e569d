﻿@using Gina2.Blazor.Helpers.PageConfigrationData;
@inherits PageConfirgurationComponent
<PageTitle>GIFNA @(CountryName ?? ContentName)</PageTitle>
<Container Fluid Padding="Padding.Is0">
    @*Move the following style to css*@
    <Card Class="allbanner bord-or" Style="background-image: url(../img/policies-list.png);">
        <Div class="container ginasearch pt-0 pb-4 mb-5 mob-0-b">
            <Div Flex="Flex.JustifyContent.Between" Class="downl-flex pt-5">
                <Div Class="item1">
                    <Heading Size="HeadingSize.Is3" data-cy="CountryTitle">@(CountryName ?? ContentName)</Heading>
                        @* <Heading Size="HeadingSize.Is3">
                            @((MarkupString)@PageConfigurations.GetPageConfigrationValueByName(ContentListConfigurationKey.SubHeading))
                        <AdminEditbut Key="@ContentListConfigurationKey.SubHeading" />
                    </Heading> *@
                </Div>
            </Div>
        </Div>
    </Card>
</Container>

<Container Fluid Class="bg-trdot pt-4">
    <Container Class="policy-li">
        <Tabs SelectedTab="@SelectedTab" Class="policy-list" SelectedTabChanged="@OnSelectedTabChanged">
            <Items>
                <Tab hidden="@(CountryCode != null ? false : true)" data-cy="Policies" Name="policies">Policies</Tab>
                <Tab hidden="@(CountryCode != null ? false : true)" data-cy="Programmes/Actions" Name="programmes-and-actions">Programmes / actions</Tab>
                <Tab hidden="@(CountryCode != null ? false : true)" data-cy="Mechanisms" Name="mechanisms">Mechanisms</Tab>
                <Tab hidden="@(CountryCode != null ? false : true)" data-cy="Commitments" Name="commitments">Commitments</Tab>
            </Items>
            <Content>
                    <TabPanel Name="@SelectedTab" Class="@(CountryCode != null ? "pt-4 m-pt-2" : "pt-7 m-pt-2")">
                    <CascadingValue Value="@CountryName" Name="CountryName">
                        @ChildContent
                    </CascadingValue>
                </TabPanel>
            </Content>
        </Tabs>
    </Container>
</Container>
