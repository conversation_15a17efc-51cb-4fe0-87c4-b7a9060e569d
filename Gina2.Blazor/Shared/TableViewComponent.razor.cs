﻿using Blazorise;
using Gina2.Blazor.ClassValues;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;

namespace Gina2.Blazor.Shared
{
    public partial class TableViewComponent<T> where T : class
    {
        [Inject]
        IMessageService MessageService { get; set; }

        [Parameter]
        public List<ClassValue> Columns { get; set; }

        [Parameter]
        public ObservableCollection<T> Data { get; set; }

        [Parameter]
        public string PageName { get; set; }

        [Parameter]
        public EventCallback<bool> PopupState { get; set; }

        [Parameter]
        public EventCallback<Guid> SelectionChanged { get; set; }

        [Parameter]
        public EventCallback<T> OnDataDeleted { get; set; }

        private async Task Edit(object id)
        {
            if (id is Guid primaryId && primaryId != Guid.Empty)
            {
                _ = SelectionChanged.InvokeAsync(primaryId);
                await PopupState.InvokeAsync(true);
            }
        }

        private async Task Delete(object id)
        {
            if (id is Guid primaryId && primaryId != Guid.Empty)
            {
                if (await MessageService.Confirm("Are you sure you want to delete the " + PageName + "?", "Confirmation"))
                {
                    T selectedData = Data.FirstOrDefault(x => x.GetType().GetProperty("Id").GetValue(x, null).Equals(primaryId));
                    if (selectedData != null)
                    {
                        Data.Remove(selectedData);
                        await OnDataDeleted.InvokeAsync();
                    }
                }
            }
        }
    }
}
