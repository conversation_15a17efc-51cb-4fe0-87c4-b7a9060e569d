﻿<Loader IsLoading="@IsLoading" />
<Divider Class="divi-blue"/>
<Div Flex="Flex.JustifyContent.Between" Class="mob-f-column">
 <Div>
<Heading Size="HeadingSize.Is3">@RegionsName</Heading>
</Div>
    <Div>
    <Select TValue="int" Class="select-blue pl-2 pr-4" SelectedValue="@SelectedValue" SelectedValueChanged="@OnSelectedValueChanged">
            <SelectItem Value="0">WHO Member States</SelectItem>
        <SelectItem Value="1">Areas and territories</SelectItem>
    </Select>
    </Div>
</Div>
<Div Class="_countryListGroup">
<ListGroup>
<Repeater Items="@FirstColumnCountries">
       <ListGroupItem>
                <NavLink href="@($"countries/{context.CountryCode}/{SelectedDataUrl}")">@context.CountryName</NavLink>
       </ListGroupItem>
</Repeater>
</ListGroup>
<ListGroup>
<Repeater Items="@SecondColumnCountries">
            <ListGroupItem><NavLink href="@($"countries/{context.CountryCode}/{SelectedDataUrl}")">@context.CountryName</NavLink></ListGroupItem>
</Repeater>
</ListGroup><ListGroup>
<Repeater Items="@ThirdColumnCountries">
            <ListGroupItem><NavLink href="@($"countries/{context.CountryCode}/{SelectedDataUrl}")">@context.CountryName</NavLink></ListGroupItem>
</Repeater>
</ListGroup>
</Div>
<Divider Class="mt-5"/>