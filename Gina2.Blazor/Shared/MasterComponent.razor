﻿@typeparam T
@inject IJSRuntime JsRuntime
@using Microsoft.AspNetCore.Components.Forms

@if (!IsShowPopup)
{
    <div class="mb-3 d-flex justify-content-between">
        <h3 class="m-0">@PageName</h3>
        <button onclick="@Add" class="btn btn-primary">Add</button>
    </div>
    <TableViewComponent T="T" PageName="@PageName" Columns="@Columns" Data="@Data" SelectionChanged="@SelectionChanged" OnDataDeleted="@DataDeleted" PopupState="ResetPopup" />
}
else
{
    <PageViewComponent T="T" Columns="@Columns" Title="@Title" SelectedData="SelectedData" PopupState="ResetPopup" SaveData="@Save" />
}

<Snackbar @ref="SuccessMessagePrompt" Color="SnackbarColor.Primary" Interval="5000" Location="SnackbarLocation.End">
    <SnackbarBody>
        @Message
    </SnackbarBody>
</Snackbar>