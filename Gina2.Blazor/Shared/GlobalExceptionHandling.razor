﻿
@inject ILogger<Error> Logger

<Container>
    <Div Flex="Flex.JustifyContent.Center.AlignItems.Center" Class="notfount">
        <h2>Sorry for the  inconvenience</h2>
        <p> We are experiencing technical difficulties and are working to correct the problem as quickly as possible. Sorry for the inconvenience. If you have questions please contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <Div Flex="Flex.JustifyContent.Center.AlignItems.Center">
            <NavLink Class="but-yellow m-1" onclick="@(()=> RefreshPage(Home))">Home</NavLink>
            <NavLink Class="but-yellow m-1" onclick="@(()=> RefreshPage(CurrentPage))">Refresh</NavLink>
        </Div>
    </Div>
</Container>