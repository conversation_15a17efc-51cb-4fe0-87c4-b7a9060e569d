using Blazored.Modal;
using Blazored.Modal.Services;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
namespace Gina2.Blazor.Shared
{
    public partial class AdminEditbutDoubleModel
    {
        [Parameter]
        public int Id { get; set; }
        [Parameter]
        public string Key { get; set; }
        [Inject]
        Blazored.Modal.Services.IModalService ModalService {get; set;}
        [CascadingParameter]
        BlazoredModalInstance BlazoredModal { get; set; } = default!;
        public bool IsLoading { get; set; } = false;
        private async Task AdminBar()
        {
            await ShowLoader(true);
            var options = new ModalOptions()
            {
                Position = ModalPosition.Middle,
                DisableBackgroundCancel = true,
                ActivateFocusTrap = false,
                HideCloseButton= true,
                OverlayCustomClass = "_zindex",
                Class = "blazored-modal size-medium",
            };
            var modelData = new ModalParameters
            {
                { "Key", Key }
            };
            ModalService.Show<AdminEdit>("Update", modelData, options);
            await ShowLoader(false);
            StateHasChanged();
        }
        private async Task ShowLoader(bool value)
        {
            await Task.Run(() =>
            {
                IsLoading = value;
            });
        }
    }
}