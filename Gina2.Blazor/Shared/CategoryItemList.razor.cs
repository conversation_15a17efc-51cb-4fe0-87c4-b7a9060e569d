﻿using Gina2.Blazor.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class CategoryItemList
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IJSRuntime JsRuntime { get; set; }

        [CascadingParameter(Name = "CountryCode")]
        public string CountryCode { get; set; }

        [CascadingParameter(Name = "Data")]
        public List<PublishedListItem> Data { get; set; }

        [CascadingParameter(Name = "Title")]
        public string Title { get; set; }

        [CascadingParameter(Name = "TypeofTitle")]
        public string TypeofTitle { get; set; }

        [CascadingParameter(Name = "TableTitle")]
        public string TableTitle { get; set; }
        [CascadingParameter(Name = "DetailUrlContext")]
        public string DetailUrlContext { get; set; }
        [CascadingParameter(Name = "Categories")]
        public IEnumerable<TypeList> Categories { get; set; }
        private List<Models.PublishedListCategoryItem> categorizedItems = new();
        private const string DefaultCategoryValue = "0";
        private string selectedCategory = DefaultCategoryValue;
        List<Models.PublishedListItem> clonesData;

        public bool IsLoading { get; set; } = false;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                clonesData = Data;
                // BuildCategorizedItems();
                await InvokeAsync(StateHasChanged);
            }
        }

        private void BuildCategorizedItems()
        {
            if (!selectedCategory.Equals(DefaultCategoryValue))
            {
                Data = clonesData.Where(d => d.Category != null && d.Category.Equals(selectedCategory, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            else
            {
                Data = clonesData;
            }
        }

        private void Filter()
        {
            BuildCategorizedItems();
            StateHasChanged();
        }

        private void Reset()
        {
            selectedCategory = DefaultCategoryValue;
            Data = clonesData;
        }

        private void OnSelectedValueChanged(string value)
        {
            selectedCategory = value;
        }

        private async Task TabGridhideshow(string name)
        {
            await JsRuntime.InvokeVoidAsync("griddataHide", name);
            StateHasChanged();
        }

    }
}