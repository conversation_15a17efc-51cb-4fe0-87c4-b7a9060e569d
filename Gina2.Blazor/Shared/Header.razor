﻿    <div class="container-fluid logo-menu"  style="@Hesderst">
    <nav class="navbar navbar-expand-lg navbar-dark">
        <Div class="navbar-brand" Match="NavLinkMatch.All">
            <a href="https://www.who.int"> <img class="img-responsive app-headerimage" id="img-app-header" width="136" src="img/@(layoutSetting.HeaderImage)" /></a>
            <span class="logotext header-logotext" id="header-title" data-cy="HeaderTitle">
               @((MarkupString)@layoutSetting.Title) 
            </span>
        </Div>
        
        @if (!IsOfficeJs)
        {
            <button class="navbar-toggler float-r" type="button" data-cy="HomeToggleMenuButton" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
           <div class="collapse navbar-collapse text-r" id="navbarNavDropdown" data-cy="HomeNavBarDropDown">
            <NavMenu />

            
           @*<div class="langSelector">
                <div class="lang-icon"><img src="../img/svg/Translation.svg"></div>
                <AntDesign.SimpleSelect DefaultValue="en" Disabled OnSelectedItemChanged="handleChange">
                    <SelectOptions>
                            <AntDesign.SimpleSelectOption Value="en" Label="English"></AntDesign.SimpleSelectOption>
                            <AntDesign.SimpleSelectOption Value="fr" Label="Français"></AntDesign.SimpleSelectOption>
                            <AntDesign.SimpleSelectOption Value="es" Label="Español"></AntDesign.SimpleSelectOption>
                    </SelectOptions>
                </AntDesign.SimpleSelect>
            </div>*@
            <LoginDisplay />
            </div>
        }
        else
        {
            <LoginDisplay />
            
        }

    </nav>

</div>
