using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.Web.Virtualization;
using Microsoft.JSInterop;
using Gina2.Blazor;
using Gina2.Blazor.Shared;
using Gina2.Blazor.Shared.TabComponents.PolicyTab;
using Gina2.Blazor.Shared.TabComponents.MechanismTab;
using Gina2.Blazor.Shared.TabComponents.ProgramAndActionTab;
using Gina2.Blazor.Shared.TabComponents.CommitmentTab;
using Gina2.Blazor.Resources;
using Microsoft.Extensions.Localization;
using Blazorise;
using Blazorise.Snackbar;
using Blazorise.Components;
using Blazorise.RichTextEdit;
using ChartJs.Blazor;
using Radzen.Blazor;
using Plk.Blazor.DragDrop;
using Blazorise.DataGrid;
using Domain.Policy;
using Gina2.MySqlRepository.Models;
using Gina2.Blazor.Shared.GControl;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Identity;
using Gena2.Blazor.Identity.Areas.Identity.Pages.Account;
using Blazored.Modal;
using Blazored.Modal.Services;
using Gina2.Blazor.Helpers;
using Gina2.Core.Constant;
using Gina2.Core.Extensions;
using Gina2.Blazor.Models;

namespace Gina2.Blazor.Shared
{
    public partial class HeaderOffice
    {
        [CascadingParameter]
        public SiteCustomizationDto layoutSetting { get; set; }

        [Parameter]
        public string Hesderst { get; set; } = "position:fixed;";
        private bool IsOfficeJs { get; set; }
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        protected override async Task OnInitializedAsync()
        {
            IsOfficeJs = await JsRuntime.InvokeAsync<bool>("CheckOfficeJs");

        }
        private async Task ToggleNavMenu()
        {
            await JsRuntime.InvokeVoidAsync("openNav", null);
        }
        void handleChange(string value)
        {
            Console.WriteLine(value);
        }
        void handleItemsChange(IEnumerable<string> value)
        {
            Console.WriteLine(value);
        }
    }
}