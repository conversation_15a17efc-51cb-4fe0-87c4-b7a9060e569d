using Blazorise;
using Gina2.Blazor.CacheService;
using Gina2.Blazor.Helpers;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;
using Blazored.Modal;
using Blazored.Modal.Services;
using Gina2.Services.PageConfigurations;
using AntDesign;
using Gina2.Core.Constant;
using Gina2.Core.Methods;
using Gina2.Core.Extensions;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Caching.Memory;
using Gina2.Blazor.Shared.GControl;
using Microsoft.JSInterop;

namespace Gina2.Blazor.Shared
{
    public partial class AdminEdit
    {
        [Inject]
        private IJSRuntime JsRuntime { get; set; }
        [Inject]
        private IMemoryCache MemoryCache { get; set; }
        [Parameter]
        public EventCallback<string> Changed { get; set; }
        [Parameter]
        public string Value { get; set; }
        [Parameter]
        public string Key { get; set; }
        public List<PageConfiguration> PageConfigurations { get; set; } = new List<PageConfiguration>();
        public string Type { get; set; }
        [Inject]
        private PageConfigrationCache pageConfigrationCache { get; set; }
        [Inject]
        NavigationManager MyNavigationManager { get; set; }
        [Inject]
        private IPageConfigurationService pageConfigrationService { get; set; }
        [CascadingParameter]
        BlazoredModalInstance BlazoredModal { get; set; } = default!;
        private _quillEditor quillEditorRef;
        public bool RichtextEditiorError { get; set; } = false;
        public bool TitleFiledError { get; set; } = false;
        public bool TitleHtmlError { get; set; } = false;
        public bool PlaceHolderFiledError { get; set; } = false;
        public bool PlaceHolderHTmlError { get; set; } = false;
        public bool TooltipFiledError { get; set; } = false;
        public bool TooltipHtmlError { get; set; } = false;
        public DateTime? RangeStartYear { get; set; }
        public bool RangeStartYearError { get; set; } = false;
        public DateTime? RangeEndYear { get; set; }
        public bool RangeEndYearError { get; set; } = false;
        public DateTime? RangeSelectedStartYear { get; set; }
        public bool RangeSelectedStartYearError { get; set; } = false;
        public DateTime? RangeSelectedEndYear { get; set; }
        public bool RangeSelectedEndYearError { get; set; } = false;
        [Inject]
        public AntDesign.NotificationService _notice { get; set; }
        private string inputValue = "";
        private string labelValue = "";
        private string tooltipValue = "";
        private string placeHolderValue = "";
        public bool IsLoading { get; set; } = false;
        protected override async Task OnInitializedAsync()
        {
            if (Key != null && Key.Split(':').Length == 3)
            {
                Key = Key.Split(':').ElementAt(1) + ":";
            }

            await SetPageConfigurations();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if(firstRender)
            {
                await JsRuntime.InvokeVoidAsync("dragPopup", "draggable", "bm-header");
            }
        }

        private async Task SetPageConfigurations()
        {
            PageConfigurations = await pageConfigrationCache.GetAllRecordAsync(MyNavigationManager.GetPageName(), Key);
            if (CheckKey() == "richtextEditior")
            {
                inputValue = PageConfigurations.FirstOrDefault().Value;
                PageConfigurations = PageConfigurations.GetPageConfigrationByName(Key);
            }
            else
            {
                PageConfigurations = PageConfigurations.GetPageConfigrationsByName(Key);
            }
            labelValue = PageConfigurations.FirstOrDefault(e => e.Name.Contains("Label"))?.Value;
            tooltipValue = PageConfigurations.FirstOrDefault(e => e.Name.ToLower().Contains("tooltip"))?.Value;
            placeHolderValue = PageConfigurations.FirstOrDefault(e => e.Name.Contains("Placeholder"))?.Value;
        }

        private async Task ShowLoader(bool value)
        {
            await Task.Run(() =>
            {
                IsLoading = value;
            });
        }

        private string CheckKey()
        {
            string type = "richtextEditior";
            var test = MyNavigationManager.Uri;
            if (PageConfigurations.Any(e => e.Name.Contains("Label") && e.Page == MyNavigationManager.GetPageName()))
            {
                type = "input";
            }
            else if (PageConfigurations.Any(e => e.Name.Contains("RangeSlider:") && e.Page == MyNavigationManager.GetPageName()))
            {
                type = "rangeSlider";
                SetRangeSliderData();
            }
            return Type = type;
        }
        private void SetRangeSliderData()
        {
            string value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(MapPageConfigurationKey.RangeStartYear)).Value;
            RangeStartYear = new DateTime(Convert.ToInt32(value), 1, 1);
            value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(MapPageConfigurationKey.RangeEndYear)).Value;
            RangeEndYear = new DateTime(Convert.ToInt32(value), 1, 1);
            value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(MapPageConfigurationKey.SelectedStartYear)).Value;
            RangeSelectedStartYear = new DateTime(Convert.ToInt32(value), 1, 1);
            value = PageConfigurations.FirstOrDefault(e => e.Name.Contains(MapPageConfigurationKey.SelectedEndYear)).Value;
            RangeSelectedEndYear = new DateTime(Convert.ToInt32(value), 1, 1);
        }
        public void ChangeRangeStartYear(DateTime? dateTime)
        {
            RangeStartYearError = false;
            if (!dateTime.HasValue)
            {
                RangeStartYear = dateTime;
                RangeStartYearError = true;
                return;
            }
            PageConfigurations.FirstOrDefault(e => e.Name.Contains("RangeSlider:StartYear")).Value = Convert.ToString(dateTime.Value.Year);
            RangeStartYear = dateTime;
            StateHasChanged();
        }
        public void ChangeRangeEndYear(DateTime? dateTime)
        {
            RangeEndYearError = false;
            if (!dateTime.HasValue)
            {
                RangeEndYear = dateTime;
                RangeEndYearError = true;
                return;
            }
            RangeEndYear = dateTime;
            PageConfigurations.FirstOrDefault(e => e.Name.Contains("RangeSlider:EndYear")).Value = Convert.ToString(dateTime.Value.Year);
        }
        public void ChangeRangeSelectedStartYear(DateTime? dateTime)
        {
            RangeSelectedStartYearError = false;
            if (!dateTime.HasValue)
            {
                RangeSelectedStartYear = dateTime;
                RangeSelectedStartYearError = true;
                return;
            }
            RangeSelectedStartYear = dateTime;
            PageConfigurations.FirstOrDefault(e => e.Name.Contains("RangeSlider:SelectedStartYear")).Value = Convert.ToString(dateTime.Value.Year);
        }
        public void ChangeRangeSelectedEndYear(DateTime? dateTime)
        {
            RangeSelectedEndYearError = false;
            if (!dateTime.HasValue)
            {
                RangeSelectedEndYearError = true;
                return;
            }
            RangeSelectedEndYear = dateTime;
            PageConfigurations.FirstOrDefault(e => e.Name.Contains("RangeSlider:SelectedEndYear")).Value = Convert.ToString(dateTime.Value.Year);
        }

        private void OnChange(string html)
        {
            RichtextEditiorError = false;
            html = html.SanitizeContent();
            if (string.IsNullOrEmpty(html) || html.IsValid())
            {
                RichtextEditiorError = true;
                return;
            }
            PageConfigurations.FirstOrDefault().Value = html;
        }
        private void TitleKeyPress(string value)
        {
            labelValue = value;
            TitleFiledError = false;
            TitleHtmlError = false;
            if (string.IsNullOrEmpty(value) || string.IsNullOrWhiteSpace(value))
            {
                TitleFiledError = true;
                return;
            }
            if (RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}"))
            {
                TitleHtmlError = true;
                return;
            }
            StateHasChanged();
        }
        private void PlaceHolderKeyPress(string value)
        {
            placeHolderValue = value;
            PlaceHolderFiledError = false;
            PlaceHolderHTmlError = false;
            if (string.IsNullOrEmpty(value))
            {
                PlaceHolderFiledError = true;
                return;
            }
            if (RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}"))
            {
                PlaceHolderHTmlError = true;
                return;
            }
        }
        private void TooltipKeyPress(string value)
        {
            tooltipValue = value;
            TooltipFiledError = false;
            TooltipHtmlError = false;
            if (string.IsNullOrEmpty(value))
            {
                TooltipFiledError = true;
                return;
            }
            if (RegexHelper.IsRegexMatch(value, @"<[^>]+>|.* {.*}"))
            {
                TooltipHtmlError = true;
                return;
            }
        }
        public async Task OnUpdateClicked()
        {
            if (Type == "richtextEditior")
            {
                if (quillEditorRef != null)
                {
                    RichtextEditiorError = false;

                    inputValue = await quillEditorRef.GetHTML();

                    var quillInput = Regex.Replace(inputValue, "<.*?>", String.Empty);

                    if (string.IsNullOrWhiteSpace(quillInput) || string.IsNullOrEmpty(inputValue) || inputValue.IsValid())
                    {
                        RichtextEditiorError = true;
                        return;
                    }
                    inputValue = inputValue.SanitizeContent();
                    PageConfigurations.FirstOrDefault().Value = inputValue;
                }
            }
            else if (Type == "input")
            {
                if (PageConfigurations.Any(e => e.Name.Contains("Label")))
                {
                    PageConfigurations.FirstOrDefault(e => e.Name.Contains("Label")).Value = labelValue;
                }
                if (PageConfigurations.Any(e => e.Name.ToLower().Contains("tooltip")))
                {
                    PageConfigurations.FirstOrDefault(e => e.Name.ToLower().Contains("tooltip")).Value = tooltipValue;
                }
                if (PageConfigurations.Any(e => e.Name.ToLower().Contains("placeholder")))
                {
                    PageConfigurations.FirstOrDefault(e => e.Name.ToLower().Contains("placeholder")).Value = placeHolderValue;
                }
            }
            await ShowLoader(true);
            var isUpdate = await pageConfigrationService.UpdateAllPageConfigurations(PageConfigurations);

            await BlazoredModal.CancelAsync();
            if (isUpdate)
            {
                await pageConfigrationCache.RefreshData();
                await OpenToaster("Record Updated", $"Record updated successfully", AntDesign.NotificationType.Success);
            }
            else
            {
                await OpenToaster("Failed", $"Record update failed", AntDesign.NotificationType.Error);
            }
            await ShowLoader(false);
            await InvokeAsync(StateHasChanged);
        }
        public async Task OnCancelClicked()
        {
            await BlazoredModal.CancelAsync();
            //await JsRuntime.InvokeVoidAsync("closeDraggableModal");
            MemoryCache.Remove("modal");
            await pageConfigrationCache.RefreshData();
            //await SetPageConfigurations();
        }

        public async Task OpenToaster(string title, string description, AntDesign.NotificationType type)
        {
            await _notice.Open(new NotificationConfig()
            {
                Message = title,
                Key = Guid.NewGuid().ToString(),
                Description = description,
                NotificationType = type
            });
            StateHasChanged();
        }
        public bool CanSave()
        {

            //bool firstComponent = RichtextEditiorError;
            bool secondComponent = TitleFiledError || PlaceHolderFiledError || TooltipHtmlError || PlaceHolderHTmlError || TooltipFiledError || TitleHtmlError;
            bool thridComponent = RangeStartYearError || RangeEndYearError || RangeSelectedStartYearError || RangeSelectedEndYearError;
            return secondComponent || thridComponent;
        }

        void IDisposable.Dispose()
        {
            MemoryCache.Remove("modal");
        }
        void showmymodal()
        {
            JsRuntime.InvokeVoidAsync("draggableModal");
        }
    }
}