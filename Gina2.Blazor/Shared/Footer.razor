﻿@using System.Text.RegularExpressions
 <footer>
<div class="container">
        <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="flex-brand">
        <span><img id="site_footer_icon" data-cy="FooterImage" class="img-responsive footer-logo-icon" src="img/@(layoutSetting.FooterImage)"/></span>
      @*  <p class="footer-text" id="site_footer_text"> @((MarkupString)@layoutSetting.FooterText) </p>
      *@  </div>
        <div class="navbar-collapse text-r">
            <ul class="nav nav-pills">
                    @{
                        string quillInput = Regex.Replace(layoutSetting.OrganizationLink != null ? layoutSetting.OrganizationLink : String.Empty, "<.*?>", String.Empty);
                    }
                    @if (!string.IsNullOrEmpty(quillInput))
                    {
                        <li class="nav-item footer-org-link" id="footer-org-link" data-cy="FooterWHOLink">
                    @((MarkupString)@layoutSetting.OrganizationLink)

                </li>
                <li class="nav-item">
                <a class="nav-link" > | </a>
                </li>
                    }
                <li class="nav-item footer-contact-link" id="footer-contact-link" data-cy="FooterContactLink">
                    @((MarkupString)@layoutSetting.ContactLink)
                </li>
            </ul>
        </div>
        </nav>
      </div>
           @* <div class="container-fluid bar-f">
                <div class="container bar-link">
                    <ul class="nav nav-pills">
                <li class="nav-item">
                <a class="nav-link" href="http://www.who.int/about/copyright/en/" target="_blank" data-cy="FooterCopyWrightLink">© WHO 2012</a>
                </li>
                <li class="nav-item">
                <a class="nav-link" > | </a>
                </li>
                <li class="nav-item">
                <a class="nav-link" href="https://www.who.int/about/privacy/en/index.html" target="_blank" data-cy="FooterPrivacyPolicyLink">Privacy Policy</a>
                </li>
                <li class="nav-item">
                <a class="nav-link" > | </a>
                </li>
                <li class="nav-item">
                <a class="nav-link" href="terms-of-use" data-cy="TermsOfUse">Terms of Use</a> 
                </li>
                <li class="nav-item">
                <a class="nav-link" > | </a>
                </li>
                <li class="nav-item">
                <a class="nav-link" href="disclaimer" data-cy="Disclaimer">Disclaimer</a> 
                </li>
            </ul>
                </div>
             </div>*@
</footer>