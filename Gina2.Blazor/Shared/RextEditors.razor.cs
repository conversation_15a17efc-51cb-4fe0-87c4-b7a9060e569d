﻿using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Shared
{
    public partial class RextEditors
    {
        [Parameter]
        public EventCallback<string> Changed { get; set; }
        [Parameter]
        public string Value { get; set; }
        private async Task OnChange(string html)
        {
            Value = html;
            await Changed.InvokeAsync(html);
        }
        
    }
}
