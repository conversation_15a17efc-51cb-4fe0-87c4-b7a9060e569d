using System;
using System.Threading;
using System.Threading.Tasks;

namespace Gina2.Blazor.Utilities
{
    public class DebounceDispatcher
    {
        private readonly TimeSpan _delay;
        private CancellationTokenSource _cts;

        public DebounceDispatcher(TimeSpan delay)
        {
            _delay = delay;
            _cts = new CancellationTokenSource();
        }

        public void Dispatch(Func<Task> action)
        {
            _cts.Cancel();
            _cts = new CancellationTokenSource();
            var token = _cts.Token;

            Task.Delay(_delay, token)
                .ContinueWith(async t =>
                {
                    if (!t.IsCanceled)
                    {
                        await action.Invoke();
                    }
                }, TaskScheduler.Default);
        }
    }
}
