﻿using Gina2.DbModels;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Helpers
{
    public static class NavigationHelper
    {
        public static string GetPageName(this NavigationManager navigationManager)
        {
            string pageName = "Home";
            string baseUrl = navigationManager.BaseUri;
            if (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}map", baseUrl)))
            {
                pageName = "Map";
            }
            else if (navigationManager.Uri.ToLower().Contains(string.Format("{0}admin/contents/home/<USER>/", baseUrl).ToLower()) || navigationManager.Uri.ToLower() == baseUrl.ToLower())
            {
                pageName = "HomeEdit";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/policies/create", baseUrl) ||
                (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl).ToLower()) && navigationManager.Uri.ToLower().Contains("policies/") && navigationManager.Uri.ToLower().EndsWith("/edit")))
            {
                pageName = "PolicyCreateEdit";
            }
            else if (navigationManager.Uri.ToLower().ToLower() == string.Format("{0}search", baseUrl) ||
                navigationManager.Uri.ToLower().ToLower() == string.Format("{0}advanced-search", baseUrl))
            {
                pageName = "search";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}countries/policies", baseUrl)
               || navigationManager.Uri.ToLower() == string.Format("{0}countries", baseUrl)
               || navigationManager.Uri.ToLower() == string.Format("{0}countries/mechanisms", baseUrl)
               || navigationManager.Uri.ToLower() == string.Format("{0}countries/programmes%20and%20actions", baseUrl)
               || navigationManager.Uri.ToLower() == string.Format("{0}countries/smart%20commitments", baseUrl))
            {
                pageName = "Countries";
            }
            else if (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl)) &&
                navigationManager.Uri.ToLower().EndsWith("/policies") ||
                navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl)) &&
                navigationManager.Uri.ToLower().EndsWith("/mechanisms") ||
                navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl)) &&
                navigationManager.Uri.ToLower().EndsWith("/programmes-and-actions") ||
                navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl)) &&
                navigationManager.Uri.ToLower().EndsWith("/commitments"))
            {
                pageName = "ContentList";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}about-us", baseUrl))
            {
                pageName = "AboutUs";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/scorecards", baseUrl))
            {
                pageName = "Scorecard";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/users", baseUrl))
            {
                pageName = "usermanagement";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/roles", baseUrl))
            {
                pageName = "role";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/profile", baseUrl))
            {
                pageName = "UserProfile";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/taxonomies", baseUrl))
            {
                pageName = "Taxonomies";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/dashboard", baseUrl))
            {
                pageName = "Dashboard";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/programme/create", baseUrl) ||
                (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl).ToLower()) && navigationManager.Uri.ToLower().Contains("programmes-and-actions/") && navigationManager.Uri.ToLower().EndsWith("/edit")))
            {
                pageName = "ProgrammeCreate";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/mechanisms/create", baseUrl) ||
                (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl).ToLower()) && navigationManager.Uri.ToLower().Contains("mechanisms/") && navigationManager.Uri.ToLower().EndsWith("/edit")))
            {
                pageName = "MechanismCreateEdit";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/commitment/create", baseUrl) ||
                (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}countries", baseUrl).ToLower()) && navigationManager.Uri.ToLower().Contains("commitments/") && navigationManager.Uri.ToLower().EndsWith("/edit")))
            {
                pageName = "CommitmentCreateEdit";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/scorecards/create", baseUrl) ||
                (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}scorecards", baseUrl).ToLower()) && navigationManager.Uri.ToLower().EndsWith("/edit")))
            {
                pageName = "ScorecardCreateEdit";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/scorecards/scoretopic", baseUrl))
            {
                pageName = "ScorecardTopic";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}disclaimer", baseUrl))
            {
                pageName = "Disclaimer";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}terms-of-use", baseUrl))
            {
                pageName = "TearmofUse";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/mapindicator", baseUrl))
            {
                pageName = "mapindicator";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/indicatortype", baseUrl))
            {
                pageName = "indicatortype";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}admin/taxonomies/topic", baseUrl))
            {
                pageName = "TaxonomyTopic";
            }
            else if (navigationManager.Uri.ToLower() == string.Format("{0}summaries", baseUrl))
            {
                pageName = "PublishedScorecard";
            }
            else if (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}admin/bulkimportexport", baseUrl)))
            {
                pageName = "bulkimportexport";
            }
            else if (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}admin/composeemail", baseUrl)))
            {
                pageName = "composeemail";
            }
            else if (navigationManager.Uri.ToLower().StartsWith(string.Format("{0}admin/static", baseUrl)))
            {
                pageName = "StaticPage";
            }
            else
            {
                pageName = "ScorecardCountry";
            }
            return pageName;
        }
    }
}
