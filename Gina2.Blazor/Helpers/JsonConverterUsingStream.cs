﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Data;
using System.IO;

namespace Gina2.Blazor.Helpers
{
    public static class JsonConverterUsingStream
    {
        public static string DataTableToJson(DataTable table)
        {
            var jsonSerializer = new JsonSerializer
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            var sw = new StringWriter();
            using (JsonWriter writer = new JsonTextWriter(sw))
            {
                writer.WriteStartArray();

                foreach (DataRow row in table.Rows)
                {
                    jsonSerializer.Serialize(writer, row.ItemArray);
                }

                writer.WriteEndArray();
            }

            return sw.ToString();
        }
    }
}
