﻿using Hangfire.Dashboard;

namespace Gina2.Blazor.Helpers
{
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();
            // Allow all authenticated users to see the Dashboard (potentially dangerous).
            if (httpContext.User.Claims?.FirstOrDefault(c => c.Type.Contains("role"))?.Value == "Admin")
            {
                return true;
            }
            return false;
        }
    }
}
