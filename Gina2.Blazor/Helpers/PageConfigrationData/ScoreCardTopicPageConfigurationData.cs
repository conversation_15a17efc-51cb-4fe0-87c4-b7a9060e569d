﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ScoreCardTopicPageConfigurationData
    {
        public static List<PageConfiguration> GetScoreCardTopicPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardTopic",
                    Name = ScoreCardTopicPageConfigurationKey.HeadingPage,
                    Type = "string",
                    Value = "Scorecard",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardTopic",
                    Name = ScoreCardTopicPageConfigurationKey.PageDescription,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "ScorecardTopic",
                  Name = ScoreCardTopicPageConfigurationKey.ScoreCardScorecardLabel,
                  Type = "string",
                  Value = "Scorecard",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ScorecardTopic",
                   Name = ScoreCardTopicPageConfigurationKey.ScoreCardScorecardTooltip,
                   Type = "string",
                   Value = "Scorecard",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ScorecardTopic",
                 Name = ScoreCardTopicPageConfigurationKey.ScoreCardTypeLabel,
                 Type = "string",
                 Value = "Type",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ScorecardTopic",
                   Name = ScoreCardTopicPageConfigurationKey.ScoreCardTypeTooltip,
                   Type = "string",
                   Value = "Type",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ScorecardTopic",
                 Name = ScoreCardTopicPageConfigurationKey.ScoreCardTopicLabel,
                 Type = "string",
                 Value = "Topics",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ScorecardTopic",
                   Name = ScoreCardTopicPageConfigurationKey.ScoreCardTopicTooltip,
                   Type = "string",
                   Value = "Topics",
                   DateCreated = DateTime.UtcNow
               });

            return configurations;
        }
    }
}
