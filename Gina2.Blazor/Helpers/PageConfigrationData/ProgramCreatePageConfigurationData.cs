﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ProgramCreatePageConfigurationData
    {
        public static List<PageConfiguration> GetProgramCreatePageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.Title,
                    Type = "string",
                    Value = "Enter programme / action(s)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.FormTitle,
                   Type = "string",
                   Value = "Fill the information for new program policy",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.InputTitleLabel,
                   Type = "string",
                   Value = "Title",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.InputTitlePlaceholder,
                   Type = "string",
                   Value = "",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.InputTitleTooltip,
                   Type = "string",
                   Value = "Title",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ProgramTitleEnglishLabel,
                    Type = "string",
                    Value = "English title",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ProgramTitleEnglishTooltip,
                    Type = "string",
                    Value = "English title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ProgramTitleEnglishPlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "ProgrammeCreate",
                  Name = ProgramCreatePageConfigurationKey.SelectProgramTypeLabel,
                  Type = "string",
                  Value = "Programme type",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramTypeToolTip,
                 Type = "string",
                 Value = "Programme type",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramLanguageLabel,
                 Type = "string",
                 Value = "Language",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramLanguageToolTip,
                 Type = "string",
                 Value = "Language",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramCountryLabel,
                 Type = "string",
                 Value = "Country(ies)",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramCountryToolTip,
                 Type = "string",
                 Value = "Country(ies)",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramMapLocationLabel,
                 Type = "string",
                 Value = "Map location",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "ProgrammeCreate",
                 Name = ProgramCreatePageConfigurationKey.SelectProgramMapLocationToolTip,
                 Type = "string",
                 Value = "Map location",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
           new PageConfiguration()
           {
               Page = "ProgrammeCreate",
               Name = ProgramCreatePageConfigurationKey.SelectProgramBriefDescriptionLabel,
               Type = "string",
               Value = "Brief description",
               DateCreated = DateTime.UtcNow
           });

            configurations.Add(
          new PageConfiguration()
          {
              Page = "ProgrammeCreate",
              Name = ProgramCreatePageConfigurationKey.SelectProgramBriefDescriptionToolTip,
              Type = "string",
              Value = "Describe programme/project",
              DateCreated = DateTime.UtcNow
          });

            configurations.Add(
         new PageConfiguration()
         {
             Page = "ProgrammeCreate",
             Name = ProgramCreatePageConfigurationKey.SelectProgramReferencesLabel,
             Type = "string",
             Value = "References",
             DateCreated = DateTime.UtcNow
         });

            configurations.Add(
         new PageConfiguration()
         {
             Page = "ProgrammeCreate",
             Name = ProgramCreatePageConfigurationKey.SelectProgramReferencesToolTip,
             Type = "string",
             Value = "References",
             DateCreated = DateTime.UtcNow
         });

            configurations.Add(
        new PageConfiguration()
        {
            Page = "ProgrammeCreate",
            Name = ProgramCreatePageConfigurationKey.ProgramPolicyLabel,
            Type = "string",
            Value = "Programme related to existing policy",
            DateCreated = DateTime.UtcNow
        });

            configurations.Add(
       new PageConfiguration()
       {
           Page = "ProgrammeCreate",
           Name = ProgramCreatePageConfigurationKey.ProgramPolicyToolTip,
           Type = "string",
           Value = "Programme related to existing policy",
           DateCreated = DateTime.UtcNow
       });


            configurations.Add(
       new PageConfiguration()
       {
           Page = "ProgrammeCreate",
           Name = ProgramCreatePageConfigurationKey.ProgramPartnersInvolvedLabel,
           Type = "string",
           Value = "Partners involved",
           DateCreated = DateTime.UtcNow
       });

            configurations.Add(
       new PageConfiguration()
       {
           Page = "ProgrammeCreate",
           Name = ProgramCreatePageConfigurationKey.ProgramPartnersInvolvedToolTip,
           Type = "string",
           Value = "Partners involved",
           DateCreated = DateTime.UtcNow
       });

            configurations.Add(
                  new PageConfiguration()
                  {
                      Page = "ProgrammeCreate",
                      Name = ProgramCreatePageConfigurationKey.ProgramActionsLabel,
                      Type = "string",
                      Value = "Actions",
                      DateCreated = DateTime.UtcNow
                  });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ProgramActionsToolTip,
                    Type = "string",
                    Value = "Actions",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.ProgramModerationNotesLabel,
                   Type = "string",
                   Value = "Moderation notes",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "ProgrammeCreate",
                  Name = ProgramCreatePageConfigurationKey.ProgramModerationNotesToolTip,
                  Type = "string",
                  Value = "Moderation notes",
                  DateCreated = DateTime.UtcNow
              });

            // newly data added

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ProgramCostLabel,
                    Type = "string",
                    Value = "Cost",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ProgramCostTooltip,
                Type = "string",
                Value = "Cost",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ProgramCostPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.ActionTopicLabel,
                   Type = "string",
                   Value = "Action topic",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTopicTooltip,
                Type = "string",
                Value = "Action topic",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionTargetGroupLabel,
                    Type = "string",
                    Value = "Target group",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTargetGroupTooltip,
                Type = "string",
                Value = "Target group",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTargetGroupPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionAgeGroupLabel,
                        Type = "string",
                        Value = "Age group",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionAgeGroupTooltip,
                Type = "string",
                Value = "Age group",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionElenaLinkLabel,
                    Type = "string",
                    Value = "eLENA link reference",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionElenaLinkTooltip,
                        Type = "string",
                        Value = "eLENA link reference",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionElenaLinkPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionAreaLabel,
                        Type = "string",
                        Value = "Area",
                        DateCreated = DateTime.UtcNow
                    });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionAreaTooltip,
                        Type = "string",
                        Value = "Area",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionAreaPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                        new PageConfiguration()
                        {
                            Page = "ProgrammeCreate",
                            Name = ProgramCreatePageConfigurationKey.ActionStatusLabel,
                            Type = "string",
                            Value = "Status",
                            DateCreated = DateTime.UtcNow
                        });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionStatusTooltip,
                        Type = "string",
                        Value = "Status",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionStatusPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });



            configurations.Add(

               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.ActionStartDateLabel,
                   Type = "string",
                   Value = "Start date",
                   DateCreated = DateTime.UtcNow
               });


            configurations.Add(

                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionStartDateTooltip,
                    Type = "string",
                    Value = "Start date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionEndDateLabel,
                    Type = "string",
                    Value = "End date",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(

               new PageConfiguration()
               {
                   Page = "ProgrammeCreate",
                   Name = ProgramCreatePageConfigurationKey.ActionEndDateTooltip,
                   Type = "string",
                   Value = "End date",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                            new PageConfiguration()
                            {
                                Page = "ProgrammeCreate",
                                Name = ProgramCreatePageConfigurationKey.ActionDeliveryLabel,
                                Type = "string",
                                Value = "Delivery(ies)",
                                DateCreated = DateTime.UtcNow
                            });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionDeliveryTooltip,
                        Type = "string",
                        Value = "Delivery(ies)",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionDeliveryPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                        new PageConfiguration()
                        {
                            Page = "ProgrammeCreate",
                            Name = ProgramCreatePageConfigurationKey.ActionImplementationDetailLabel,
                            Type = "string",
                            Value = "Implementation details",
                            DateCreated = DateTime.UtcNow
                        });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionImplementationDetailTooltip,
                Type = "string",
                Value = "Implementation details",
                DateCreated = DateTime.UtcNow
            });
            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionImplementationDetailPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });
            configurations.Add(
                        new PageConfiguration()
                        {
                            Page = "ProgrammeCreate",
                            Name = ProgramCreatePageConfigurationKey.ActionOutcomeIndicatorLabel,
                            Type = "string",
                            Value = "Outcome indicator(s)",
                            DateCreated = DateTime.UtcNow
                        });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionOutcomeIndicatorTooltip,
                Type = "string",
                Value = "Outcome indicator(s)",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                            new PageConfiguration()
                            {
                                Page = "ProgrammeCreate",
                                Name = ProgramCreatePageConfigurationKey.ActionMAndESystemLabel,
                                Type = "string",
                                Value = "M&E system",
                                DateCreated = DateTime.UtcNow
                            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionMAndESystemTooltip,
                Type = "string",
                Value = "M&E system",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                                new PageConfiguration()
                                {
                                    Page = "ProgrammeCreate",
                                    Name = ProgramCreatePageConfigurationKey.ActionTargetPopulationLabel,
                                    Type = "string",
                                    Value = "Target population size",
                                    DateCreated = DateTime.UtcNow
                                });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionTargetPopulationTooltip,
                        Type = "string",
                        Value = "Target population size",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTargetPopulationPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                              new PageConfiguration()
                              {
                                  Page = "ProgrammeCreate",
                                  Name = ProgramCreatePageConfigurationKey.ActionConverageLevelLabel,
                                  Type = "string",
                                  Value = "Coverage level(%)",
                                  DateCreated = DateTime.UtcNow
                              });


            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ProgrammeCreate",
                        Name = ProgramCreatePageConfigurationKey.ActionConverageLevelTooltip,
                        Type = "string",
                        Value = "Coverage level(%)",
                        DateCreated = DateTime.UtcNow
                    });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionConverageLevelPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionConverageTypeLabel,
                Type = "string",
                Value = "Coverage type",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionConverageTypeTooltip,
                Type = "string",
                Value = "Converage type",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionBaselineLabel,
                Type = "string",
                Value = "Baseline",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPostInterventionLabel,
                Type = "string",
                Value = "Post-intervention",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPostInterventionTooltip,
                Type = "string",
                Value = "Post-intervention",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPostInterventionPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSocialDeterminantLabel,
                Type = "string",
                Value = "Outcome reported by social determinants",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSocialDeterminantTooltip,
                Type = "string",
                Value = "Outcome reported by social determinants",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSocialDeterminantPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionProblemAndSolutionLabel,
                Type = "string",
                Value = "Problems and solutions",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTypicalProblemLabel,
                Type = "string",
                Value = "Typical problems",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTypicalProblemTooltip,
                Type = "string",
                Value = "Typical problems",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionTypicalProblemPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });
            

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSolutionLabel,
                Type = "string",
                Value = "Solutions",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSolutionTooltip,
                Type = "string",
                Value = "Solutions",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionSolutionPlaceHolder,
                Type = "string",
                Value = "",
                DateCreated = DateTime.UtcNow
            });


            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionotherLessanLearntLabel,
                Type = "string",
                Value = "Other lessons learnt",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionotherLessanLearntTooltip,
                Type = "string",
                Value = "Other lessons learnt",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionPersonalStoryLabel,
                    Type = "string",
                    Value = "Personal story",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPersonalStoryTooltip,
                Type = "string",
                Value = "Personal story",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPlaceLabel,
                Type = "string",
                Value = "Place",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "ProgrammeCreate",
                Name = ProgramCreatePageConfigurationKey.ActionPlaceTooltip,
                Type = "string",
                Value = "Place",
                DateCreated = DateTime.UtcNow
            });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ProgrammeCreate",
                    Name = ProgramCreatePageConfigurationKey.ActionPlacePlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
