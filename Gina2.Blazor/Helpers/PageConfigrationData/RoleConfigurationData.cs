﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class RoleConfigurationData
    {
        public static List<PageConfiguration> GetRolePageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "role",
                    Name = RolePageConfigurationKey.Header,
                    Type = "string",
                    Value = "Roles",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "role",
                    Name = RolePageConfigurationKey.SubHeader,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "role",
                   Name = RolePageConfigurationKey.PageHeading,
                   Type = "string",
                   Value = "Roles",
                   DateCreated = DateTime.UtcNow
               });

            return configurations;
        }
    }
}
