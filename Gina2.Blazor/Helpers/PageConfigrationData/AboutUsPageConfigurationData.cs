﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class AboutUsPageConfigurationData
    {
        public static List<PageConfiguration> GetAboutUsPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "AboutUs",
                    Name = AboutUsPageConfigurationKey.Title,
                    Type = "string",
                    Value = "About GIFNA",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "AboutUs",
                   Name = AboutUsPageConfigurationKey.Description,
                   Type = "string",
                   Value = "Supporting the development, implementation, monitoring and assessment of national nutrition policies and progammes is one of the core activities of the WHO Department of Nutrition and Food Safety (NFS).",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "AboutUs",
                    Name = AboutUsPageConfigurationKey.Body,
                    Type = "string",
                    Value = "<main class=\"b-layout-content tabsel\"><!--!--><p _bl_3dbbdf12-6b51-4381-bbbe-31b58507eb0d=\"\"><!--!-->" +
                    "Monitoring of country progress is being done through the use of the Global database on the Implementation of Nutrition Action (GIFNA)," +
                    "which was developed and launched in 2012 with the support of the Bill and Melinda Gates Foundation.GIFNA builds on the former WHO Global Database on National Nutrition Policies and Programmes developed after the 1st International Conference on Nutrition in 1992.GIFNA, in turn, is informed by WHO’s periodically conducted global policy reviews," +
                    "routine policy monitoring activities in collaboration with Regional and Country Offices as well as by partners’ databases." +
                    "</p><p _bl_0a841e46 - ba1c - 4f25 - 9606 - 4b3c6d31ba3a =\"\">GIFNA provides a repository of policies," +
                    "actions and mechanisms related to nutrition.It is an interactive platform for sharing standardized information on nutrition policies and action, i.e.what are the commitments made and who is doing what, where, when, why and how(including lessons learnt).Users can apply this tool to:" +
                    "</p><ul class=\"list-group listabout\"><!--!--><li class=\"list-group-item\" aria-disabled=\"false\">Map nutrition policies and action</li><!--!-->" +
                    "<li class=\"list-group-item\" aria-disabled=\"false\">Link policies and action to nutrition status indicators</li><!--!-->" +
                    "<li class=\"list-group-item\" aria-disabled=\"false\">Monitor implementation of key nutrition action</li><!--!-->" +
                    "<li class=\"list-group-item\" aria-disabled=\"false\">Identify overlaps and gaps</li><!--!--><li class=\"list-group-item\" aria-disabled=\"false\">Share experience on implementation practices</li></ul></main>",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "AboutUs",
                  Name = AboutUsPageConfigurationKey.WHOResourceTitle,
                  Value = "WHO resources",
                  Type = "string",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "AboutUs",
                   Name = AboutUsPageConfigurationKey.WHOResourceBody,
                   Type = "string",
                   Value = "<div class=\"downl-flex d-flex justify-content-start\"><ul class=\"list-group list-group-flush listabouts\"><!--!--><li class=\"list-group-item\" aria-disabled=\"false\">WHO European database on nutrition, obesity and physical activity (NOPA)</li><!--!-->" +
                                        "<!--!--><li class=\"list-group-item\" aria-disabled=\"false\">e-Library of Evidence for Nutrition Actions(eLENA)</li><!--!-->"+
                                        "<!--!--><li class=\"list-group-item\" aria-disabled=\"false\">Nutrition Landscape Information System(NLiS)</li></ul></div>",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "AboutUs",
                 Name = AboutUsPageConfigurationKey.PartnerandDataResourceTitle,
                 Value = "Partners and data sources",
                 Type = "string",
                 DateCreated = DateTime.UtcNow
             });
            configurations.Add(
             new PageConfiguration()
             {
                 Page = "AboutUs",
                 Name = AboutUsPageConfigurationKey.DonorsTitle,
                 Value = "Donors",
                 Type = "string",
                 DateCreated = DateTime.UtcNow
             });
            configurations.Add(
               new PageConfiguration()
               {
                   Page = "AboutUs",
                   Name = AboutUsPageConfigurationKey.DonorsBody,
                   Type = "string",
                   Value = "<div class=\"downl-flex d-flex justify-content-start\"><ul class=\"list-group list-group-flush listabouts\"><!--!--><li class=\"list-group-item\" aria-disabled=\"false\"><a href=\"https://www.gatesfoundation.org\" target=\"_blank\">Bill & Melinda Gates Foundation</a></li><!--!-->" +
                                        "<!--!--><li class=\"list-group-item\" aria-disabled=\"false\"><a href=\"https://resolvetosavelives.org\" target=\"_blank\">Resolve to Save Lives</a></li></ul></div>",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
             new PageConfiguration()
             {
                 Page = "AboutUs",
                 Name = AboutUsPageConfigurationKey.PartnerandDataResourceBody,
                 Value = "<div class=\"downl-flex d-flex justify-content-start\" >" +
                         "<ul class=\"list-group list-group-flush listabouts\"><li class=\"list-group-item\" aria-disabled=\"false\">" +
                         "Food Fortification Initiative</li><li class=\"list-group-item\" aria-disabled=\"false\">FAOLEX</li><!--!-->" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">SUN movement</li><!--!-->" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">IBFAN World Breastfeeding Trends Initiative (WBTi)</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">Micronutrient Initiative (MI)</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">Global Alliance for Improved Nutrition (GAIN)</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">Iodized salt consumption (UNICEF)</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">Vitamin A supplementation coverage (UNICEF)</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">Coverage Monitoring Network</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">World Vision International</li>" +
                         "<li class=\"list-group-item\" aria-disabled=\"false\">1,000 days</li></ul></div>",
                 Type = "string",
                 DateCreated = DateTime.UtcNow
             });
            return configurations;
        }
    }
}
