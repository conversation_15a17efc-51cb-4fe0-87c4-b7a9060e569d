﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ContentListConfigurationData
    {
        public static List<PageConfiguration> GetContentListPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ContentList",
                        Name = ContentListConfigurationKey.SubHeading,
                        Type = "string",
                        Value = "Sub header",
                        DateCreated = DateTime.UtcNow
                    });
            return configurations;
        }
    }
}
