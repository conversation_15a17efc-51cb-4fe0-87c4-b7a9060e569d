﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class SearchPageConfigurationData
    {
        public static List<PageConfiguration> GetSearchPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "search",
                    Name = SearchPageConfigurationKey.SearchHeading,
                    Type = "string",
                    Value = "GIFNA filter",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.GeneralFilters,
                   Type = "string",
                   Value = "General filters",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.SubHeader,
                   Type = "string",
                   Value = @"<div>The filters on the right side allow you to tailor your search strategy. Click to select, the selected criteria will change to blue, click again to deselect. The table on the left shows the results of the applied search criteria. Clicking on ""
                                <a onclick = ""@(()=>OnMapExportClicked)\""> Display results on the map </a>"" triggers a map with the search results to which you can add the nutrition indicators. The CSV link at the bottom of the results table allows the export to a CSV file.</div>",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.DataType,
                   Type = "string",
                   Value = "Data type",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.Regions,
                   Type = "string",
                   Value = "Regions",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.Countries,
                   Type = "string",
                   Value = "Countries",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.PublishedYear,
                   Type = "string",
                   Value = "Published year",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.AdvancedFilters,
                   Type = "string",
                   Value = "Advanced filters",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.Keywords,
                   Type = "string",
                   Value = "Keywords",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.NutritionTopics,
                   Type = "string",
                   Value = "Nutrition topics",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "search",
                   Name = SearchPageConfigurationKey.SearchTopics,
                   Type = "string",
                   Value = "Search topics",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "search",
                  Name = SearchPageConfigurationKey.Country,
                  Type = "string",
                  Value = "Country",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "search",
                 Name = SearchPageConfigurationKey.WHORegion,
                 Type = "string",
                 Value = "WHO region",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "search",
                 Name = SearchPageConfigurationKey.Partners,
                 Type = "string",
                 Value = "Partners",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "search",
                 Name = SearchPageConfigurationKey.PolicyFilters,
                 Type = "string",
                 Value = "Policy filters",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "search",
                Name = SearchPageConfigurationKey.ActionFilters,
                Type = "string",
                Value = "Action filters",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "search",
                Name = SearchPageConfigurationKey.LanguageFilters,
                Type = "string",
                Value = "Language filters",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
           new PageConfiguration()
           {
               Page = "search",
               Name = SearchPageConfigurationKey.CommitmentFilters,
               Type = "string",
               Value = "Commitment filters",
               DateCreated = DateTime.UtcNow
           });

            configurations.Add(
          new PageConfiguration()
          {
              Page = "search",
              Name = SearchPageConfigurationKey.ProgramTypes,
              Type = "string",
              Value = "Program types",
              DateCreated = DateTime.UtcNow
          });

            configurations.Add(
        new PageConfiguration()
        {
            Page = "search",
            Name = SearchPageConfigurationKey.FundingSource,
            Type = "string",
            Value = "Funding source",
            DateCreated = DateTime.UtcNow
        });

            configurations.Add(
           new PageConfiguration()
           {
               Page = "search",
               Name = SearchPageConfigurationKey.TargetGroup,
               Type = "string",
               Value = "Target group",
               DateCreated = DateTime.UtcNow
           });

            configurations.Add(
        new PageConfiguration()
        {
            Page = "search",
            Name = SearchPageConfigurationKey.DeliveryChannel,
            Type = "string",
            Value = "Delivery channel",
            DateCreated = DateTime.UtcNow
        });

            configurations.Add(
                   new PageConfiguration()
                   {
                       Page = "search",
                       Name = SearchPageConfigurationKey.ProblemAndSolution,
                       Type = "string",
                       Value = "Problem & solution",
                       DateCreated = DateTime.UtcNow
                   });

            configurations.Add(
                  new PageConfiguration()
                  {
                      Page = "search",
                      Name = SearchPageConfigurationKey.ICN2Recommend,
                      Type = "string",
                      Value = "ICN2 FFA recommendation actions",
                      DateCreated = DateTime.UtcNow
                  });

            configurations.Add(
                 new PageConfiguration()
                 {
                     Page = "search",
                     Name = SearchPageConfigurationKey.MechanismFilters,
                     Type = "string",
                     Value = "Mechanism filters",
                     DateCreated = DateTime.UtcNow
                 });

            configurations.Add(
                 new PageConfiguration()
                 {
                     Page = "search",
                     Name = SearchPageConfigurationKey.MechanismTypes,
                     Type = "string",
                     Value = "Mechanism types",
                     DateCreated = DateTime.UtcNow
                 });
            return configurations;
        }
    }
}