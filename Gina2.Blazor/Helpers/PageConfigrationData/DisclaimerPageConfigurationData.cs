﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class DisclaimerPageConfigurationData
    {
        public static List<PageConfiguration> GetDisclaimerPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Disclaimer",
                    Name = DisclaimerPageConfigurationKey.DisclaimerBodyContent,
                    Type = "string",
                    Value = @"<p>
                              The World Health Organization(WHO) makes the Global database on the Implementation of Nutrition Action (GIFNA) platform available to registered users wishing to post content,
                                          and other users wishing to read and review posted content, for the purpose of managing information about nutrition actions in an interactive and collaborative manner.
                          </p> <p>
                              Contributors(authors) are solely responsible for their contributions, and readers are solely responsible for the interpretation of the posted content.The views expressed on the GIFNA Website are those of the authors and do not necessarily reflect those of WHO.
                          </p><p>
                              In no event shall WHO be responsible for the accuracy of information posted by any user on the GIFNA Website.WHO makes no warranties or representations regarding the completeness or accuracy of any content posted on the GINA Website and shall not be held liable for any damages whatsoever arising out of the participation in the GINA platform and/or the use of contributions.WHO reserves the right to make updates and changes to posted content without notice and accepts no liability for any errors or omissions in this regard.
                          </p><p>
                              WHO accepts no responsibility whatsoever for any inaccurate advice or information that may be posted on the GIFNA website or referred to in sources reached via links or other external references to the content of the GINA Website.
                          </p><p>
                              The GIFNA Website may contain links to resources on external websites.WHO is not responsible for the accuracy or content of any external link.  The presence of any resource or external link on the GINA Website does not imply that the resource, or its author or entity, is endorsed or recommended by the World Health Organization. These links are provided for convenience only.
                          </p><p>
                              The designations employed and the presentation of content on the GIFNA Website, including maps and other illustrative materials, do not imply the expression of any opinion whatsoever on the part of WHO concerning the legal status of any country, territory, city or area, or of its authorities, or concerning the delineation of frontiers and borders. Dotted lines on maps represent approximate border lines for which there may not yet be full agreement.
                          </p><p>
                              The mention of specific companies or of certain manufacturers' products does not imply that they are endorsed or recommended by WHO in preference to others of a similar nature that are not mentioned. Errors and omissions excepted, the names of proprietary products are distinguished by initial capital letters.
                          </p>",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
