﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class MapIndicatorPageConfigurationData
    {
        public static List<PageConfiguration> GetMapIndicatorData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
               new PageConfiguration()
               {
                   Page = "mapindicator",
                   Name = MapIndicatorConfigurationKey.PageDescription,
                   Type = "string",
                   Value = "Description",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "mapindicator",
                    Name = MapIndicatorConfigurationKey.Title,
                    Type = "string",
                    Value = "Map indicator options",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "mapindicator",
                    Name = MapIndicatorConfigurationKey.Heading,
                    Type = "string",
                    Value = "Add map indicator",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "mapindicator",
                    Name = MapIndicatorConfigurationKey.IndicatorTypeLabel,
                    Type = "string",
                    Value = "Indicator type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "mapindicator",
                    Name = MapIndicatorConfigurationKey.IndicatorTypeTooltip,
                    Type = "string",
                    Value = "Indicator type",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "mapindicator",
                    Name = MapIndicatorConfigurationKey.IndicatorTypePlaceHolder,
                    Type = "string",
                    Value = "Any",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "mapindicator",
                   Name = MapIndicatorConfigurationKey.IndicatorStartValueLabel,
                   Type = "string",
                   Value = "Start value",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorStartValueTooltip,
                  Type = "string",
                  Value = "Range start value",
                  DateCreated = DateTime.UtcNow
              });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorStartValuePlaceHolder,
                  Type = "string",
                  Value = "please enter start value",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "mapindicator",
                   Name = MapIndicatorConfigurationKey.IndicatorEndValueLabel,
                   Type = "string",
                   Value = "End value",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorEndValueTooltip,
                  Type = "string",
                  Value = "Range end value",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorEndValuePlaceHolder,
                  Type = "string",
                  Value = "please enter end value",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "mapindicator",
                 Name = MapIndicatorConfigurationKey.IndicatorSymbolLabel,
                 Type = "string",
                 Value = "Symbol",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorSymbolTooltip,
                  Type = "string",
                  Value = "Symbol",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorSymbolPlaceHolder,
                  Type = "string",
                  Value = "Any",
                  DateCreated = DateTime.UtcNow
              });


            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorColorLabel,
                  Type = "string",
                  Value = "Legend",
                  DateCreated = DateTime.UtcNow
              });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorColorTooltip,
                  Type = "string",
                  Value = "Any",
                  DateCreated = DateTime.UtcNow
              });



            // added there
            configurations.Add(
               new PageConfiguration()
               {
                   Page = "mapindicator",
                   Name = MapIndicatorConfigurationKey.IndicatorOrderLabel,
                   Type = "string",
                   Value = "Indicator order",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorOrderTooltip,
                  Type = "string",
                  Value = "Indicator order",
                  DateCreated = DateTime.UtcNow
              });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "mapindicator",
                  Name = MapIndicatorConfigurationKey.IndicatorOrderPlaceHolder,
                  Type = "string",
                  Value = "Indicator order",
                  DateCreated = DateTime.UtcNow
              });




            return configurations;
        }
    }
}
