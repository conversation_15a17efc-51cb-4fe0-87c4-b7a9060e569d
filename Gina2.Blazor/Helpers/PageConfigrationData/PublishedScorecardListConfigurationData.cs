﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class PublishedScorecardListConfigurationData
    {
        public static List<PageConfiguration> GetPublishedScorecardPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "PublishedScorecard",
                    Name = PublishedScorecardConfigKey.Title,
                    Type = "string",
                    Value = "Scorecard list",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "PublishedScorecard",
                   Name = PublishedScorecardConfigKey.HeaderText,
                   Type = "string",
                   Value = "The largest number of diet-related deaths, an estimated 1.89 million each year, is associated with excessive intake of sodium, a well-established cause of raised blood pressure and increased risk of cardiovascular disease.",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "PublishedScorecard",
                   Name = PublishedScorecardConfigKey.FooterText,
                   Type = "string",
                   Value = "The largest number of diet-related deaths, an estimated 1.89 million each year, is associated with excessive intake of sodium, a well-established cause of raised blood pressure and increased risk of cardiovascular disease. The global average sodium intake is estimated to be 4310 mg/day (10.78 g of salt per day), which far exceeds the physiological requirement and is more than double the World Health Organization (WHO) recommendation of <2000 mg of sodium (equivalent to <5 g of salt) per day in adults.",
                   DateCreated = DateTime.UtcNow
               });

            return configurations;
        }
    }
}
