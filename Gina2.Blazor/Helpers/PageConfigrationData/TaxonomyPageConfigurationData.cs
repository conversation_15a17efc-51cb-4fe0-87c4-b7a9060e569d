﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class TaxonomyPageConfigurationData
    {
        public static List<PageConfiguration> GetTaxonomyPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Taxonomies",
                    Name = TaxonomyPageConfigurationKey.Title,
                    Type = "string",
                    Value = "Taxonomies",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "Taxonomies",
                   Name = TaxonomyPageConfigurationKey.Description,
                   Type = "string",
                   Value = "Taxonomy is for categorizing content. Terms are grouped into vocabularies. For example, a vocabulary called \"Fruit\" would contain the terms \"Apple\" and \"Banana\".",
                   DateCreated = DateTime.UtcNow
               });

            return configurations;
        }
    }
}
