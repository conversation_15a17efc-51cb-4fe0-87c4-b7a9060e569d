﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class CountryPageConfigurationData
    {
        public static List<PageConfiguration> GetCountryPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Countries",
                    Name = CountryPageConfigurationKey.Title,
                    Type = "string",
                    Value = "Country",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "Countries",
                   Name = CountryPageConfigurationKey.Description,
                   Type = "string",
                   Value = "This page presents all countries. Click on a country name to open the list of policies, Programmes / actions, Mechanisms, Commitments, Commitments highlights in that country. On that list you can click on the title to open any factsheet. The CSV link at the bottom allows to export the available policy data to a CSV file.",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
               new PageConfiguration()
               {
                   Page = "Countries",
                   Name = CountryPageConfigurationKey.Heading,
                   Type = "string",
                   Value = "Choose your country",
                   DateCreated = DateTime.UtcNow
               });
            return configurations;
        }
    }
}
