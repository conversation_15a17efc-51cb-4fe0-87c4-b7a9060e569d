﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ScorecardCreateConfigData
    {
        public static List<PageConfiguration> GetScorecardCreatePageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.EnterScoreCard,
                    Type = "string",
                    Value = "Enter scorecard",
                    DateCreated = DateTime.UtcNow
                }); 

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.EnterScoreCardDescription,
                    Type = "string",
                    Value = "Enter scorecard description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardHeadingCreate,
                    Type = "string",
                    Value = "Enter the information for score card",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardSubHeadingCreate,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardHeadingEdit,
                    Type = "string",
                    Value = "Edit the information for Score Card",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardTitleLabel,
                    Type = "string",
                    Value = "Score card title",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardTitlePlaceHolder,
                    Type = "string",
                    Value = "Score card title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardTitleTooltip,
                    Type = "string",
                    Value = "Score card title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardDescriptionLabel,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardDescriptionTooltip,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardURLAliasLabel,
                    Type = "string",
                    Value = "URL alias",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardURLAliasPlaceholder,
                    Type = "string",
                    Value = "URL alias",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecardURLAliasTooltip,
                    Type = "string",
                    Value = "URL alias",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.BottomTextLabel,
                    Type = "string",
                    Value = "Text group bottom",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.BottomTextTooltip,
                    Type = "string",
                    Value = "Text group bottom",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecountryURLAliasLabel,
                    Type = "string",
                    Value = "Country URL",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecountryAliasTooltip,
                    Type = "string",
                    Value = "Country url",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "ScorecardCreateEdit",
                    Name = ScorecardCreateConfiguratioKey.ScorecountryAliasPlaceholder,
                    Type = "string",
                    Value = "Enter country url",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
