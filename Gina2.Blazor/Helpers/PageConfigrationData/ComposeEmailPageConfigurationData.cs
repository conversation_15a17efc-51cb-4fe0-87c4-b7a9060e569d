﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ComposeEmailPageConfigurationData
    {
        public static List<PageConfiguration> GetComposeEmailPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.Title,
                    Type = "string",
                    Value = "Compose email",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.SubTitle,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.EmailTypeLabel,
                    Type = "string",
                    Value = "Email type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.EmailTypeTooltip,
                    Type = "string",
                    Value = "Email type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.DateRangeLabel,
                    Type = "string",
                    Value = "Date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "composeemail",
                    Name = ComposeEmailPageConfigurationKey.DateRangeTooltip,
                    Type = "string",
                    Value = "please select date range",
                    DateCreated = DateTime.UtcNow
                });
            return configurations;
        }

    }
}
