﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class BulkImportDataTypePageConfigurationData
    {
        public static List<PageConfiguration> GetBulkImportDataTypePageConfigurationData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.Title,
                    Type = "string",
                    Value = "Bulk import & export",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.SubTitle,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.DataTypeLabel,
                    Type = "string",
                    Value = "Data type",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.DataTypePlaceHolder,
                    Type = "string",
                    Value = "Data type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.DataTypeTooltip,
                    Type = "string",
                    Value = "Data type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.FileUploadLabel,
                    Type = "string",
                    Value = "File upload",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.FileUploadPlaceHolder,
                    Type = "string",
                    Value = "File upload ",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "bulkimportexport",
                    Name = BulkImportPageConfigurationKey.FileUploadTooltip,
                    Type = "string",
                    Value = "File upload",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
