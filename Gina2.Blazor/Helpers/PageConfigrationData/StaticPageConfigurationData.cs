﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class StaticPageConfigurationData
    {
        public static List<PageConfiguration> GetStaticPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "StaticPage",
                        Name = StaticPagePopupConfigurationKey.Heading,
                        Type = "string",
                        Value = "Static page",
                        DateCreated = DateTime.UtcNow
                    });
            return configurations;
        }
    }
}
