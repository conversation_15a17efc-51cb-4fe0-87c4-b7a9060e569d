﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ScorecardCountryPageConfigurationData
    {
        public static List<PageConfiguration> GetScorecardCountryPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ScorecardCountry",
                        Name = ScorecardCountryPageConfigurationKey.Title,
                        Type = "string",
                        Value = "Scorecard country title",
                        DateCreated = DateTime.UtcNow
                    }); 
            configurations.Add(
                    new PageConfiguration()
                    {
                        Page = "ScorecardCountry",
                        Name = ScorecardCountryPageConfigurationKey.Description,
                        Type = "string",
                        Value = "Scorecard country description",
                        DateCreated = DateTime.UtcNow
                    });
            return configurations;
        }
    }
}
