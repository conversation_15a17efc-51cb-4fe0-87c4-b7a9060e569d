﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class CommitmentPageConfigurationData
    {
        public static List<PageConfiguration> GetAdminCommitmentData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentHeadingCreate,
                    Type = "string",
                    Value = "Fill the information for new commitment",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentHeadingEdit,
                    Type = "string",
                    Value = "Edit commitment",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitleLabel,
                    Type = "string",
                    Value = "Title of commitment",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitlePlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitleTooltip,
                    Type = "string",
                    Value = "Title of commitment",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitleEnglishLabel,
                    Type = "string",
                    Value = "Title of commitment",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitleEnglishPlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentTitleEnglishTooltip,
                    Type = "string",
                    Value = "Commitment title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentBackgroundInfoLabel,
                    Type = "string",
                    Value = "Background information",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentBackgroundInfoTooltip,
                    Type = "string",
                    Value = "Background information",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentCountryLabel,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentCountryTooltip,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentMinistryLabel,
                    Type = "string",
                    Value = "Ministry, department or national agency",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentMinistryTooltip,
                    Type = "string",
                    Value = "Ministry, department or national agency",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentSectorLabel,
                    Type = "string",
                    Value = "Sector",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentSectorTooltip,
                    Type = "string",
                    Value = "Sector",
                    DateCreated = DateTime.UtcNow
                });
            
            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEndorsedByLabel,
                    Type = "string",
                    Value = "Endorsed by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEndorsedByTooltip,
                    Type = "string",
                    Value = "Endorsed by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEndorsedByPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

                configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentStartYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentStartMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentDateLabel,
                    Type = "string",
                    Value = "Date of commitment made",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentDateTooltip,
                    Type = "string",
                    Value = "Date of commitment made",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentDatePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEventLabel,
                    Type = "string",
                    Value = "Event",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEventTooltip,
                    Type = "string",
                    Value = "Event",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentEventPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentLinkToPolicyLabel,
                    Type = "string",
                    Value = "Link to policy(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentLinkToPolicyTooltip,
                    Type = "string",
                    Value = "Link to policy(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentResourceAllocLabel,
                    Type = "string",
                    Value = "Resource allocation",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentResourceAllocTooltip,
                    Type = "string",
                    Value = "Resource allocation",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

               new PageConfiguration()
               {
                   Page = "CommitmentCreateEdit",
                   Name = CommitmentPageConfigurationKey.CommitmentResourceAllocPlaceholder,
                   Type = "string",
                   Value = "",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentPlannedProgressLabel,
                    Type = "string",
                    Value = "Planned progress monitoring",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentPlannedProgressTooltip,
                    Type = "string",
                    Value = "Planned progress monitoring",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentPlannedProgressPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

          

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentURLLinkLabel,
                    Type = "string",
                    Value = "URL for further information",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentURLLinkTooltip,
                    Type = "string",
                    Value = "URL for further information",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentURLLinkPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentFurtherNoteLabel,
                    Type = "string",
                    Value = "Further notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentFurtherNoteTooltip,
                    Type = "string",
                    Value = "Further notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentFurtherNotePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

           
            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentFileUploadLabel,
                    Type = "string",
                    Value = "Document upload",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentFileUploadTooltip,
                    Type = "string",
                    Value = "Document upload",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentModerationNoteLabel,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentModerationNotePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.CommitmentModerationNoteTooltip,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentTitleLabel,
                    Type = "string",
                    Value = "SMART commitment title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentTitlePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentTitleTooltip,
                    Type = "string",
                    Value = "SMART commitment title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentNumberLabel,
                    Type = "string",
                    Value = "Commitment number",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentNumberTooltip,
                    Type = "string",
                    Value = "Commitment number",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentDetailsLabel,
                    Type = "string",
                    Value = "SMART commitment details",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentDetailsTooltip,
                    Type = "string",
                    Value = "SMART commitment details",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartDateLabel,
                    Type = "string",
                    Value = "Start date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartDateTooltip,
                    Type = "string",
                    Value = "Start date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartMonthTooltip,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentStartYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentQuestionsLabel,
                    Type = "string",
                    Value = "Questions",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentQuestionsTooltip,
                    Type = "string",
                    Value = "Questions",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentLinksIcn2Label,
                    Type = "string",
                    Value = "Links to the ICN2 Framework for Action",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentLinksIcn2Tooltip,
                    Type = "string",
                    Value = "Links to the ICN2 Framework for Action",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentLinksSdgLabel,
                    Type = "string",
                    Value = "Links to the Sustainable Development Goals (SDGs)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentLinksSdgTooltip,
                    Type = "string",
                    Value = "Links to the Sustainable Development Goals (SDGs)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentLabel,
                    Type = "string",
                    Value = "SMART commitments",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "CommitmentCreateEdit",
                    Name = CommitmentPageConfigurationKey.SmartCommitmentTooltip,
                    Type = "string",
                    Value = "SMART commitments",
                    DateCreated = DateTime.UtcNow
                });
            return configurations;
        }
    }
}
