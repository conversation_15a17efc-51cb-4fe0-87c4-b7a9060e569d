﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class UserProfilePageConfigurationData
    {
        public static List<PageConfiguration> GetUserProfilePageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.Title,
                    Type = "string",
                    Value = "User profile",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.SubTitle,
                    Type = "string",
                    Value = "Sub title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileFirstNameLabel,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileFirstNameTooltip,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileFirstNamePlaceholder,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileLastNameLabel,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileLastNameTooltip,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileLastNamePlaceholder,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileUserNameLabel,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileUserNameTooltip,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileUserNamePlaceholder,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileEmailLabel,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileEmailTooltip,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileEmailPlaceholder,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileOrganizationLabel,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileOrganizationTooltip,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileOrganizationPlaceholder,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "UserProfile",
                   Name = UserProfilePageConfigurationKey.UserProfileNationalityLabel,
                   Type = "string",
                   Value = "Select nationality",
                   DateCreated = DateTime.UtcNow
               });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileNationalityTooltip,
                    Type = "string",
                    Value = "Select nationality",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "UserProfile",
                   Name = UserProfilePageConfigurationKey.UserProfileRegionsOfInterestLabel,
                   Type = "string",
                   Value = "Regions of interest",
                   DateCreated = DateTime.UtcNow
               });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileRegionsOfInterestTooltip,
                    Type = "string",
                    Value = "Regions of interest",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "UserProfile",
                   Name = UserProfilePageConfigurationKey.UserProfileCountriesOfInterestLabel,
                   Type = "string",
                   Value = "Countries of interest",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileCountriesOfInterestTooltip,
                    Type = "string",
                    Value = "Countries of interest",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "UserProfile",
                    Name = UserProfilePageConfigurationKey.UserProfileTopicOfInterestTooltip,
                    Type = "string",
                    Value = "Topic of interest",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "UserProfile",
                   Name = UserProfilePageConfigurationKey.UserProfileTopicOfInterestLabel,
                   Type = "string",
                   Value = "Topic of interest",
                   DateCreated = DateTime.UtcNow
               });

            return configurations;
        }
    }
}
