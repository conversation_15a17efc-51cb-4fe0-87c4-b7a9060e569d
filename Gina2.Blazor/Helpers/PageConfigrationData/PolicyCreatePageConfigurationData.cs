﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class PolicyCreatePageConfigurationData
    {
        public static List<PageConfiguration> GetAdminPolicyData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyHeading,
                    Type = "string",
                    Value = "Fill the information for new policy",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitleLabel,
                    Type = "string",
                    Value = "Policy title",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitlePlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitleTooltip,
                    Type = "string",
                    Value = "Policy title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitleEnglishLabel,
                    Type = "string",
                    Value = "Policy title (English)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitleEnglishPlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTitleEnglishTooltip,
                    Type = "string",
                    Value = "Provide english translation if other language has been chosen",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLanguageLabel,
                    Type = "string",
                    Value = "Language",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLanguageTooltip,
                    Type = "string",
                    Value = "Language",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTypeLabel,
                    Type = "string",
                    Value = "Type of policy",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTypeTooltip,
                    Type = "string",
                    Value = "Type of policy",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyCountryLabel,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyCountryTooltip,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyRegionLabel,
                    Type = "string",
                    Value = "Province/Region",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyRegionTooltip,
                    Type = "string",
                    Value = "Province/Region",
                    DateCreated = DateTime.UtcNow
                });
          

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyRegionPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartDateLabel,
                    Type = "string",
                    Value = "Start date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartDateTooltip,
                    Type = "string",
                    Value = "Start date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartMonthTooltip,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStartYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndDateLabel,
                    Type = "string",
                    Value = "End date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndDateTooltip,
                    Type = "string",
                    Value = "End date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndMonthTooltip,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyEndYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishDateLabel,
                    Type = "string",
                    Value = "Publish date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishDateTooltip,
                    Type = "string",
                    Value = "Publish date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishMonthTooltip,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishByLabel,
                    Type = "string",
                    Value = "Published by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishByTooltip,
                    Type = "string",
                    Value = "Published by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyPublishByPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyDocAdoptedLabel,
                    Type = "string",
                    Value = "Is the policy document adopted?",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyDocAdoptedTooltip,
                    Type = "string",
                    Value = "Is the policy document adopted?",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedDateLabel,
                    Type = "string",
                    Value = "Adopted date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedDateTooltip,
                    Type = "string",
                    Value = "Adopted date",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedMonthTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedByLabel,
                    Type = "string",
                    Value = "Adopted by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedByTooltip,
                    Type = "string",
                    Value = "Adopted by",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyAdoptedByPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PartnersInvolvedLabel,
                    Type = "string",
                    Value = "Partners involved",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PartnersInvolvedTooltip,
                    Type = "string",
                    Value = "Partners involved",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyExtractsLabel,
                    Type = "string",
                    Value = "Extracts",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyExtractsTooltip,
                    Type = "string",
                    Value = "Please list the goals, objectives or targets related to nutrition in the Policy document, if any.",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTopicLabel,
                    Type = "string",
                    Value = "Topics",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyTopicTooltip,
                    Type = "string",
                    Value = "Topics",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLinkToActionLabel,
                    Type = "string",
                    Value = "Link to existing action records",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLinkToActionTooltip,
                    Type = "string",
                    Value = "Link to existing action records",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyURLLinkLabel,
                    Type = "string",
                    Value = "URL link",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyURLLinkTooltip,
                    Type = "string",
                    Value = "URL link",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyURLLinkPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyFurtherNoteLabel,
                    Type = "string",
                    Value = "Further notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyFurtherNoteTooltip,
                    Type = "string",
                    Value = "Further notes",
                    DateCreated = DateTime.UtcNow
                });

            

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyReferenceLabel,
                    Type = "string",
                    Value = "Reference",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyReferenceTooltip,
                    Type = "string",
                    Value = "Reference",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyFileUploadLabel,
                    Type = "string",
                    Value = "File upload",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyFileUploadTooltip,
                    Type = "string",
                    Value = "File upload",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyModerationNoteLabel,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyModerationNotePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyModerationNoteTooltip,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyGoalsLabel,
                    Type = "string",
                    Value = "Extracts",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyGoalsTooltip,
                    Type = "string",
                    Value = "Extracts",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStratogiesLabel,
                    Type = "string",
                    Value = "Strategies and activities related to nutrition",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyStratogiesTooltip,
                    Type = "string",
                    Value = "Strategies and activities related to nutrition",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyIndicatorLabel,
                    Type = "string",
                    Value = "M&E Indicators related to nutrition",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyIndicatorTooltip,
                    Type = "string",
                    Value = "M&E Indicators related to nutrition",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLegislationLabel,
                    Type = "string",
                    Value = "Legislation details",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "PolicyCreateEdit",
                    Name = PolicyCreateConfigurationKey.PolicyLegislationTooltip,
                    Type = "string",
                    Value = "Legislation details",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
