﻿using AntDesign;
using Gina2.Blazor.CacheService;
using Gina2.DbModels;
using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public class PageConfirgurationComponent : ComponentBase
    {
        [Inject]
        public PageConfigrationCache pageConfigrationCache { get; set; }
        [Inject]
        public AntDesign.NotificationService _notice { get; set; }
        public List<PageConfiguration> PageConfigurations { get; set; } = new List<PageConfiguration>();
        [Inject]
        NavigationManager MyNavigationManager { get; set; }
        public bool IsLoading { get; set; } = false;
        public bool IsAccordianLoading { get; set; } = false;
        public void RefreshMe()
        {
            Task.Run(async () => await GetConfigration());
            Task.Run(async () => await InvokeAsync(StateHasChanged));
        }
        public async Task GetConfigration()
        {
            PageConfigurations = await pageConfigrationCache.GetAllRecordAsync(MyNavigationManager.GetPageName());
        }
        protected override async Task OnInitializedAsync()
        {
            await Task.Run(() => IsLoading = true);
            pageConfigrationCache.RefreshRequested += RefreshMe;
            await GetConfigration();
        }
        public async Task OpenToaster(string title, string description, AntDesign.NotificationType type= AntDesign.NotificationType.Success)
        {
            await _notice.Open(new NotificationConfig()
            {
                Message = title,
                Key = Guid.NewGuid().ToString(),
                Description = description,
                NotificationType = type
            });
            await InvokeAsync(StateHasChanged);
        }

        public async Task OpenErrorToaster(string message)
        {
            await OpenToastMessage("Error", message, AntDesign.NotificationType.Error);
            StateHasChanged();
        }

        public async Task OpenInfoToaster(string message)
        {
            await OpenToastMessage("Info", message, AntDesign.NotificationType.Info);
            StateHasChanged();
        }

        public async Task OpenSuccessToaster(string message)
        {
            await OpenToastMessage("Success", message, AntDesign.NotificationType.Success);
            StateHasChanged();
        }

        public async Task OpenValidationToaster(string message)
        {
            await OpenToastMessage("Validation Error", message, AntDesign.NotificationType.Warning);
            StateHasChanged();
        }
        public async Task OpenToastMessage(string title, string message, AntDesign.NotificationType type)
        {
            await _notice.Open(new NotificationConfig()
            {
                Message = title,
                Key = Guid.NewGuid().ToString(),
                Description = message,
                NotificationType = type
            });
        }
    }
}
