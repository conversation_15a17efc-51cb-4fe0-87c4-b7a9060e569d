﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class TaxonomyTopicPageConfigurationData
    {
        public static List<PageConfiguration> GetTaxonomyTopicPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
               new PageConfiguration()
               {
                   Page = "TaxonomyTopic",
                   Name = TaxonomyTopicConfigurationKey.PageDescription,
                   Type = "string",
                   Value = "Description",
                   DateCreated = DateTime.UtcNow
               });
            return configurations;
        }
    }
}
