﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class MechanismPageConfigurationData
    {
        public static List<PageConfiguration> GetAdminMechanismData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismHeading,
                    Type = "string",
                    Value = "Fill the information for create mechanisms",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitleLabel,
                    Type = "string",
                    Value = "Mechanism title",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitlePlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitleTooltip,
                    Type = "string",
                    Value = "Mechanism title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitleEnglishLabel,
                    Type = "string",
                    Value = "English title",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitleEnglishPlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTitleEnglishTooltip,
                    Type = "string",
                    Value = "English title",
                    DateCreated = DateTime.UtcNow
                });

            
            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTypeLabel,
                    Type = "string",
                    Value = "Type of mechanism",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTypeTooltip,
                    Type = "string",
                    Value = "Type of mechanism",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismCountryLabel,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismCountryTooltip,
                    Type = "string",
                    Value = "Country(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartDateLabel,
                    Type = "string",
                    Value = "Start year and month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartDateTooltip,
                    Type = "string",
                    Value = "Start year and month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartMonthLabel,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartMonthTooltip,
                    Type = "string",
                    Value = "Month",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartYearLabel,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismStartYearTooltip,
                    Type = "string",
                    Value = "Year",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLeadGovLabel,
                    Type = "string",
                    Value = "Lead government agency in mechanism",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLeadGovPlaceHolder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLeadGovTooltip,
                    Type = "string",
                    Value = "Lead government agency in mechanism",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.PartnersInvolvedLabel,
                    Type = "string",
                    Value = "Partners involved",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.PartnersInvolvedTooltip,
                    Type = "string",
                    Value = "Partners involved",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.FunctionsLabel,
                    Type = "string",
                    Value = "Functions",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.FunctionsTooltip,
                    Type = "string",
                    Value = "Functions",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTopicLabel,
                    Type = "string",
                    Value = "Topics",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismTopicTooltip,
                    Type = "string",
                    Value = "Topics",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLinkToPoliciesLabel,
                    Type = "string",
                    Value = "Link to policy(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLinkToPoliciesTooltip,
                    Type = "string",
                    Value = "Link to policy(ies)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismURLLinkLabel,
                    Type = "string",
                    Value = "URL link",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismURLLinkTooltip,
                    Type = "string",
                    Value = "URL link",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

              new PageConfiguration()
              {
                  Page = "MechanismCreateEdit",
                  Name = MechanismPageConfigurationKey.MechanismURLLinkPlaceholder,
                  Type = "string",
                  Value = "",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLessonLearntLabel,
                    Type = "string",
                    Value = "Lessons learnt",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismLessonLearntPlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

               new PageConfiguration()
               {
                   Page = "MechanismCreateEdit",
                   Name = MechanismPageConfigurationKey.MechanismLessonLearntTooltip,
                   Type = "string",
                   Value = "Lessons learnt",
                   DateCreated = DateTime.UtcNow
               });
          

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismNoteLabel,
                    Type = "string",
                    Value = "Notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismNoteTooltip,
                    Type = "string",
                    Value = "Notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismNotePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismModerationNoteLabel,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismModerationNotePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismModerationNoteTooltip,
                    Type = "string",
                    Value = "Moderation notes",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismMandateLabel,
                    Type = "string",
                    Value = "Mandate",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismMandateGroup,
                    Type = "string",
                    Value = "Mandate",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismMandatePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismMandateTooltip,
                    Type = "string",
                    Value = "Mandate",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismOtherTypeLabel,
                    Type = "string",
                    Value = "Other",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismOtherTypeGroup,
                    Type = "string",
                    Value = "Other",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismOtherTypePlaceholder,
                    Type = "string",
                    Value = "",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "MechanismCreateEdit",
                    Name = MechanismPageConfigurationKey.MechanismOtherTypeTooltip,
                    Type = "string",
                    Value = "Other",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
