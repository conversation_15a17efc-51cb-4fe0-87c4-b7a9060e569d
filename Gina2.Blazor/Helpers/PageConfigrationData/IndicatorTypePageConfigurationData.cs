﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class IndicatorTypePageConfigurationData
    {
        public static List<PageConfiguration> GetIndicatorTypeData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.Title,
                    Type = "string",
                    Value = "Indicator type",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.SubTitle,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.Heading,
                    Type = "string",
                    Value = "Add indicator type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.IndicatorCodeLabel,
                    Type = "string",
                    Value = "Indicator code",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.IndicatorCodeTooltip,
                    Type = "string",
                    Value = "Indicator code",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "indicatortype",
                    Name = IndicatorTypeConfigurationKey.IndicatorCodePlaceHolder,
                    Type = "string",
                    Value = "Please enter indicator code",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "indicatortype",
                   Name = IndicatorTypeConfigurationKey.IndicatorNameLabel,
                   Type = "string",
                   Value = "Indicator name",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "indicatortype",
                  Name = IndicatorTypeConfigurationKey.IndicatorNameTooltip,
                  Type = "string",
                  Value = "Indicator name",
                  DateCreated = DateTime.UtcNow
              });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "indicatortype",
                  Name = IndicatorTypeConfigurationKey.IndicatorFilterCriteriaLabel,
                  Type = "string",
                  Value = "Filter criteria",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "indicatortype",
                   Name = IndicatorTypeConfigurationKey.IndicatorFilterCriteriaTooltip,
                   Type = "string",
                   Value = "Filter criteria",
                   DateCreated = DateTime.UtcNow
               });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "indicatortype",
                  Name = IndicatorTypeConfigurationKey.IndicatorFilterCriteriaPlaceHolder,
                  Type = "string",
                  Value = "Range end value",
                  DateCreated = DateTime.UtcNow
              });

            return configurations;
        }
    }
}
