﻿using DocumentFormat.OpenXml.Wordprocessing;
using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class HomePageConfigurationData
    {
        public static List<PageConfiguration> GetHomePageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "HomeEdit",
                    Name = HomeEditPageConfigurationKey.FirstSectionTitle,
                    Type = "string",
                    Value = "Features",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "HomeEdit",
                    Name = HomeEditPageConfigurationKey.SecondSectionTitle,
                    Type = "string",
                    Value = "Nutritional themes",
                    DateCreated = DateTime.UtcNow
                });
            return configurations;
        }
    }
}
