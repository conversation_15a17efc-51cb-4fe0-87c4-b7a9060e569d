﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class ScorecardPageConfigurationData 
    {
        public static List<PageConfiguration> GetScorecardPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Scorecard",
                    Name = ScorecardPageConfigurationKey.Title,
                    Type = "string",
                    Value = "Scorecard List",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Scorecard",
                    Name = ScorecardPageConfigurationKey.SubTitle,
                    Type = "string",
                    Value = "Description",
                    DateCreated = DateTime.UtcNow
                });

            return configurations;
        }
    }
}
