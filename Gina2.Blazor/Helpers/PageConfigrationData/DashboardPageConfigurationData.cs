﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class DashboardPageConfigurationData
    {
        public static List<PageConfiguration> GetDashboardPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Dashboard",
                    Name = DashboardPageConfiguration.Title,
                    Type = "string",
                    Value = "Dashboard",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Dashboard",
                    Name = DashboardPageConfiguration.Description,
                    Type = "string",
                    Value = "Welcome to your dashboard",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Dashboard",
                    Name = DashboardPageConfiguration.ContentHeading,
                    Type = "string",
                    Value = "My contents",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "Dashboard",
                   Name = DashboardPageConfiguration.ContentSectionTable1,
                   Type = "string",
                   Value = "My drafts",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "Dashboard",
                  Name = DashboardPageConfiguration.Mydraftsneedsreview,
                  Type = "string",
                  Value = "My drafts needs review",
                  DateCreated = DateTime.UtcNow
              });
            configurations.Add(
              new PageConfiguration()
              {
                  Page = "Dashboard",
                  Name = DashboardPageConfiguration.SentForCorrectionByme,
                  Type = "string",
                  Value = "My / Others drafts which have sent back for correction",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
              new PageConfiguration()
              {
                  Page = "Dashboard",
                  Name = DashboardPageConfiguration.ContentSectionTable3,
                  Type = "string",
                  Value = "My published contents",
                  DateCreated = DateTime.UtcNow
              });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.OthersContentHeading,
                 Type = "string",
                 Value = "Others content",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.ReturnedToMeForCorrection,
                 Type = "string",
                 Value = "Drafts which have been returned to me for correction/inputs",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.OthersContentTable2,
                 Type = "string",
                 Value = "Other’s drafts needs review",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.DelegatedToMe,
                 Type = "string",
                 Value = "Other’s drafts, which have been delegated to me for inputs",
                 DateCreated = DateTime.UtcNow
             });
           
            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.OthersContentTable4,
                 Type = "string",
                 Value = "Other's drafts needs review",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
             new PageConfiguration()
             {
                 Page = "Dashboard",
                 Name = DashboardPageConfiguration.DelegatedByMe,
                 Type = "string",
                 Value = "Sent to delegate by me",
                 DateCreated = DateTime.UtcNow
             });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "Dashboard",
                Name = DashboardPageConfiguration.UsersContentHeading,
                Type = "string",
                Value = "Other’s drafts I published",
                DateCreated = DateTime.UtcNow
            });

            configurations.Add(
            new PageConfiguration()
            {
                Page = "Dashboard",
                Name = DashboardPageConfiguration.UsersContentTable1,
                Type = "string",
                Value = "User's content",
                DateCreated = DateTime.UtcNow
            });
            return configurations;
        }
    }
}
