﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class MapPageConfigurationData
    {
        public static List<PageConfiguration> GetMapPageData(this List<PageConfiguration> configurations)
        {

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Heading,
                    Type = "string",
                    Value = "Map",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "Map",
                   Name = MapPageConfigurationKey.SubHeader,
                   Type = "string",
                   Value = "The filters  allow you to tailor your search strategy.The table below shows the results of the applied search criteria.Indicator will show only in map,and with other selected fitlter criteria.",
                   DateCreated = DateTime.UtcNow
               });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Filter,
                    Type = "string",
                    Value = "Filter",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.IndicatorNotes,
                    Type = "string",
                    Value = "The last selected year data will be showing for indicator",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Datatype,
                    Type = "string",
                    Value = "Data type",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Areadata,
                    Type = "string",
                    Value = "Thematic area of GIFNA data",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Indicator,
                    Type = "string",
                    Value = "Background nutrition indicator",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.Description,
                    Type = "string",
                    Value = "The boundaries and names shown and the designations used on this map do not imply the expression of any opinion whatsoever on the part of the World Health Organization concerning the legal status of any country, territory, city or area or of its authorities, or concerning the delimitation of its frontiers or boundaries. Dotted lines on maps represent approximate border lines for which there may not yet be full agreeement. From 2000 onwards, the map depicts year-specific boundaries and names; for 1990-2000 they correspond to the year 2000. In future versions, all boundaries and names will be year-specific.",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.RangeStartYear,
                    Type = "int",
                    Value = "1990",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.RangeEndYear,
                    Type = "int",
                    Value = "2022",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.SelectedStartYear,
                    Type = "int",
                    Value = "2010",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.SelectedEndYear,
                    Type = "int",
                    Value = "2020",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(

                new PageConfiguration()
                {
                    Page = "Map",
                    Name = MapPageConfigurationKey.RangeTitle,
                    Type = "int",
                    Value = "Select range",
                    DateCreated = DateTime.UtcNow
                });
            return configurations;
        }
    }
}
