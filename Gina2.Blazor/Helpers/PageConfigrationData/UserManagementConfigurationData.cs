﻿using Gina2.Core.Constant;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers.PageConfigrationData
{
    public static class UserManagementConfigurationData
    {
        public static List<PageConfiguration> GetUserManagementPageData(this List<PageConfiguration> configurations)
        {
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.HeadingTop,
                    Type = "string",
                    Value = "User management",
                    DateCreated = DateTime.UtcNow
                });
            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.SubHeading,
                    Type = "string",
                    Value = "Sub heading",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.HeadingPage,
                   Type = "string",
                   Value = "Users",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchTitleLabel,
                   Type = "string",
                   Value = "Search",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchTitlePlaceholder,
                   Type = "string",
                   Value = "Name, Email, Organization",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchTitleTooltip,
                   Type = "string",
                   Value = "Search users",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchRoleLabel,
                   Type = "string",
                   Value = "Role",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchRoleTooltip,
                   Type = "string",
                   Value = "Role",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchStatusLabel,
                   Type = "string",
                   Value = "Status",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchStatusTooltip,
                   Type = "string",
                   Value = "Status",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchUpdateStatusLabel,
                   Type = "string",
                   Value = "Update options",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
               new PageConfiguration()
               {
                   Page = "usermanagement",
                   Name = UserManagementPageConfigurationKey.UserSearchUpdateStatusTooltip,
                   Type = "string",
                   Value = "Update options",
                   DateCreated = DateTime.UtcNow
               });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserFirstNameLabel,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserFirstNameTooltip,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserFirstNamePlaceholder,
                    Type = "string",
                    Value = "First name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserLastNameLabel,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserLastNameTooltip,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserLastNamePlaceholder,
                    Type = "string",
                    Value = "Last name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserUserNameLabel,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserUserNameTooltip,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserUserNamePlaceholder,
                    Type = "string",
                    Value = "User name",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserEmailLabel,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserEmailTooltip,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserEmailPlaceholder,
                    Type = "string",
                    Value = "Email",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserOrganizationLabel,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserOrganizationTooltip,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserOrganizationPlaceholder,
                    Type = "string",
                    Value = "Organization",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserRoleLabel,
                    Type = "string",
                    Value = "User role",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserRoleTooltip,
                    Type = "string",
                    Value = "User role",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserApproverRegionLabel,
                    Type = "string",
                    Value = "Approver region(s)",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserApproverRegionTooltip,
                    Type = "string",
                    Value = "Approver region(s)",
                    DateCreated = DateTime.UtcNow
                });

            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserApproverCountriesLabel,
                    Type = "string",
                    Value = "Approver country(ies)",
                    DateCreated = DateTime.UtcNow
                });


            configurations.Add(
                new PageConfiguration()
                {
                    Page = "usermanagement",
                    Name = UserManagementPageConfigurationKey.UserApproverCountriesTooltip,
                    Type = "string",
                    Value = "Approver country(ies)",
                    DateCreated = DateTime.UtcNow
                });
            return configurations;
        }
    }
}
