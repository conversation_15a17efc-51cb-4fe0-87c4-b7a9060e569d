﻿using Gina2.Blazor.Areas.Identity.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json.Linq;

namespace Gina2.Blazor.Helpers.RefreshToken
{
    public class RefreshToken : IRefreshToken
    {

        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IConfiguration _configuration;

        public RefreshToken(UserManager<ApplicationUser> userManager, IConfiguration configuration)
        {
            _userManager = userManager;
            _configuration = configuration;
        }
        public async Task<ApplicationUser> Refreshtoken(ApplicationUser user)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_configuration["AzureAdB2B:Authority"]}/oauth2/v2.0/token")
                {
                    Content = new FormUrlEncodedContent(new List<KeyValuePair<string, string>>
                    {
                        new("client_id", _configuration["AzureAdB2B:ClientId"]),
                        new("grant_type", "refresh_token"),
                        new("refresh_token", user.UserRefreshToken),
                        new("client_secret", _configuration["AzureAdB2B:ClientSecret"]),
                        new("scope", "https://graph.microsoft.com/.default")
                    })
                };
                var response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrWhiteSpace(jsonContent))
                    {
                        var parseObject = JObject.Parse(jsonContent);
                        if (parseObject != null)
                        {
                            user.UserToken = parseObject["access_token"]?.ToString();
                            user.UserRefreshToken = parseObject["refresh_token"]?.ToString();
                            user.Status = "Active";
                            await _userManager.UpdateAsync(user);
                        }
                    }
                }
                return user;
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
