﻿using Microsoft.AspNetCore.Components;

namespace Gina2.Blazor.Helpers
{
    public class TextHighlighter
    {
        public static MarkupString Highlight(string value, string highlightedValue)
        {
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrWhiteSpace(highlightedValue))
            {
                return new MarkupString(value ?? string.Empty);
            }

            value = value.Replace(highlightedValue, $"<span style='background-color:#f8ff00;'>{highlightedValue}</span>", StringComparison.OrdinalIgnoreCase);

            return new MarkupString(value);
        }
    }
}