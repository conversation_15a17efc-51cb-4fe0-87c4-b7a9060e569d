﻿using Gina2.Core;
using Gina2.DbModels;

namespace Gina2.Blazor.Helpers
{
    public class AppSettingsHelper
    {
        private readonly IConfiguration _configuration;

        public AppSettingsHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string GetBlobStorageConnectionString()
        {
            string value = _configuration.GetValue<string>(Constants.BlobStorage.AzureBlobAccessKey);

            if (!string.IsNullOrWhiteSpace(value))
            {
                return value;
            }

            return EnvironmentVariableProvider.BlobStorage.ConnectionString;
        }
    }

    public static class HelperMethod
    {
        public static string GetPageConfigrationValueByName(this List<PageConfiguration> PageConfigurations , string key)
        {
            return PageConfigurations.FirstOrDefault(e => e.Name == key)?.Value;
        }
        public static List<PageConfiguration> GetPageConfigrationByName(this List<PageConfiguration> PageConfigurations ,string key)
        {
            return PageConfigurations.Where(e => e.Name == key).ToList();
        }
        public static List<PageConfiguration> GetPageConfigrationsByName(this List<PageConfiguration> PageConfigurations, string key)
        {
            return PageConfigurations.Where(e => e.Name.Contains(key)).ToList();
        }
    }
}