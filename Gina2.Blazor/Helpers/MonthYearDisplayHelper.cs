﻿using System.Globalization;

namespace Gina2.Blazor.Helpers
{
    public static class MonthYearDisplayHelper
    {
        public static string GetMonthAndYearString(int? monthNumber, int? year)
        
        {
            if (!year.HasValue && !monthNumber.HasValue)
            {
                return "-";
            }
            string month = String.Empty;

            if(monthNumber.HasValue && monthNumber > 0)
            {
                month = new DateTimeFormatInfo().GetMonthName(monthNumber.Value);
            }

            return $"{month} {year}";
        }

        public static DateTime? GetMonthAndYearDate(int? monthNumber, int? year)
        {
            if (!year.HasValue)
            {
                return null;
            }

            if (!monthNumber.HasValue)
            {

                return new DateTime(year.Value, 1, 1); ;
            }

            return new DateTime(year.Value,monthNumber.Value,1);
        }
        public static DateTime? GetMonthAndYearEndDate(int? monthNumber, int? year)
        {
            if (!year.HasValue)
            {
                return null;
            }

            if (!monthNumber.HasValue)
            {

                return new DateTime(year.Value, 12, 31); ;
            }

            return new DateTime(year.Value, monthNumber.Value, 1);
        }
    }
}