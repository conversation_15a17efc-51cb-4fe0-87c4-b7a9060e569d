﻿using Joonasw.AspNetCore.SecurityHeaders.Csp;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System;
using System.Security.Cryptography;

namespace Gina2.Blazor.Helpers
{
    /// <summary>
    /// Tag helper for adding a nonce to
    /// inline scripts and styles.
    /// </summary>
    [HtmlTargetElement("script", Attributes = "asp-add-nonce")]
    [HtmlTargetElement("style", Attributes = "asp-add-nonce")]
    public class NonceTagHelper : TagHelper
    {
        [HtmlAttributeName("asp-add-nonce")]
        public bool AddNonce { get; set; }



        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            if (AddNonce)
            {
                // The nonce service is created per request, so we
                // get the same nonce here as the CSP header
                byte[] nonceBytes = new byte[32];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(nonceBytes);
                }

                output.Attributes.Add("nonce", Convert.ToBase64String(nonceBytes));
            }
        }
    }
}
