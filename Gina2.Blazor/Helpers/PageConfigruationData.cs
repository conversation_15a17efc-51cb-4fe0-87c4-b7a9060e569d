﻿using Gina2.Blazor.Areas.Identity.Data;
using Gina2.Blazor.CacheService;
using Gina2.Blazor.Helpers.PageConfigrationData;
using Gina2.DbModels;
using Gina2.MySqlRepository;
using Gina2.Services.ScoreCarad;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Gina2.Blazor.Helpers
{
    public class PageConfigruationData
    {
        private readonly IDbContextFactory<SqlDbContext> _DbFactory;
        private readonly ILogger<PageConfigruationData> _logger;
        private readonly IPageConfigurationCache _redisCache;
        public PageConfigruationData(IDbContextFactory<SqlDbContext> dbFactory, 
                                    ILogger<PageConfigruationData> logger
)
        {
            _DbFactory = dbFactory;
            _logger = logger;
           
        }
        public async Task SeedData()
        {
            try
            {
                using var _dbContext = _DbFactory.CreateDbContext();
                var pageconfigationData = GetData(); //100
                var databaseRecord = await _dbContext.PageConfigurations.ToListAsync();//99
                var result = pageconfigationData.Where(p => !databaseRecord.Any(x => x.Name == p.Name && x.Page == p.Page)).ToList(); //1
                var removeRecord = databaseRecord.Where(p => !pageconfigationData.Any(x => x.Name == p.Name && x.Page == p.Page)).ToList(); //delete if not available
                var updateRecord = pageconfigationData.Where(p=> databaseRecord.Any(x=>x.Name == p.Name && x.Page == p.Page && x.Value != p.Value)).ToList();
                if (removeRecord.Any())
                {
                    _dbContext.PageConfigurations.RemoveRange(removeRecord);
                }

                if (result.Any())
                {
                    await _dbContext.PageConfigurations.AddRangeAsync(result);
                }

                if(updateRecord.Any())
                {
                    foreach(var record in updateRecord)
                    {
                        var existingRecord = databaseRecord.First(x=>x.Name == record.Name && x.Page == record.Page);
                        existingRecord.Value = record.Value;
                        _dbContext.PageConfigurations.Update(existingRecord);

                    }
                }
                //_redisCache.RefreshData();
                var totalRecordChange = await _dbContext.SaveChangesAsync();
                _logger.LogInformation("total record updated {totalRecordChange}", totalRecordChange);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error ::", ex);

                throw;
            }
        }

        public List<PageConfiguration> GetData()
        {
            List<PageConfiguration> pageconfig = new();
            pageconfig.GetMapPageData();
            pageconfig.GetHomePageData();
            pageconfig.GetAdminPolicyData();
            pageconfig.GetSearchPageData();
            pageconfig.GetCountryPageData();
            pageconfig.GetContentListPageData();
            pageconfig.GetAboutUsPageData();
            pageconfig.GetScorecardPageData();
            pageconfig.GetPublishedScorecardPageData();
            pageconfig.GetScorecardCountryPageData();
            pageconfig.GetUserManagementPageData();
            pageconfig.GetRolePageData();
            pageconfig.GetTaxonomyPageData();
            pageconfig.GetUserProfilePageData();
            pageconfig.GetDashboardPageData();
            pageconfig.GetAdminMechanismData();
            pageconfig.GetAdminCommitmentData();
            pageconfig.GetProgramCreatePageData();
            pageconfig.GetScoreCardTopicPageData();
            pageconfig.GetScorecardCreatePageData();
            pageconfig.GetDisclaimerPageData();
            pageconfig.GetTermofUsePageData();
            pageconfig.GetMapIndicatorData();
            pageconfig.GetIndicatorTypeData();
            pageconfig.GetBulkImportDataTypePageConfigurationData();
            pageconfig.GetTaxonomyTopicPageData();
            pageconfig.GetComposeEmailPageData();
            pageconfig.GetStaticPageData();

            return pageconfig;
        }
    }
}

