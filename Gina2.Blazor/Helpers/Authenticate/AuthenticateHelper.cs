﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Gina2.MySqlRepository.Generic;
using Microsoft.AspNetCore.Components.Authorization;
using System.Globalization;
using System.Linq.Expressions;
using System.Security.Claims;

namespace Gina2.Blazor.Helpers.Authenticate
{
    public class AuthenticateHelper : IAuthenticateHelper
    {
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        private readonly IServiceProvider _serviceProvider;

        public AuthenticateHelper(AuthenticationStateProvider authenticationStateProvider, IServiceProvider serviceProvider)
        {
            _authenticationStateProvider = authenticationStateProvider;
            _serviceProvider = serviceProvider;
        }

        public async Task<(string, string)> IsAuthenticated()
        {
            var _authenticationState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var identityUser = _authenticationState.User.Identity;
            var roles = _authenticationState.User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();

            return (roles.Any() ? roles.FirstOrDefault().ToLower() : string.Empty, string.IsNullOrEmpty(identityUser.Name) ? string.Empty : identityUser.Name.ToLower());
        }

        public async Task<List<T>> IsPublished<T>(Expression<Func<T, bool>> predicate) where T : class
        {
            var genericRepository = _serviceProvider.GetRequiredService<IGenericRepository<T>>();
            var data = await genericRepository.FindAllAsync(predicate);
            return data.ToList();
        }
    }
}
